using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.CompilerServices;

namespace Entity.Tenant
{
    /// <summary>
    /// オーダー情報
    /// </summary>
    [Table("karte_edition")]
    [Serializable]
    [Index(nameof(HpId), nameof(RaiinNo), nameof(Edition), Name = "karte_edition")]

    /// <summary>
    /// create index to speed up performance
    /// </summary>
    [Index(nameof(HpId), nameof(PtId), nameof(SinDate), Name = "karte_edition_idx01")]
    public class KarteEdition : EmrCloneable<KarteEdition>
    {
        /// <summary>
        /// 医療機関識別ID
        /// </summary>

        [Column("hp_id", Order = 1)]
        public int HpId { get; set; }

        /// <summary>
        /// 患者ID
        ///     患者を識別するためのシステム固有の番号
        /// </summary>
        [Column("pt_id")]
        public long PtId { get; set; }

        [Column("sin_date")]
        public int SinDate { get; set; }

        /// <summary>
        /// 来院番号
        /// </summary>

        [Column("raiin_no", Order = 2)]
        public long RaiinNo { get; set; }


        [Column("edition", Order = 3)]
        [CustomAttribute.DefaultValue(1)]
        public int Edition { get; set; }

        [Column("karte_status")]
        [CustomAttribute.DefaultValue(1)]
        public int KarteStatus { get; set; }

        [Column("is_deleted")]
        [CustomAttribute.DefaultValue(0)]
        public int IsDeleted { get; set; }

        /// <summary>
        /// 作成日時	
        /// </summary>
        [Column("create_date")]
        [CustomAttribute.DefaultValueSql("current_timestamp")]
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 作成者
        /// </summary>
        [Column(name: "create_id")]
        [CustomAttribute.DefaultValue(0)]
        public int CreateId { get; set; }

        /// <summary>
        /// 更新日時	
        /// </summary>
        [Column("update_date")]
        public DateTime UpdateDate { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [Column(name: "update_id")]
        [CustomAttribute.DefaultValue(0)]
        public int UpdateId { get; set; }

        [Column("approval_date")]
        public DateTime? ApprovalDate { get; set; }

        [Column(name: "approval_id")]
        [CustomAttribute.DefaultValue(0)]
        public int ApprovalId { get; set; }
    }
}