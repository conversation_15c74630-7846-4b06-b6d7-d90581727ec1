using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Tenant
{
    [Table(name: "common_center_item_mst")]
    public class CommonCenterItemMst
    {
        [Column("center_cd")]
        [StringLength(10)]
        public string CenterCd { get; set; } = string.Empty;

        [Column("item_cd")]
        [StringLength(10)]
        public string ItemCd { get; set; } = string.Empty;

        [Column("kensa_item_cd")]
        [StringLength(20)]
        public string KensaItemCd { get; set; } = string.Empty;

        [Column("start_date")]
        public int StartDate { get; set; } = 0;

        [Column("end_date")]
        public int? EndDate { get; set; } = 0;

        [Column("name")]
        [StringLength(120)]
        public string? Name { get; set; }

        [Column("kana_name")]
        [StringLength(20)]
        public string? KanaName { get; set; }

        [Column("santei_item_cd")]
        [StringLength(10)]
        public string? SanteiItemCd { get; set; }

        [Column("update_date")]
        public DateTime UpdateDate { get; set; }
    }
}
