﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Tenant
{
    [Table("patient_message")]
    public class PatientMessage: EmrCloneable<PatientMessage>
    {
        [Column("message_id")]
        public string MessageId { get; set; }

        [Column("message_type")]
        public string MessageType { get; set; }

        [Column("channel_id")]
        public int ChannelId { get; set; }

        [Column("content")]
        public string Content { get; set; }

        [Column("hospital_id")]
        public int HospitalId { get; set; }

        [Column("is_patient")]
        public int IsPatient { get; set; }

        [Column("posted_member")]
        public string PostedMember { get; set; }

        [Column("created_by")]
        public string CreatedBy { get; set; }

        [Column("updated_by")]
        public string UpdatedBy { get; set; }

        [Column("created_at")]
        public string CreatedAt { get; set; }

        [Column("updated_at")]
        public string UpdatedAt { get; set; }

        [Column("is_deleted")]
        [CustomAttribute.DefaultValue(0)]
        public int IsDeleted { get; set; }
    }
}
