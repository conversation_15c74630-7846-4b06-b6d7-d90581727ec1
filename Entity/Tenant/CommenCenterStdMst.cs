using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Tenant
{
    [Table("common_center_std_mst")]
    public class CommonCenterStdMst
    {
        /// <summary>
        /// センターコード
        /// </summary>
        [Key]
        [Column("center_cd")]
        [MaxLength(10)]
        public string CenterCd { get; set; }

        /// <summary>
        /// 電子カルテ検査コード
        /// </summary>
        [Key]
        [Column("kensa_item_cd")]
        [MaxLength(10)]
        public string KensaItemCd { get; set; }
        /// <summary>
        /// 開始日
        /// yyyymmdd
        /// </summary>
        [Column("start_date")]
        [CustomAttribute.DefaultValue(0)]
        public int StartDate { get; set; }

        /// <summary>
        /// 終了日
        /// yyyymmdd
        /// </summary>
        [Column("end_date")]
        [CustomAttribute.DefaultValue(99999999)]
        public int EndDate { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        [Column("meal_std")]
        [MaxLength(60)]
        public string? MealStd { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        [Column("meal_std_low")]
        [MaxLength(60)]
        public string? MealStdLow { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        [Column("meal_std_high")]
        [MaxLength(60)]
        public string? MealStdHigh { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        [Column("femel_std")]
        [MaxLength(60)]
        public string? FemelStd { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        [Column("femel_std_low")]
        [MaxLength(60)]
        public string? FemelStdLow { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        [Column("femel_std_high")]
        [MaxLength(60)]
        public string? FemelStdHigh { get; set; }
        
        /// <summary>
        /// 更新日時
        /// </summary>
        [Column("update_date")]
        public DateTime UpdateDate { get; set; }
    }
}