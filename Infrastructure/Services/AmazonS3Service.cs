﻿using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Domain.Models.KarteFile;
using Infrastructure.Common;
using Infrastructure.Constants;
using Infrastructure.Interfaces;
using Infrastructure.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System.Net;
using System.Text;

namespace Infrastructure.Services;

public sealed class AmazonS3Service : IAmazonS3Service, IDisposable
{
    private readonly AmazonS3Options _options;
    private readonly IAmazonS3 _s3Client;
    private readonly ITenantProvider _tenantProvider;
    private IConfiguration _configuration;

    public AmazonS3Service(IOptions<AmazonS3Options> optionsAccessor, ITenantProvider tenantProvider, IConfiguration configuration)
    {
        _configuration = configuration;
        _options = optionsAccessor.Value;
        if (_options != null)
        {
            var regionEndpoint = RegionEndpoint.GetBySystemName(_options.Region);
            var isUseKey = _configuration.GetSection(ConfigConstant.AmazonS3)[ConfigConstant.IsUseKey] == "true";
            if (isUseKey)
            {
                _s3Client = new AmazonS3Client(_options.AwsAccessKeyId, _options.AwsSecretAccessKey, regionEndpoint);
            }
            else
            {
                _s3Client = new AmazonS3Client(regionEndpoint);
            }

        }
        _tenantProvider = tenantProvider;
    }

    public async Task<bool> ObjectExistsAsync(string key)
    {
        try
        {
            var response = await _s3Client.GetObjectAsync(_options.BucketName, key);
            return response.HttpStatusCode == HttpStatusCode.OK;
        }
        catch (AmazonS3Exception e)
        {
            if (e.StatusCode == HttpStatusCode.NotFound)
            {
                return false;
            }

            throw;
        }
    }

    public async Task<bool> FolderExistsAsync(string folderPath)
    {
        try
        {
            // Thêm dấu "/" vào cuối nếu chưa có
            if (!folderPath.EndsWith("/"))
                folderPath += "/";

            // Tạo request để liệt kê các đối tượng có tiền tố này
            var request = new ListObjectsV2Request
            {
                BucketName = _options.BucketName,
                Prefix = folderPath,
                MaxKeys = 1 // Chỉ cần 1 key là biết folder tồn tại
            };

            var response = await _s3Client.ListObjectsV2Async(request);

            // Nếu có ít nhất 1 đối tượng với tiền tố này, folder được coi là tồn tại
            return response.S3Objects.Count > 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Lỗi kiểm tra folder: {ex.Message}");
            return false;
        }
    }

    public void Dispose()
    {
        _s3Client.Dispose();
        _tenantProvider.DisposeDataContext();
    }

    public async Task<bool> DeleteObjectAsync(string key)
    {
        try
        {
            var listVersionsRequest = new ListVersionsRequest
            {
                BucketName = _options.BucketName,
                Prefix = key
            };
            var listVersionsReplicationRequest = new ListVersionsRequest
            {
                BucketName = _options.BucketNameReplication,
                Prefix = key
            };

            /// Delete 
            await _DeleteObjectByBucket(listVersionsRequest, _options.BucketName);

            ///Copy, delete replication
            await _CopyObjectReplyCation(listVersionsReplicationRequest, key);
            await _DeleteObjectByBucket(listVersionsReplicationRequest, _options.BucketNameReplication);
            return true;
        }
        catch (AmazonS3Exception)
        {
            return false;
        }
    }

    public async Task<bool> DeleteObjectIfExistsAsync(int hpId, string key)
    {
        var metadataRequest = new GetObjectMetadataRequest
        {
            BucketName = _options.BucketName,
            Key = GenPathFolderFileWithOutPtId(hpId) + key
        };

        try
        {
            await _s3Client.GetObjectMetadataAsync(metadataRequest);
            var deleteRequest = new DeleteObjectRequest
            {
                BucketName = _options.BucketName,
                Key = GenPathFolderFileWithOutPtId(hpId) + key
            };
            await _s3Client.DeleteObjectAsync(deleteRequest);
            return true;
        }
        catch (AmazonS3Exception e) when (e.ErrorCode == "NoSuchKey")
        {
            throw;
        }
    }


    public async Task<bool> DeleteLastestVerObjectAsync(string key)
    {
        try
        {
            string bucketName = _options.BucketName;
            var listVersionsRequest = new ListVersionsRequest
            {
                BucketName = bucketName,
                Prefix = key
            };

            /// Delete 
            var listVersionsResponse = await _s3Client.ListVersionsAsync(listVersionsRequest);

            var objectsToDelete = listVersionsResponse.Versions
                                  .Where(item => item.IsLatest)
                                  .Select(v => new KeyVersion { Key = v.Key, VersionId = v.VersionId })
                                  .ToList();

            if (objectsToDelete.Any())
            {
                var deleteObjectsRequest = new DeleteObjectsRequest
                {
                    BucketName = bucketName,
                    Objects = objectsToDelete
                };

                var deleteObjectsResponse = await _s3Client.DeleteObjectsAsync(deleteObjectsRequest);

                // Check the response for any errors
                if (deleteObjectsResponse.DeleteErrors.Any())
                {
                    Console.WriteLine("Some objects could not be deleted. Error details:");
                    foreach (var error in deleteObjectsResponse.DeleteErrors)
                    {
                        Console.WriteLine($"Object Key: {error.Key}, VersionId: {error.VersionId}, Code: {error.Code}, Message: {error.Message}");
                    }
                }
            }
            return true;
        }
        catch (AmazonS3Exception)
        {
            return false;
        }
    }

    public async Task<bool> MoveObjectAsync(string sourceFile, string destinationFile)
    {
        try
        {
            var request = new CopyObjectRequest
            {
                SourceBucket = _options.BucketName,
                SourceKey = sourceFile,
                DestinationBucket = _options.BucketName,
                DestinationKey = destinationFile
            };
            await _s3Client.CopyObjectAsync(request);

            var response = await _s3Client.DeleteObjectAsync(_options.BucketName, sourceFile);
            return Convert.ToBoolean(response.DeleteMarker);
        }
        catch (AmazonS3Exception)
        {
            return false;
        }
    }

    public async Task<bool> CopyObjectAsync(string sourceFile, string destinationFile)
    {
        try
        {
            var request = new CopyObjectRequest
            {
                SourceBucket = _options.BucketName,
                SourceKey = sourceFile,
                DestinationBucket = _options.BucketName,
                DestinationKey = destinationFile
            };
            var response = await _s3Client.CopyObjectAsync(request);

            return response.HttpStatusCode == HttpStatusCode.OK;
        }
        catch (AmazonS3Exception)
        {
            return false;
        }
    }

    public async Task<List<string>> GetListObjectAsync(string prefix)
    {
        List<string> listObjects = new();

        var listRequest = new ListObjectsV2Request
        {
            BucketName = _options.BucketName,
            Prefix = prefix
        };

        ListObjectsV2Response listResponse;
        do
        {
            // Get a list of objects
            listResponse = await _s3Client.ListObjectsV2Async(listRequest);
            foreach (S3Object obj in listResponse.S3Objects)
            {
                listObjects.Add(obj.Key);
            }
            listRequest.ContinuationToken = listResponse.NextContinuationToken;
        } while (listResponse.IsTruncated);

        return listObjects;
    }

    public async Task<string> GetObjectAsync(string key)
    {
        string objectLink = string.Empty;

        GetObjectResponse listResponse;
        // Get a list of objects
        listResponse = await _s3Client.GetObjectAsync(_options.BucketName, key);
        objectLink = listResponse.Key;

        return objectLink;
    }

    public async Task<string> UploadObjectAsync(string path, string fileName, Stream stream, bool getOnlyId = false)
    {
        var memoryStream = await stream.ToMemoryStreamAsync();
        return await UploadObjectAsync(path, fileName, memoryStream, getOnlyId);
    }

    public async Task<string> UploadObjectAsync(string path, string fileName, MemoryStream memoryStream, bool getOnlyId = false)
    {
        try
        {
            var request = new PutObjectRequest
            {
                BucketName = _options.BucketName,
                Key = path + fileName,
                InputStream = memoryStream,
            };
            var response = await _s3Client.PutObjectAsync(request);
            var checkOnlyId = getOnlyId ? request.Key : GetAccessUrl(request.Key);
            return response.HttpStatusCode == HttpStatusCode.OK ? checkOnlyId ?? string.Empty : string.Empty;
        }
        catch (AmazonS3Exception ex)
        {
            Console.WriteLine($"S3 Error creating folder: '{ex.Message}'");
            return string.Empty;
        }
    }

    public async Task<string> UploadFolderAsync(string path, string folderName, bool getOnlyId = false)
    {
        try
        {
            var request = new PutObjectRequest
            {
                BucketName = _options.BucketName,
                Key = path + folderName,
                ContentBody = "",
            };
            var response = await _s3Client.PutObjectAsync(request);
            var checkOnlyId = getOnlyId ? request.Key : GetAccessUrl(request.Key);
            return response.HttpStatusCode == HttpStatusCode.OK ? checkOnlyId ?? string.Empty : string.Empty;
        }
        catch (AmazonS3Exception)
        {
            return string.Empty;
        }
    }


    public string GetFolderUploadToPtNum(List<string> folders, long ptNum)
    {
        var tenantId = _tenantProvider.GetDomainName();
        var ptNumString = ptNum.ToString();
        if (ptNum.ToString().Length < 4)
        {
            ptNumString = ptNumString.PadLeft(4, '0');
        }
        string last4Characters = ptNumString.Substring(ptNumString.Length - 4);
        StringBuilder result = new();
        result.Append(tenantId);
        result.Append("/");
        foreach (var item in folders)
        {
            result.Append(item);
            result.Append("/");
        }
        result.Append(last4Characters.Substring(0, 2));
        result.Append("/");
        result.Append(last4Characters.Substring(2, 2));
        result.Append("/");
        result.Append(ptNum.ToString());
        result.Append("/");
        return result.ToString();
    }

    public string GetUniqueFileNameKey(string fileName)
    {
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
        var extension = Path.GetExtension(fileName);
        var uniqueFileName = $"{fileNameWithoutExtension}-{Guid.NewGuid()}{extension}";
        int fileNameLength = uniqueFileName.Length;
        if (fileNameLength > 100)
        {
            uniqueFileName = uniqueFileName.Substring(fileNameLength - 100, 100);
        }
        return uniqueFileName;
    }

    public string GetFolderUploadOther(List<string> folders)
    {
        var tenantId = _tenantProvider.GetDomainName();
        StringBuilder result = new();
        result.Append(tenantId);
        result.Append("/");
        foreach (var item in folders)
        {
            result.Append(item);
            result.Append("/");
        }
        return result.ToString();
    }

    public string GetAccessBaseS3() => $"{_options.BaseAccessUrl}/";

    public async Task<(bool valid, string key)> S3FilePathIsExists(string locationFile)
    {
        var s3Key = locationFile.Replace("\\", "/").TrimStart('/');
        var listS3Objects = await _s3Client.ListObjectsV2Async(new ListObjectsV2Request
        {
            BucketName = _options.BucketName,
            Prefix = s3Key, // eg myfolder/myimage.jpg (no / at start)
            MaxKeys = 1
        });

        return (listS3Objects.S3Objects.Any(), s3Key);
    }

    public string GenerateS3FileName(String fileName)
    {
        string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
        string fileNameExtension = Path.GetExtension(fileName);

        StringBuilder s3FileNameBuilder = new StringBuilder(fileNameWithoutExtension);
        s3FileNameBuilder.Append("_");
        s3FileNameBuilder.Append(Guid.NewGuid());
        if (!string.IsNullOrEmpty(fileNameExtension))
        {
            s3FileNameBuilder.Append(fileNameExtension);
        }

        return s3FileNameBuilder.ToString();
    }

    public string GetLinkDownload(int hpId, long ptId, string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return string.Empty;
        }
        var cloudFrontHost = GetCloudFrontHost();

        return CombineUrl(cloudFrontHost, hpId, fileName, ptId);

    }

    public string CombineUrl(string host, int hpId, string fileName, long ptId)
    {
        if (string.IsNullOrEmpty(host) || hpId < 0 || string.IsNullOrEmpty(fileName))
        {
            return string.Empty;
        }

        var folderKarteFile = GetFolderKarteFile(hpId, ptId);

        host = host.TrimEnd('/');
        folderKarteFile = folderKarteFile.TrimStart('/').TrimEnd('/');
        fileName = fileName.TrimStart('/');

        return $"{host}/{folderKarteFile}/{fileName}";
    }

    public string GenerateDownloadLink(int hpId, long ptId, string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return string.Empty;
        }
        var cloudFrontHost = GetCloudFrontHost();

        return GenerateUrl(cloudFrontHost, hpId, fileName, ptId);

    }

    public string GenerateUrl(string host, int hpId, string fileName, long ptId)
    {
        if (string.IsNullOrEmpty(host) || hpId < 0 || string.IsNullOrEmpty(fileName))
        {
            return string.Empty;
        }
        if (fileName.StartsWith("/"))
        {
            fileName = fileName.Substring(1);
        }
        return $"{host}{fileName}";
    }

    public string GenPathFolderFile(long ptId, int hpId)
    {
        var ptNumString = ptId.ToString();
        if (ptId.ToString().Length < 4)
        {
            ptNumString = ptNumString.PadLeft(4, '0');
        }
        string last4Characters = ptNumString.Substring(ptNumString.Length - 4);
        string firstTwoChars = last4Characters.Substring(0, 2);
        string lastTwoChars = last4Characters.Substring(2, 2);

        return $"{hpId}/{ConfigConstant.Store}/{ConfigConstant.Files}/{firstTwoChars}/{lastTwoChars}/{ptId}/";
    }

    private string GenPathFolderFileWithOutPtId(int hpId)
    {
        return $"{hpId}/{ConfigConstant.Custombutton_image}/";
    }

    public List<FilePreSignedUrlModel> GeneratePreSignedUrls(List<string> fileNames, long ptId, int hpId)
    {
        var preSignedUrls = new List<FilePreSignedUrlModel>();
        if (fileNames == null || fileNames.Count == 0)
        {
            return preSignedUrls;
        }

        foreach (var fileName in fileNames)
        {
            if (string.IsNullOrEmpty(fileName))
            {
                preSignedUrls.Add(new FilePreSignedUrlModel { FileName = fileName, PreSignedUrl = string.Empty });
                continue;
            }
            string folderPath = ptId > 0 ? GenPathFolderFile(ptId, hpId) : GenPathFolderFileWithOutPtId(hpId);
            string uploadPath = string.Empty;
            bool folderExist = false;
            if (FolderExistsAsync(folderPath).Result)
            {
                folderExist = true;
            }

            if (folderExist)
            {
                uploadPath = folderPath;
            }
            else
            {
                var responseFolder = UploadFolderAsync(folderPath, string.Empty);
                responseFolder.Wait();
                if (responseFolder.Result.Length > 0)
                {
                    uploadPath = folderPath;
                }
            }

            string bucketName = _options.BucketName;
            string objectKey = uploadPath + fileName;
            DateTime expiration = DateTime.UtcNow.AddMinutes(30);
            var requestPut = new GetPreSignedUrlRequest
            {
                BucketName = bucketName,
                Key = objectKey,
                Expires = expiration,
                Verb = HttpVerb.PUT
            };

            string preSignedUrl = _s3Client.GetPreSignedURL(requestPut);

            preSignedUrls.Add(new FilePreSignedUrlModel
            {
                FileName = fileName,
                PreSignedUrl = preSignedUrl
            });
        }

        return preSignedUrls;
    }

    public async Task<string> GetPreSignedUrlsAsync(string fileName, long ptId, int hpId)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return string.Empty;
        }
        string folderPath = ptId > 0 ? GenPathFolderFile(ptId, hpId) : GenPathFolderFileWithOutPtId(hpId);
        if (!await CheckObjectExistsAsync(_options.BucketName, folderPath + fileName))
        {
            return string.Empty;
        }
        var request = new GetPreSignedUrlRequest
        {
            BucketName = _options.BucketName,
            Key = folderPath + fileName,
            Expires = DateTime.UtcNow.AddDays(1),
            Verb = HttpVerb.GET
        };
        return _s3Client.GetPreSignedURL(request);
    }


    public async Task<bool> CheckObjectExistsAsync(string bucketName, string objectKey)
    {
        try
        {
            var request = new ListObjectsV2Request
            {
                BucketName = bucketName,
                Prefix = objectKey
            };
            var response = await _s3Client.ListObjectsV2Async(request);
            return response.S3Objects.Any(obj => obj.Key == objectKey);
        }
        catch (AmazonS3Exception ex)
        {
            Console.WriteLine($"Error occurred: {ex.Message}");
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Unexpected error occurred: {ex.Message}");
            return false;
        }
    }

    public string GetFolderKarteFile(int hpId, long ptId)
    {
        if (hpId < 0 || ptId < 0)
        {
            return string.Empty;
        }

        var subFolder = GetSubFolderByPtId(ptId);
        subFolder = subFolder.TrimStart('/').TrimEnd('/');

        return $"{hpId}/{ConfigConstant.Store}/{ConfigConstant.Files}/{subFolder}/{ptId}/";
    }

    public string GetSubFolderByPtId(long ptId)
    {
        var sPtId = ptId.ToString();
        if (sPtId.Length < 4)
        {
            sPtId = sPtId.PadLeft(4, '0');
        }

        string last4Characters = sPtId.Substring(sPtId.Length - 4);
        string firstTwoChars = last4Characters.Substring(0, 2);
        string lastTwoChars = last4Characters.Substring(2, 2);

        return $"/{firstTwoChars}/{lastTwoChars}/";
    }

    public string GetCloudFrontHost()
    {
        return _configuration.GetSection(ConfigConstant.AmazonS3)[ConfigConstant.CloudFrontHost] ?? string.Empty;
    }

    public string GetPreSignedUrlCommons(string key)
    {
        if (string.IsNullOrEmpty(key))
        {
            return string.Empty;
        }
        var request = new GetPreSignedUrlRequest
        {
            BucketName = _options.BucketName,
            Key = key,
            Expires = DateTime.UtcNow.AddHours(1),
            Verb = HttpVerb.GET
        };
        string url = _s3Client.GetPreSignedURL(request);
        return url;
    }
    public async Task<bool> DeleteObjectNotHaveReplicalAsync(string key)
    {
        try
        {
            var listVersionsRequest = new ListVersionsRequest
            {
                BucketName = _options.BucketName,
                Prefix = key
            };

            /// Delete 
            await _DeleteObjectByBucket(listVersionsRequest, _options.BucketName);
            return true;
        }
        catch (AmazonS3Exception)
        {
            return false;
        }
    }

    public string GetPreSignedUrlsWithCusBucket(string fileName, long ptId, int hpId)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return string.Empty;
        }
        var request = new GetPreSignedUrlRequest
        {
            BucketName = _options.S3CustomerFileBucket,
            Key = fileName,
            Expires = DateTime.UtcNow.AddHours(1),
            Verb = HttpVerb.GET
        };
        string url = _s3Client.GetPreSignedURL(request);
        return url;
    }

    public byte[] GetFileS3UsePresign(string url)
    {
        var urlLinkFile = GetPreSignedUrlCommons(url);
        using (var httpClient = new HttpClient())
        {
            var responseStream = httpClient.GetStreamAsync(urlLinkFile).Result;
            using (var stream = new MemoryStream())
            {
                responseStream.CopyTo(stream);
                byte[] bytes = stream.ToArray();

                return bytes;
            }
        }
    }


    #region Private function
    private static string CombineUrl(string host, string s3ObjectKey)
    {
        if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(s3ObjectKey))
        {
            return string.Empty;
        }

        host = host.TrimEnd('/');
        s3ObjectKey = s3ObjectKey.TrimStart('/');

        return $"{host}/{s3ObjectKey}";
    }

    private string GetAccessUrl(string key)
    {
        return $"{_options.BaseAccessUrl}/{key}";
    }
    private static string GetLeftName(string inputString)
    {
        int lastIndex = inputString.LastIndexOf('/');
        int secondLastIndex = inputString.LastIndexOf('/', lastIndex - 1);

        if (lastIndex >= 0 && secondLastIndex >= 0)
        {
            string result = inputString.Substring(0, secondLastIndex + 1);
            return result;
        }
        return string.Empty;
    }
    private static string GetRightName(string inputString)
    {

        int lastIndex = inputString.LastIndexOf('/');
        int secondLastIndex = inputString.LastIndexOf('/', lastIndex - 1);

        if (lastIndex >= 0 && secondLastIndex >= 0)
        {
            string result = inputString.Substring(secondLastIndex + 1, lastIndex - secondLastIndex - 1);
            return result + "/";
        }
        return string.Empty;
    }
    private static string AddFrefixDelete(string inputString)
    {
        char separator = '/';
        string[] segments = inputString.Split(separator);

        for (int i = 0; i < segments.Length; i++)
        {
            if (!string.IsNullOrWhiteSpace(segments[i]))
            {
                segments[i] = $"delete-{segments[i]}";
            }
        }

        return string.Join("/", segments);
    }
    private static string CutString(string substring1, string inputString)
    {
        int firstOccurrenceIndex = inputString.IndexOf(substring1);

        if (firstOccurrenceIndex != -1)
        {
            string part1 = inputString.Substring(0, firstOccurrenceIndex);
            string part2 = inputString.Substring(firstOccurrenceIndex + substring1.Length);
            return part1 + part2;
        }

        return inputString;
    }
    private async Task _DeleteObjectByBucket(ListVersionsRequest listVersionsRequest, string bucketName)
    {
        ListVersionsResponse listVersionsResponse;
        do
        {
            listVersionsResponse = await _s3Client.ListVersionsAsync(listVersionsRequest);

            var objectsToDelete = listVersionsResponse.Versions
                .Select(v => new KeyVersion { Key = v.Key, VersionId = v.VersionId })
                .ToList();

            if (objectsToDelete.Any())
            {
                var deleteObjectsRequest = new DeleteObjectsRequest
                {
                    BucketName = bucketName,
                    Objects = objectsToDelete
                };

                var deleteObjectsResponse = await _s3Client.DeleteObjectsAsync(deleteObjectsRequest);

                // Check the response for any errors
                if (deleteObjectsResponse.DeleteErrors.Any())
                {
                    Console.WriteLine("Some objects could not be deleted. Error details:");
                    foreach (var error in deleteObjectsResponse.DeleteErrors)
                    {
                        Console.WriteLine($"Object Key: {error.Key}, VersionId: {error.VersionId}, Code: {error.Code}, Message: {error.Message}");
                    }
                }
            }

            // Set markers for the next iteration
            listVersionsRequest.KeyMarker = listVersionsResponse.NextKeyMarker;
            listVersionsRequest.VersionIdMarker = listVersionsResponse.NextVersionIdMarker;

        } while (listVersionsResponse.IsTruncated);
        Console.WriteLine("Objects deleted in replication successfully.");

    }
    private async Task _CopyObjectReplyCation(ListVersionsRequest listVersionsReplicationRequest, string key)
    {
        if (!key.EndsWith("/"))
        {
            key += "/";
        }
        ListVersionsResponse listVersionsReplicationResponse;
        do
        {
            listVersionsReplicationResponse = await _s3Client.ListVersionsAsync(listVersionsReplicationRequest);
            /// Copy to Replication (frefix: delete-)
            var listVersionLastest = listVersionsReplicationResponse.Versions.Where(i => i.IsLatest && i.IsDeleteMarker == false);
            if (listVersionLastest.Any())
            {
                var rootFolder = string.Empty;
                int count = key.Split("/").Length - 1;
                if (count == 1)
                {
                    rootFolder = $"delete-{key}";
                }
                else
                {
                    rootFolder = GetLeftName(key) + "delete-" + GetRightName(key);
                }
                Parallel.ForEach(listVersionLastest, version =>
                {
                    try
                    {
                        var copyObjectRequest = new CopyObjectRequest
                        {
                            SourceBucket = _options.BucketNameReplication,
                            SourceKey = version.Key,
                            DestinationBucket = _options.BucketNameReplication,
                        };
                        if (version.Key.EndsWith("/"))
                        {

                            char separator = '/';
                            var checkKey = key;
                            if (!key.EndsWith(separator.ToString()))
                            {
                                checkKey = key + separator;
                            }
                            var cutString = CutString(checkKey, version.Key);
                            copyObjectRequest.DestinationKey = rootFolder + AddFrefixDelete(cutString);
                        }
                        else
                        {
                            if (listVersionLastest.Count() == 1)
                            {
                                if (rootFolder.EndsWith("/"))
                                {
                                    rootFolder = rootFolder.Substring(0, rootFolder.Length - 1);
                                }
                                copyObjectRequest.DestinationKey = rootFolder;
                            }
                            else
                            {
                                char separator = '/';
                                var checkKey = key;
                                if (!key.EndsWith(separator.ToString()))
                                {
                                    checkKey = key + separator;
                                }
                                var cutString = CutString(checkKey, version.Key);
                                copyObjectRequest.DestinationKey = rootFolder + AddFrefixDelete(cutString);
                            }

                        }
                        var copyObjectResponse = _s3Client.CopyObjectAsync(copyObjectRequest).Result;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Exception copy {version.Key}: {ex.Message}");
                    }

                });
            }
            // Set markers for the next iteration
            listVersionsReplicationRequest.KeyMarker = listVersionsReplicationResponse.NextKeyMarker;
            listVersionsReplicationRequest.VersionIdMarker = listVersionsReplicationResponse.NextVersionIdMarker;

        } while (listVersionsReplicationResponse.IsTruncated);
        Console.WriteLine("Objects copied to replication successfully.");
    }
    #endregion
}