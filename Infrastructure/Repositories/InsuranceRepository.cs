﻿using System.Data;
using System.Linq.Expressions;
using System.Text.Json;
using Domain.Constant;
using Domain.Models.Insurance;
using Domain.Models.Insurance.AIChat;
using Domain.Models.InsuranceInfor;
using Domain.Models.InsuranceMst;
using Domain.Models.MaxMoney;
using Domain.Models.Online;
using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Extension;
using Helper.Mapping;
using Helper.Redis;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;

namespace Infrastructure.Repositories
{
    public class InsuranceRepository : RepositoryBase, IInsuranceRepository
    {
        private readonly IDatabase _cache;
        private readonly IConfiguration _configuration;
        private readonly IAmazonS3Service _amazonS3Service;

        public InsuranceRepository(ITenantProvider tenantProvider, IConfiguration configuration, IAmazonS3Service amazonS3Service = null) : base(tenantProvider)
        {
            _configuration = configuration;
            GetRedis();
            _cache = RedisConnectorHelper.Connection.GetDatabase();
            _amazonS3Service = amazonS3Service;
        }
        public void GetRedis()
        {
            string connection = string.Concat(_configuration["Redis:RedisHost"], ":", _configuration["Redis:RedisPort"]);
            if (RedisConnectorHelper.RedisHost != connection)
            {
                RedisConnectorHelper.RedisHost = connection;
            }
        }
        public InsuranceDataModel GetInsuranceListById(int hpId, long ptId, int sinDate, bool flag = true, bool isDeletedPtHokenInf = false)
        {
            int prefCd = 0;
            var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
            if (hpInf != null)
            {
                prefCd = hpInf.PrefNo;
            }

            #region max-id-insurance
            int maxIdHokenInf = NoTrackingDataContext.PtHokenInfs.Where(h => h.HpId == hpId && h.PtId == ptId).DefaultIfEmpty()?.Max(p => p == null ? 0 : p.HokenId) ?? 0;
            int maxIdKohi = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId).DefaultIfEmpty()?.Max(p => p == null ? 0 : p.HokenId) ?? 0;
            int maxPidHokenPattern = NoTrackingDataContext.PtHokenPatterns.Where(x => x.PtId == ptId && x.HpId == hpId).DefaultIfEmpty()?.Max(p => p == null ? 0 : p.HokenPid) ?? 0;
            #endregion

            #region PtHokenInf
            IQueryable<PtHokenInf> hokenInfQuery = NoTrackingDataContext.PtHokenInfs.Where(h => h.HpId == hpId && h.PtId == ptId && (isDeletedPtHokenInf || (h.IsDeleted == DeleteTypes.None || h.HokenId == maxIdHokenInf))).OrderByDescending(x => x.HokenId);

            // if flag is true, get hokenMst between startDate and endDate
            var hokenMasterInfQuery = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId && (!flag || (h.StartDate <= sinDate && sinDate <= h.EndDate)) &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                     .GroupBy(x => new
                                     {
                                         x.HpId,
                                         x.HokenNo,
                                         x.HokenEdaNo,
                                     }).Select(grp => new
                                     {
                                         HpId = grp.Key.HpId,
                                         HokenNo = (int?)grp.Key.HokenNo ?? 0,
                                         HokenEdaNo = grp.Key.HokenEdaNo,
                                         StartDate = grp.Max(x => x.StartDate)
                                     });

            var hokenMasterInfQueryDF = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                        .GroupBy(x => new
                                        {
                                            x.HpId,
                                            x.HokenNo,
                                            x.HokenEdaNo
                                        }).Select(grp => new
                                        {
                                            HpId = grp.Key.HpId,
                                            HokenNo = grp.Key.HokenNo,
                                            HokenEdaNo = grp.Key.HokenEdaNo,
                                            StartDate = grp.Max(x => x.StartDate)
                                        });

            var hokenMasterInfQueryTarget = from hokenDf in hokenMasterInfQueryDF
                                            join hokenPrioritize in hokenMasterInfQuery
                                            on new { hokenDf.HokenEdaNo, hokenDf.HokenNo, hokenDf.HpId } equals new { hokenPrioritize.HokenEdaNo, hokenPrioritize.HokenNo, hokenPrioritize.HpId } into obj
                                            from hoken in obj.DefaultIfEmpty()
                                            select new
                                            {
                                                HpId = hokenDf.HpId,
                                                HokenNo = hokenDf.HokenNo,
                                                HokenEdaNo = hokenDf.HokenEdaNo,
                                                StartDate = (hoken.HokenNo == 0 && hoken.HokenEdaNo == 0) ? hokenDf.StartDate : (int?)hoken.StartDate ?? 0
                                            };

            IQueryable<HokenMst> hokenMasterFinal = from hoken in hokenMasterInfQueryTarget
                                                    select NoTrackingDataContext.HokenMsts.FirstOrDefault(h => h.HpId == hpId && h.HokenNo == hoken.HokenNo && h.HokenEdaNo == hoken.HokenEdaNo && h.StartDate == hoken.StartDate && (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1));

            var queryHokenInf = (from inf in hokenInfQuery
                                 join hkMaster in hokenMasterFinal on new { inf.HokenNo, inf.HokenEdaNo } equals new { hkMaster.HokenNo, hkMaster.HokenEdaNo } into hkMtObject
                                 from hkObj in hkMtObject.DefaultIfEmpty()
                                 join roudou in NoTrackingDataContext.RoudouMsts
                                 on hkObj.PrefNo.ToString() equals roudou.RoudouCd into rouObject
                                 from rou in rouObject.DefaultIfEmpty()
                                 select new
                                 {
                                     HokenInf = inf,
                                     HokenMaster = hkObj,
                                     HokenCheckList = (from hkC in NoTrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId && x.PtID == ptId && x.IsDeleted == DeleteStatus.None && x.HokenGrp == HokenGroupConstant.HokenGroupHokenPattern
                                                                            && x.HokenId == inf.HokenId).OrderByDescending(o => o.CheckDate)
                                                       join userMst in NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId)
                                                       on hkC.CheckId equals userMst.UserId
                                                       select new
                                                       {
                                                           HokenCheck = hkC,
                                                           UserInfo = userMst
                                                       }
                                                ).ToList(),
                                     RousaiTenkis = NoTrackingDataContext.PtRousaiTenkis.Where(x => x.HpId == hpId && x.PtId == ptId && x.HokenId == inf.HokenId && x.IsDeleted == DeleteStatus.None).OrderBy(x => x.EndDate).ToList(),
                                     Roudou = rou
                                 }).ToList();

            List<HokenInfModel> hokenInfList = queryHokenInf.Select(item =>
            {
                string houbetuNo = string.Empty;
                string hokensyaNoSearch = string.Empty;
                CIUtil.GetHokensyaHoubetu(item.HokenInf.HokensyaNo ?? string.Empty, ref hokensyaNoSearch, ref houbetuNo);
                HokensyaMst? hokensyaMst = NoTrackingDataContext.HokensyaMsts.FirstOrDefault(x => x.HpId == hpId && x.HokensyaNo == hokensyaNoSearch && x.Houbetu == houbetuNo);
                bool isReceKisaiOrNoHoken = false;
                if (item.HokenMaster != null)
                    isReceKisaiOrNoHoken = IsReceKisai(item.HokenMaster) || IsNoHoken(item.HokenMaster, item.HokenInf.HokenKbn, item.HokenInf.Houbetu ?? string.Empty);

                return new HokenInfModel(hpId,
                                        ptId,
                                        item.HokenInf.HokenId,
                                        item.HokenInf.SeqNo,
                                        item.HokenInf.HokenNo,
                                        item.HokenInf.HokenEdaNo,
                                        item.HokenInf.HokenKbn,
                                        item.HokenInf.HokensyaNo ?? string.Empty,
                                        item.HokenInf.Kigo ?? string.Empty,
                                        item.HokenInf.Bango ?? string.Empty,
                                        item.HokenInf.EdaNo ?? string.Empty,
                                        item.HokenInf.HonkeKbn,
                                        item.HokenInf.StartDate,
                                        item.HokenInf.EndDate,
                                        item.HokenInf.SikakuDate,
                                        item.HokenInf.KofuDate,
                                        GetConfirmDate(item.HokenCheckList.FirstOrDefault()?.HokenCheck),
                                        item.HokenInf.KogakuKbn,
                                        item.HokenInf.TasukaiYm,
                                        item.HokenInf.TokureiYm1,
                                        item.HokenInf.TokureiYm2,
                                        item.HokenInf.GenmenKbn,
                                        item.HokenInf.GenmenRate,
                                        item.HokenInf.GenmenGaku,
                                        item.HokenInf.SyokumuKbn,
                                        item.HokenInf.KeizokuKbn,
                                        item.HokenInf.Tokki1 ?? string.Empty,
                                        item.HokenInf.Tokki2 ?? string.Empty,
                                        item.HokenInf.Tokki3 ?? string.Empty,
                                        item.HokenInf.Tokki4 ?? string.Empty,
                                        item.HokenInf.Tokki5 ?? string.Empty,
                                        item.HokenInf.RousaiKofuNo ?? string.Empty,
                                        item.HokenInf.RousaiRoudouCd ?? string.Empty,
                                        item.HokenInf.RousaiSaigaiKbn,
                                        item.HokenInf.RousaiKantokuCd ?? string.Empty,
                                        item.HokenInf.RousaiSyobyoDate,
                                        item.HokenInf.RyoyoStartDate,
                                        item.HokenInf.RyoyoEndDate,
                                        item.HokenInf.RousaiSyobyoCd ?? string.Empty,
                                        item.HokenInf.RousaiJigyosyoName ?? string.Empty,
                                        item.HokenInf.RousaiPrefName ?? string.Empty,
                                        item.HokenInf.RousaiCityName ?? string.Empty,
                                        item.HokenInf.RousaiReceCount,
                                        string.Empty,
                                        string.Empty,
                                        string.Empty,
                                        sinDate,
                                        item.HokenInf.JibaiHokenName ?? string.Empty,
                                        item.HokenInf.JibaiHokenTanto ?? string.Empty,
                                        item.HokenInf.JibaiHokenTel ?? string.Empty,
                                        item.HokenInf.JibaiJyusyouDate,
                                        item.HokenInf.Houbetu ?? string.Empty,
                                        item.HokenCheckList.Select(c => new ConfirmDateModel(c.HokenCheck.HokenGrp, c.HokenCheck.HokenId, c.HokenCheck.SeqNo, c.HokenCheck.CheckId, c.UserInfo.Name ?? string.Empty, c.HokenCheck.CheckCmt ?? string.Empty, c.HokenCheck.CheckDate)).ToList(),
                                        item.RousaiTenkis.Select(x => new RousaiTenkiModel(x.Sinkei, x.Tenki, x.EndDate, x.IsDeleted, x.SeqNo)).ToList(),
                                        isReceKisaiOrNoHoken,
                                        item.HokenInf.IsDeleted,
                                        ConvertHokenMstModel(item.HokenMaster, item.Roudou?.RoudouName ?? string.Empty),
                                        hokensyaMst == null ? new HokensyaMstModel() : new HokensyaMstModel(
                                                                                        hokensyaMst.HpId,
                                                                                        hokensyaMst.Name ?? string.Empty,
                                                                                        hokensyaMst.KanaName ?? string.Empty,
                                                                                        hokensyaMst.HoubetuKbn ?? string.Empty,
                                                                                        hokensyaMst.Houbetu ?? string.Empty,
                                                                                        hokensyaMst.HokenKbn,
                                                                                        hokensyaMst.PrefNo,
                                                                                        hokensyaMst.HokensyaNo ?? string.Empty,
                                                                                        hokensyaMst.Kigo ?? string.Empty,
                                                                                        hokensyaMst.Bango ?? string.Empty,
                                                                                        hokensyaMst.RateHonnin,
                                                                                        hokensyaMst.RateKazoku,
                                                                                        hokensyaMst.PostCode ?? string.Empty,
                                                                                        hokensyaMst.Address1 ?? string.Empty,
                                                                                        hokensyaMst.Address2 ?? string.Empty,
                                                                                        hokensyaMst.Tel1 ?? string.Empty,
                                                                                        hokensyaMst.IsKigoNa),
                                        false,
                                        false
                                        );
            }).ToList();

            #endregion PtHokenInf

            #region PtHokenKohi
            IQueryable<PtKohi> kohiQuery = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteTypes.None || x.HokenId == maxIdKohi)).OrderByDescending(entity => entity.HokenId);

            var hokenMasterKohiQuery = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId && h.StartDate <= sinDate && sinDate <= h.EndDate &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                       .GroupBy(x => new
                                       {
                                           x.HpId,
                                           x.HokenNo,
                                           x.HokenEdaNo,
                                           x.PrefNo
                                       }).Select(grp => new
                                       {
                                           HpId = grp.Key.HpId,
                                           HokenNo = (int?)grp.Key.HokenNo ?? 0,
                                           HokenEdaNo = grp.Key.HokenEdaNo,
                                           PrefNo = grp.Key.PrefNo,
                                           StartDate = grp.Max(x => x.StartDate)
                                       });

            var hokenMasterKohiQueryDF = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                        .GroupBy(x => new
                                        {
                                            x.HpId,
                                            x.HokenNo,
                                            x.HokenEdaNo,
                                            x.PrefNo
                                        }).Select(grp => new
                                        {
                                            HpId = grp.Key.HpId,
                                            HokenNo = grp.Key.HokenNo,
                                            HokenEdaNo = grp.Key.HokenEdaNo,
                                            PrefNo = grp.Key.PrefNo,
                                            StartDate = grp.Max(x => x.StartDate)
                                        });


            var hokenMasterKohiQueryTarget = from hokenDf in hokenMasterKohiQueryDF
                                             join hokenPrioritize in hokenMasterKohiQuery
                                             on new { hokenDf.HokenEdaNo, hokenDf.HokenNo, hokenDf.HpId, hokenDf.PrefNo } equals new { hokenPrioritize.HokenEdaNo, hokenPrioritize.HokenNo, hokenPrioritize.HpId, hokenPrioritize.PrefNo } into obj
                                             from hoken in obj.DefaultIfEmpty()
                                             select new
                                             {
                                                 HpId = hokenDf.HpId,
                                                 HokenNo = hokenDf.HokenNo,
                                                 HokenEdaNo = hokenDf.HokenEdaNo,
                                                 PrefNo = hokenDf.PrefNo,
                                                 StartDate = (hoken.HokenNo == 0 && hoken.HokenEdaNo == 0) ? hokenDf.StartDate : (int?)hoken.StartDate ?? 0
                                             };

            IQueryable<HokenMst> hokenMasterKohiFinal = from hoken in hokenMasterKohiQueryTarget
                                                        select NoTrackingDataContext.HokenMsts.FirstOrDefault(h => h.HpId == hpId && h.HokenNo == hoken.HokenNo && h.HokenEdaNo == hoken.HokenEdaNo && h.StartDate == hoken.StartDate && h.PrefNo == hoken.PrefNo);

            var queryKohi = (from kohi in kohiQuery
                             join hkMaster in hokenMasterKohiFinal on new { kohi.HokenNo, kohi.HokenEdaNo, kohi.PrefNo } equals new { hkMaster.HokenNo, hkMaster.HokenEdaNo, hkMaster.PrefNo } into hkMtObject
                             from hkObj in hkMtObject.DefaultIfEmpty()
                             join roudou in NoTrackingDataContext.RoudouMsts
                             on hkObj.PrefNo.ToString() equals roudou.RoudouCd into rouObject
                             from rou in rouObject.DefaultIfEmpty()
                             select new
                             {
                                 Kohi = kohi,
                                 HokenMaster = hkObj,
                                 HokenCheckList = (from hkC in NoTrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId && x.PtID == ptId && x.IsDeleted == DeleteStatus.None && x.HokenGrp == HokenGroupConstant.HokenGroupKohi
                                                                              && x.HokenId == kohi.HokenId).OrderByDescending(o => o.CheckDate)
                                                   join userMst in NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId)
                                                   on hkC.CheckId equals userMst.UserId
                                                   select new
                                                   {
                                                       HokenCheck = hkC,
                                                       UserInfo = userMst
                                                   }
                                                 ).ToList(),
                                 Roudou = rou
                             }).ToList();

            List<KohiInfModel> kohiInfList = queryKohi.Select(obj => new KohiInfModel(obj.Kohi.FutansyaNo ?? string.Empty,
                                                          obj.Kohi.JyukyusyaNo ?? string.Empty,
                                                          obj.Kohi.HokenId,
                                                          obj.Kohi.StartDate,
                                                          obj.Kohi.EndDate,
                                                          GetConfirmDate(obj.HokenCheckList.FirstOrDefault()?.HokenCheck),
                                                          obj.Kohi.Rate,
                                                          obj.Kohi.GendoGaku,
                                                          obj.Kohi.SikakuDate,
                                                          obj.Kohi.KofuDate,
                                                          obj.Kohi.TokusyuNo ?? string.Empty,
                                                          obj.Kohi.HokenSbtKbn,
                                                          obj.Kohi.Houbetu ?? string.Empty,
                                                          obj.Kohi.HokenNo,
                                                          obj.Kohi.HokenEdaNo,
                                                          obj.Kohi.PrefNo,
                                                          ConvertHokenMstModel(obj.HokenMaster, obj.Roudou?.RoudouName ?? string.Empty),
                                                          sinDate,
                                                          obj.HokenCheckList.Select(c => new ConfirmDateModel(c.HokenCheck.HokenGrp, c.HokenCheck.HokenId, c.HokenCheck.SeqNo, c.HokenCheck.CheckId, c.UserInfo.Name ?? string.Empty, c.HokenCheck.CheckCmt ?? string.Empty, c.HokenCheck.CheckDate)).ToList(),
                                                          false,
                                                          obj.Kohi.IsDeleted,
                                                          false,
                                                          obj.Kohi.SeqNo)).ToList();
            #endregion PtHokenKohi

            #region PtHokenPattern
            var dataHokenPatterList = NoTrackingDataContext.PtHokenPatterns.Where(x => x.PtId == ptId && x.HpId == hpId && (x.IsDeleted == DeleteTypes.None || x.HokenPid == maxPidHokenPattern) && sinDate >= x.StartDate && x.EndDate >= sinDate).OrderByDescending(x => x.HokenPid);
            var dataKohi = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteStatus.None);
            var dataHokenInf = NoTrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteTypes.None || x.HokenId == maxIdHokenInf));

            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(pt => pt.HpId == hpId && pt.PtId == ptId);
            int birthDayPt = 0;
            if (ptInf != null)
                birthDayPt = ptInf.Birthday;

            var joinQuery = from ptHokenPattern in dataHokenPatterList
                            join ptHokenInf in dataHokenInf on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.HokenId } equals
                                new { ptHokenInf.HpId, ptHokenInf.PtId, ptHokenInf.HokenId }
                            join ptKohi1 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi1Id } equals
                                new { ptKohi1.HpId, ptKohi1.PtId, Kohi1Id = ptKohi1.HokenId } into datakohi1
                            from ptKohi1 in datakohi1.DefaultIfEmpty()
                            join ptKohi2 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi2Id } equals
                                new { ptKohi2.HpId, ptKohi2.PtId, Kohi2Id = ptKohi2.HokenId } into datakohi2
                            from ptKohi2 in datakohi2.DefaultIfEmpty()
                            join ptKohi3 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi3Id } equals
                                new { ptKohi3.HpId, ptKohi3.PtId, Kohi3Id = ptKohi3.HokenId } into datakohi3
                            from ptKohi3 in datakohi3.DefaultIfEmpty()
                            join ptKohi4 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi4Id } equals
                                new { ptKohi4.HpId, ptKohi4.PtId, Kohi4Id = ptKohi4.HokenId } into datakohi4
                            from ptKohi4 in datakohi4.DefaultIfEmpty()
                            select new
                            {
                                ptHokenPattern.HpId,
                                ptHokenPattern.PtId,
                                ptHokenPattern.HokenId,
                                ptHokenPattern.SeqNo,
                                ptHokenInf.HokenNo,
                                ptHokenInf.HokenEdaNo,
                                ptHokenPattern.HokenSbtCd,
                                ptHokenPattern.HokenPid,
                                ptHokenPattern.HokenKbn,
                                ptHokenPattern.StartDate,
                                ptHokenPattern.EndDate,
                                ptKohi1,
                                ptKohi2,
                                ptKohi3,
                                ptKohi4,
                                ptHokenPattern.HokenMemo,
                                HokenInfIsDeleted = ptHokenInf.IsDeleted,
                                PatternIsDeleted = ptHokenPattern.IsDeleted,
                                Houbetu = ptHokenInf.Houbetu
                            };


            var itemList = joinQuery.ToList();
            List<InsuranceModel> listInsurance = new List<InsuranceModel>();
            foreach (var item in itemList)
            {
                HokenInfModel hokenInf = hokenInfList.FirstOrDefault(h => h.HokenNo == item.HokenNo && h.HokenEdaNo == item.HokenEdaNo && h.HokenId == item.HokenId) ?? new HokenInfModel();

                KohiInfModel Kohi1 = kohiInfList.FirstOrDefault(h => item.ptKohi1 != null && h.HokenNo == item.ptKohi1.HokenNo && h.HokenEdaNo == item.ptKohi1.HokenEdaNo && item.ptKohi1.HokenId == h.HokenId) ?? new KohiInfModel(0);
                KohiInfModel Kohi2 = kohiInfList.FirstOrDefault(h => item.ptKohi2 != null && h.HokenNo == item.ptKohi2.HokenNo && h.HokenEdaNo == item.ptKohi2.HokenEdaNo && item.ptKohi2.HokenId == h.HokenId) ?? new KohiInfModel(0);
                KohiInfModel Kohi3 = kohiInfList.FirstOrDefault(h => item.ptKohi3 != null && h.HokenNo == item.ptKohi3.HokenNo && h.HokenEdaNo == item.ptKohi3.HokenEdaNo && item.ptKohi3.HokenId == h.HokenId) ?? new KohiInfModel(0);
                KohiInfModel Kohi4 = kohiInfList.FirstOrDefault(h => item.ptKohi4 != null && h.HokenNo == item.ptKohi4.HokenNo && h.HokenEdaNo == item.ptKohi4.HokenEdaNo && item.ptKohi4.HokenId == h.HokenId) ?? new KohiInfModel(0);

                listInsurance.Add(new InsuranceModel(
                    item.HpId,
                    item.PtId,
                    birthDayPt,
                    item.SeqNo,
                    item.HokenSbtCd,
                    item.HokenPid,
                    item.HokenKbn,
                    sinDate,
                    item.HokenMemo,
                    hokenInf,
                    kohi1: Kohi1,
                    kohi2: Kohi2,
                    kohi3: Kohi3,
                    kohi4: Kohi4,
                    item.PatternIsDeleted,
                    item.StartDate,
                    item.EndDate,
                    false
                ));
            }
            #endregion PtHokenPattern

            return new InsuranceDataModel(listInsurance, hokenInfList, kohiInfList, maxIdHokenInf, maxIdKohi, maxPidHokenPattern);
        }

        public bool CheckExistHokenPIdList(List<int> hokenPIds, List<int> hpIds, List<long> ptIds)
        {
            if (hokenPIds.Count == 0) return true;
            var countPtHokens = NoTrackingDataContext.PtHokenInfs.Count(p => hokenPIds.Contains(p.HokenId) && p.IsDeleted != 1 && hpIds.Contains(p.HpId) && ptIds.Contains(p.PtId));
            return countPtHokens == hokenPIds.Count;
        }

        public bool CheckExistHokenId(int hpId, int hokenId)
        {
            var check = NoTrackingDataContext.PtHokenInfs.Any(h => h.HpId == hpId && h.HokenId == hokenId && h.IsDeleted == 0);
            return check;
        }

        public bool CheckExistHokenPids(int hpId, List<int> hokenPids)
        {
            hokenPids = hokenPids.Distinct().ToList();
            var check = NoTrackingDataContext.PtHokenPatterns.Any(x => x.HpId == hpId && hokenPids.Contains(x.HokenPid));
            return check;
        }

        public List<HokenInfModel> GetCheckListHokenInf(int hpId, long ptId, List<int> hokenPids)
        {
            var result = NoTrackingDataContext.PtHokenPatterns.Where(h => h.HpId == hpId && hokenPids.Contains(h.HokenPid) && h.PtId == ptId && h.IsDeleted == 0);
            return result.Select(r => new HokenInfModel(r.HokenPid, r.PtId, r.HpId, r.StartDate, r.EndDate)).ToList();
        }

        private KohiInfModel GetKohiInfModel(PtKohi? kohiInf, PtHokenCheck? ptHokenCheck, HokenMst? hokenMst, int sinDate, List<ConfirmDateModel> confirmDateList)
        {
            if (kohiInf == null)
            {
                return new KohiInfModel(0);
            }
            return new KohiInfModel(
                kohiInf.FutansyaNo ?? string.Empty,
                kohiInf.JyukyusyaNo ?? string.Empty,
                kohiInf.HokenId,
                kohiInf.StartDate,
                kohiInf.EndDate,
                GetConfirmDate(ptHokenCheck),
                kohiInf.Rate,
                kohiInf.GendoGaku,
                kohiInf.SikakuDate,
                kohiInf.KofuDate,
                kohiInf.TokusyuNo ?? string.Empty,
                kohiInf.HokenSbtKbn,
                kohiInf.Houbetu ?? string.Empty,
                kohiInf.HokenNo,
                kohiInf.HokenEdaNo,
                kohiInf.PrefNo,
                GetHokenMstModel(hokenMst),
                sinDate,
                confirmDateList,
                false,
                kohiInf.IsDeleted,
                false,
                kohiInf.SeqNo
                );
        }

        private HokenMstModel GetHokenMstModel(HokenMst? hokenMst)
        {
            if (hokenMst == null)
            {
                return new HokenMstModel();
            }
            return Mapper.Map(hokenMst, new HokenMstModel(), (src, dest) =>
            {
                return dest;
            });
        }

        private int GetConfirmDate(PtHokenCheck? ptHokenCheck)
        {
            return ptHokenCheck is null ? 0 : DateTimeToInt(ptHokenCheck.CheckDate);
        }

        private static int DateTimeToInt(DateTime dateTime, string format = "yyyyMMdd")
        {
            int result = 0;
            result = Int32.Parse(dateTime.ToString(format));
            return result;
        }

        public IEnumerable<InsuranceModel> GetListHokenPattern(int hpId, long ptId, int sinDate, bool allowDisplayDeleted, bool isAllHoken = true, bool isHoken = true, bool isJihi = true, bool isRosai = true, bool isJibai = true)
        {
            var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where
                                (
                                    p => p.HpId == hpId && p.PtId == ptId && (p.IsDeleted == 0 || allowDisplayDeleted) &&
                                        (
                                            isAllHoken ||
                                            isHoken && (p.HokenKbn == 1 || p.HokenKbn == 2) ||
                                            isJihi && p.HokenKbn == 0 ||
                                            isRosai && (p.HokenKbn == 11 || p.HokenKbn == 12 || p.HokenKbn == 13) ||
                                            isJibai && p.HokenKbn == 14)).ToList();

            List<int> hokenIds = new();
            foreach (var ptHokenPattern in ptHokenPatterns)
            {
                if (ptHokenPattern.Kohi1Id > 0)
                    hokenIds.Add(ptHokenPattern.Kohi1Id);
                if (ptHokenPattern.Kohi2Id > 0)
                    hokenIds.Add(ptHokenPattern.Kohi2Id);
                if (ptHokenPattern.Kohi3Id > 0)
                    hokenIds.Add(ptHokenPattern.Kohi3Id);
                if (ptHokenPattern.Kohi4Id > 0)
                    hokenIds.Add(ptHokenPattern.Kohi4Id);
            }
            hokenIds = hokenIds.Distinct().ToList();
            var ptKohis = NoTrackingDataContext.PtKohis.Where(k => k.PtId == ptId && k.HpId == hpId).AsEnumerable().Where(k => hokenIds.Contains(k.HokenId)).ToList();

            #region Get hoken Mst
            int prefCd = 0;
            var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
            if (hpInf != null)
            {
                prefCd = hpInf.PrefNo;
            }

            var hokenInfList = NoTrackingDataContext.PtHokenInfs.Where(item => item.HpId == hpId && item.PtId == ptId && hokenIds.Contains(item.HokenId)).ToList();
            var hokenNoList = hokenInfList.Select(item => item.HokenNo).Distinct().ToList();
            var hokenEdaNoList = hokenInfList.Select(item => item.HokenEdaNo).Distinct().ToList();

            var hokenMstDBList = NoTrackingDataContext.HokenMsts.Where(item => item.HpId == hpId
                                                                               && (item.PrefNo == prefCd || item.PrefNo == 0 || item.IsOtherPrefValid == 1)
                                                                               && hokenNoList.Contains(item.HokenNo)
                                                                               && hokenEdaNoList.Contains(item.HokenEdaNo))
                                                                .ToList();

            var hokenMstList = (from hokenPattern in ptHokenPatterns
                                join hokenInf in hokenInfList
                                on hokenPattern.HokenId equals hokenInf.HokenId
                                join hokenMst in hokenMstDBList
                                on new { hokenInf.HokenNo, hokenInf.HokenEdaNo }
                                equals new { hokenMst.HokenNo, hokenMst.HokenEdaNo }
                                select new
                                {
                                    hokenPattern.HokenId,
                                    hokenPattern.HokenPid,
                                    hokenPattern.SeqNo,
                                    hokenMst
                                })
                               .ToList();
            #endregion

            PtKohi kohi1 = new PtKohi(), kohi2 = new PtKohi(), kohi3 = new PtKohi(), kohi4 = new PtKohi();
            var result = new List<InsuranceModel>();
            foreach (var ptHokenPattern in ptHokenPatterns)
            {
                kohi1 = ptKohis?.FirstOrDefault(k => k.HokenId == ptHokenPattern.Kohi1Id && k.IsDeleted == 0) ?? new();
                kohi2 = ptKohis?.FirstOrDefault(k => k.HokenId == ptHokenPattern.Kohi2Id && k.IsDeleted == 0) ?? new();
                kohi3 = ptKohis?.FirstOrDefault(k => k.HokenId == ptHokenPattern.Kohi3Id && k.IsDeleted == 0) ?? new();
                kohi4 = ptKohis?.FirstOrDefault(k => k.HokenId == ptHokenPattern.Kohi4Id && k.IsDeleted == 0) ?? new();
                HokenMst hokenMst = hokenMstList.FirstOrDefault(item => item.HokenId == ptHokenPattern.HokenId
                                                                        && item.HokenPid == ptHokenPattern.HokenPid
                                                                        && item.SeqNo == ptHokenPattern.SeqNo)?.hokenMst ?? new();

                result.Add(new InsuranceModel(
                        ptHokenPattern.HpId,
                        ptHokenPattern.PtId,
                        ptHokenPattern.SeqNo,
                        ptHokenPattern.HokenSbtCd,
                        ptHokenPattern.HokenPid,
                        ptHokenPattern.HokenKbn,
                        ptHokenPattern.HokenId,
                        ptHokenPattern.StartDate,
                        ptHokenPattern.EndDate,
                        sinDate,
                        new HokenMstModel(hokenMst.HokenNo, hokenMst.HokenEdaNo, hokenMst.StartDate, hokenMst.PrefNo, hokenMst.Houbetu ?? string.Empty, hokenMst.FutanRate),
                        ConvertToKohiModel(kohi1),
                        ConvertToKohiModel(kohi2),
                        ConvertToKohiModel(kohi3),
                        ConvertToKohiModel(kohi4)
                        ));
            }

            return result;
        }

        private bool IsReceKisai(HokenMst HokenMasterModel)
        {

            if (HokenMasterModel != null)
            {
                return HokenMasterModel.ReceKisai == 3;
            }
            return false;

        }

        private bool IsNoHoken(HokenMst HokenMasterModel, int hokenKbn, string houbetu)
        {

            if (HokenMasterModel != null)
            {
                return HokenMasterModel.HokenSbtKbn == 0;
            }
            return hokenKbn == 1 && houbetu == HokenConstant.HOUBETU_NASHI;
        }

        public InsuranceModel GetPtHokenInf(int hpId, int hokenPid, long ptId, int sinDate)
        {


            var dataHokenPatterList = NoTrackingDataContext.PtHokenPatterns.Where(x => x.IsDeleted == DeleteStatus.None && x.PtId == ptId && x.HpId == hpId && x.HokenPid == hokenPid).OrderByDescending(x => x.HokenPid);
            var dataKohi = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteStatus.None);
            var dataHokenInf = NoTrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId && x.PtId == ptId);
            var dataHokenCheck = NoTrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId && x.PtID == ptId && x.IsDeleted == DeleteStatus.None);
            var dataPtInf = NoTrackingDataContext.PtInfs.Where(pt => pt.HpId == hpId && pt.PtId == ptId && pt.IsDelete == DeleteStatus.None);
            var joinQuery = from ptHokenPattern in dataHokenPatterList
                            join ptHokenInf in dataHokenInf on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.HokenId } equals
                                new { ptHokenInf.HpId, ptHokenInf.PtId, ptHokenInf.HokenId } //into ptHokenInfs from ptHokenInf in ptHokenInfs.DefaultIfEmpty()
                            join ptKohi1 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi1Id } equals
                                new { ptKohi1.HpId, ptKohi1.PtId, Kohi1Id = ptKohi1.HokenId } into datakohi1
                            from ptKohi1 in datakohi1.DefaultIfEmpty()
                            join ptKohi2 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi2Id } equals
                                new { ptKohi2.HpId, ptKohi2.PtId, Kohi2Id = ptKohi2.HokenId } into datakohi2
                            from ptKohi2 in datakohi2.DefaultIfEmpty()
                            join ptKohi3 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi3Id } equals
                                new { ptKohi3.HpId, ptKohi3.PtId, Kohi3Id = ptKohi3.HokenId } into datakohi3
                            from ptKohi3 in datakohi3.DefaultIfEmpty()
                            join ptKohi4 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi4Id } equals
                                new { ptKohi4.HpId, ptKohi4.PtId, Kohi4Id = ptKohi4.HokenId } into datakohi4
                            from ptKohi4 in datakohi4.DefaultIfEmpty()
                            from ptInf in dataPtInf
                            select new
                            {
                                ptHokenPattern.HpId,
                                ptHokenPattern.PtId,
                                ptHokenPattern.HokenId,
                                ptHokenPattern.SeqNo,
                                ptHokenInf.HokenNo,
                                ptHokenInf.HokenEdaNo,
                                ptHokenPattern.HokenSbtCd,
                                ptHokenPattern.HokenPid,
                                ptHokenPattern.HokenKbn,
                                ptHokenInf = ptHokenInf,
                                ptHokenInf.HokensyaNo,
                                ptHokenInf.Kigo,
                                ptHokenInf.Bango,
                                ptHokenInf.EdaNo,
                                ptHokenInf.HonkeKbn,
                                ptHokenInf.HokensyaAddress,
                                ptHokenInf.HokensyaName,
                                ptHokenInf.HokensyaTel,
                                ptHokenPattern.StartDate,
                                ptHokenPattern.EndDate,
                                ptHokenInf.SikakuDate,
                                ptHokenInf.KofuDate,
                                ptKohi1,
                                ptKohi2,
                                ptKohi3,
                                ptKohi4,
                                ptHokenInf.KogakuKbn,
                                ptHokenInf.TasukaiYm,
                                ptHokenInf.TokureiYm1,
                                ptHokenInf.TokureiYm2,
                                ptHokenInf.GenmenKbn,
                                ptHokenInf.GenmenRate,
                                ptHokenInf.GenmenGaku,
                                ptHokenInf.SyokumuKbn,
                                ptHokenInf.KeizokuKbn,
                                ptHokenInf.Tokki1,
                                ptHokenInf.Tokki2,
                                ptHokenInf.Tokki3,
                                ptHokenInf.Tokki4,
                                ptHokenInf.Tokki5,
                                ptHokenInf.RousaiKofuNo,
                                ptHokenInf.RousaiRoudouCd,
                                ptHokenInf.RousaiSaigaiKbn,
                                ptHokenInf.RousaiKantokuCd,
                                ptHokenInf.RousaiSyobyoDate,
                                ptHokenInf.RyoyoStartDate,
                                ptHokenInf.RyoyoEndDate,
                                ptHokenInf.RousaiSyobyoCd,
                                ptHokenInf.RousaiJigyosyoName,
                                ptHokenInf.RousaiPrefName,
                                ptHokenInf.RousaiCityName,
                                ptHokenInf.RousaiReceCount,
                                ptHokenInf.JibaiHokenName,
                                ptHokenInf.JibaiHokenTanto,
                                ptHokenInf.JibaiHokenTel,
                                ptHokenInf.JibaiJyusyouDate,
                                ptInf.Birthday,
                                ptHokenPattern.HokenMemo,
                                HobetuHokenInf = ptHokenInf.Houbetu,
                                HokenInfStartDate = ptHokenInf.StartDate,
                                HokenInfEndDate = ptHokenInf.EndDate
                            };
            var itemList = joinQuery.ToList();
            List<InsuranceModel> listInsurance = new List<InsuranceModel>();

            var confirmDateList =
                (
                    from hokenCheck in NoTrackingDataContext.PtHokenChecks.Where(p => p.PtID == ptId && p.HpId == hpId && p.IsDeleted == 0)
                    join userMst in NoTrackingDataContext.UserMsts.Where(p => p.HpId == hpId).AsQueryable()
                    on hokenCheck.CheckId equals userMst.UserId
                    select new
                    {
                        hokenCheck,
                        userMst
                    }
                ).ToList();

            List<ConfirmDateModel> GetConfirmDateList(int hokenGrp, int hokenId)
            {
                if (confirmDateList == null)
                {
                    return new List<ConfirmDateModel>();
                }

                return confirmDateList
                    .Where(c => c.hokenCheck.HokenGrp == hokenGrp && c.hokenCheck.HokenId == hokenId)
                    .Select(c => new ConfirmDateModel(c.hokenCheck.HokenGrp, c.hokenCheck.HokenId, c.hokenCheck.SeqNo, c.hokenCheck.CheckId, c.userMst.KanaName ?? string.Empty, c.hokenCheck.CheckCmt ?? string.Empty, c.hokenCheck.CheckDate))
                    .ToList();
            }

            List<int> hokenNoList = new List<int>();
            hokenNoList.AddRange(itemList.Select(i => i.ptHokenInf != null ? i.ptHokenInf.HokenNo : 0));
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi1 != null ? i.ptKohi1.HokenNo : 0));
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi2 != null ? i.ptKohi2.HokenNo : 0));
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi3 != null ? i.ptKohi3.HokenNo : 0));
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi4 != null ? i.ptKohi4.HokenNo : 0));
            hokenNoList = hokenNoList.Distinct().ToList();

            List<int> hokenEdaNoList = new List<int>();
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptHokenInf != null ? i.ptHokenInf.HokenEdaNo : 0).ToList());
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi1 != null ? i.ptKohi1.HokenEdaNo : 0));
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi2 != null ? i.ptKohi2.HokenEdaNo : 0));
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi3 != null ? i.ptKohi3.HokenEdaNo : 0));
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi4 != null ? i.ptKohi4.HokenEdaNo : 0));
            hokenEdaNoList = hokenEdaNoList.Distinct().ToList();

            List<HokenMst> hokenMstList = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId && hokenNoList.Contains(h.HokenNo) && hokenEdaNoList.Contains(h.HokenEdaNo)).ToList();

            if (itemList.Count > 0)
            {
                foreach (var item in itemList)
                {
                    string houbetu = string.Empty;
                    bool isReceKisaiOrNoHoken = false;

                    HokenMst? hokenMst = hokenMstList.FirstOrDefault(h => h.HokenNo == item.HokenNo && h.HokenEdaNo == item.HokenEdaNo);
                    if (hokenMst != null)
                    {
                        houbetu = hokenMst.Houbetu ?? string.Empty;
                        isReceKisaiOrNoHoken = IsReceKisai(hokenMst) || IsNoHoken(hokenMst, item.HokenKbn, houbetu ?? string.Empty);
                    }
                    var ptRousaiTenkis = NoTrackingDataContext.PtRousaiTenkis.Where(x => x.HpId == hpId && x.PtId == ptId && x.HokenId == item.HokenId).OrderBy(x => x.EndDate)
                        .Select(x => new RousaiTenkiModel(x.Sinkei, x.Tenki, x.EndDate, x.IsDeleted, x.SeqNo)).ToList();

                    //get FindHokensyaMstByNoNotrack
                    string houbetuNo = string.Empty;
                    string hokensyaNoSearch = string.Empty;
                    CIUtil.GetHokensyaHoubetu(item.HokensyaNo ?? string.Empty, ref hokensyaNoSearch, ref houbetuNo);
                    var hokensyaMst = NoTrackingDataContext.HokensyaMsts.Where(x => x.HpId == hpId && x.HokensyaNo == hokensyaNoSearch && x.Houbetu == houbetuNo).Select(x => new HokensyaMstModel(x.IsKigoNa)).FirstOrDefault();

                    var ptHokenCheckOfHokenPattern = dataHokenCheck
                    .Where(x => x.HokenId == item.HokenId && x.HokenGrp == HokenGroupConstant.HokenGroupHokenPattern)
                    .OrderByDescending(x => x.CheckDate).FirstOrDefault();

                    HokenInfModel hokenInf = new HokenInfModel(
                                            hpId,
                                            ptId,
                                            item.HokenId,
                                            item.SeqNo,
                                            item.HokenNo,
                                            item.HokenEdaNo,
                                            item.HokenKbn,
                                            item.HokensyaNo ?? string.Empty,
                                            item.Kigo ?? string.Empty,
                                            item.Bango ?? string.Empty,
                                            item.EdaNo ?? string.Empty,
                                            item.HonkeKbn,
                                            item.StartDate,
                                            item.EndDate,
                                            item.SikakuDate,
                                            item.KofuDate,
                                            GetConfirmDate(ptHokenCheckOfHokenPattern),
                                            item.KogakuKbn,
                                            item.TasukaiYm,
                                            item.TokureiYm1,
                                            item.TokureiYm2,
                                            item.GenmenKbn,
                                            item.GenmenRate,
                                            item.GenmenGaku,
                                            item.SyokumuKbn,
                                            item.KeizokuKbn,
                                            item.Tokki1 ?? string.Empty,
                                            item.Tokki2 ?? string.Empty,
                                            item.Tokki3 ?? string.Empty,
                                            item.Tokki4 ?? string.Empty,
                                            item.Tokki5 ?? string.Empty,
                                            item.RousaiKofuNo ?? string.Empty,
                                            item.RousaiRoudouCd ?? string.Empty,
                                            item.RousaiSaigaiKbn,
                                            item.RousaiKantokuCd ?? string.Empty,
                                            item.RousaiSyobyoDate,
                                            item.RyoyoStartDate,
                                            item.RyoyoEndDate,
                                            item.RousaiSyobyoCd ?? string.Empty,
                                            item.RousaiJigyosyoName ?? string.Empty,
                                            item.RousaiPrefName ?? string.Empty,
                                            item.RousaiCityName ?? string.Empty,
                                            item.RousaiReceCount,
                                            item.HokensyaName ?? string.Empty,
                                            item.HokensyaAddress ?? string.Empty,
                                            item.HokensyaTel ?? string.Empty,
                                            sinDate,
                                            item.JibaiHokenName ?? string.Empty,
                                            item.JibaiHokenTanto ?? string.Empty,
                                            item.JibaiHokenTel ?? string.Empty,
                                            item.JibaiJyusyouDate,
                                            houbetu ?? string.Empty,
                                            GetConfirmDateList(1, item.HokenId),
                                            ptRousaiTenkis,
                                            isReceKisaiOrNoHoken,
                                            0,
                                            Mapper.Map(hokenMst, new HokenMstModel(), (src, dest) =>
                                            {
                                                return dest;
                                            }),
                                            hokensyaMst ?? new HokensyaMstModel(),
                                            false,
                                            false
                                            );

                    HokenMst? hokenMst1 = null;
                    PtHokenCheck? ptHokenCheckOfKohi1 = null;
                    HokenMst? hokenMst2 = null;
                    PtHokenCheck? ptHokenCheckOfKohi2 = null;
                    HokenMst? hokenMst3 = null;
                    PtHokenCheck? ptHokenCheckOfKohi3 = null;
                    HokenMst? hokenMst4 = null;
                    PtHokenCheck? ptHokenCheckOfKohi4 = null;

                    if (item.ptKohi1 is not null)
                    {
                        hokenMst1 = hokenMstList.FirstOrDefault(h => h.HokenNo == item.ptKohi1.HokenNo && h.HokenEdaNo == item.ptKohi1.HokenEdaNo);
                        ptHokenCheckOfKohi1 = dataHokenCheck
                                                .Where(x => x.HokenId == item.ptKohi1.HokenId && x.HokenGrp == HokenGroupConstant.HokenGroupKohi)
                                                .OrderByDescending(x => x.CheckDate).FirstOrDefault();
                    }


                    if (item.ptKohi2 is not null)
                    {
                        hokenMst2 = hokenMstList.FirstOrDefault(h => h.HokenNo == item.ptKohi2.HokenNo && h.HokenEdaNo == item.ptKohi2.HokenEdaNo);
                        ptHokenCheckOfKohi2 = dataHokenCheck
                                               .Where(x => x.HokenId == item.ptKohi2.HokenId && x.HokenGrp == HokenGroupConstant.HokenGroupKohi)
                                               .OrderByDescending(x => x.CheckDate).FirstOrDefault();
                    }


                    if (item.ptKohi3 is not null)
                    {
                        hokenMst3 = hokenMstList.FirstOrDefault(h => h.HokenNo == item.ptKohi3.HokenNo && h.HokenEdaNo == item.ptKohi3.HokenEdaNo);
                        ptHokenCheckOfKohi3 = dataHokenCheck
                                               .Where(x => x.HokenId == item.ptKohi3.HokenId && x.HokenGrp == HokenGroupConstant.HokenGroupKohi)
                                               .OrderByDescending(x => x.CheckDate).FirstOrDefault();
                    }


                    if (item.ptKohi4 is not null)
                    {
                        hokenMst4 = hokenMstList.FirstOrDefault(h => h.HokenNo == item.ptKohi4.HokenNo && h.HokenEdaNo == item.ptKohi4.HokenEdaNo);
                        ptHokenCheckOfKohi4 = dataHokenCheck
                                               .Where(x => x.HokenId == item.ptKohi4.HokenId && x.HokenGrp == HokenGroupConstant.HokenGroupKohi)
                                               .OrderByDescending(x => x.CheckDate).FirstOrDefault();
                    }

                    InsuranceModel insuranceModel = new InsuranceModel(
                        item.HpId,
                        item.PtId,
                        item.Birthday,
                        item.SeqNo,
                        item.HokenSbtCd,
                        item.HokenPid,
                        item.HokenKbn,
                        sinDate,
                        item.HokenMemo,
                        hokenInf,
                        kohi1: GetKohiInfModel(item.ptKohi1, ptHokenCheckOfKohi1, hokenMst1, sinDate, GetConfirmDateList(2, item.ptKohi1?.HokenId ?? 0)),
                        kohi2: GetKohiInfModel(item.ptKohi2, ptHokenCheckOfKohi2, hokenMst2, sinDate, GetConfirmDateList(2, item.ptKohi2?.HokenId ?? 0)),
                        kohi3: GetKohiInfModel(item.ptKohi3, ptHokenCheckOfKohi3, hokenMst3, sinDate, GetConfirmDateList(2, item.ptKohi3?.HokenId ?? 0)),
                        kohi4: GetKohiInfModel(item.ptKohi4, ptHokenCheckOfKohi4, hokenMst4, sinDate, GetConfirmDateList(2, item.ptKohi4?.HokenId ?? 0)),
                        0,
                        item.StartDate,
                        item.EndDate,
                        false
                    );
                    listInsurance.Add(insuranceModel);
                }
            }
            return listInsurance.FirstOrDefault() ?? new InsuranceModel();
        }


        public HokenInfModel? GetPtHokenInfByHokenId(int hpId, long ptId, int hokenId, long seqNo)
        {
            PtHokenInf? ptHokenInf = null;
            int prefCd = 0;
            var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
            if (hpInf != null)
            {
                prefCd = hpInf.PrefNo;
            }

            if (hokenId == 0)
            {
                var hokenMst = NoTrackingDataContext.HokenMsts.Where(h => h.HokenName == "保険なし" && (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1) && h.HpId == hpId).OrderByDescending(h => h.StartDate).ThenByDescending(h => h.EndDate).FirstOrDefault();
                if (hokenMst != null)
                {
                    ptHokenInf = NoTrackingDataContext.PtHokenInfs.FirstOrDefault(p => p.HokenNo == hokenMst.HokenNo && p.HokenEdaNo == hokenMst.HokenEdaNo && p.HpId == hpId && p.PtId == ptId);
                }
            }
            else
            {
                ptHokenInf = NoTrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId && x.PtId == ptId && x.HokenId == hokenId && x.SeqNo == seqNo).FirstOrDefault();
            }
            if (ptHokenInf == null)
            {
                return null;
            }
            var hokenMaster = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId &&
                                                                     (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1) && h.HokenEdaNo == ptHokenInf.HokenEdaNo && h.HokenNo == ptHokenInf.HokenNo).OrderByDescending(h => h.StartDate).ThenByDescending(h => h.EndDate).FirstOrDefault();

            return new HokenInfModel(
                hokenId: ptHokenInf.HokenId,
                startDate: ptHokenInf.StartDate,
                endDate: ptHokenInf.EndDate,
                hokenKbn: ptHokenInf.HokenKbn,
                hokensyaNo: ptHokenInf.HokensyaNo ?? string.Empty,
                houbetu: ptHokenInf.Houbetu ?? string.Empty,
                seqNo: ptHokenInf.SeqNo,
                hokenMstHobetu: hokenMaster?.Houbetu ?? string.Empty,
                hokenMstFutaRate: hokenMaster?.FutanRate ?? 0,
                hokenMaster?.HokenSbtKbn ?? 0
            );
        }

        public List<(int, int)> GetListHistoryPid(int hpId, long ptId, int sinDate, List<int> historyPids, int selectedHokenPid)
        {
            var distinctHistoryPids = historyPids.Distinct();
            List<(int, int)> result = new();
            var hokenPatternModels = GetInsuranceList(hpId, ptId, sinDate).ToList();
            foreach (var historyPid in distinctHistoryPids)
            {
                var historyPidList = GetDefaultSelectPattern(historyPid, selectedHokenPid, hokenPatternModels);
                result.Add(new(historyPid, historyPidList));
            }
            return result;
        }

        public int GetDefaultSelectPattern(int historyPid, int selectedHokenPid, List<InsuranceModel> hokenPatternModels)
        {
            bool _isSameKohiHoubetu(InsuranceModel pattern1, InsuranceModel pattern2)
            {
                if (pattern1.HokenSbtCd == pattern2.HokenSbtCd)
                {
                    return pattern1.Kohi1.Houbetu == pattern2.Kohi1.Houbetu
                        && pattern1.Kohi2.Houbetu == pattern2.Kohi2.Houbetu
                        && pattern1.Kohi3.Houbetu == pattern2.Kohi3.Houbetu
                        && pattern1.Kohi4.Houbetu == pattern2.Kohi4.Houbetu;
                }

                return false;
            }
            var historyHokenPattern = hokenPatternModels.FirstOrDefault(p => p.HokenPid == historyPid);
            if (historyHokenPattern == null)
            {
                return selectedHokenPid;
            }

            var syosaisinHokenPattern = hokenPatternModels.FirstOrDefault(p => p.HokenPid == selectedHokenPid);
            if (syosaisinHokenPattern?.HokenSbtCd == 0)
            {
                // Rousai, jibai, jihi => use syosaisin
                return selectedHokenPid;
            }
            // ■初再診が主保険なし（HOKEN_SBT_CD[5xx]）の場合
            else if (syosaisinHokenPattern?.HokenSbtCd >= 500)
            {
                if (!historyHokenPattern.IsExpirated)
                {
                    // ① 履歴のPIDが有効な保険パターンの場合は、履歴と同じPID
                    return historyHokenPattern.HokenPid;
                }
                // HokenNashi - 保険なし
                else if (_isSameKohiHoubetu(historyHokenPattern, syosaisinHokenPattern))
                {
                    // ② 初再診と履歴が同じ組合せの法別番号の公費を持つ場合は初再診のPID
                    return syosaisinHokenPattern.HokenPid;
                }
                else
                {
                    var sameKohiPattern = hokenPatternModels
                        .Where(p => p.HokenSbtCd >= 500 && !p.IsExpirated && _isSameKohiHoubetu(historyHokenPattern, p))
                        .OrderBy(p => p.IsExpirated)
                        .ThenBy(p => p.HokenPid)
                        .FirstOrDefault();
                    if (sameKohiPattern != null)
                    {
                        // ③ 主保険なしで有効な保険パターンの中で、履歴と同じ組合せの法別番号の公費を持つPID
                        return sameKohiPattern.HokenPid;
                    }
                    else if (!syosaisinHokenPattern.IsExpirated)
                    {
                        // ④ ③までに該当する保険パターンが存在しない場合、初再診が有効な保険組合せの場合は初再診のPID	
                        return syosaisinHokenPattern.HokenPid;
                    }
                    else
                    {
                        return historyPid;
                    }
                }
            }
            else
            {
                if (!historyHokenPattern.IsExpirated)
                {
                    // ① 履歴のPIDが有効な保険パターンの場合は、履歴と同じPID
                    return historyHokenPattern.HokenPid;
                }
                else
                {
                    if (!hokenPatternModels.Any(p => p.HokenSbtCd < 500 && p.HokenSbtCd > 0
                                                && !p.IsEmptyHoken
                                                && !p.IsExpirated
                                                && p.HokenInf.HokenId == syosaisinHokenPattern?.HokenInf.HokenId))
                    {
                        //② 初再診と同じ主保険を持つ有効な保険パターンがない場合は、履歴と同じPID
                        return historyPid;
                    }

                    var sameHokenPatternBuntenKohi = hokenPatternModels
                            .Where(p => p.HokenSbtCd < 500 && p.HokenSbtCd > 0
                                   && !p.IsEmptyHoken
                                   && !p.IsExpirated
                                   && p.HokenInf.HokenId == syosaisinHokenPattern?.HokenInf.HokenId
                                   && p.BuntenKohis.Count > 0)
                            .OrderBy(p => p.IsExpirated)
                            .ThenBy(p => p.HokenPid)
                            .FirstOrDefault();

                    if (sameHokenPatternBuntenKohi == null)
                    {
                        // ③ 初再診と同じ主保険を持つ有効な保険パターンの中で、分点公費（HOKEN_MST.HOKEN_SBT_KBN=6）を持つ保険パターンがない場合は、初再診の保険PID
                        return syosaisinHokenPattern?.HokenPid ?? 0;
                    }
                    else
                    {
                        var sameHokenPattern = hokenPatternModels
                                                .Where(p => p.HokenSbtCd < 500 && p.HokenSbtCd > 0
                                                       && !p.IsEmptyHoken
                                                       && !p.IsExpirated
                                                       && p.HokenPid == syosaisinHokenPattern?.HokenPid
                                                       && _isSameKohiHoubetu(historyHokenPattern, p))
                                                .OrderBy(p => p.IsExpirated)
                                                .ThenBy(p => p.HokenPid)
                                                .FirstOrDefault();
                        if (sameHokenPattern != null)
                        {
                            // ④ 初再診と同じ主保険を持つ有効な保険パターンの中で、履歴と同じ組合せの法別番号の公費を持つPID
                            return sameHokenPattern.HokenPid;
                        }
                        else
                        {
                            // ⑤ 初再診と同じ主保険を持つ有効な保険パターンの中で、履歴の法別番号の一致率が高くて組合せ数が少ないPID
                            var sameHokenPatternDiffHoubetu = hokenPatternModels
                                                    .Where(p => p.HokenSbtCd < 500 && p.HokenSbtCd > 0
                                                           && !p.IsEmptyHoken
                                                           && !p.IsExpirated
                                                           && p.HokenInf.HokenId == syosaisinHokenPattern?.HokenInf.HokenId)
                                                    .OrderBy(p => p.IsExpirated)
                                                    .ThenBy(p => p.HokenPid)
                                                    .ToList();
                            if (sameHokenPatternDiffHoubetu.Count > 0)
                            {
                                List<string> historyHoubetuList = new List<string>();
                                if (!historyHokenPattern.IsEmptyKohi1 && !string.IsNullOrEmpty(historyHokenPattern.Kohi1.Houbetu))
                                {
                                    historyHoubetuList.Add(historyHokenPattern.Kohi1.Houbetu);
                                }
                                if (!historyHokenPattern.IsEmptyKohi2 && !string.IsNullOrEmpty(historyHokenPattern.Kohi2.Houbetu))
                                {
                                    historyHoubetuList.Add(historyHokenPattern.Kohi2.Houbetu);
                                }
                                if (!historyHokenPattern.IsEmptyKohi3 && !string.IsNullOrEmpty(historyHokenPattern.Kohi3.Houbetu))
                                {
                                    historyHoubetuList.Add(historyHokenPattern.Kohi3.Houbetu);
                                }
                                if (!historyHokenPattern.IsEmptyKohi4 && !string.IsNullOrEmpty(historyHokenPattern.Kohi4.Houbetu))
                                {
                                    historyHoubetuList.Add(historyHokenPattern.Kohi4.Houbetu);
                                }

                                int maxPoint = 0;
                                InsuranceModel? foundPattern = null;
                                foreach (var hokenPattern in sameHokenPatternDiffHoubetu)
                                {
                                    int houbetuPoint = hokenPattern.HoubetuPoint(historyHoubetuList);
                                    if ((houbetuPoint > maxPoint) || (houbetuPoint == maxPoint && foundPattern != null && hokenPattern.KohiCount < foundPattern.KohiCount))
                                    {
                                        maxPoint = houbetuPoint;
                                        foundPattern = hokenPattern;
                                    }
                                }
                                if (foundPattern != null)
                                {
                                    return foundPattern.HokenPid;
                                }
                            }
                        }
                    }
                }
                return historyPid;
            }
        }

        public List<InsuranceModel> GetInsuranceList(int hpId, long ptId, int sinDate, bool isDeleted = false, bool isCache = false)
        {
            int prefCd = 0;
            var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
            if (hpInf != null)
            {
                prefCd = hpInf.PrefNo;
            }

            PtInf? ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(p => p.HpId == hpId && p.PtId == ptId && p.IsDelete == 0);
            if (ptInf == null)
            {
                return new List<InsuranceModel>();
            }
            int birthDay = ptInf.Birthday;

            var dataHokenPatterList = NoTrackingDataContext.PtHokenPatterns.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteStatus.None || isDeleted)).OrderByDescending(x => x.HokenPid);
            var dataKohi = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteStatus.None || isDeleted));
            var dataHokenInf = NoTrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteStatus.None || isDeleted));
            var joinQuery = from ptHokenPattern in dataHokenPatterList
                            join ptHokenInf in dataHokenInf on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.HokenId } equals
                                new { ptHokenInf.HpId, ptHokenInf.PtId, ptHokenInf.HokenId }
                            join ptKohi1 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi1Id } equals
                                new { ptKohi1.HpId, ptKohi1.PtId, Kohi1Id = ptKohi1.HokenId } into datakohi1
                            from ptKohi1 in datakohi1.DefaultIfEmpty()
                            join ptKohi2 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi2Id } equals
                                new { ptKohi2.HpId, ptKohi2.PtId, Kohi2Id = ptKohi2.HokenId } into datakohi2
                            from ptKohi2 in datakohi2.DefaultIfEmpty()
                            join ptKohi3 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi3Id } equals
                                new { ptKohi3.HpId, ptKohi3.PtId, Kohi3Id = ptKohi3.HokenId } into datakohi3
                            from ptKohi3 in datakohi3.DefaultIfEmpty()
                            join ptKohi4 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi4Id } equals
                                new { ptKohi4.HpId, ptKohi4.PtId, Kohi4Id = ptKohi4.HokenId } into datakohi4
                            from ptKohi4 in datakohi4.DefaultIfEmpty()
                            select new
                            {
                                ptHokenPattern.HpId,
                                ptHokenPattern.PtId,
                                ptHokenPattern.HokenId,
                                ptHokenPattern.SeqNo,
                                ptHokenInf.HokenNo,
                                ptHokenInf.HokenEdaNo,
                                HokenSbtCd = ptHokenPattern.HokenSbtCd,
                                ptHokenPattern.HokenPid,
                                ptHokenPattern.HokenKbn,
                                ptHokenInf = ptHokenInf,
                                ptHokenInf.HokensyaNo,
                                ptHokenInf.Kigo,
                                ptHokenInf.Bango,
                                ptHokenInf.EdaNo,
                                ptHokenInf.HonkeKbn,
                                ptHokenPattern.StartDate,
                                ptHokenPattern.EndDate,
                                ptHokenInf.SikakuDate,
                                ptHokenInf.KofuDate,
                                ptKohi1,
                                ptKohi2,
                                ptKohi3,
                                ptKohi4,
                                FutansyaNo = ptKohi1.FutansyaNo,
                                ptHokenInf.KogakuKbn,
                                ptHokenInf.TasukaiYm,
                                ptHokenInf.TokureiYm1,
                                ptHokenInf.TokureiYm2,
                                ptHokenInf.GenmenKbn,
                                ptHokenInf.GenmenRate,
                                ptHokenInf.GenmenGaku,
                                ptHokenInf.SyokumuKbn,
                                ptHokenInf.KeizokuKbn,
                                ptHokenInf.Tokki1,
                                ptHokenInf.Tokki2,
                                ptHokenInf.Tokki3,
                                ptHokenInf.Tokki4,
                                ptHokenInf.Tokki5,
                                ptHokenInf.RousaiKofuNo,
                                ptHokenInf.RousaiRoudouCd,
                                ptHokenInf.RousaiSaigaiKbn,
                                ptHokenInf.RousaiKantokuCd,
                                ptHokenInf.RousaiSyobyoDate,
                                ptHokenInf.RyoyoStartDate,
                                ptHokenInf.RyoyoEndDate,
                                ptHokenInf.RousaiSyobyoCd,
                                ptHokenInf.RousaiJigyosyoName,
                                ptHokenInf.RousaiPrefName,
                                ptHokenInf.RousaiCityName,
                                ptHokenInf.RousaiReceCount,
                                ptHokenInf.JibaiHokenName,
                                ptHokenInf.JibaiHokenTanto,
                                ptHokenInf.JibaiHokenTel,
                                ptHokenInf.JibaiJyusyouDate,
                                ptHokenPattern.HokenMemo,
                                HobetuHokenInf = ptHokenInf.Houbetu,
                                HokenInfStartDate = ptHokenInf.StartDate,
                                HokenInfEndDate = ptHokenInf.EndDate,
                                HokenInfIsDeleted = ptHokenInf.IsDeleted,
                                PatternIsDeleted = ptHokenPattern.IsDeleted,
                                Houbetu = ptHokenInf.Houbetu
                            };

            var itemList = joinQuery.ToList();

            List<int> hokenNoList = new List<int>();
            hokenNoList.AddRange(itemList.Select(i => i.ptHokenInf != null ? i.ptHokenInf.HokenNo : 0).ToList());
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi1 != null ? i.ptKohi1.HokenNo : 0).ToList());
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi2 != null ? i.ptKohi2.HokenNo : 0).ToList());
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi3 != null ? i.ptKohi3.HokenNo : 0).ToList());
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi4 != null ? i.ptKohi4.HokenNo : 0).ToList());
            hokenNoList = hokenNoList.Distinct().ToList();

            List<int> hokenEdaNoList = new List<int>();
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptHokenInf != null ? i.ptHokenInf.HokenEdaNo : 0).ToList());
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi1 != null ? i.ptKohi1.HokenEdaNo : 0).ToList());
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi2 != null ? i.ptKohi2.HokenEdaNo : 0).ToList());
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi3 != null ? i.ptKohi3.HokenEdaNo : 0).ToList());
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi4 != null ? i.ptKohi4.HokenEdaNo : 0).ToList());
            hokenEdaNoList = hokenEdaNoList.Distinct().ToList();

            List<HokenMst> hokenMstList = NoTrackingDataContext.HokenMsts.Where(h => (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1) && h.HpId == hpId && hokenNoList.Contains(h.HokenNo) && hokenEdaNoList.Contains(h.HokenEdaNo))
                                                                         .OrderBy(e => e.HpId)
                                                                         .ThenBy(e => e.HokenNo)
                                                                         .ThenBy(e => e.HokenEdaNo)
                                                                         .ThenByDescending(e => e.StartDate)
                                                                         .ThenBy(e => e.HokenSbtKbn)
                                                                         .ThenBy(e => e.SortNo)
                                                                         .ToList();

            List<PtHokenCheck> ptHokenCheckList = NoTrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId && x.PtID == ptId && x.IsDeleted == DeleteStatus.None).ToList();

            List<InsuranceModel> listInsurance = new List<InsuranceModel>();

            var confirmDateList =
                (
                    from hokenCheck in NoTrackingDataContext.PtHokenChecks.Where(p => p.PtID == ptId && p.HpId == hpId && p.IsDeleted == 0)
                    join userMst in NoTrackingDataContext.UserMsts.Where(p => p.HpId == hpId).AsQueryable()
                    on hokenCheck.CheckId equals userMst.UserId
                    select new
                    {
                        hokenCheck,
                        userMst
                    }
                ).ToList();

            List<ConfirmDateModel> GetConfirmDateList(int hokenGrp, int hokenId)
            {
                if (confirmDateList == null)
                {
                    return new List<ConfirmDateModel>();
                }

                return confirmDateList
                    .Where(c => c.hokenCheck.HokenGrp == hokenGrp && c.hokenCheck.HokenId == hokenId)
                    .Select(c => new ConfirmDateModel(c.hokenCheck.HokenGrp, c.hokenCheck.HokenId, c.hokenCheck.SeqNo, c.hokenCheck.CheckId, c.userMst.KanaName ?? string.Empty, c.hokenCheck.CheckCmt ?? string.Empty, c.hokenCheck.CheckDate))
                    .ToList();
            }

            PtHokenCheck? GetLastPtHokenCheck(int id, int hokenGrp)
            {
                return ptHokenCheckList
                    .Where(h => h.HokenId == id && h.HokenGrp == hokenGrp)
                    .OrderByDescending(x => x.CheckDate)
                    .FirstOrDefault();
            }

            KohiInfModel GenerateKohiModel(PtKohi? ptKohi)
            {
                if (ptKohi == null)
                {
                    return GetKohiInfModel(null, null, null, sinDate, new List<ConfirmDateModel>());
                }
                int hokenNo = ptKohi.HokenNo;
                int hokenEdaNo = ptKohi.HokenEdaNo;
                HokenMst? hokenMst = hokenMstList.FirstOrDefault(h => h.HokenNo == hokenNo && h.HokenEdaNo == hokenEdaNo);

                return GetKohiInfModel(
                    ptKohi,
                    GetLastPtHokenCheck(ptKohi.HokenId, HokenGroupConstant.HokenGroupKohi),
                    hokenMst,
                    sinDate,
                    GetConfirmDateList(HokenGroupConstant.HokenGroupKohi, ptKohi.HokenId));
            }
            foreach (var item in itemList)
            {
                HokenMst? hokenMst = hokenMstList.FirstOrDefault(h => h.HokenNo == item.ptHokenInf.HokenNo && h.HokenEdaNo == item.ptHokenInf.HokenEdaNo);
                string houbetu = string.Empty;
                bool isReceKisaiOrNoHoken = false;
                if (hokenMst != null)
                {
                    houbetu = hokenMst.Houbetu ?? string.Empty;
                    isReceKisaiOrNoHoken = IsReceKisai(hokenMst) || IsNoHoken(hokenMst, item.HokenKbn, houbetu ?? string.Empty);
                }

                //get FindHokensyaMstByNoNotrack
                string houbetuNo = string.Empty;
                string hokensyaNoSearch = string.Empty;
                CIUtil.GetHokensyaHoubetu(item.HokensyaNo ?? string.Empty, ref hokensyaNoSearch, ref houbetuNo);

                HokenInfModel hokenInf = new HokenInfModel(
                                        hpId,
                                        ptId,
                                        item.HokenId,
                                        item.SeqNo,
                                        item.HokenNo,
                                        item.HokenEdaNo,
                                        item.HokenKbn,
                                        item.HokensyaNo ?? string.Empty,
                                        item.Kigo ?? string.Empty,
                                        item.Bango ?? string.Empty,
                                        item.EdaNo ?? string.Empty,
                                        item.HonkeKbn,
                                        item.HokenInfStartDate,
                                        item.HokenInfEndDate,
                                        item.SikakuDate,
                                        item.KofuDate,
                                        GetConfirmDate(GetLastPtHokenCheck(item.ptHokenInf.HokenId, HokenGroupConstant.HokenGroupHokenPattern)),
                                        item.KogakuKbn,
                                        item.TasukaiYm,
                                        item.TokureiYm1,
                                        item.TokureiYm2,
                                        item.GenmenKbn,
                                        item.GenmenRate,
                                        item.GenmenGaku,
                                        item.SyokumuKbn,
                                        item.KeizokuKbn,
                                        item.Tokki1 ?? string.Empty,
                                        item.Tokki2 ?? string.Empty,
                                        item.Tokki3 ?? string.Empty,
                                        item.Tokki4 ?? string.Empty,
                                        item.Tokki5 ?? string.Empty,
                                        item.RousaiKofuNo ?? string.Empty,
                                        item.RousaiRoudouCd ?? string.Empty,
                                        item.RousaiSaigaiKbn,
                                        item.RousaiKantokuCd ?? string.Empty,
                                        item.RousaiSyobyoDate,
                                        item.RyoyoStartDate,
                                        item.RyoyoEndDate,
                                        item.RousaiSyobyoCd ?? string.Empty,
                                        item.RousaiJigyosyoName ?? string.Empty,
                                        item.RousaiPrefName ?? string.Empty,
                                        item.RousaiCityName ?? string.Empty,
                                        item.RousaiReceCount,
                                        string.Empty,
                                        string.Empty,
                                        string.Empty,
                                        sinDate,
                                        item.JibaiHokenName ?? string.Empty,
                                        item.JibaiHokenTanto ?? string.Empty,
                                        item.JibaiHokenTel ?? string.Empty,
                                        item.JibaiJyusyouDate,
                                        houbetu ?? string.Empty,
                                        GetConfirmDateList(1, item.HokenId),
                                        new List<RousaiTenkiModel>(),
                                        isReceKisaiOrNoHoken,
                                        item.HokenInfIsDeleted,
                                        Mapper.Map(hokenMst, new HokenMstModel(), (src, dest) =>
                                        {
                                            dest.ChangePropertiesNoAutoMap(src.HokenSname ?? string.Empty);
                                            return dest;
                                        }),
                                        new HokensyaMstModel(),
                                        false,
                                        false,
                                        item.HokenSbtCd,
                                        item.FutansyaNo
                                        );

                // format hokenName 
                string hokenName = (FormatHokenName(
                    hokenId: hokenInf.HokenId,
                    hokenKbn: hokenInf.HokenKbn,
                    hokenSname: hokenMst?.HokenSname ?? string.Empty,
                    honkeKbn: hokenInf.HonkeKbn,
                    hokensyaNo: hokenInf.HokensyaNo ?? string.Empty,
                    startDate: hokenInf.StartDate,
                    endDate: hokenInf.EndDate,
                    sinDate
                )).Trim();

                int prefNo = 0;
                if (hpInf != null)
                {
                    prefNo = hpInf.PrefNo;
                }
                string kohiName1 = "", kohiName2 = "", kohiName3 = "", kohiName4 = "";
                if (item.ptKohi1 != null)
                {
                    var kohi1HokenMst = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && (x.PrefNo == prefNo || x.PrefNo == 0 || x.IsOtherPrefValid == 1) && x.HokenNo == item.ptKohi1.HokenNo && x.HokenEdaNo == item.ptKohi1.HokenEdaNo)
                    .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .ThenByDescending(e => e.StartDate).FirstOrDefault();
                    kohiName1 = (item.ptKohi1.HokenId.ToString("D2") + " " + (kohi1HokenMst?.HokenNameCd ?? string.Empty) + (string.IsNullOrEmpty(item.ptKohi1.Houbetu) ? "" : item.ptKohi1.Houbetu)).TrimEnd();
                }

                if (item.ptKohi2 != null)
                {
                    var kohi2HokenMst = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && (x.PrefNo == prefNo || x.PrefNo == 0 || x.IsOtherPrefValid == 1) && x.HokenNo == item.ptKohi2.HokenNo && x.HokenEdaNo == item.ptKohi2.HokenEdaNo)
                    .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .ThenByDescending(e => e.StartDate).FirstOrDefault();
                    kohiName2 = (item.ptKohi2.HokenId.ToString("D2") + " " + (kohi2HokenMst?.HokenNameCd ?? string.Empty) + (string.IsNullOrEmpty(item.ptKohi2.Houbetu) ? "" : item.ptKohi2.Houbetu)).TrimEnd();
                }

                if (item.ptKohi3 != null)
                {
                    var kohi3HokenMst = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && (x.PrefNo == prefNo || x.PrefNo == 0 || x.IsOtherPrefValid == 1) && x.HokenNo == item.ptKohi3.HokenNo && x.HokenEdaNo == item.ptKohi3.HokenEdaNo)
                    .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .ThenByDescending(e => e.StartDate).FirstOrDefault();
                    kohiName3 = (item.ptKohi3.HokenId.ToString("D2") + " " + (kohi3HokenMst?.HokenNameCd ?? string.Empty) + (string.IsNullOrEmpty(item.ptKohi3.Houbetu) ? "" : item.ptKohi3.Houbetu)).TrimEnd();
                }

                if (item.ptKohi4 != null)
                {
                    var kohi4HokenMst = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && (x.PrefNo == prefNo || x.PrefNo == 0 || x.IsOtherPrefValid == 1) && x.HokenNo == item.ptKohi4.HokenNo && x.HokenEdaNo == item.ptKohi4.HokenEdaNo)
                    .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .ThenByDescending(e => e.StartDate).FirstOrDefault();
                    kohiName4 = (item.ptKohi4.HokenId.ToString("D2") + " " + (kohi4HokenMst?.HokenNameCd ?? string.Empty) + (string.IsNullOrEmpty(item.ptKohi4.Houbetu) ? "" : item.ptKohi4.Houbetu)).TrimEnd();
                }

                // format hokenName = HokenId (2 numbers) + hoken_mst.hoken_name_cd + pt_kohi.houbetu
                var pthokenPatternName = hokenName + " + " + kohiName1 + " + " + kohiName2 + " + " + kohiName3 + " + " + kohiName4;
                pthokenPatternName = pthokenPatternName.TrimEnd(' ', '+');

                InsuranceModel insuranceModel = new InsuranceModel(
                    item.HpId,
                    item.PtId,
                    birthDay,
                    item.SeqNo,
                    item.HokenSbtCd,
                    item.HokenPid,
                    item.HokenKbn,
                    sinDate,
                    item.HokenMemo,
                    hokenInf,
                    kohi1: GenerateKohiModel(item.ptKohi1),
                    kohi2: GenerateKohiModel(item.ptKohi2),
                    kohi3: GenerateKohiModel(item.ptKohi3),
                    kohi4: GenerateKohiModel(item.ptKohi4),
                    item.PatternIsDeleted,
                    item.StartDate,
                    item.EndDate,
                    false,
                    pthokenPatternName,
                    item.Houbetu
                );
                listInsurance.Add(insuranceModel);
            }

            listInsurance = listInsurance.OrderBy(i => i.IsExpirated).ThenByDescending(i => i.HokenPid).ToList();

            return listInsurance;
        }


        public List<InsuranceSummaryModel> GetInsuranceSummaryList(int hpId, long ptId, int sinDate, bool isDeleted = false, bool isCache = false)
        {
            var key = GetCacheKey() + CacheKeyConstant.InsuranceList + "-" + hpId + "-" + ptId;
            if (isCache && _cache.KeyExists(key))
            {
                var results = _cache.StringGet(key);
                if (!results.IsNull)
                {
                    var json = results.AsString();
                    var datas = JsonSerializer.Deserialize<List<InsuranceSummaryModel>>(json);
                    return datas ?? new();
                }
                else
                {
                    DeleteKeyInsuranceList(hpId, ptId);
                }
            }
            int prefCd = 0;
            var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
            if (hpInf != null)
            {
                prefCd = hpInf.PrefNo;
            }

            PtInf? ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(p => p.HpId == hpId && p.PtId == ptId && p.IsDelete == 0);
            if (ptInf == null)
            {
                return new List<InsuranceSummaryModel>();
            }
            int birthDay = ptInf.Birthday;

            var dataHokenPatterList = NoTrackingDataContext.PtHokenPatterns.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteStatus.None || isDeleted)).OrderByDescending(x => x.HokenPid);
            var dataKohi = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteStatus.None || isDeleted));
            var dataHokenInf = NoTrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteStatus.None || isDeleted));
            var joinQuery = from ptHokenPattern in dataHokenPatterList
                            join ptHokenInf in dataHokenInf on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.HokenId } equals
                                new { ptHokenInf.HpId, ptHokenInf.PtId, ptHokenInf.HokenId }
                            join ptKohi1 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi1Id } equals
                                new { ptKohi1.HpId, ptKohi1.PtId, Kohi1Id = ptKohi1.HokenId } into datakohi1
                            from ptKohi1 in datakohi1.DefaultIfEmpty()
                            join ptKohi2 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi2Id } equals
                                new { ptKohi2.HpId, ptKohi2.PtId, Kohi2Id = ptKohi2.HokenId } into datakohi2
                            from ptKohi2 in datakohi2.DefaultIfEmpty()
                            join ptKohi3 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi3Id } equals
                                new { ptKohi3.HpId, ptKohi3.PtId, Kohi3Id = ptKohi3.HokenId } into datakohi3
                            from ptKohi3 in datakohi3.DefaultIfEmpty()
                            join ptKohi4 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi4Id } equals
                                new { ptKohi4.HpId, ptKohi4.PtId, Kohi4Id = ptKohi4.HokenId } into datakohi4
                            from ptKohi4 in datakohi4.DefaultIfEmpty()
                            select new
                            {
                                ptHokenPattern.HpId,
                                ptHokenPattern.PtId,
                                ptHokenPattern.HokenId,
                                ptHokenPattern.SeqNo,
                                ptHokenInf.HokenNo,
                                ptHokenInf.HokenEdaNo,
                                HokenSbtCd = ptHokenPattern.HokenSbtCd,
                                ptHokenPattern.HokenPid,
                                ptHokenPattern.HokenKbn,
                                ptHokenInf = ptHokenInf,
                                ptHokenInf.HokensyaNo,
                                ptHokenInf.Kigo,
                                ptHokenInf.Bango,
                                ptHokenInf.EdaNo,
                                ptHokenInf.HonkeKbn,
                                ptHokenPattern.StartDate,
                                ptHokenPattern.EndDate,
                                ptHokenInf.SikakuDate,
                                ptHokenInf.KofuDate,
                                ptKohi1,
                                ptKohi2,
                                ptKohi3,
                                ptKohi4,
                                FutansyaNo = ptKohi1.FutansyaNo,
                                ptHokenInf.KogakuKbn,
                                ptHokenInf.TasukaiYm,
                                ptHokenInf.TokureiYm1,
                                ptHokenInf.TokureiYm2,
                                ptHokenInf.GenmenKbn,
                                ptHokenInf.GenmenRate,
                                ptHokenInf.GenmenGaku,
                                ptHokenInf.SyokumuKbn,
                                ptHokenInf.KeizokuKbn,
                                ptHokenInf.Tokki1,
                                ptHokenInf.Tokki2,
                                ptHokenInf.Tokki3,
                                ptHokenInf.Tokki4,
                                ptHokenInf.Tokki5,
                                ptHokenInf.RousaiKofuNo,
                                ptHokenInf.RousaiRoudouCd,
                                ptHokenInf.RousaiSaigaiKbn,
                                ptHokenInf.RousaiKantokuCd,
                                ptHokenInf.RousaiSyobyoDate,
                                ptHokenInf.RyoyoStartDate,
                                ptHokenInf.RyoyoEndDate,
                                ptHokenInf.RousaiSyobyoCd,
                                ptHokenInf.RousaiJigyosyoName,
                                ptHokenInf.RousaiPrefName,
                                ptHokenInf.RousaiCityName,
                                ptHokenInf.RousaiReceCount,
                                ptHokenInf.JibaiHokenName,
                                ptHokenInf.JibaiHokenTanto,
                                ptHokenInf.JibaiHokenTel,
                                ptHokenInf.JibaiJyusyouDate,
                                ptHokenPattern.HokenMemo,
                                HobetuHokenInf = ptHokenInf.Houbetu,
                                HokenInfStartDate = ptHokenInf.StartDate,
                                HokenInfEndDate = ptHokenInf.EndDate,
                                HokenInfIsDeleted = ptHokenInf.IsDeleted,
                                PatternIsDeleted = ptHokenPattern.IsDeleted,
                                Houbetu = ptHokenInf.Houbetu
                            };

            var itemList = joinQuery.ToList();

            List<int> hokenNoList = new List<int>();
            hokenNoList.AddRange(itemList.Select(i => i.ptHokenInf != null ? i.ptHokenInf.HokenNo : 0).ToList());
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi1 != null ? i.ptKohi1.HokenNo : 0).ToList());
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi2 != null ? i.ptKohi2.HokenNo : 0).ToList());
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi3 != null ? i.ptKohi3.HokenNo : 0).ToList());
            hokenNoList.AddRange(itemList.Select(i => i.ptKohi4 != null ? i.ptKohi4.HokenNo : 0).ToList());
            hokenNoList = hokenNoList.Distinct().ToList();

            List<int> hokenEdaNoList = new List<int>();
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptHokenInf != null ? i.ptHokenInf.HokenEdaNo : 0).ToList());
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi1 != null ? i.ptKohi1.HokenEdaNo : 0).ToList());
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi2 != null ? i.ptKohi2.HokenEdaNo : 0).ToList());
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi3 != null ? i.ptKohi3.HokenEdaNo : 0).ToList());
            hokenEdaNoList.AddRange(itemList.Select(i => i.ptKohi4 != null ? i.ptKohi4.HokenEdaNo : 0).ToList());
            hokenEdaNoList = hokenEdaNoList.Distinct().ToList();

            List<HokenMst> hokenMstList = NoTrackingDataContext.HokenMsts.Where(h => (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1) && h.HpId == hpId && hokenNoList.Contains(h.HokenNo) && hokenEdaNoList.Contains(h.HokenEdaNo))
                                                                         .OrderBy(e => e.HpId)
                                                                         .ThenBy(e => e.HokenNo)
                                                                         .ThenBy(e => e.HokenEdaNo)
                                                                         .ThenByDescending(e => e.StartDate)
                                                                         .ThenBy(e => e.HokenSbtKbn)
                                                                         .ThenBy(e => e.SortNo)
                                                                         .ToList();

            List<PtHokenCheck> ptHokenCheckList = NoTrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId && x.PtID == ptId && x.IsDeleted == DeleteStatus.None).ToList();

            List<InsuranceModel> listInsurance = new List<InsuranceModel>();

            var confirmDateList =
                (
                    from hokenCheck in NoTrackingDataContext.PtHokenChecks.Where(p => p.PtID == ptId && p.HpId == hpId && p.IsDeleted == 0)
                    join userMst in NoTrackingDataContext.UserMsts.Where(p => p.HpId == hpId).AsQueryable()
                    on hokenCheck.CheckId equals userMst.UserId
                    select new
                    {
                        hokenCheck,
                        userMst
                    }
                ).ToList();

            List<ConfirmDateModel> GetConfirmDateList(int hokenGrp, int hokenId)
            {
                if (confirmDateList == null)
                {
                    return new List<ConfirmDateModel>();
                }

                return confirmDateList
                    .Where(c => c.hokenCheck.HokenGrp == hokenGrp && c.hokenCheck.HokenId == hokenId)
                    .Select(c => new ConfirmDateModel(c.hokenCheck.HokenGrp, c.hokenCheck.HokenId, c.hokenCheck.SeqNo, c.hokenCheck.CheckId, c.userMst.KanaName ?? string.Empty, c.hokenCheck.CheckCmt ?? string.Empty, c.hokenCheck.CheckDate))
                    .ToList();
            }

            PtHokenCheck? GetLastPtHokenCheck(int id, int hokenGrp)
            {
                return ptHokenCheckList
                    .Where(h => h.HokenId == id && h.HokenGrp == hokenGrp)
                    .OrderByDescending(x => x.CheckDate)
                    .FirstOrDefault();
            }

            KohiInfModel GenerateKohiModel(PtKohi? ptKohi)
            {
                if (ptKohi == null)
                {
                    return GetKohiInfModel(null, null, null, sinDate, new List<ConfirmDateModel>());
                }
                int hokenNo = ptKohi.HokenNo;
                int hokenEdaNo = ptKohi.HokenEdaNo;
                HokenMst? hokenMst = hokenMstList.FirstOrDefault(h => h.HokenNo == hokenNo && h.HokenEdaNo == hokenEdaNo);

                return GetKohiInfModel(
                    ptKohi,
                    GetLastPtHokenCheck(ptKohi.HokenId, HokenGroupConstant.HokenGroupKohi),
                    hokenMst,
                    sinDate,
                    GetConfirmDateList(HokenGroupConstant.HokenGroupKohi, ptKohi.HokenId));
            }
            foreach (var item in itemList)
            {
                HokenMst? hokenMst = hokenMstList.FirstOrDefault(h => h.HokenNo == item.ptHokenInf.HokenNo && h.HokenEdaNo == item.ptHokenInf.HokenEdaNo);
                string houbetu = string.Empty;
                bool isReceKisaiOrNoHoken = false;
                if (hokenMst != null)
                {
                    houbetu = hokenMst.Houbetu ?? string.Empty;
                    isReceKisaiOrNoHoken = IsReceKisai(hokenMst) || IsNoHoken(hokenMst, item.HokenKbn, houbetu ?? string.Empty);
                }

                //get FindHokensyaMstByNoNotrack
                string houbetuNo = string.Empty;
                string hokensyaNoSearch = string.Empty;
                CIUtil.GetHokensyaHoubetu(item.HokensyaNo ?? string.Empty, ref hokensyaNoSearch, ref houbetuNo);

                HokenInfModel hokenInf = new HokenInfModel(
                                        hpId,
                                        ptId,
                                        item.HokenId,
                                        item.SeqNo,
                                        item.HokenNo,
                                        item.HokenEdaNo,
                                        item.HokenKbn,
                                        item.HokensyaNo ?? string.Empty,
                                        item.Kigo ?? string.Empty,
                                        item.Bango ?? string.Empty,
                                        item.EdaNo ?? string.Empty,
                                        item.HonkeKbn,
                                        item.HokenInfStartDate,
                                        item.HokenInfEndDate,
                                        item.SikakuDate,
                                        item.KofuDate,
                                        GetConfirmDate(GetLastPtHokenCheck(item.ptHokenInf.HokenId, HokenGroupConstant.HokenGroupHokenPattern)),
                                        item.KogakuKbn,
                                        item.TasukaiYm,
                                        item.TokureiYm1,
                                        item.TokureiYm2,
                                        item.GenmenKbn,
                                        item.GenmenRate,
                                        item.GenmenGaku,
                                        item.SyokumuKbn,
                                        item.KeizokuKbn,
                                        item.Tokki1 ?? string.Empty,
                                        item.Tokki2 ?? string.Empty,
                                        item.Tokki3 ?? string.Empty,
                                        item.Tokki4 ?? string.Empty,
                                        item.Tokki5 ?? string.Empty,
                                        item.RousaiKofuNo ?? string.Empty,
                                        item.RousaiRoudouCd ?? string.Empty,
                                        item.RousaiSaigaiKbn,
                                        item.RousaiKantokuCd ?? string.Empty,
                                        item.RousaiSyobyoDate,
                                        item.RyoyoStartDate,
                                        item.RyoyoEndDate,
                                        item.RousaiSyobyoCd ?? string.Empty,
                                        item.RousaiJigyosyoName ?? string.Empty,
                                        item.RousaiPrefName ?? string.Empty,
                                        item.RousaiCityName ?? string.Empty,
                                        item.RousaiReceCount,
                                        string.Empty,
                                        string.Empty,
                                        string.Empty,
                                        sinDate,
                                        item.JibaiHokenName ?? string.Empty,
                                        item.JibaiHokenTanto ?? string.Empty,
                                        item.JibaiHokenTel ?? string.Empty,
                                        item.JibaiJyusyouDate,
                                        houbetu ?? string.Empty,
                                        GetConfirmDateList(1, item.HokenId),
                                        new List<RousaiTenkiModel>(),
                                        isReceKisaiOrNoHoken,
                                        item.HokenInfIsDeleted,
                                        Mapper.Map(hokenMst, new HokenMstModel(), (src, dest) =>
                                        {
                                            dest.ChangePropertiesNoAutoMap(src.HokenSname ?? string.Empty);
                                            return dest;
                                        }),
                                        new HokensyaMstModel(),
                                        false,
                                        false,
                                        item.HokenSbtCd,
                                        item.FutansyaNo
                                        );

                // format hokenName 
                string hokenName = (FormatHokenName(
                    hokenId: hokenInf.HokenId,
                    hokenKbn: hokenInf.HokenKbn,
                    hokenSname: hokenMst?.HokenSname ?? string.Empty,
                    honkeKbn: hokenInf.HonkeKbn,
                    hokensyaNo: hokenInf.HokensyaNo ?? string.Empty,
                    startDate: hokenInf.StartDate,
                    endDate: hokenInf.EndDate,
                    sinDate
                )).Trim();

                int prefNo = 0;
                if (hpInf != null)
                {
                    prefNo = hpInf.PrefNo;
                }
                string kohiName1 = "", kohiName2 = "", kohiName3 = "", kohiName4 = "";
                if (item.ptKohi1 != null)
                {
                    var kohi1HokenMst = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && (x.PrefNo == prefNo || x.PrefNo == 0 || x.IsOtherPrefValid == 1) && x.HokenNo == item.ptKohi1.HokenNo && x.HokenEdaNo == item.ptKohi1.HokenEdaNo)
                    .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .ThenByDescending(e => e.StartDate).FirstOrDefault();
                    kohiName1 = (item.ptKohi1.HokenId.ToString("D2") + " " + kohi1HokenMst?.HokenNameCd ?? string.Empty + (string.IsNullOrEmpty(item.ptKohi1.Houbetu) ? "" : "(" + item.ptKohi1.Houbetu + ")")).TrimEnd();
                }

                if (item.ptKohi2 != null)
                {
                    var kohi2HokenMst = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && (x.PrefNo == prefNo || x.PrefNo == 0 || x.IsOtherPrefValid == 1) && x.HokenNo == item.ptKohi2.HokenNo && x.HokenEdaNo == item.ptKohi2.HokenEdaNo)
                    .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .ThenByDescending(e => e.StartDate).FirstOrDefault();
                    kohiName2 = (item.ptKohi2.HokenId.ToString("D2") + " " + kohi2HokenMst?.HokenNameCd ?? string.Empty + (string.IsNullOrEmpty(item.ptKohi2.Houbetu) ? "" : "(" + item.ptKohi2.Houbetu + ")")).TrimEnd();
                }

                if (item.ptKohi3 != null)
                {
                    var kohi3HokenMst = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && (x.PrefNo == prefNo || x.PrefNo == 0 || x.IsOtherPrefValid == 1) && x.HokenNo == item.ptKohi3.HokenNo && x.HokenEdaNo == item.ptKohi3.HokenEdaNo)
                    .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .ThenByDescending(e => e.StartDate).FirstOrDefault();
                    kohiName3 = (item.ptKohi3.HokenId.ToString("D2") + " " + kohi3HokenMst?.HokenNameCd ?? string.Empty + (string.IsNullOrEmpty(item.ptKohi3.Houbetu) ? "" : "(" + item.ptKohi3.Houbetu + ")")).TrimEnd();
                }

                if (item.ptKohi4 != null)
                {
                    var kohi4HokenMst = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && (x.PrefNo == prefNo || x.PrefNo == 0 || x.IsOtherPrefValid == 1) && x.HokenNo == item.ptKohi4.HokenNo && x.HokenEdaNo == item.ptKohi4.HokenEdaNo)
                    .OrderBy(e => e.HpId)
                    .ThenBy(e => e.HokenNo)
                    .ThenByDescending(e => e.PrefNo)
                    .ThenBy(e => e.SortNo)
                    .ThenByDescending(e => e.StartDate).FirstOrDefault();
                    kohiName4 = (item.ptKohi4.HokenId.ToString("D2") + " " + kohi4HokenMst?.HokenNameCd ?? string.Empty + (string.IsNullOrEmpty(item.ptKohi4.Houbetu) ? "" : "(" + item.ptKohi4.Houbetu + ")")).TrimEnd();
                }

                // format hokenName = HokenId (2 numbers) + hoken_mst.hoken_name_cd + pt_kohi.houbetu
                var pthokenPatternName = hokenName + " + " + kohiName1 + " + " + kohiName2 + " + " + kohiName3 + " + " + kohiName4;
                pthokenPatternName = pthokenPatternName.TrimEnd(' ', '+');

                InsuranceModel insuranceModel = new InsuranceModel(
                    item.HpId,
                    item.PtId,
                    birthDay,
                    item.SeqNo,
                    item.HokenSbtCd,
                    item.HokenPid,
                    item.HokenKbn,
                    sinDate,
                    item.HokenMemo,
                    hokenInf,
                    kohi1: GenerateKohiModel(item.ptKohi1),
                    kohi2: GenerateKohiModel(item.ptKohi2),
                    kohi3: GenerateKohiModel(item.ptKohi3),
                    kohi4: GenerateKohiModel(item.ptKohi4),
                    item.PatternIsDeleted,
                    item.StartDate,
                    item.EndDate,
                    false,
                    pthokenPatternName,
                    item.Houbetu
                );
                listInsurance.Add(insuranceModel);
            }

            listInsurance = listInsurance.OrderBy(i => i.IsExpirated).ThenByDescending(i => i.HokenPid).ToList();

            var result = listInsurance.Select(i => new InsuranceSummaryModel(i.HokenPid, i.HokenSName, i.DisplayRateOnly, i.HokenName, i.HokenSName, i.GetHokenPatternType(), (int)i.OrderHokenType)).ToList();
            if (isCache && !_cache.KeyExists(key))
            {
                var datas = JsonSerializer.Serialize<List<InsuranceSummaryModel>>(result);
                _cache.StringSet(key, datas, TimeSpan.FromHours(1));
            }

            return result;
        }

        private HokenMstModel ConvertHokenMstModel(HokenMst? hokenMst, string prefactureName)
        {
            if (hokenMst != null)
            {
                var itemHokenMst = new HokenMstModel(
                                        hokenMst.FutanKbn,
                                        hokenMst.FutanRate,
                                        hokenMst.StartDate,
                                        hokenMst.EndDate,
                                        hokenMst.HokenNo,
                                        hokenMst.HokenEdaNo,
                                        hokenMst.HokenSname ?? string.Empty,
                                        hokenMst.Houbetu ?? string.Empty,
                                        hokenMst.HokenSbtKbn,
                                        hokenMst.CheckDigit,
                                        hokenMst.AgeStart,
                                        hokenMst.AgeEnd,
                                        hokenMst.IsFutansyaNoCheck,
                                        hokenMst.IsJyukyusyaNoCheck,
                                        hokenMst.JyukyuCheckDigit,
                                        hokenMst.IsTokusyuNoCheck,
                                        hokenMst.HokenName ?? string.Empty,
                                        hokenMst.HokenNameCd ?? string.Empty,
                                        hokenMst.HokenKohiKbn,
                                        hokenMst.IsOtherPrefValid,
                                        hokenMst.ReceKisai,
                                        hokenMst.IsLimitList,
                                        hokenMst.IsLimitListSum,
                                        hokenMst.EnTen,
                                        hokenMst.KaiLimitFutan,
                                        hokenMst.DayLimitFutan,
                                        hokenMst.MonthLimitFutan,
                                        hokenMst.MonthLimitCount,
                                        hokenMst.LimitKbn,
                                        hokenMst.CountKbn,
                                        hokenMst.FutanYusen,
                                        hokenMst.CalcSpKbn,
                                        hokenMst.MonthSpLimit,
                                        hokenMst.KogakuTekiyo,
                                        hokenMst.KogakuTotalKbn,
                                        hokenMst.KogakuHairyoKbn,
                                        hokenMst.ReceSeikyuKbn,
                                        hokenMst.ReceKisaiKokho,
                                        hokenMst.ReceKisai2,
                                        hokenMst.ReceTenKisai,
                                        hokenMst.ReceFutanRound,
                                        hokenMst.ReceZeroKisai,
                                        hokenMst.ReceSpKbn,
                                        prefactureName,
                                        hokenMst.PrefNo,
                                        hokenMst.SortNo,
                                        hokenMst.SeikyuYm,
                                        hokenMst.ReceFutanHide,
                                        hokenMst.ReceFutanKbn,
                                        hokenMst.KogakuTotalAll,
                                        false,
                                        hokenMst.DayLimitCount,
                                        new List<ExceptHokensyaModel>());
                return itemHokenMst;
            }
            return new HokenMstModel();
        }

        public bool DeleteInsuranceScan(int hpId, long seqNo, int userId)
        {
            var model = TrackingDataContext.PtHokenScans.FirstOrDefault(x => x.HpId == hpId && x.SeqNo == seqNo && x.IsDeleted == DeleteStatus.None);

            if (model is null)
                return false;

            model.IsDeleted = DeleteStatus.DeleteFlag;
            model.UpdateDate = CIUtil.GetJapanDateTimeNow();
            model.UpdateId = userId;

            return TrackingDataContext.SaveChanges() > 0;
        }

        public bool CheckHokenPatternUsed(int hpId, long ptId, int hokenPid)
        {
            return NoTrackingDataContext.OdrInfs.Any(
                                 x => x.HpId == hpId &&
                                 x.PtId == ptId &&
                                 x.HokenPid == hokenPid &&
                                 x.IsDeleted == DeleteStatus.None);
        }

        public List<KohiPriorityModel> GetKohiPriorityList(int hpId)
        {
            var key = GetCacheKey() + CacheKeyConstant.KohiPriority + "-" + hpId;
            IEnumerable<KohiPriorityModel> kohiPriorityList;
            if (!_cache.KeyExists(key))
            {
                kohiPriorityList = ReloadCache_KohiPriority(key, hpId);
            }
            else
            {
                kohiPriorityList = ReadCache_KohiPriority(key);
            }
            return kohiPriorityList.ToList();
        }
        #region [set cache kohiPriority]
        private IEnumerable<KohiPriorityModel> ReloadCache_KohiPriority(string key, int hpId)
        {
            var data = NoTrackingDataContext.KohiPriorities.Select(x => new KohiPriorityModel(x.PriorityNo, x.PrefNo, x.Houbetu)).ToList();

            var json = JsonSerializer.Serialize(data);
            _cache.StringSet(key, json);
            return data;
        }
        private List<KohiPriorityModel> ReadCache_KohiPriority(string key)
        {
            var results = _cache.StringGet(key);
            var json = results.AsString();
            var datas = JsonSerializer.Deserialize<List<KohiPriorityModel>>(json);
            return datas ?? new();
        }
        #endregion [set cache kohiPriority]

        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        public List<InsuranceScanModel> GetListInsuranceScanByPtId(int hpId, long ptId)
        {
            Stream nullMemory = Stream.Null;
            var datas = NoTrackingDataContext.PtHokenScans.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None).ToList();
            if (datas.Any())
            {
                return datas.Select(x => new InsuranceScanModel(
                                    x.HpId,
                                    x.PtId,
                                    x.SeqNo,
                                    x.HokenGrp,
                                    x.HokenId,
                                    x.FileName ?? string.Empty,
                                    nullMemory,
                                    x.IsDeleted,
                                    x.UpdateDate.ToString("yyyy/MM/dd HH:mm"))).ToList();
            }
            else
            {
                return new List<InsuranceScanModel>();
            }
        }

        public int GetHokenKbnByHokenId(int hpId, int hokenId, long ptId)
        {
            var ptHokenInf = NoTrackingDataContext.PtHokenInfs.FirstOrDefault(item => item.HpId == hpId && item.HokenId == hokenId && item.PtId == ptId);
            return ptHokenInf?.HokenKbn ?? 0;
        }

        public KohiInfModel ConvertToKohiModel(PtKohi kohi)
        {
            return new KohiInfModel(
                   kohi.HokenNo,
                   kohi.HokenId,
                   kohi.HokenSbtKbn,
                   kohi.Houbetu ?? string.Empty,
                   kohi.SeqNo
                );
        }

        public bool CheckExistHokenPid(int hpId, int hokenPid)
        {
            var check = NoTrackingDataContext.PtHokenPatterns.Any(h => h.HpId == hpId && h.HokenPid == hokenPid && h.IsDeleted == 0);
            return check;
        }

        public List<HokenInfModel> FindPtHokenList(int hpId, long ptId, int sinDay)
        {
            var prefCd = NoTrackingDataContext.HpInfs.FirstOrDefault(h => h.HpId == hpId)?.PrefNo ?? 0;
            List<HokenInfModel> ptHokenList = new List<HokenInfModel>();

            var listPtHokenInf = NoTrackingDataContext.PtHokenInfs
                .Where(hoken => hoken.HpId == hpId
                                                   && hoken.PtId == ptId
                                                   && hoken.IsDeleted == 0).ToList();

            var predicateHokenMst = CreateHokenMstExpression(listPtHokenInf);

            if (listPtHokenInf?.Count == 0) return ptHokenList;

            var hokenMstListRepo = NoTrackingDataContext.HokenMsts
                .Where(
                    entity => entity.HpId == hpId
                              && (entity.PrefNo == prefCd
                                  || entity.PrefNo == 0
                                  || entity.IsOtherPrefValid == 1))
                .OrderBy(e => e.HpId)
                .ThenBy(e => e.HokenNo)
                .ThenBy(e => e.HokenEdaNo)
                .ThenByDescending(e => e.StartDate)
                .ThenBy(e => e.HokenSbtKbn)
                .ThenBy(e => e.SortNo);

            var hokenMstList = predicateHokenMst == null ? new() : hokenMstListRepo.Where(predicateHokenMst).ToList();

            return listPtHokenInf?.Select(item => CreatePtHokenInfModel(hpId, ptId,
                item, hokenMstList.Where(itemMst =>
                    itemMst.HokenNo == item.HokenNo && itemMst.HokenEdaNo == item.HokenEdaNo).ToList(), sinDay)).ToList() ?? new();
        }

        public HokenInfModel CreatePtHokenInfModel(int hpId, long ptId, PtHokenInf ePtHokenInf, List<HokenMst> hokenMstLists, int sinDay)
        {
            HokenInfModel? hokenInfModel = null;
            if (ePtHokenInf != null)
            {
                HokenMst hokenMst;
                var hokMstMapped = hokenMstLists
                   .FindAll(hk =>
                   hk.HokenNo == ePtHokenInf.HokenNo
                   && hk.HokenEdaNo == ePtHokenInf.HokenEdaNo)
                   .OrderByDescending(hk => hk.StartDate);

                if (hokMstMapped.Count() > 1)
                {
                    // pick one newest within startDate <= sinday
                    var firstMapped = hokMstMapped.FirstOrDefault(hokMst => hokMst.StartDate <= sinDay);
                    if (firstMapped == null)
                    {
                        // does not exist any hoken master with startDate <= sinday, pick lastest hoken mst (with min start date)
                        // pick last cause by all hoken master is order by start date descending
                        hokenMst = hokMstMapped.LastOrDefault() ?? new();
                    }
                    else
                    {
                        hokenMst = firstMapped;
                    }
                }
                else
                {
                    // have just one hoken mst with HokenNo and HokenEdaNo
                    hokenMst = hokMstMapped?.FirstOrDefault() ?? new();
                }
                HokenMstModel? hokenMstModel = null;
                if (hokenMst != null)
                {
                    hokenMstModel = Mapper.Map(hokenMst, new HokenMstModel(), (src, dest) =>
                    {
                        dest.ChangePropertiesNoAutoMap(src.HokenSname ?? string.Empty);
                        return dest;
                    });
                }
                hokenInfModel = new HokenInfModel(hpId,
                                        ptId,
                                        ePtHokenInf.HokenId,
                                        ePtHokenInf.SeqNo,
                                        ePtHokenInf.HokenNo,
                                        ePtHokenInf.HokenEdaNo,
                                        ePtHokenInf.HokenKbn,
                                        ePtHokenInf.HokensyaNo ?? string.Empty,
                                        ePtHokenInf.Kigo ?? string.Empty,
                                        ePtHokenInf.Bango ?? string.Empty,
                                        ePtHokenInf.EdaNo ?? string.Empty,
                                        ePtHokenInf.HonkeKbn,
                                        ePtHokenInf.StartDate,
                                        ePtHokenInf.EndDate,
                                        ePtHokenInf.SikakuDate,
                                        ePtHokenInf.KofuDate,
                                        new(),
                                        ePtHokenInf.KogakuKbn,
                                        ePtHokenInf.TasukaiYm,
                                        ePtHokenInf.TokureiYm1,
                                        ePtHokenInf.TokureiYm2,
                                        ePtHokenInf.GenmenKbn,
                                        ePtHokenInf.GenmenRate,
                                        ePtHokenInf.GenmenGaku,
                                        ePtHokenInf.SyokumuKbn,
                                        ePtHokenInf.KeizokuKbn,
                                        ePtHokenInf.Tokki1 ?? string.Empty,
                                        ePtHokenInf.Tokki2 ?? string.Empty,
                                        ePtHokenInf.Tokki3 ?? string.Empty,
                                        ePtHokenInf.Tokki4 ?? string.Empty,
                                        ePtHokenInf.Tokki5 ?? string.Empty,
                                        ePtHokenInf.RousaiKofuNo ?? string.Empty,
                                        ePtHokenInf.RousaiRoudouCd ?? string.Empty,
                                        ePtHokenInf.RousaiSaigaiKbn,
                                        ePtHokenInf.RousaiKantokuCd ?? string.Empty,
                                        ePtHokenInf.RousaiSyobyoDate,
                                        ePtHokenInf.RyoyoStartDate,
                                        ePtHokenInf.RyoyoEndDate,
                                        ePtHokenInf.RousaiSyobyoCd ?? string.Empty,
                                        ePtHokenInf.RousaiJigyosyoName ?? string.Empty,
                                        ePtHokenInf.RousaiPrefName ?? string.Empty,
                                        ePtHokenInf.RousaiCityName ?? string.Empty,
                                        ePtHokenInf.RousaiReceCount,
                                        string.Empty,
                                        string.Empty,
                                        string.Empty,
                                        sinDay,
                                        ePtHokenInf.JibaiHokenName ?? string.Empty,
                                        ePtHokenInf.JibaiHokenTanto ?? string.Empty,
                                        ePtHokenInf.JibaiHokenTel ?? string.Empty,
                                        ePtHokenInf.JibaiJyusyouDate,
                                        ePtHokenInf.Houbetu ?? string.Empty,
                                        new(),
                                        new(),
                                        false,
                                        ePtHokenInf.IsDeleted,
                                        hokenMstModel ?? new(),
                                        new(),
                                        false,
                                        false
                                        );
            }

            return hokenInfModel ?? new();
        }

        private Expression<Func<HokenMst, bool>>? CreateHokenMstExpression(List<PtHokenInf>? listPtHokenInf)
        {
            var param = Expression.Parameter(typeof(HokenMst));
            Expression? expression = null;

            CreateHokenMstExpression(listPtHokenInf, ref expression, ref param);

            return expression != null
                ? Expression.Lambda<Func<HokenMst, bool>>(body: expression, parameters: param)
                : null;
        }

        private void CreateHokenMstExpression(List<PtHokenInf>? listPtHokenInf, ref Expression? expression, ref ParameterExpression param)
        {
            if (listPtHokenInf != null)
            {
                foreach (var item in listPtHokenInf)
                {
                    if (item != null)
                    {
                        var valHokenNo = Expression.Constant(item.HokenNo);
                        var memberHokenNo = Expression.Property(param, nameof(HokenMst.HokenNo));

                        var valHokenEdaNo = Expression.Constant(item.HokenEdaNo);
                        var memberHokenEdaNo = Expression.Property(param, nameof(HokenMst.HokenEdaNo));

                        var expressionHoken = Expression.And(Expression.Equal(valHokenNo, memberHokenNo),
                            Expression.Equal(valHokenEdaNo, memberHokenEdaNo));

                        expression = expression == null ? expressionHoken : Expression.Or(expression, expressionHoken);
                    }
                }
            }
        }

        private string FormatHokenName(int hokenId, int hokenKbn, string hokenSname, int honkeKbn, string hokensyaNo, int startDate, int endDate, int sinDate)
        {
            string insuranceCategory = string.Empty;
            // hokenKbn
            // 0: hoken_mst.hoken_sname	
            // 1: 社保 	
            // 2: 	
            // pt_hoken_inf.hokensya_no has 6 numbers: 国保													
            // pt_hoken_inf.hokensya_no has 8 numbers and start = 39: 後期													
            // pt_hoken_inf.hokensya_no has 8 numbers and start = 67: 退職													
            // 11: 労災（短期給付）	
            // 12: 労災（傷病年金）	
            // 13: 労災（アフターケア）	
            // 14: 自賠責	
            switch (hokenKbn)
            {
                case 0:
                    insuranceCategory = hokenSname;
                    break;
                case 1:
                    if (hokenSname == "保険なし")
                    {
                        insuranceCategory = hokenSname;
                    }
                    else
                    {
                        insuranceCategory = "社保";
                    }
                    break;
                case 2:
                    int numbersHokensyaNo = hokensyaNo.Length;
                    if (numbersHokensyaNo == 6)
                    {
                        insuranceCategory = "国保";
                    }
                    else if (numbersHokensyaNo == 8)
                    {
                        if (hokensyaNo.StartsWith("39"))
                        {
                            insuranceCategory = "後期";
                        }
                        else if (hokensyaNo.StartsWith("67"))
                        {
                            insuranceCategory = "退職";
                        }
                        else
                        {
                            insuranceCategory = string.Empty;
                        }
                    }
                    else
                    {
                        insuranceCategory = string.Empty;
                    }
                    break;
                case 11:
                    insuranceCategory = "労災（短期給付）";
                    break;
                case 12:
                    insuranceCategory = "労災（傷病年金）";
                    break;
                case 13:
                    insuranceCategory = "労災（アフターケア）";
                    break;
                case 14:
                    insuranceCategory = "自賠責";
                    break;
                default:
                    insuranceCategory = string.Empty;
                    break;
            }

            string insuranceSelfOrFamily = string.Empty;
            // honke_kbn (only shown when insuranceCategory = 社保 | 国保 | 退職
            //     1: 本人
            //     2: 家族
            if (insuranceCategory == "社保" || insuranceCategory == "国保" || insuranceCategory == "退職" || hokenKbn == 0)
            {
                switch (honkeKbn)
                {
                    case 1:
                        insuranceSelfOrFamily = "本人";
                        break;
                    case 2:
                        insuranceSelfOrFamily = "家族";
                        break;
                    default:
                        insuranceSelfOrFamily = string.Empty;
                        break;
                }
            }


            return (hokenId.ToString("D2") + " " + insuranceCategory + (string.IsNullOrEmpty(insuranceSelfOrFamily) ? "" : "(" + insuranceSelfOrFamily + ")")).TrimEnd();
        }

        public List<HokenInfModel> FindHokenInfByPtId(int hpId, long ptId, int sinDate, int isDeleted = DeleteTypes.None)
        {
            List<HokenInfModel> result = new();
            var hokenInfList = NoTrackingDataContext.PtHokenInfs.Where(item => item.HpId == hpId && item.PtId == ptId && item.IsDeleted == isDeleted)
                                                                .OrderByDescending(entity => entity.HokenId)
                                                                .ToList();

            var hokenIdList = hokenInfList.Where(item => item.IsDeleted != 1 && item.HokenId > 0)
                                          .Select(item => item.HokenId)
                                          .Distinct()
                                          .ToList();

            var rousaiTenkiList = NoTrackingDataContext.PtRousaiTenkis.Where(item => item.HpId == hpId
                                                                                     && item.PtId == ptId
                                                                                     && hokenIdList.Contains(item.HokenId)
                                                                                     && item.IsDeleted != 1)
                                                                      .ToList();

            int prefCd = 0;
            var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
            if (hpInf != null)
            {
                prefCd = hpInf.PrefNo;
            }
            var hokenMasters = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1));

            //Add list rousaitenki by rousaiId and hokenKbn
            foreach (var hokenInf in hokenInfList)
            {
                List<RousaiTenkiModel> ptRousaiTenkis = new();
                if (hokenInf.IsDeleted != 1 && hokenInf.HokenId > 0)
                {
                    ptRousaiTenkis = rousaiTenkiList.Where(item => item.HpId == hpId
                                                                      && item.PtId == ptId
                                                                      && item.HokenId == hokenInf.HokenId
                                                                      && item.IsDeleted != 1)
                                                       .Select(item => new RousaiTenkiModel(item.Sinkei, item.Tenki, item.EndDate, item.IsDeleted, item.SeqNo))
                                                       .OrderBy(item => item.RousaiTenkiEndDate)
                                                       .ToList();
                }
                var hokenMst = hokenMasters.Where(i => i.HokenNo == hokenInf.HokenNo && i.HokenEdaNo == hokenInf.HokenEdaNo).OrderByDescending(h => h.StartDate).ThenByDescending(h => h.EndDate).FirstOrDefault();

                var isExpired = !(hokenInf.StartDate <= sinDate && hokenInf.EndDate >= sinDate);
                // format hokenName 
                string hokenName = (isExpired ? "✕ " : "") + (FormatHokenName(
                    hokenId: hokenInf.HokenId,
                    hokenKbn: hokenInf.HokenKbn,
                    hokenSname: hokenMst?.HokenSname ?? string.Empty,
                    honkeKbn: hokenInf.HonkeKbn,
                    hokensyaNo: hokenInf.HokensyaNo ?? string.Empty,
                    startDate: hokenInf.StartDate,
                    endDate: hokenInf.EndDate,
                    sinDate)
                + " " + hokenInf.HokensyaNo).Trim() + (isExpired ? "（有効期限切れ）" : "");

                var hokenInfModel = new HokenInfModel(
                                        hpId,
                                        ptId,
                                        hokenInf.HokenId,
                                        hokenInf.SeqNo,
                                        hokenInf.HokenNo,
                                        hokenInf.HokenEdaNo,
                                        hokenInf.HokenKbn,
                                        hokenInf.HokensyaNo ?? string.Empty,
                                        hokenInf.Kigo ?? string.Empty,
                                        hokenInf.Bango ?? string.Empty,
                                        hokenInf.EdaNo ?? string.Empty,
                                        hokenInf.HonkeKbn,
                                        hokenInf.StartDate,
                                        hokenInf.EndDate,
                                        hokenInf.SikakuDate,
                                        hokenInf.KofuDate,
                                        0,
                                        hokenInf.KogakuKbn,
                                        hokenInf.TasukaiYm,
                                        hokenInf.TokureiYm1,
                                        hokenInf.TokureiYm2,
                                        hokenInf.GenmenKbn,
                                        hokenInf.GenmenRate,
                                        hokenInf.GenmenGaku,
                                        hokenInf.SyokumuKbn,
                                        hokenInf.KeizokuKbn,
                                        hokenInf.Tokki1 ?? string.Empty,
                                        hokenInf.Tokki2 ?? string.Empty,
                                        hokenInf.Tokki3 ?? string.Empty,
                                        hokenInf.Tokki4 ?? string.Empty,
                                        hokenInf.Tokki5 ?? string.Empty,
                                        hokenInf.RousaiKofuNo ?? string.Empty,
                                        hokenInf.RousaiRoudouCd ?? string.Empty,
                                        hokenInf.RousaiSaigaiKbn,
                                        hokenInf.RousaiKantokuCd ?? string.Empty,
                                        hokenInf.RousaiSyobyoDate,
                                        hokenInf.RyoyoStartDate,
                                        hokenInf.RyoyoEndDate,
                                        hokenInf.RousaiSyobyoCd ?? string.Empty,
                                        hokenInf.RousaiJigyosyoName ?? string.Empty,
                                        hokenInf.RousaiPrefName ?? string.Empty,
                                        hokenInf.RousaiCityName ?? string.Empty,
                                        hokenInf.RousaiReceCount,
                                        hokenInf.HokensyaName ?? string.Empty,
                                        hokenInf.HokensyaAddress ?? string.Empty,
                                        hokenInf.HokensyaTel ?? string.Empty,
                                        sinDate,
                                        hokenInf.JibaiHokenName ?? string.Empty,
                                        hokenInf.JibaiHokenTanto ?? string.Empty,
                                        hokenInf.JibaiHokenTel ?? string.Empty,
                                        hokenInf.JibaiJyusyouDate,
                                        string.Empty,
                                        new(),
                                        ptRousaiTenkis,
                                        true,
                                        0,
                                        new HokenMstModel(hokenName, hokenMst?.HokenSbtKbn ?? 0, hokenMst?.Houbetu ?? string.Empty),
                                        new(),
                                        false,
                                        false);
                result.Add(hokenInfModel);
            }
            result = result.OrderBy(r => r.IsExpirated).ThenByDescending(r => r.HokenId).ToList();
            return result;
        }

        #region AI Chart
        public InsuranceDataDto GetInsurancesDataById(int hpId, long ptId, int sinDate, bool flag = true, bool isDeletedPtHokenInf = false)
        {
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(pt => pt.HpId == hpId && pt.PtId == ptId);
            int birthDayPt = 0;
            int limitConsFlg = 0;
            int prefCd = 0;
            var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
            if (hpInf != null)
            {
                prefCd = hpInf.PrefNo;
            }
            if (ptInf != null)
            {
                birthDayPt = ptInf.Birthday;
                limitConsFlg = ptInf.LimitConsFlg;
            }

            #region max-id-insurance
            int maxIdHokenInf = NoTrackingDataContext.PtHokenInfs.Where(h => h.HpId == hpId && h.PtId == ptId).DefaultIfEmpty()?.Max(p => p == null ? 0 : p.HokenId) ?? 0;
            int maxIdKohi = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId).DefaultIfEmpty()?.Max(p => p == null ? 0 : p.HokenId) ?? 0;
            int maxPidHokenPattern = NoTrackingDataContext.PtHokenPatterns.Where(x => x.PtId == ptId && x.HpId == hpId).DefaultIfEmpty()?.Max(p => p == null ? 0 : p.HokenPid) ?? 0;
            #endregion

            #region PtHokenInf
            IQueryable<PtHokenInf> hokenInfQuery = NoTrackingDataContext.PtHokenInfs.Where(h => h.HpId == hpId && h.PtId == ptId && (isDeletedPtHokenInf || (h.IsDeleted == DeleteTypes.None || h.HokenId == maxIdHokenInf))).OrderByDescending(x => x.HokenId);

            // if flag is true, get hokenMst between startDate and endDate
            var hokenMasterInfQuery = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId && (!flag || (h.StartDate <= sinDate && sinDate <= h.EndDate)) &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                     .GroupBy(x => new
                                     {
                                         x.HpId,
                                         x.HokenNo,
                                         x.HokenEdaNo,
                                     }).Select(grp => new
                                     {
                                         HpId = grp.Key.HpId,
                                         HokenNo = (int?)grp.Key.HokenNo ?? 0,
                                         HokenEdaNo = grp.Key.HokenEdaNo,
                                         StartDate = grp.Max(x => x.StartDate)
                                     });

            var hokenMasterInfQueryDF = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                        .GroupBy(x => new
                                        {
                                            x.HpId,
                                            x.HokenNo,
                                            x.HokenEdaNo
                                        }).Select(grp => new
                                        {
                                            HpId = grp.Key.HpId,
                                            HokenNo = grp.Key.HokenNo,
                                            HokenEdaNo = grp.Key.HokenEdaNo,
                                            StartDate = grp.Max(x => x.StartDate)
                                        });

            var hokenMasterInfQueryTarget = from hokenDf in hokenMasterInfQueryDF
                                            join hokenPrioritize in hokenMasterInfQuery
                                            on new { hokenDf.HokenEdaNo, hokenDf.HokenNo, hokenDf.HpId } equals new { hokenPrioritize.HokenEdaNo, hokenPrioritize.HokenNo, hokenPrioritize.HpId } into obj
                                            from hoken in obj.DefaultIfEmpty()
                                            select new
                                            {
                                                HpId = hokenDf.HpId,
                                                HokenNo = hokenDf.HokenNo,
                                                HokenEdaNo = hokenDf.HokenEdaNo,
                                                StartDate = (hoken.HokenNo == 0 && hoken.HokenEdaNo == 0) ? hokenDf.StartDate : (int?)hoken.StartDate ?? 0
                                            };

            IQueryable<HokenMst> hokenMasterFinal = from hoken in hokenMasterInfQueryTarget
                                                    select NoTrackingDataContext.HokenMsts.FirstOrDefault(h => h.HpId == hpId && h.HokenNo == hoken.HokenNo && h.HokenEdaNo == hoken.HokenEdaNo && h.StartDate == hoken.StartDate && (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1));

            var queryHokenInf = (from inf in hokenInfQuery
                                 join hkMaster in hokenMasterFinal on new { inf.HokenNo, inf.HokenEdaNo } equals new { hkMaster.HokenNo, hkMaster.HokenEdaNo } into hkMtObject
                                 from hkObj in hkMtObject.DefaultIfEmpty()
                                 select new
                                 {
                                     inf.HpId,
                                     inf.PtId,
                                     inf.HokenId,
                                     inf.SeqNo,
                                     inf.HokenNo,
                                     inf.HokenEdaNo,
                                     inf.HokensyaNo,
                                     inf.Kigo,
                                     inf.Bango,
                                     inf.EdaNo,
                                     inf.StartDate,
                                     inf.EndDate,
                                     inf.IsDeleted,
                                     limitConsFlg,
                                     inf.HokenKbn,
                                     inf.Houbetu,
                                     hkObj.HokenSbtKbn,
                                     hkObj.HokenSname,
                                     HokenCheckList = (from hkC in NoTrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId && x.PtID == ptId && x.IsDeleted == DeleteStatus.None && x.HokenGrp == HokenGroupConstant.HokenGroupHokenPattern
                                                                            && x.HokenId == inf.HokenId).OrderByDescending(o => o.CheckDate)
                                                       join userMst in NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId)
                                                       on hkC.CheckId equals userMst.UserId
                                                       select new
                                                       {
                                                           hkC.PtID,
                                                           hkC.HokenGrp,
                                                           hkC.HokenId,
                                                           hkC.SeqNo,
                                                           hkC.CheckId,
                                                           hkC.CheckCmt,
                                                           CheckName = userMst.Name,
                                                           hkC.CheckDate,
                                                           hkC.IsDeleted,
                                                       }
                                                ).ToList(),
                                 }).ToList();

            List<HokenInfDto> hokenInfList = queryHokenInf.Select(item =>
            {
                return new HokenInfDto(
                    hpId,
                    ptId,
                    item.HokenId,
                    item.SeqNo,
                    item.HokenNo,
                    item.HokenEdaNo,
                    item.HokenKbn,
                    item.HokensyaNo,
                    item.Kigo,
                    item.Bango,
                    item.EdaNo,
                    item.HokenKbn,
                    item.StartDate,
                    item.EndDate,
                    DateTimeToInt(item.HokenCheckList.FirstOrDefault()?.CheckDate),
                    sinDate,
                    item.IsDeleted,
                    item.HokenCheckList.Select(e => new HokenCheckDto(e.PtID, e.SeqNo, e.HokenGrp, e.HokenId, DateTimeToInt(e.CheckDate), e.CheckId, e.CheckCmt, e.CheckName)).ToList(),
                    item.HokenSbtKbn,
                    item.HokenSname,
                    item.Houbetu
                    );
            }).ToList();

            #endregion PtHokenInf

            #region PtHokenKohi
            IQueryable<PtKohi> kohiQuery = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteTypes.None || x.HokenId == maxIdKohi)).OrderByDescending(entity => entity.HokenId);

            var hokenMasterKohiQuery = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId && h.StartDate <= sinDate && sinDate <= h.EndDate &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                       .GroupBy(x => new
                                       {
                                           x.HpId,
                                           x.HokenNo,
                                           x.HokenEdaNo,
                                           x.PrefNo
                                       }).Select(grp => new
                                       {
                                           HpId = grp.Key.HpId,
                                           HokenNo = (int?)grp.Key.HokenNo ?? 0,
                                           HokenEdaNo = grp.Key.HokenEdaNo,
                                           PrefNo = grp.Key.PrefNo,
                                           StartDate = grp.Max(x => x.StartDate)
                                       });

            var hokenMasterKohiQueryDF = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                        .GroupBy(x => new
                                        {
                                            x.HpId,
                                            x.HokenNo,
                                            x.HokenEdaNo,
                                            x.PrefNo
                                        }).Select(grp => new
                                        {
                                            HpId = grp.Key.HpId,
                                            HokenNo = grp.Key.HokenNo,
                                            HokenEdaNo = grp.Key.HokenEdaNo,
                                            PrefNo = grp.Key.PrefNo,
                                            StartDate = grp.Max(x => x.StartDate)
                                        });


            var hokenMasterKohiQueryTarget = from hokenDf in hokenMasterKohiQueryDF
                                             join hokenPrioritize in hokenMasterKohiQuery
                                             on new { hokenDf.HokenEdaNo, hokenDf.HokenNo, hokenDf.HpId, hokenDf.PrefNo } equals new { hokenPrioritize.HokenEdaNo, hokenPrioritize.HokenNo, hokenPrioritize.HpId, hokenPrioritize.PrefNo } into obj
                                             from hoken in obj.DefaultIfEmpty()
                                             select new
                                             {
                                                 HpId = hokenDf.HpId,
                                                 HokenNo = hokenDf.HokenNo,
                                                 HokenEdaNo = hokenDf.HokenEdaNo,
                                                 PrefNo = hokenDf.PrefNo,
                                                 StartDate = (hoken.HokenNo == 0 && hoken.HokenEdaNo == 0) ? hokenDf.StartDate : (int?)hoken.StartDate ?? 0
                                             };

            IQueryable<HokenMst> hokenMasterKohiFinal = from hoken in hokenMasterKohiQueryTarget
                                                        select NoTrackingDataContext.HokenMsts.FirstOrDefault(h => h.HpId == hpId && h.HokenNo == hoken.HokenNo && h.HokenEdaNo == hoken.HokenEdaNo && h.StartDate == hoken.StartDate && h.PrefNo == hoken.PrefNo);

            var queryKohi = (from kohi in kohiQuery
                             join hkMaster in hokenMasterKohiFinal on new { kohi.HokenNo, kohi.HokenEdaNo, kohi.PrefNo } equals new { hkMaster.HokenNo, hkMaster.HokenEdaNo, hkMaster.PrefNo } into hkMtObject
                             from hkObj in hkMtObject.DefaultIfEmpty()
                             join roudou in NoTrackingDataContext.RoudouMsts
                             on hkObj.PrefNo.ToString() equals roudou.RoudouCd into rouObject
                             from rou in rouObject.DefaultIfEmpty()
                             select new
                             {
                                 kohi.FutansyaNo,
                                 kohi.HokenId,
                                 kohi.JyukyusyaNo,
                                 kohi.TokusyuNo,
                                 kohi.StartDate,
                                 kohi.EndDate,
                                 kohi.HokenSbtKbn,
                                 kohi.HokenNo,
                                 kohi.HokenEdaNo,
                                 kohi.IsDeleted,
                                 kohi.Houbetu,
                                 kohi.SeqNo,
                                 hkObj.HokenNameCd,
                                 HokenCheckList = (from hkC in NoTrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId && x.PtID == ptId && x.IsDeleted == DeleteStatus.None && x.HokenGrp == HokenGroupConstant.HokenGroupKohi
                                                                              && x.HokenId == kohi.HokenId).OrderByDescending(o => o.CheckDate)
                                                   join userMst in NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId)
                                                   on hkC.CheckId equals userMst.UserId
                                                   select new
                                                   {
                                                       hkC.PtID,
                                                       hkC.HokenGrp,
                                                       hkC.HokenId,
                                                       hkC.SeqNo,
                                                       hkC.CheckId,
                                                       hkC.CheckCmt,
                                                       CheckName = userMst.Name,
                                                       hkC.CheckDate,
                                                       hkC.IsDeleted,
                                                   }
                                                 ).ToList(),
                                 Roudou = rou
                             }).ToList();

            List<KohiInfDto> kohiInfList = queryKohi.Select(obj => new KohiInfDto(
                                                          obj.FutansyaNo ?? string.Empty,
                                                          obj.JyukyusyaNo ?? string.Empty,
                                                          obj.HokenId,
                                                          obj.StartDate,
                                                          obj.EndDate,
                                                          DateTimeToInt(obj.HokenCheckList.FirstOrDefault()?.CheckDate),
                                                          obj.TokusyuNo ?? string.Empty,
                                                          obj.HokenSbtKbn,
                                                          obj.HokenNo,
                                                          obj.HokenEdaNo,
                                                          sinDate,
                                                          obj.IsDeleted,
                                                          obj.HokenCheckList.Select(e => new HokenCheckDto(e.PtID, e.SeqNo, e.HokenGrp, e.HokenId, DateTimeToInt(e.CheckDate), e.CheckId, e.CheckCmt, e.CheckName)).ToList(),
                                                          obj.HokenNameCd,
                                                          obj.Houbetu,
                                                          0,
                                                          obj.SeqNo
                                                          )
            ).ToList();
            #endregion PtHokenKohi

            #region PtHokenPattern
            var dataHokenPatterList = NoTrackingDataContext.PtHokenPatterns.Where(x => x.PtId == ptId && x.HpId == hpId && (x.IsDeleted == DeleteTypes.None || x.HokenPid == maxPidHokenPattern)).OrderByDescending(x => x.HokenPid);
            var dataKohi = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteStatus.None);
            var dataHokenInf = NoTrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteTypes.None || x.HokenId == maxIdHokenInf));

            var joinQuery = from ptHokenPattern in dataHokenPatterList
                            join ptHokenInf in dataHokenInf on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.HokenId } equals
                                new { ptHokenInf.HpId, ptHokenInf.PtId, ptHokenInf.HokenId }
                            join ptKohi1 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi1Id } equals
                                new { ptKohi1.HpId, ptKohi1.PtId, Kohi1Id = ptKohi1.HokenId } into datakohi1
                            from ptKohi1 in datakohi1.DefaultIfEmpty()
                            join ptKohi2 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi2Id } equals
                                new { ptKohi2.HpId, ptKohi2.PtId, Kohi2Id = ptKohi2.HokenId } into datakohi2
                            from ptKohi2 in datakohi2.DefaultIfEmpty()
                            join ptKohi3 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi3Id } equals
                                new { ptKohi3.HpId, ptKohi3.PtId, Kohi3Id = ptKohi3.HokenId } into datakohi3
                            from ptKohi3 in datakohi3.DefaultIfEmpty()
                            join ptKohi4 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi4Id } equals
                                new { ptKohi4.HpId, ptKohi4.PtId, Kohi4Id = ptKohi4.HokenId } into datakohi4
                            from ptKohi4 in datakohi4.DefaultIfEmpty()
                            select new
                            {
                                ptHokenPattern.HpId,
                                ptHokenPattern.PtId,
                                ptHokenPattern.HokenId,
                                ptHokenPattern.SeqNo,
                                ptHokenInf.HokenNo,
                                ptHokenInf.HokenEdaNo,
                                ptHokenPattern.HokenSbtCd,
                                ptHokenPattern.HokenPid,
                                ptHokenPattern.HokenKbn,
                                ptHokenPattern.StartDate,
                                ptHokenPattern.EndDate,
                                ptKohi1,
                                ptKohi2,
                                ptKohi3,
                                ptKohi4,
                                ptHokenPattern.HokenMemo,
                                HokenInfIsDeleted = ptHokenInf.IsDeleted,
                                PatternIsDeleted = ptHokenPattern.IsDeleted,
                                Houbetu = ptHokenInf.Houbetu
                            };


            var itemList = joinQuery.ToList();
            List<HokenPatternDto> listInsurance = new List<HokenPatternDto>();
            foreach (var item in itemList)
            {
                HokenInfDto hokenInf = hokenInfList.FirstOrDefault(h => h.HokenNo == item.HokenNo && h.HokenEdaNo == item.HokenEdaNo && h.HokenId == item.HokenId) ?? new HokenInfDto();

                KohiInfDto Kohi1 = kohiInfList.FirstOrDefault(h => item.ptKohi1 != null && h.HokenNo == item.ptKohi1.HokenNo && h.HokenEdaNo == item.ptKohi1.HokenEdaNo && item.ptKohi1.HokenId == h.HokenId) ?? new KohiInfDto();
                KohiInfDto Kohi2 = kohiInfList.FirstOrDefault(h => item.ptKohi2 != null && h.HokenNo == item.ptKohi2.HokenNo && h.HokenEdaNo == item.ptKohi2.HokenEdaNo && item.ptKohi2.HokenId == h.HokenId) ?? new KohiInfDto();
                KohiInfDto Kohi3 = kohiInfList.FirstOrDefault(h => item.ptKohi3 != null && h.HokenNo == item.ptKohi3.HokenNo && h.HokenEdaNo == item.ptKohi3.HokenEdaNo && item.ptKohi3.HokenId == h.HokenId) ?? new KohiInfDto();
                KohiInfDto Kohi4 = kohiInfList.FirstOrDefault(h => item.ptKohi4 != null && h.HokenNo == item.ptKohi4.HokenNo && h.HokenEdaNo == item.ptKohi4.HokenEdaNo && item.ptKohi4.HokenId == h.HokenId) ?? new KohiInfDto();

                listInsurance.Add(new HokenPatternDto(
                    item.HpId,
                    item.PtId,
                    item.HokenSbtCd,
                    item.HokenPid,
                    item.HokenKbn,
                    item.HokenMemo,
                    sinDate,
                    item.PatternIsDeleted,
                    hokenInf,
                    Kohi1,
                    Kohi2,
                    Kohi3,
                    Kohi4
                ));
            }
            #endregion PtHokenPattern

            return new InsuranceDataDto(listInsurance, hokenInfList, kohiInfList, maxIdHokenInf, maxIdKohi, maxPidHokenPattern);
        }

        public List<HokenInfDto> GetHokenInfByPtId(int hpId, long ptId, int sinDate, bool flag = true, bool isDeletedPtHokenInf = false)
        {

            var (prefCd, birthDayPt, limitConsFlg) = GetPtInfByPtId(hpId, ptId);
            #region PtHokenInf
            IQueryable<PtHokenInf> hokenInfQuery = NoTrackingDataContext.PtHokenInfs.Where(h => h.HpId == hpId && h.PtId == ptId && (isDeletedPtHokenInf || (h.IsDeleted == DeleteTypes.None))).OrderByDescending(x => x.HokenId);
            var dataHokenPatterList = NoTrackingDataContext.PtHokenPatterns.Where(x => x.PtId == ptId && x.HpId == hpId && (x.IsDeleted == DeleteTypes.None)).OrderByDescending(x => x.HokenPid);

            var listKarteEdition = NoTrackingDataContext.KarteEditions
                .Where(e => e.HpId == hpId &&
                       e.PtId == ptId &&
                       e.KarteStatus == KarteStatusConst.Official &&
                       e.IsDeleted == DeleteTypes.None);

            var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None)
                .OrderByDescending(x => x.SinDate)
                .ThenByDescending(e => e.UketukeTime)
                .ThenByDescending(e => e.RaiinNo);

            var lastRaiinInf = (from raiinInf in listRaiinInf
                                join karteEdition in listKarteEdition on
                                    raiinInf.RaiinNo equals karteEdition.RaiinNo
                                select raiinInf)
                    .OrderByDescending(x => x.SinDate)
                    .ThenByDescending(e => e.UketukeTime)
                    .ThenByDescending(e => e.RaiinNo);
            // if flag is true, get hokenMst between startDate and endDate
            var hokenMasterInfQuery = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId && (!flag || (h.StartDate <= sinDate && sinDate <= h.EndDate)) &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                     .GroupBy(x => new
                                     {
                                         x.HpId,
                                         x.HokenNo,
                                         x.HokenEdaNo,
                                     }).Select(grp => new
                                     {
                                         HpId = grp.Key.HpId,
                                         HokenNo = (int?)grp.Key.HokenNo ?? 0,
                                         HokenEdaNo = grp.Key.HokenEdaNo,
                                         StartDate = grp.Max(x => x.StartDate)
                                     });

            var hokenMasterInfQueryDF = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                        .GroupBy(x => new
                                        {
                                            x.HpId,
                                            x.HokenNo,
                                            x.HokenEdaNo
                                        }).Select(grp => new
                                        {
                                            HpId = grp.Key.HpId,
                                            HokenNo = grp.Key.HokenNo,
                                            HokenEdaNo = grp.Key.HokenEdaNo,
                                            StartDate = grp.Max(x => x.StartDate)
                                        });

            var hokenMasterInfQueryTarget = from hokenDf in hokenMasterInfQueryDF
                                            join hokenPrioritize in hokenMasterInfQuery
                                            on new { hokenDf.HokenEdaNo, hokenDf.HokenNo, hokenDf.HpId } equals new { hokenPrioritize.HokenEdaNo, hokenPrioritize.HokenNo, hokenPrioritize.HpId } into obj
                                            from hoken in obj.DefaultIfEmpty()
                                            select new
                                            {
                                                HpId = hokenDf.HpId,
                                                HokenNo = hokenDf.HokenNo,
                                                HokenEdaNo = hokenDf.HokenEdaNo,
                                                StartDate = (hoken.HokenNo == 0 && hoken.HokenEdaNo == 0) ? hokenDf.StartDate : (int?)hoken.StartDate ?? 0
                                            };

            IQueryable<HokenMst> hokenMasterFinal = from hoken in hokenMasterInfQueryTarget
                                                    select NoTrackingDataContext.HokenMsts.FirstOrDefault(h => h.HpId == hpId && h.HokenNo == hoken.HokenNo && h.HokenEdaNo == hoken.HokenEdaNo && h.StartDate == hoken.StartDate && (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1));

            var queryHokenInf = (from inf in hokenInfQuery
                                 join hkMaster in hokenMasterFinal on new { inf.HokenNo, inf.HokenEdaNo } equals new { hkMaster.HokenNo, hkMaster.HokenEdaNo } into hkMtObject
                                 from hkObj in hkMtObject.DefaultIfEmpty()
                                 join roudou in NoTrackingDataContext.RoudouMsts
                                 on inf.RousaiRoudouCd equals roudou.RoudouCd into rouObject
                                 from rou in rouObject.DefaultIfEmpty()
                                 select new
                                 {
                                     hokenInf = inf,
                                     limitConsFlg,
                                     hkObj = hkObj,
                                     HokenCheckList = (from hkC in NoTrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId && x.PtID == ptId && x.IsDeleted == DeleteStatus.None && x.HokenGrp == HokenGroupConstant.HokenGroupHokenPattern
                                                                            && x.HokenId == inf.HokenId).OrderByDescending(o => o.CheckDate)
                                                       join userMst in NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId)
                                                       on hkC.CheckId equals userMst.UserId
                                                       select new
                                                       {
                                                           hkC.PtID,
                                                           hkC.HokenGrp,
                                                           hkC.HokenId,
                                                           hkC.SeqNo,
                                                           hkC.CheckId,
                                                           hkC.CheckCmt,
                                                           CheckName = userMst.Name,
                                                           hkC.CheckDate,
                                                           hkC.IsDeleted,
                                                           hkC.OnlineConfirmationId
                                                       }
                                                ).ToList(),

                                     HokenPattern = dataHokenPatterList.Where(e => e.HokenId == inf.HokenId).Select(e => new { e.HokenPid, e.Kohi1Id, e.Kohi2Id, e.Kohi3Id, e.Kohi4Id }).ToList(),
                                     RousaiTenkis = NoTrackingDataContext.PtRousaiTenkis.Where(x => x.HpId == hpId && x.PtId == ptId && x.HokenId == inf.HokenId && x.IsDeleted == DeleteStatus.None).OrderBy(x => x.EndDate).ToList(),
                                     Roudou = rou
                                 }).ToList();

            List<HokenInfDto> hokenInfList = queryHokenInf.Select(item =>
            {
                var countKohi = 0;
                var listHokenPid = new List<int>();
                int sinDateRecentUse = 0;
                if (item.HokenPattern.Count() > 0)
                {
                    listHokenPid = item.HokenPattern.Select(e => e.HokenPid).ToList();
                    sinDateRecentUse = lastRaiinInf.FirstOrDefault(e => listHokenPid.Contains(e.HokenPid))?.SinDate ?? 0;
                    countKohi = item.HokenPattern.Select(e => (e.Kohi1Id == 0 ? 0 : 1) + (e.Kohi2Id == 0 ? 0 : 1) + (e.Kohi3Id == 0 ? 0 : 1) + (e.Kohi4Id == 0 ? 0 : 1)).OrderByDescending(e => e).FirstOrDefault();
                }

                return new HokenInfDto(
                    hpId,
                    ptId,
                    item.hokenInf.HokenId,
                    item.hokenInf.SeqNo,
                    item.hokenInf.HokenNo,
                    item.hokenInf.HokenEdaNo,
                    item.hokenInf.HokenKbn,
                    item.hokenInf.HokensyaNo ?? string.Empty,
                    item.hokenInf.Kigo ?? string.Empty,
                    item.hokenInf.Bango ?? string.Empty,
                    item.hokenInf.EdaNo ?? string.Empty,
                    item.hokenInf.HonkeKbn,
                    item.hokenInf.StartDate,
                    item.hokenInf.EndDate,
                    DateTimeToInt(item.HokenCheckList.FirstOrDefault()?.CheckDate),
                    sinDate,
                    item.hokenInf.IsDeleted,
                    item.HokenCheckList.Select(e => new HokenCheckDto(e.PtID, e.SeqNo, e.HokenGrp, e.HokenId, DateTimeToInt(e.CheckDate), e.CheckId, e.CheckCmt, e.CheckName, e.OnlineConfirmationId)).ToList(),
                    item.hkObj?.HokenSbtKbn,
                    item.hkObj?.HokenSname,
                    item.hokenInf.Houbetu ?? string.Empty,
                    item.hkObj?.HokenNo ?? 0,
                    item.hkObj?.HokenEdaNo ?? 0,
                    item.hokenInf.RousaiKofuNo,
                    item.hokenInf.JibaiHokenName,
                    sinDateRecentUse,
                    item.hkObj?.HokenNameCd ?? string.Empty,
                    item.hokenInf.InsuredName ?? string.Empty,
                    item.hokenInf.SikakuDate,
                    item.hokenInf.KofuDate,
                    item.hokenInf.HokensyaName ?? string.Empty,
                    item.hokenInf.KogakuKbn,
                    item.hokenInf.TasukaiYm,
                    item.hokenInf.TokureiYm1,
                    item.hokenInf.TokureiYm2,
                    item.hokenInf.GenmenKbn,
                    item.hokenInf.GenmenRate,
                    item.hokenInf.GenmenGaku,
                    item.hokenInf.SyokumuKbn,
                    item.hokenInf.KeizokuKbn,
                    item.hokenInf.Tokki1,
                    item.hokenInf.Tokki2,
                    item.hokenInf.Tokki3,
                    item.hokenInf.Tokki4,
                    item.hokenInf.Tokki5,
                    DateTimeToInt(item.HokenCheckList.FirstOrDefault(e => e.OnlineConfirmationId > 0)?.CheckDate),
                    item.hokenInf.RousaiRoudouCd ?? string.Empty,
                    item.hokenInf.RousaiSaigaiKbn,
                    item.hokenInf.RousaiKantokuCd ?? string.Empty,
                    item.hokenInf.RousaiSyobyoDate,
                    item.hokenInf.RyoyoStartDate,
                    item.hokenInf.RyoyoEndDate,
                    item.hokenInf.RousaiSyobyoCd ?? string.Empty,
                    item.hokenInf.RousaiJigyosyoName ?? string.Empty,
                    item.hokenInf.RousaiPrefName ?? string.Empty,
                    item.hokenInf.RousaiCityName ?? string.Empty,
                    item.hokenInf.RousaiReceCount,
                    item.hokenInf.JibaiHokenTanto ?? string.Empty,
                    item.hokenInf.JibaiHokenTel ?? string.Empty,
                    item.hokenInf.JibaiJyusyouDate,
                    item.RousaiTenkis.Select(x => new RousaiTenkiModel(x.Sinkei, x.Tenki, x.EndDate, x.IsDeleted, x.SeqNo)).ToList(),
                    item.hkObj?.FutanRate ?? 0,
                    item.Roudou?.RoudouCd ?? string.Empty,
                    item.Roudou?.RoudouName ?? string.Empty,
                    countKohi
                    );
            }).ToList();

            var listHoken = hokenInfList.Where(e => (e.HokenKbn == 1 || e.HokenKbn == 2) && !e.IsExpirated)
                .OrderByDescending(e => e.SinDateRecentUse)
                .ThenByDescending(e => e.CountKohi)
                .ThenByDescending(e => e.HokenId)
                .ToList();

            var listNotHoken = hokenInfList.Where(e => (e.HokenKbn == 0 || e.IsJibai || e.IsRousai) && !e.IsExpirated)
                .OrderByDescending(e => e.SinDateRecentUse)
                .ThenByDescending(e => e.HokenId)
                .ToList();
            var expirated = hokenInfList.Where(e => e.IsExpirated).OrderByDescending(e => e.HokenId).ToList();

            var hokenSort = new List<HokenInfDto>();
            hokenSort.AddRange(listHoken);
            hokenSort.AddRange(listNotHoken);
            hokenSort.AddRange(expirated);

            if (hokenSort.Count > 0)
                hokenSort[0].IsDefault = true;

            return hokenSort.OrderBy(e => e.IsExpirated).ThenByDescending(e => e.HokenId).ToList();
            #endregion PtHokenInf
        }

        public List<KohiInfDto> GetKohiInfByPtId(int hpId, long ptId, int sinDate, bool flag = true, bool isDeletedPtHokenInf = false)
        {
            var (prefCd, birthDayPt, limitConsFlg) = GetPtInfByPtId(hpId, ptId);

            #region PtHokenKohi
            IQueryable<PtKohi> kohiQuery = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteTypes.None)).OrderByDescending(entity => entity.HokenId);
            var dataHokenPatterList = NoTrackingDataContext.PtHokenPatterns.Where(x => x.PtId == ptId && x.HpId == hpId && (x.IsDeleted == DeleteTypes.None)).OrderByDescending(x => x.HokenPid);

            var listKarteEdition = NoTrackingDataContext.KarteEditions
                .Where(e => e.HpId == hpId &&
                       e.PtId == ptId &&
                       e.KarteStatus == KarteStatusConst.Official &&
                       e.IsDeleted == DeleteTypes.None);

            var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None)
                .OrderByDescending(x => x.SinDate)
                .ThenByDescending(e => e.UketukeTime)
                .ThenByDescending(e => e.RaiinNo);

            var lastRaiinInf = (from raiinInf in listRaiinInf
                                join karteEdition in listKarteEdition on
                                    raiinInf.RaiinNo equals karteEdition.RaiinNo
                                select raiinInf)
                    .OrderByDescending(x => x.SinDate)
                    .ThenByDescending(e => e.UketukeTime)
                    .ThenByDescending(e => e.RaiinNo);

            var hokenMasterKohiQuery = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId && h.StartDate <= sinDate && sinDate <= h.EndDate &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                       .GroupBy(x => new
                                       {
                                           x.HpId,
                                           x.HokenNo,
                                           x.HokenEdaNo,
                                           x.PrefNo
                                       }).Select(grp => new
                                       {
                                           HpId = grp.Key.HpId,
                                           HokenNo = (int?)grp.Key.HokenNo ?? 0,
                                           HokenEdaNo = grp.Key.HokenEdaNo,
                                           PrefNo = grp.Key.PrefNo,
                                           StartDate = grp.Max(x => x.StartDate)
                                       });

            var hokenMasterKohiQueryDF = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1))
                                        .GroupBy(x => new
                                        {
                                            x.HpId,
                                            x.HokenNo,
                                            x.HokenEdaNo,
                                            x.PrefNo
                                        }).Select(grp => new
                                        {
                                            HpId = grp.Key.HpId,
                                            HokenNo = grp.Key.HokenNo,
                                            HokenEdaNo = grp.Key.HokenEdaNo,
                                            PrefNo = grp.Key.PrefNo,
                                            StartDate = grp.Max(x => x.StartDate)
                                        });


            var hokenMasterKohiQueryTarget = from hokenDf in hokenMasterKohiQueryDF
                                             join hokenPrioritize in hokenMasterKohiQuery
                                             on new { hokenDf.HokenEdaNo, hokenDf.HokenNo, hokenDf.HpId, hokenDf.PrefNo } equals new { hokenPrioritize.HokenEdaNo, hokenPrioritize.HokenNo, hokenPrioritize.HpId, hokenPrioritize.PrefNo } into obj
                                             from hoken in obj.DefaultIfEmpty()
                                             select new
                                             {
                                                 HpId = hokenDf.HpId,
                                                 HokenNo = hokenDf.HokenNo,
                                                 HokenEdaNo = hokenDf.HokenEdaNo,
                                                 PrefNo = hokenDf.PrefNo,
                                                 StartDate = (hoken.HokenNo == 0 && hoken.HokenEdaNo == 0) ? hokenDf.StartDate : (int?)hoken.StartDate ?? 0
                                             };

            IQueryable<HokenMst> hokenMasterKohiFinal = from hoken in hokenMasterKohiQueryTarget
                                                        select NoTrackingDataContext.HokenMsts.FirstOrDefault(h => h.HpId == hpId && h.HokenNo == hoken.HokenNo && h.HokenEdaNo == hoken.HokenEdaNo && h.StartDate == hoken.StartDate && h.PrefNo == hoken.PrefNo);

            var queryKohi = (from kohi in kohiQuery
                             join hkMaster in hokenMasterKohiFinal on new { kohi.HokenNo, kohi.HokenEdaNo, kohi.PrefNo } equals new { hkMaster.HokenNo, hkMaster.HokenEdaNo, hkMaster.PrefNo } into hkMtObject
                             from hkObj in hkMtObject.DefaultIfEmpty()
                             join roudou in NoTrackingDataContext.RoudouMsts
                             on hkObj.PrefNo.ToString() equals roudou.RoudouCd into rouObject
                             from rou in rouObject.DefaultIfEmpty()
                             select new
                             {
                                 kohi.FutansyaNo,
                                 kohi.HokenId,
                                 kohi.JyukyusyaNo,
                                 kohi.TokusyuNo,
                                 kohi.StartDate,
                                 kohi.EndDate,
                                 kohi.HokenSbtKbn,
                                 kohi.HokenNo,
                                 kohi.HokenEdaNo,
                                 kohi.IsDeleted,
                                 kohi.Houbetu,
                                 kohi.SeqNo,
                                 hkObj.HokenNameCd,
                                 kohi.Rate,
                                 kohi.GendoGaku,
                                 kohi.SikakuDate,
                                 kohi.KofuDate,
                                 kohi.PrefNo,
                                 HokenCheckList = (from hkC in NoTrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId && x.PtID == ptId && x.IsDeleted == DeleteStatus.None && x.HokenGrp == HokenGroupConstant.HokenGroupKohi
                                                                              && x.HokenId == kohi.HokenId).OrderByDescending(o => o.CheckDate)
                                                   join userMst in NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId)
                                                   on hkC.CheckId equals userMst.UserId
                                                   select new
                                                   {
                                                       hkC.PtID,
                                                       hkC.HokenGrp,
                                                       hkC.HokenId,
                                                       hkC.SeqNo,
                                                       hkC.CheckId,
                                                       hkC.CheckCmt,
                                                       CheckName = userMst.Name,
                                                       hkC.CheckDate,
                                                       hkC.IsDeleted,
                                                       hkC.OnlineConfirmationId
                                                   }
                                                 ).ToList(),
                                 Roudou = rou,
                                 HokenPattern = dataHokenPatterList.Where(e => e.Kohi1Id == kohi.HokenId || e.Kohi2Id == kohi.HokenId || e.Kohi3Id == kohi.HokenId || e.Kohi4Id == kohi.HokenId)
                                 .Select(e => new { e.HokenPid, e.Kohi1Id, e.Kohi2Id, e.Kohi3Id, e.Kohi4Id }).ToList(),

                             }).ToList();

            List<KohiInfDto> kohiInfList = queryKohi.Select(obj =>
            {
                var countKohi = 0;
                var listHokenPId = new List<int>();
                if (obj.HokenPattern.Count() > 0)
                {
                    listHokenPId = obj.HokenPattern.Select(e => e.HokenPid).ToList();
                    countKohi = obj.HokenPattern.Select(e => (e.Kohi1Id == 0 ? 0 : 1) + (e.Kohi2Id == 0 ? 0 : 1) + (e.Kohi3Id == 0 ? 0 : 1) + (e.Kohi4Id == 0 ? 0 : 1)).OrderByDescending(e => e).FirstOrDefault();
                }
                return new KohiInfDto(
                                      obj.FutansyaNo ?? string.Empty,
                                      obj.JyukyusyaNo ?? string.Empty,
                                      obj.HokenId,
                                      obj.StartDate,
                                      obj.EndDate,
                                      DateTimeToInt(obj.HokenCheckList.FirstOrDefault()?.CheckDate),
                                      obj.TokusyuNo ?? string.Empty,
                                      obj.HokenSbtKbn,
                                      obj.HokenNo,
                                      obj.HokenEdaNo,
                                      sinDate,
                                      obj.IsDeleted,
                                      obj.HokenCheckList.Select(e => new HokenCheckDto(e.PtID, e.SeqNo, e.HokenGrp, e.HokenId, DateTimeToInt(e.CheckDate), e.CheckId, e.CheckCmt, e.CheckName, e.OnlineConfirmationId)).ToList(),
                                      obj.HokenNameCd,
                                      obj.Houbetu,
                                      lastRaiinInf.FirstOrDefault(e => listHokenPId.Contains(e.HokenPid))?.SinDate ?? 0,
                                      obj.SeqNo,
                                      obj.Rate,
                                      obj.GendoGaku,
                                      obj.SikakuDate,
                                      obj.KofuDate,
                                      DateTimeToInt(obj.HokenCheckList.FirstOrDefault(e => e.OnlineConfirmationId > 0)?.CheckDate),
                                      obj.PrefNo,
                                      countKohi
                                      );
            }
            ).ToList();

            var kohis = kohiInfList.Where(e => !e.IsExpirated).OrderByDescending(e => e.SinDateRecentUse).ThenByDescending(e => e.CountKohi).ThenByDescending(e => e.HokenId).ToList();
            var expirated = kohiInfList.Where(e => e.IsExpirated).OrderByDescending(e => e.HokenId).ToList();

            var kohiSort = new List<KohiInfDto>();
            kohiSort.AddRange(kohis);
            kohiSort.AddRange(expirated);

            if (kohiSort.Count > 0)
                kohiSort[0].IsDefault = true;
            #endregion PtHokenKohi

            return kohiSort.OrderBy(e => e.IsExpirated).OrderByDescending(e => e.HokenId).ToList();
        }

        public List<HokenPatternDto> GetHokenPatternByPtId(int hpId, long ptId, int sinDate, bool flag = true, bool isDeletedPtHokenInf = false, bool isOdrInf = false)
        {

            var hokenInfList = GetHokenInfByPtId(hpId, ptId, sinDate, flag, isDeletedPtHokenInf);
            var kohiInfList = GetKohiInfByPtId(hpId, ptId, sinDate, flag, isDeletedPtHokenInf);

            #region PtHokenPattern
            var dataHokenPatterList = NoTrackingDataContext.PtHokenPatterns.Where(x => x.PtId == ptId && x.HpId == hpId && (x.IsDeleted == DeleteTypes.None)).OrderByDescending(x => x.HokenPid);
            var dataKohi = NoTrackingDataContext.PtKohis.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteStatus.None);
            var dataHokenInf = NoTrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId && x.PtId == ptId && (x.IsDeleted == DeleteTypes.None));

            var listKarteEdition = NoTrackingDataContext.KarteEditions
                .Where(e => e.HpId == hpId &&
                       e.PtId == ptId &&
                       e.KarteStatus == KarteStatusConst.Official &&
                       e.IsDeleted == DeleteTypes.None);

            var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None)
                .OrderByDescending(x => x.SinDate)
                .ThenByDescending(e => e.UketukeTime)
                .ThenByDescending(e => e.RaiinNo);

            var lastRaiinInf = (from raiinInf in listRaiinInf
                                join karteEdition in listKarteEdition on
                                    raiinInf.RaiinNo equals karteEdition.RaiinNo
                                select raiinInf)
                    .OrderByDescending(x => x.SinDate)
                    .ThenByDescending(e => e.UketukeTime)
                    .ThenByDescending(e => e.RaiinNo);

            var usedPattern = new List<UsedPattern>();
            var listHokenPid = new List<int>();
            if (isOdrInf)
                listHokenPid = NoTrackingDataContext.OdrInfs.Where(e => e.HpId == hpId && e.PtId == ptId && e.IsDeleted == DeleteTypes.None).Select(e => e.HokenPid).Distinct().ToList();

            var joinQuery = from ptHokenPattern in dataHokenPatterList
                            join ptHokenInf in dataHokenInf on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.HokenId } equals
                                new { ptHokenInf.HpId, ptHokenInf.PtId, ptHokenInf.HokenId }
                            join ptKohi1 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi1Id } equals
                                new { ptKohi1.HpId, ptKohi1.PtId, Kohi1Id = ptKohi1.HokenId } into datakohi1
                            from ptKohi1 in datakohi1.DefaultIfEmpty()
                            join ptKohi2 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi2Id } equals
                                new { ptKohi2.HpId, ptKohi2.PtId, Kohi2Id = ptKohi2.HokenId } into datakohi2
                            from ptKohi2 in datakohi2.DefaultIfEmpty()
                            join ptKohi3 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi3Id } equals
                                new { ptKohi3.HpId, ptKohi3.PtId, Kohi3Id = ptKohi3.HokenId } into datakohi3
                            from ptKohi3 in datakohi3.DefaultIfEmpty()
                            join ptKohi4 in dataKohi on
                                new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.Kohi4Id } equals
                                new { ptKohi4.HpId, ptKohi4.PtId, Kohi4Id = ptKohi4.HokenId } into datakohi4
                            from ptKohi4 in datakohi4.DefaultIfEmpty()
                            select new
                            {
                                ptHokenPattern.HpId,
                                ptHokenPattern.PtId,
                                ptHokenPattern.HokenId,
                                ptHokenPattern.SeqNo,
                                ptHokenInf.HokenNo,
                                ptHokenInf.HokenEdaNo,
                                ptHokenPattern.HokenSbtCd,
                                ptHokenPattern.HokenPid,
                                ptHokenPattern.HokenKbn,
                                ptHokenPattern.StartDate,
                                ptHokenPattern.EndDate,
                                ptKohi1,
                                ptKohi2,
                                ptKohi3,
                                ptKohi4,
                                ptHokenPattern.HokenMemo,
                                HokenInfIsDeleted = ptHokenInf.IsDeleted,
                                PatternIsDeleted = ptHokenPattern.IsDeleted,
                                RaiinInf = lastRaiinInf.FirstOrDefault(e => e.HokenPid == ptHokenPattern.HokenPid),
                            };

            var itemList = joinQuery.ToList();
            List<HokenPatternDto> listInsurance = new List<HokenPatternDto>();
            foreach (var item in itemList)
            {
                HokenInfDto hokenInf = hokenInfList.FirstOrDefault(h => h.HokenNo == item.HokenNo && h.HokenEdaNo == item.HokenEdaNo && h.HokenId == item.HokenId) ?? new HokenInfDto();

                KohiInfDto Kohi1 = kohiInfList.FirstOrDefault(h => item.ptKohi1 != null && h.HokenNo == item.ptKohi1.HokenNo && h.HokenEdaNo == item.ptKohi1.HokenEdaNo && item.ptKohi1.HokenId == h.HokenId) ?? new KohiInfDto();
                KohiInfDto Kohi2 = kohiInfList.FirstOrDefault(h => item.ptKohi2 != null && h.HokenNo == item.ptKohi2.HokenNo && h.HokenEdaNo == item.ptKohi2.HokenEdaNo && item.ptKohi2.HokenId == h.HokenId) ?? new KohiInfDto();
                KohiInfDto Kohi3 = kohiInfList.FirstOrDefault(h => item.ptKohi3 != null && h.HokenNo == item.ptKohi3.HokenNo && h.HokenEdaNo == item.ptKohi3.HokenEdaNo && item.ptKohi3.HokenId == h.HokenId) ?? new KohiInfDto();
                KohiInfDto Kohi4 = kohiInfList.FirstOrDefault(h => item.ptKohi4 != null && h.HokenNo == item.ptKohi4.HokenNo && h.HokenEdaNo == item.ptKohi4.HokenEdaNo && item.ptKohi4.HokenId == h.HokenId) ?? new KohiInfDto();

                listInsurance.Add(new HokenPatternDto(
                    item.HpId,
                    item.PtId,
                    item.HokenSbtCd,
                    item.HokenPid,
                    item.HokenKbn,
                    item.HokenMemo,
                    sinDate,
                    item.PatternIsDeleted,
                    hokenInf,
                    Kohi1,
                    Kohi2,
                    Kohi3,
                    Kohi4,
                    item.HokenId,
                    item.RaiinInf?.SinDate ?? 0,
                    item.SeqNo,
                    null,
                    listHokenPid?.FirstOrDefault(e => e == item.HokenPid) ?? 0
                ));
            }

            var listHoken = listInsurance.Where(e => (e.HokenKbn == 1 || e.HokenKbn == 2) && !e.IsExpirated)
                .OrderByDescending(e => e.SinDateRecentUse)
                .ThenByDescending(e => e.CountKohi)
                .ThenByDescending(e => e.HokenPid)
                .ToList();

            var listNotHoken = listInsurance.Where(e => (e.HokenKbn == 0 || e.HokenInf.IsRousai || e.HokenInf.IsJibai) && !e.IsExpirated)
                .OrderByDescending(e => e.SinDateRecentUse)
                .ThenByDescending(e => e.HokenPid)
                .ToList();

            var listExpirated = listInsurance.Where(e => e.IsExpirated).OrderByDescending(e => e.HokenPid).ToList();
            var insuranceData = new List<HokenPatternDto>();
            insuranceData.AddRange(listHoken);
            insuranceData.AddRange(listNotHoken);
            insuranceData.AddRange(listExpirated);

            if (insuranceData.Count > 0)
                insuranceData[0].IsDefault = true;

            #endregion PtHokenPattern

            return insuranceData.OrderBy(e => e.IsExpirated).ThenByDescending(e => e.HokenPid).ToList();
        }

        private (int, int, int) GetPtInfByPtId(int hpId, long ptId)
        {
            int prefCd = 0;
            var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
            if (hpInf != null)
            {
                prefCd = hpInf.PrefNo;
            }
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(pt => pt.HpId == hpId && pt.PtId == ptId);
            int birthDayPt = 0;
            int limitConsFlg = 0;
            if (ptInf != null)
            {
                birthDayPt = ptInf.Birthday;
                limitConsFlg = ptInf.LimitConsFlg;
            }
            return (prefCd, birthDayPt, limitConsFlg);
        }

        private static int DateTimeToInt(DateTime? dateTime, string format = "yyyyMMdd")
        {
            int result = 0;
            if (dateTime.HasValue)
                result = Int32.Parse(dateTime?.ToString(format));
            return result;
        }
        #endregion

        public HokenInfModel GetHokenInf(int hpId, long ptId, int hokenId)
        {
            var hokenInf = NoTrackingDataContext.PtHokenInfs.Where(item => item.HpId == hpId && item.PtId == ptId && item.HokenId == hokenId).OrderByDescending(h => h.SeqNo).FirstOrDefault();

            if (hokenInf == null)
            {
                return new();
            }

            var rousaiTenkiList = NoTrackingDataContext.PtRousaiTenkis.Where(item => item.HpId == hpId
                                                                                     && item.PtId == ptId
                                                                                     && item.HokenId == hokenId
                                                                                     && item.IsDeleted != 1).ToList();

            int prefCd = 0;
            var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
            if (hpInf != null)
            {
                prefCd = hpInf.PrefNo;
            }
            var hokenMasters = NoTrackingDataContext.HokenMsts.Where(h => h.HpId == hpId &&
                                                                            (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1));

            List<RousaiTenkiModel> ptRousaiTenkis = new();
            if (hokenInf.IsDeleted != 1 && hokenInf.HokenId > 0 && (new int[] { 11, 12, 13, 14 }).Contains(hokenInf.HokenKbn))
            {
                ptRousaiTenkis = rousaiTenkiList.Where(item => item.HpId == hpId
                                                                  && item.PtId == ptId
                                                                  && item.HokenId == hokenInf.HokenId
                                                                  && item.IsDeleted != 1)
                                                   .Select(item => new RousaiTenkiModel(item.Sinkei, item.Tenki, item.EndDate, item.IsDeleted, item.SeqNo))
                                                   .OrderBy(item => item.RousaiTenkiEndDate)
                                                   .ToList();
            }
            var hokenMst = hokenMasters.Where(i => i.HokenNo == hokenInf.HokenNo && i.HokenEdaNo == hokenInf.HokenEdaNo).OrderByDescending(h => h.StartDate).OrderByDescending(h => h.EndDate).FirstOrDefault();
            var hokenCheckList = NoTrackingDataContext.PtHokenChecks.Where(h => h.HpId == hpId && h.PtID == ptId && h.HokenGrp == HokenGroupConstant.HokenGroupHokenPattern && h.HokenId == hokenId && h.IsDeleted == DeleteTypes.None)
                                                                .Select(e => new ConfirmDateModel(e.HokenGrp, e.HokenId, e.SeqNo, e.CheckId, string.Empty, e.CheckCmt, e.CheckDate, e.OnlineConfirmationId))
                                                                .ToList();
            var hokensyaMst = NoTrackingDataContext.HokensyaMsts.Where(e => e.HpId == hpId && e.HokensyaNo == hokenInf.HokensyaNo && e.IsDelete == DeleteTypes.None)
                                .Select(e => new HokensyaMstModel(e.HpId, e.Name ?? string.Empty, e.KanaName ?? string.Empty, e.HoubetuKbn ?? string.Empty, e.Houbetu ?? string.Empty, e.HokenKbn, e.PrefNo, e.HokensyaNo ?? string.Empty, e.Kigo ?? string.Empty, e.Bango ?? string.Empty, e.RateHonnin, e.RateKazoku, e.PostCode ?? string.Empty, e.Address1 ?? string.Empty, e.Address2 ?? string.Empty, e.Tel1 ?? string.Empty, e.IsKigoNa))
                                .FirstOrDefault();
            if (hokensyaMst == null) hokensyaMst = new();

            var filingInfModels = NoTrackingDataContext.FilingInf.Where(e => e.HpId == hpId && e.PtId == ptId && e.HokenId == hokenId && e.FileNo == 1 && e.IsDeleted == DeleteTypes.None)
                .Select(e => new FilingInfModel
                {
                    FileLink = e.S3FileName ?? string.Empty,
                    FileId = e.FileId
                })
                .ToList();
            var tasks = filingInfModels.Select(async item =>
            {
                item.FileLink = await _amazonS3Service.GetPreSignedUrlsAsync(item.FileLink, ptId, hpId);
            });
            Task.WhenAll(tasks).Wait();

            var hokenInfModel = new HokenInfModel(hokenInf.PtId, hokenInf.HokenId, hokenInf.SeqNo, hokenInf.HokenNo, hokenInf.HokenEdaNo, hokenInf.HokenKbn, hokenInf.HokensyaNo ?? string.Empty, hokenInf.Kigo ?? string.Empty, hokenInf.Bango ?? string.Empty, hokenInf.EdaNo ?? string.Empty, hokenInf.HonkeKbn, hokenInf.HokensyaName ?? string.Empty, hokenInf.InsuredName ?? string.Empty, hokenInf.SikakuDate, hokenInf.KofuDate, hokenInf.StartDate, hokenInf.EndDate, hokenInf.KogakuKbn, hokenInf.TasukaiYm, hokenInf.TokureiYm1, hokenInf.TokureiYm2, hokenInf.GenmenKbn, hokenInf.GenmenRate, hokenInf.GenmenGaku, hokenInf.SyokumuKbn, hokenInf.KeizokuKbn, hokenInf.Tokki1 ?? string.Empty, hokenInf.Tokki2 ?? string.Empty, hokenInf.Tokki3 ?? string.Empty, hokenInf.Tokki4 ?? string.Empty, hokenInf.Tokki5 ?? string.Empty, hokenInf.RousaiKofuNo ?? string.Empty, hokenInf.RousaiRoudouCd ?? string.Empty, hokenInf.RousaiSaigaiKbn, hokenInf.RousaiKantokuCd ?? string.Empty, hokenInf.RousaiSyobyoDate, hokenInf.RyoyoStartDate, hokenInf.RyoyoEndDate, hokenInf.RousaiSyobyoCd ?? string.Empty, hokenInf.RousaiJigyosyoName ?? string.Empty, hokenInf.RousaiPrefName ?? string.Empty, hokenInf.RousaiCityName ?? string.Empty, hokenInf.RousaiReceCount, ptRousaiTenkis, hokenInf.JibaiHokenName ?? string.Empty, hokenInf.JibaiHokenTanto ?? string.Empty, hokenInf.JibaiHokenTel ?? string.Empty, hokenInf.JibaiJyusyouDate, hokenCheckList.OrderByDescending(e => e.SeqNo).ToList(), hokensyaMst, hokenInf.Houbetu ?? string.Empty, filingInfModels);

            return hokenInfModel;
        }

        public void ProcessKohiId(int hpId, long ptId, int kohiId, long kohiSeqNo, ref int secondNum, ref int maruchoCount, ref int hokenSbtKbn, ref string houbetu)
        {
            if (kohiId <= 0) return;
            secondNum++;

            var kohiEntity = NoTrackingDataContext.PtKohis.FirstOrDefault(
                k => k.HokenId == kohiId
                    && k.HpId == hpId
                    && k.PtId == ptId
                    && k.SeqNo == kohiSeqNo
            );

            if (kohiEntity != null)
            {
                int prefNo = 0;
                var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
                if (hpInf != null)
                {
                    prefNo = hpInf.PrefNo;
                }

                var kohiMstModel = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId && (x.PrefNo == prefNo || x.PrefNo == 0 || x.IsOtherPrefValid == 1) && x.HokenNo == kohiEntity.HokenNo && x.HokenEdaNo == kohiEntity.HokenEdaNo)
                                    .OrderBy(e => e.HpId)
                                    .ThenBy(e => e.HokenNo)
                                    .ThenByDescending(e => e.PrefNo)
                                    .ThenBy(e => e.SortNo)
                                    .ThenByDescending(e => e.StartDate).FirstOrDefault();

                if (kohiMstModel != null)
                {
                    hokenSbtKbn = kohiMstModel.HokenSbtKbn;
                    houbetu = kohiMstModel.Houbetu ?? string.Empty;
                    if (kohiMstModel.HokenSbtKbn == 2)
                    {
                        maruchoCount++;
                    }
                }
            }
        }

        public void UpsertHokenInf(int hpId, long ptId, int userId, int hokenId, int kohi1Id, int kohi2Id, int kohi3Id, int kohi4Id)
        {
            HokenMst? hokenMst = null;
            int prefCd = 0;
            var hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault();
            if (hpInf != null)
            {
                prefCd = hpInf.PrefNo;
            }
            if ((kohi1Id > 0 || kohi2Id > 0 || kohi3Id > 0 || kohi4Id > 0) && hokenId == 0)
            {
                hokenMst = NoTrackingDataContext.HokenMsts.Where(h => h.HokenName == "保険なし" && (h.PrefNo == prefCd || h.PrefNo == 0 || h.IsOtherPrefValid == 1) && h.HpId == hpId).OrderByDescending(h => h.StartDate).ThenByDescending(h => h.EndDate).FirstOrDefault();
                if (hokenMst != null)
                {
                    var ptHokenInf = NoTrackingDataContext.PtHokenInfs.FirstOrDefault(p => p.HokenNo == hokenMst.HokenNo && p.HokenEdaNo == hokenMst.HokenEdaNo && p.HpId == hpId && p.PtId == ptId);
                    var max = NoTrackingDataContext.PtHokenInfs.Where(p => p.HpId == hpId && p.PtId == ptId).Max(p => p.HokenId);
                    if (ptHokenInf == null)
                    {
                        var hokenInf = new PtHokenInf
                        {
                            HpId = hpId,
                            PtId = ptId,
                            HokenNo = hokenMst.HokenNo,
                            HokenEdaNo = hokenMst.HokenEdaNo,
                            StartDate = hokenMst.StartDate,
                            EndDate = hokenMst.EndDate,
                            HokenId = ++max,
                            HokenKbn = 1,
                            CreateId = userId,
                            UpdateId = userId,
                            UpdateDate = CIUtil.GetJapanDateTimeNow(),
                            CreateDate = CIUtil.GetJapanDateTimeNow(),
                            Houbetu = hokenMst.Houbetu
                        };
                        TrackingDataContext.Add(hokenInf);
                        TrackingDataContext.SaveChanges();
                    }
                }
            }
        }

        public KohiInfModel? GetKohiDetail(int hpId, long ptId, int seqNo, int hokenId, int sinDate)
        {
            var kohi = NoTrackingDataContext.PtKohis.FirstOrDefault(kohi => kohi.HpId == hpId &&
                                                                                  kohi.PtId == ptId &&
                                                                                  kohi.HokenId == hokenId &&
                                                                                  kohi.SeqNo == seqNo &&
                                                                                  kohi.IsDeleted == DeleteTypes.None);
            if (kohi == null) return null;

            var hokenCheckList = (from hkC in NoTrackingDataContext.PtHokenChecks.Where(x => x.HpId == hpId &&
                                                                                             x.PtID == ptId &&
                                                                                             x.IsDeleted == DeleteStatus.None &&
                                                                                             x.HokenGrp == HokenGroupConstant.HokenGroupKohi &&
                                                                                             x.HokenId == kohi.HokenId // TODO need check again
                                                                                             ).OrderByDescending(o => o.CheckDate)
                                  join userMst in NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId)
                                  on hkC.CheckId equals userMst.UserId
                                  select new
                                  {
                                      HokenCheck = hkC,
                                      UserInfo = userMst
                                  }).ToList();

            var filingInfModels = NoTrackingDataContext.FilingInf.Where(e => e.HpId == hpId && e.PtId == ptId && e.HokenId == hokenId && e.FileNo == 2 && e.IsDeleted == DeleteTypes.None)
                .Select(e => new FilingInfModel
                {
                    FileLink = e.S3FileName ?? string.Empty,
                    FileId = e.FileId
                })
                .ToList();

            var tasks = filingInfModels.Select(async item =>
            {
                item.FileLink = await _amazonS3Service.GetPreSignedUrlsAsync(item.FileLink, ptId, hpId);
            });
            Task.WhenAll(tasks).Wait();

            return new KohiInfModel(kohi.FutansyaNo ?? string.Empty, kohi.JyukyusyaNo ?? string.Empty, kohi.HokenId, kohi.StartDate, kohi.EndDate, GetConfirmDate(hokenCheckList.FirstOrDefault()?.HokenCheck), kohi.Rate, kohi.GendoGaku,
                kohi.SikakuDate, kohi.KofuDate, kohi.TokusyuNo ?? string.Empty, kohi.HokenSbtKbn, kohi.Houbetu ?? string.Empty, kohi.HokenNo, kohi.HokenEdaNo, kohi.PrefNo, new HokenMstModel(), sinDate,
                hokenCheckList.Select(c => new ConfirmDateModel(c.HokenCheck.HokenGrp, c.HokenCheck.HokenId, c.HokenCheck.SeqNo, c.HokenCheck.CheckId, c.UserInfo.Name ?? string.Empty, c.HokenCheck.CheckCmt ?? string.Empty, c.HokenCheck.CheckDate, c.HokenCheck.OnlineConfirmationId)).ToList(), // TODO fixed online confirm id
                false, kohi.IsDeleted, false, kohi.SeqNo, kohi.Birthday, filingInfModels);
        }

        public (bool resultSave, int kohiId, long onlineConfirmHistoryId) SaveKohi(
            int hpId,
            int userId,
            long ptId,
            KohiInfModel kohiModel,
            List<LimitListModel> limitListModels,
            List<PatientInforConfirmOnlineDto> patientInfoFields,
            PtKyuseiModel? ptKyuseiModel,
            OnlineConfirmationHistoryModel onlineCmfHisModel,
            bool isConfirmOnline,
            EndDateModel? endDateModel)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                int defaultMaxDate = 99999999;
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                var japanDate = CIUtil.GetJapanDateTimeNow();

                #region ConfirmOnline

                long onlineConfirmHistoryId = 0;
                if (isConfirmOnline)
                {
                    PtInf? patientInfo = TrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId);
                    if (patientInfo is null) return (false, 0, 0);

                    if (endDateModel != null)
                    {
                        var oldKohi = TrackingDataContext.PtKohis.FirstOrDefault(e => e.HpId == hpId && e.PtId == ptId && e.HokenId == endDateModel.HokenId && e.SeqNo == endDateModel.SeqNo && e.IsDeleted == DeleteTypes.None);
                        if (oldKohi != null)
                        {
                            oldKohi.EndDate = endDateModel.EndDate;
                            oldKohi.UpdateDate = japanDate;
                            oldKohi.UpdateId = userId;
                        }
                    }

                    if (patientInfoFields.Any())
                    {
                        patientInfo.CreateDate = DateTime.SpecifyKind(patientInfo.CreateDate, DateTimeKind.Utc);
                        patientInfo.UpdateDate = japanDate;
                        patientInfo.UpdateId = userId;

                        if (ptKyuseiModel != null)
                        {
                            var ptKyusei = new PtKyusei()
                            {
                                HpId = hpId,
                                PtId = ptId,
                                KanaName = ptKyuseiModel.KanaName,
                                Name = ptKyuseiModel.Name,
                                EndDate = ptKyuseiModel.EndDate,
                                CreateDate = japanDate,
                                CreateId = userId,
                                UpdateDate = japanDate,
                                UpdateId = userId
                            };
                            TrackingDataContext.PtKyuseis.Add(ptKyusei);
                        }
                    }

                    foreach (var item in patientInfoFields)
                    {
                        string pascalCaseCellName = FirstCharToUpper(item.FieldName);

                        switch (pascalCaseCellName)
                        {
                            case nameof(PtInf.Name):
                                patientInfo.Name = item.FieldValue;
                                break;
                            case nameof(PtInf.KanaName):
                                patientInfo.KanaName = item.FieldValue;
                                break;
                            case nameof(PtInf.Birthday):
                                patientInfo.Birthday = int.Parse(item.FieldValue);
                                break;
                            case nameof(PtInf.Sex):
                                patientInfo.Sex = int.Parse(item.FieldValue);
                                break;
                            case nameof(PtInf.HomePost):
                                patientInfo.HomePost = item.FieldValue;
                                break;
                            case nameof(PtInf.HomeAddress1):
                                patientInfo.HomeAddress1 = item.FieldValue;
                                break;
                            case nameof(PtInf.HomeAddress2):
                                patientInfo.HomeAddress2 = item.FieldValue;
                                break;
                            case nameof(PtInf.Setanusi):
                                patientInfo.Setanusi = item.FieldValue;
                                break;
                        }
                    }

                    var onlineConfirmationHistory = new OnlineConfirmationHistory()
                    {
                        HpId = hpId,
                        PtId = ptId,
                        OnlineConfirmationDate = onlineCmfHisModel.OnlineConfirmationDate,
                        ConfirmationType = onlineCmfHisModel.ConfirmationType,
                        ConfirmationResult = onlineCmfHisModel.ConfirmationResult,
                        CreateDate = japanDate,
                        CreateId = userId,
                        InfoConsFlg = onlineCmfHisModel.InfoConsFlg,
                        PrescriptionIssueType = onlineCmfHisModel.PrescriptionIssueType,
                        UketukeStatus = onlineCmfHisModel.UketukeStatus,
                        UpdateDate = japanDate,
                        UpdateId = userId
                    };

                    TrackingDataContext.OnlineConfirmationHistories.Add(onlineConfirmationHistory);
                    TrackingDataContext.SaveChanges();

                    onlineConfirmHistoryId = onlineConfirmationHistory.ID;
                    if (onlineConfirmHistoryId <= 0)
                    {
                        transaction.Rollback();
                        return (false, 0, 0);
                    }

                    //patientInfo.ReferenceNo = patientInfo.PtNum;
                }
                #endregion

                #region HokenKohi
                var kohi = TrackingDataContext.PtKohis.FirstOrDefault(kohi => kohi.HpId == hpId &&
                                                                          kohi.PtId == ptId &&
                                                                          kohi.HokenId == kohiModel.HokenId &&
                                                                          kohi.SeqNo == kohiModel.SeqNo &&
                                                                          kohi.IsDeleted == DeleteTypes.None);
                List<LimitListInf> maxMoneyDatabases = new List<LimitListInf>();

                if (kohi == null)
                {
                    var newKohiId = GenKohiId(hpId, ptId);
                    kohi = new PtKohi()
                    {
                        UpdateId = userId,
                        UpdateDate = CIUtil.GetJapanDateTimeNow(),
                        CreateId = userId,
                        CreateDate = CIUtil.GetJapanDateTimeNow(),
                        EndDate = kohiModel.EndDate == 0 ? defaultMaxDate : kohiModel.EndDate,
                        Birthday = kohiModel.Birthday,
                        FutansyaNo = kohiModel.FutansyaNo,
                        GendoGaku = kohiModel.GendoGaku,
                        HokenEdaNo = kohiModel.HokenEdaNo,
                        HokenNo = kohiModel.HokenNo,
                        HokenSbtKbn = kohiModel.HokenSbtKbn,
                        Houbetu = kohiModel.Houbetu,
                        IsDeleted = kohiModel.IsDeleted,
                        JyukyusyaNo = kohiModel.JyukyusyaNo,
                        KofuDate = kohiModel.KofuDate,
                        PrefNo = kohiModel.PrefNo,
                        Rate = kohiModel.Rate,
                        SikakuDate = kohiModel.SikakuDate,
                        StartDate = kohiModel.StartDate,
                        TokusyuNo = kohiModel.TokusyuNo,
                        HpId = hpId,
                        PtId = ptId,
                        HokenId = newKohiId
                    };
                    TrackingDataContext.PtKohis.Add(kohi);

                    if (!kohiModel.ConfirmDateList.Any() && isConfirmOnline == false)
                    {
                        var newHokenCheck = new PtHokenCheck
                        {
                            CreateId = userId,
                            CreateDate = CIUtil.GetJapanDateTimeNow(),
                            CheckDate = CIUtil.GetJapanDateTimeNow(),
                            CheckCmt = string.Empty,
                            HokenId = kohi.HokenId,
                            CheckId = userId,
                            PtID = ptId,
                            HokenGrp = HokenGroupConstant.HokenGroupKohi,
                            HpId = hpId,
                            OnlineConfirmationId = 0,
                            UpdateDate = CIUtil.GetJapanDateTimeNow(),
                            UpdateId = userId
                        };

                        TrackingDataContext.PtHokenChecks.Add(newHokenCheck);
                    }

                    #region PtHokenCheck
                    TrackingDataContext.PtHokenChecks.AddRange(Mapper.Map<ConfirmDateModel, PtHokenCheck>(kohiModel.ConfirmDateList, (srcCf, destCf) =>
                    {
                        var checkDate = srcCf.ConfirmDate == 0 ? kohiModel.SinDate : srcCf.ConfirmDate;
                        destCf.CreateId = userId;
                        destCf.CreateDate = CIUtil.GetJapanDateTimeNow();
                        destCf.CheckDate = DateTime.SpecifyKind(CIUtil.IntToDate(checkDate), DateTimeKind.Utc);
                        destCf.CheckCmt = srcCf.CheckComment;
                        destCf.HokenId = kohi.HokenId;
                        destCf.CheckId = userId;
                        destCf.PtID = ptId;
                        destCf.HokenGrp = HokenGroupConstant.HokenGroupKohi;
                        destCf.HpId = hpId;
                        destCf.OnlineConfirmationId = onlineConfirmHistoryId == 0 ? srcCf.OnlineConfirmationId : (int)onlineConfirmHistoryId;
                        destCf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        destCf.UpdateId = userId;
                        return destCf;
                    }));
                    #endregion
                }
                else
                {
                    maxMoneyDatabases = TrackingDataContext.LimitListInfs.Where(x => x.HpId == hpId
                                               && x.PtId == ptId
                                               && x.KohiId == kohiModel.HokenId
                                               && x.IsDeleted == DeleteTypes.None).ToList();

                    Mapper.Map(kohiModel, kohi, (src, dest) =>
                    {
                        dest.EndDate = src.EndDate == 0 ? defaultMaxDate : src.EndDate;
                        dest.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        dest.CreateDate = DateTime.SpecifyKind(dest.CreateDate, DateTimeKind.Utc);
                        dest.UpdateId = userId;
                        return dest;
                    });
                    TrackingDataContext.PtKohis.Update(kohi);
                    var databaseHokenChecks = TrackingDataContext.PtHokenChecks.Where(c => c.PtID == ptId && c.HpId == hpId && c.IsDeleted == DeleteTypes.None && c.HokenId == kohiModel.HokenId).ToList();

                    //ConfirmDate
                    UpdateHokenCheck(databaseHokenChecks, kohiModel.ConfirmDateList, hpId, ptId, kohiModel.HokenId, userId, true, (int)onlineConfirmHistoryId);
                }

                var resultKohi = TrackingDataContext.SaveChanges() > 0;
                var kohiId = kohi.HokenId;

                #endregion

                #region Maxmoney

                foreach (var item in maxMoneyDatabases)
                {
                    var exist = limitListModels.FirstOrDefault(x => x.SeqNo == item.SeqNo && x.Id == item.Id);
                    if (exist != null && CheckLimitListInfChanged(item, exist))
                    {
                        item.SortKey = exist.SortKey;
                        item.FutanGaku = exist.FutanGaku;
                        item.TotalGaku = exist.TotalGaku;
                        item.Biko = exist.Biko;
                        item.SinDate = exist.SinDateY * 10000 + exist.SinDateM * 100 + exist.SinDateD;
                        item.IsDeleted = exist.IsDeleted;
                        item.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        item.UpdateId = userId;
                    }
                }

                TrackingDataContext.LimitListInfs.AddRange(Mapper.Map<LimitListModel, LimitListInf>(limitListModels.Where(x => x.SeqNo == 0 && x.Id == 0 && x.IsDeleted == DeleteStatus.None), (src, dest) =>
                {
                    dest.CreateDate = CIUtil.GetJapanDateTimeNow();
                    dest.UpdateDate = dest.CreateDate;
                    dest.PtId = ptId;
                    dest.HpId = hpId;
                    dest.KohiId = kohiId;
                    dest.SinDate = src.SinDateY * 10000 + src.SinDateM * 100 + src.SinDateD;
                    dest.UpdateId = userId;
                    dest.CreateId = userId;
                    return dest;
                }));

                #endregion

                var resultMaxmoney = TrackingDataContext.SaveChanges() > 0;

                if (resultKohi || resultMaxmoney) transaction.Commit();
                else transaction.Rollback();

                return (resultKohi || resultMaxmoney, kohiId, onlineConfirmHistoryId);
            });
        }

        private int GenKohiId(int hpId, long ptId)
        {
            var kohi = NoTrackingDataContext.PtKohis.Where(item => item.HpId == hpId && item.PtId == ptId).OrderByDescending(item => item.HokenId).FirstOrDefault();
            int kohiId = 1;
            if (kohi != null) kohiId += kohi.HokenId;
            return kohiId;
        }

        public void UpdateHokenCheck(List<PtHokenCheck> databaseList, List<ConfirmDateModel> savingList, int hpId, long ptId, int hokenId, int actUserId, bool hokenKohi = false, int onlineConfirmationId = 0)
        {
            int hokenGrp = 1;
            if (hokenKohi)
            {
                hokenGrp = 2;
            }
            var checkDatabaseData = databaseList.Where(c => c.HokenId == hokenId && c.HokenGrp == hokenGrp).ToList();

            var date = CIUtil.GetJapanDateTimeNow();

            var confirmDates = Mapper.Map<ConfirmDateModel, PtHokenCheck>(savingList.Where(c => c.ConfirmDate > 0), (srcCf, destCf) =>
            {
                destCf.HpId = hpId;
                destCf.PtID = ptId;
                destCf.HokenGrp = hokenGrp;
                destCf.HokenId = hokenId;
                destCf.CheckDate = DateTime.SpecifyKind(CIUtil.IntToDate(srcCf.ConfirmDate), DateTimeKind.Utc);
                destCf.CheckId = actUserId;
                destCf.CheckCmt = srcCf.CheckComment;
                destCf.CreateId = actUserId;
                destCf.CreateDate = date;
                destCf.UpdateDate = date;
                destCf.UpdateId = actUserId;
                destCf.OnlineConfirmationId = srcCf.OnlineConfirmationId;
                return destCf;
            }).DistinctBy(e => e.CheckDate).ToList();

            foreach (var createItem in confirmDates)
            {
                if (createItem.SeqNo == 0)
                {
                    if (checkDatabaseData.Any(item => item.CheckDate.ToString("yyyyMMdd") == createItem.CheckDate.ToString("yyyyMMdd")))
                    {
                        continue;
                    }
                    createItem.OnlineConfirmationId = onlineConfirmationId;
                    TrackingDataContext.PtHokenChecks.Add(createItem);
                }
                else
                {
                    var checkDateDb = checkDatabaseData.FirstOrDefault(e => e.SeqNo == createItem.SeqNo);
                    if (checkDateDb != null)
                    {
                        checkDateDb.OnlineConfirmationId = onlineConfirmationId;
                        checkDateDb.UpdateDate = date;
                        checkDateDb.UpdateId = actUserId;
                        checkDateDb.CheckId = actUserId;
                    }
                }
            }
        }

        private bool CheckLimitListInfChanged(LimitListInf limitListInf, LimitListModel limitListModel)
        {
            if (limitListInf.IsDeleted != limitListModel.IsDeleted ||
                limitListInf.SortKey != limitListModel.SortKey ||
                limitListInf.FutanGaku != limitListModel.FutanGaku ||
                limitListInf.TotalGaku != limitListModel.TotalGaku ||
                limitListInf.Biko != limitListModel.Biko ||
                limitListInf.SinDate != limitListModel.SinDateY * 10000 + limitListModel.SinDateM * 100 + limitListModel.SinDateD)
            {
                return true;
            }

            return false;
        }

        public bool DeleteHokenInf(int hpId, int userId, long ptId, int seqNo, int hokenId)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
                {
                    using var transaction = TrackingDataContext.Database.BeginTransaction();
                    try
                    {
                        var ptHokenInf = TrackingDataContext.PtHokenInfs.FirstOrDefault(item => item.HpId == hpId && item.SeqNo == seqNo && item.PtId == ptId && item.HokenId == hokenId && item.IsDeleted == DeleteStatus.None);

                        if (ptHokenInf is null)
                        {
                            return false;
                        }

                        ptHokenInf.IsDeleted = DeleteStatus.DeleteFlag;
                        ptHokenInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        ptHokenInf.UpdateId = userId;

                        var listPattern = TrackingDataContext.PtHokenPatterns.Where(item => item.HpId == hpId && item.PtId == ptId && item.HokenId == hokenId && item.IsDeleted == DeleteStatus.None).ToList();
                        foreach (var pattern in listPattern)
                        {
                            pattern.IsDeleted = DeleteStatus.DeleteFlag;
                            pattern.UpdateDate = ptHokenInf.UpdateDate;
                            pattern.UpdateId = ptHokenInf.UpdateId;
                        }

                        var result = TrackingDataContext.SaveChanges() > 0;
                        transaction.Commit();

                        return result;
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                });
        }

        public bool DeleteKohi(int hpId, int userId, long ptId, int seqNo, int hokenId)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    var ptKohi = TrackingDataContext.PtKohis.FirstOrDefault(item => item.HpId == hpId && item.SeqNo == seqNo && item.PtId == ptId && item.HokenId == hokenId && item.IsDeleted == DeleteStatus.None);

                    if (ptKohi is null)
                        return false;

                    ptKohi.IsDeleted = DeleteStatus.DeleteFlag;
                    ptKohi.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    ptKohi.UpdateId = userId;

                    var listPattern = TrackingDataContext.PtHokenPatterns.Where(item => item.HpId == hpId && item.PtId == ptId && item.IsDeleted == DeleteStatus.None && (item.Kohi1Id == hokenId || item.Kohi2Id == hokenId || item.Kohi3Id == hokenId || item.Kohi4Id == hokenId)).ToList();
                    foreach (var pattern in listPattern)
                    {
                        pattern.IsDeleted = DeleteStatus.DeleteFlag;
                        pattern.UpdateDate = ptKohi.UpdateDate;
                        pattern.UpdateId = ptKohi.UpdateId;
                    }

                    var result = TrackingDataContext.SaveChanges() > 0;
                    transaction.Commit();

                    return result;
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    throw;
                }
            });
        }

        public int CheckDeleteHoken(int hpId, long ptId, int hokenId, int kohiId)
        {
            var hokenPattern = NoTrackingDataContext.PtHokenPatterns.Where(e => e.HpId == hpId && e.PtId == ptId && e.IsDeleted == DeleteTypes.None);

            var hokenPIds = new List<int>();
            if (hokenId > 0)
            {
                var hokenIdDb = NoTrackingDataContext.PtHokenInfs.FirstOrDefault(e => e.HokenId == hokenId && e.PtId == ptId && e.IsDeleted == DeleteTypes.None)?.HokenId ?? 0;
                if (hokenIdDb == 0) return MessageDeleteTypes.None;

                hokenPIds = hokenPattern.Where(e => hokenIdDb == e.HokenId).Select(e => e.HokenPid).ToList();
                var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(e => e.HpId == hpId && e.PtId == ptId && hokenPIds.Any(b => b == e.HokenPid) && e.IsDeleted == DeleteTypes.None).Select(e => e.RaiinNo).ToList();
                var odrInf = NoTrackingDataContext.OdrInfs.Any(e => e.HpId == hpId && e.PtId == ptId && raiinInfs.Any(b => b == e.RaiinNo) && e.IsDeleted == DeleteTypes.None);
                if (odrInf) return MessageDeleteTypes.ExistOrder;

                var isByomei = NoTrackingDataContext.PtByomeis.Any(e => e.HpId == hpId && e.PtId == ptId && hokenPIds.Any(b => b == e.HokenPid) && e.IsDeleted == DeleteTypes.None);
                if (isByomei) return MessageDeleteTypes.ExistByomei;
            }

            if (kohiId > 0)
            {
                var ptKohiHokenId = NoTrackingDataContext.PtKohis.FirstOrDefault(e => e.HpId == hpId && e.PtId == ptId && e.HokenId == kohiId && e.IsDeleted == DeleteTypes.None)?.HokenId ?? 0;
                if (ptKohiHokenId == 0) return MessageDeleteTypes.None;

                hokenPIds = hokenPattern.Where(e => ptKohiHokenId == e.Kohi1Id || ptKohiHokenId == e.Kohi2Id || ptKohiHokenId == e.Kohi3Id || ptKohiHokenId == e.Kohi4Id).Select(e => e.HokenPid).ToList();
                var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(e => e.HpId == hpId && e.PtId == ptId && hokenPIds.Any(b => b == e.HokenPid) && e.IsDeleted == DeleteTypes.None).Select(e => e.RaiinNo).ToList();
                var odrInf = NoTrackingDataContext.OdrInfs.Any(e => e.HpId == hpId && e.PtId == ptId && raiinInfs.Any(b => b == e.RaiinNo) && e.IsDeleted == DeleteTypes.None);
                if (odrInf) return MessageDeleteTypes.ExistKohi;
            }

            return MessageDeleteTypes.None;

        }

        public bool IsValidPeriod(int hpId, long ptId, int sinDate, int hokenId)
        {
            var isValidPeriod = NoTrackingDataContext.PtHokenInfs.Any(e => e.HpId == hpId && e.PtId == ptId
                                                                            && e.StartDate <= sinDate && e.EndDate >= sinDate
                                                                            && e.HokenId != hokenId && ((e.HokenKbn == 1 && e.Houbetu != HokenConstant.HOUBETU_NASHI) || e.HokenKbn == 2)
                                                                            && e.IsDeleted == DeleteTypes.None
                                                                            );
            return isValidPeriod;
        }

        public int HasElderHoken(int sinDate, int hpId, long ptId, int ptInfBirthday)
        {
            if (sinDate >= 20080401)
            {

                var listHokenInfs = NoTrackingDataContext.PtHokenInfs.Where(x => x.HpId == hpId && x.PtId == ptId).ToList();
                int age = CIUtil.SDateToAge(ptInfBirthday, sinDate);

                // hoken exist in at least 1 pattern
                var inUsedHokens = listHokenInfs.Where(hoken => hoken.HokenId > 0 && hoken.IsDeleted == 0 && (hoken.StartDate <= sinDate && hoken.EndDate >= sinDate)
                                                            && ((hoken.HokenKbn == 1 && hoken.Houbetu != "0" && hoken.Houbetu != "99")
                                                             || (hoken.HokenKbn == 2 && hoken.Houbetu != "39")));

                var elderHokenQuery = listHokenInfs.Where(hoken => hoken.HokenId > 0 && hoken.IsDeleted == 0 && (hoken.StartDate <= sinDate && hoken.EndDate >= sinDate)
                                                            && (hoken.HokenKbn == 2 && hoken.Houbetu == "39"));

                if (listHokenInfs != null)
                {
                    if (age >= 75 && inUsedHokens.Any()) return 1;

                    else if (age < 65 && elderHokenQuery.Any()) return 2;
                }
            }
            return 0;
        }

        private string FirstCharToUpper(string s)
        {
            if (string.IsNullOrEmpty(s))
            {
                return string.Empty;
            }
            return char.ToUpper(s[0]) + s.Substring(1);
        }

        public (int, List<int>) GetPtHokenInfByHokenPtId(int hpId, long ptId, int hokenPid)
        {
            var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns.FirstOrDefault(p => p.HpId == hpId && p.HokenPid == hokenPid && p.PtId == ptId);
            return ptHokenPatterns == null
                ? (0, new List<int>())
                : (ptHokenPatterns.HokenId, new List<int> { ptHokenPatterns.Kohi1Id, ptHokenPatterns.Kohi2Id, ptHokenPatterns.Kohi3Id, ptHokenPatterns.Kohi4Id });
        }

        public (KohiInfModel? KohiInf, bool IsDifference) HasKohiInfoDifference(int hpId, long ptId, string futansyaNo, string jyukyusyaNo, int? startDate, int? endDate,
            int? selfPayAmount, int? birthDay)
        {
            if (endDate == null || endDate == 0)
            {
                endDate = 99999999;
            }
            if (startDate == null)
            {
                startDate = 0;
            }

            var kohiInf = NoTrackingDataContext.PtKohis.Where(
                x => x.HpId == hpId &&
                     x.PtId == ptId &&
                     (x.FutansyaNo == HenkanJ.ToHalfsize(futansyaNo)
                        || x.FutansyaNo == HenkanJ.ToFullsize(futansyaNo)
                        || x.FutansyaNo == futansyaNo
                        || (x.FutansyaNo ?? string.Empty) == (futansyaNo ?? string.Empty)) &&
                     (x.JyukyusyaNo == HenkanJ.ToHalfsize(jyukyusyaNo)
                        || x.JyukyusyaNo == HenkanJ.ToFullsize(jyukyusyaNo)
                        || x.JyukyusyaNo == jyukyusyaNo
                        || (x.JyukyusyaNo ?? string.Empty) == (jyukyusyaNo ?? string.Empty)) &&
                     x.PrefNo == 0 &&
                     x.HokenNo == 12 &&
                     x.StartDate == startDate &&
                     ((endDate == 99999999 && x.EndDate == 0) || x.EndDate == endDate) &&
                     x.Birthday == birthDay &&
                     x.IsDeleted == DeleteTypes.None
                     ).OrderByDescending(x => x.HokenId).FirstOrDefault();

            var kohiModel = kohiInf != null
                ? new KohiInfModel(
                    kohiInf.HokenNo,
                    kohiInf.HokenId,
                    kohiInf.HokenSbtKbn,
                    kohiInf.Houbetu ?? string.Empty,
                    kohiInf.SeqNo
                )
                : null;

            if (kohiInf is { GendoGaku: 0 } && (selfPayAmount != 0 && selfPayAmount != null))
            {
                var (prefCd, _, _) = GetPtInfByPtId(hpId, kohiInf.PtId);
                var hokenMst = NoTrackingDataContext.HokenMsts.FirstOrDefault(
                    x => x.HokenNo == kohiInf.HokenNo &&
                         x.HokenEdaNo == kohiInf.HokenEdaNo &&
                         x.HpId == hpId &&
                         (x.PrefNo == prefCd || x.PrefNo == 0 || x.IsOtherPrefValid == 1)
                    );

                return (kohiModel, hokenMst?.MonthLimitFutan != selfPayAmount);
            }

            return
                (selfPayAmount != 0 && selfPayAmount != null)
                    ? (kohiModel, kohiInf == null || kohiInf.GendoGaku != selfPayAmount)
                    : (kohiModel, kohiInf == null);
        }

        public HokenInfModel? GetHokenInf(
            int hpId,
            long ptId,
            string hokensyaNo,
            string kigo,
            string bango,
            string? edaNo,
            int? honkeKbn,
            int kofuDate,
            int startDate,
            int? endDate,
            int age,
            int sinDate,
            string? kogakuValue,
            bool isApplicable)
        {
            if (endDate == null || endDate == 0)
            {
                endDate = 99999999;
            }

            var hokenInf = NoTrackingDataContext.PtHokenInfs
                .Where(x =>
                    x.HpId == hpId &&
                    x.PtId == ptId &&
                    (x.HokensyaNo == HenkanJ.ToHalfsize(hokensyaNo)
                        || x.HokensyaNo == HenkanJ.ToFullsize(hokensyaNo)
                        || x.HokensyaNo == hokensyaNo
                        || (x.HokensyaNo ?? string.Empty) == (hokensyaNo ?? string.Empty)) &&
                    (x.Kigo == HenkanJ.ToHalfsize(kigo)
                        || x.Kigo == HenkanJ.ToFullsize(kigo)
                        || x.Kigo == kigo
                        || (x.Kigo ?? string.Empty) == (kigo ?? string.Empty)) &&
                    (x.Bango == HenkanJ.ToHalfsize(bango)
                        || x.Bango == HenkanJ.ToFullsize(bango)
                        || x.Bango == bango
                        || (x.Bango ?? string.Empty) == (bango ?? string.Empty)) &&
                    (string.IsNullOrEmpty(edaNo)
                        || x.EdaNo == edaNo
                        || (x.EdaNo == HenkanJ.ToHalfsize(edaNo)
                        || x.EdaNo == HenkanJ.ToFullsize(edaNo))) &&
                    (honkeKbn == null || x.HonkeKbn == honkeKbn) &&
                    (kofuDate == 0 || x.KofuDate == kofuDate) &&
                    (startDate == 0 || x.StartDate == startDate) &&
                    ((endDate == 99999999 && x.EndDate == 0) || x.EndDate == endDate) &&
                    x.IsDeleted == DeleteStatus.None &&
                    (isApplicable ? x.HokenNo == 68 && x.HokenEdaNo == 0 : x.HokenNo != 68 || x.HokenEdaNo != 0)
                ).AsEnumerable()
                .Where(x => GetKogakuValue(
                    age,
                    x.HokensyaNo ?? string.Empty,
                    sinDate,
                    x.KogakuKbn.ToString()
                    ) == kogakuValue)
                .MaxBy(x => x.HokenId);

            return hokenInf == null ? null : new HokenInfModel(hokenInf.PtId, hokenInf.HokenId, hokenInf.SeqNo, hokenInf.HokenNo, hokenInf.HokenEdaNo, hokenInf.HokenKbn, hokenInf.HokensyaNo ?? string.Empty, hokenInf.Kigo ?? string.Empty, hokenInf.Bango ?? string.Empty, hokenInf.EdaNo ?? string.Empty, hokenInf.HonkeKbn, hokenInf.HokensyaName ?? string.Empty, hokenInf.InsuredName ?? string.Empty, hokenInf.SikakuDate, hokenInf.KofuDate, hokenInf.StartDate, hokenInf.EndDate, hokenInf.KogakuKbn, hokenInf.TasukaiYm, hokenInf.TokureiYm1, hokenInf.TokureiYm2, hokenInf.GenmenKbn, hokenInf.GenmenRate, hokenInf.GenmenGaku, hokenInf.SyokumuKbn, hokenInf.KeizokuKbn, hokenInf.Tokki1 ?? string.Empty, hokenInf.Tokki2 ?? string.Empty, hokenInf.Tokki3 ?? string.Empty, hokenInf.Tokki4 ?? string.Empty, hokenInf.Tokki5 ?? string.Empty, hokenInf.RousaiKofuNo ?? string.Empty, hokenInf.RousaiRoudouCd ?? string.Empty, hokenInf.RousaiSaigaiKbn, hokenInf.RousaiKantokuCd ?? string.Empty, hokenInf.RousaiSyobyoDate, hokenInf.RyoyoStartDate, hokenInf.RyoyoEndDate, hokenInf.RousaiSyobyoCd ?? string.Empty, hokenInf.RousaiJigyosyoName ?? string.Empty, hokenInf.RousaiPrefName ?? string.Empty, hokenInf.RousaiCityName ?? string.Empty, hokenInf.RousaiReceCount, new(), hokenInf.JibaiHokenName ?? string.Empty, hokenInf.JibaiHokenTanto ?? string.Empty, hokenInf.JibaiHokenTel ?? string.Empty, hokenInf.JibaiJyusyouDate, new(), new(), hokenInf.Houbetu ?? string.Empty);
        }

        public bool HasMaruchoDifference(long ptId, int hpId, int prefNo, int hokenNo, int hokenEdaNo, int? startDate, int? endDate)
        {
            if (endDate == null || endDate == 0)
            {
                endDate = 99999999;
            }
            if (startDate == null)
            {
                startDate = 0;
            }

            return !NoTrackingDataContext.PtKohis.Any(x =>
                x.PtId == ptId &&
                x.HpId == hpId &&
                x.PrefNo == prefNo &&
                x.HokenNo == hokenNo &&
                x.HokenEdaNo == hokenEdaNo &&
                x.StartDate == startDate &&
                ((endDate == 99999999 && x.EndDate == 0) || x.EndDate == endDate) &&
                x.IsDeleted == DeleteStatus.None
            );
        }

        public string GetKogakuValue(int age, string hokensyaNo, int sindate, string? kogakuKbn)
        {
            bool isOver70 = age >= 70;
            bool isSpecialInsurance = hokensyaNo.Length == 8 && HenkanJ.ToHalfsize(hokensyaNo).StartsWith("39");
            if (kogakuKbn == "0")
            {
                return string.Empty;
            }

            if (isOver70 || isSpecialInsurance)
            {
                if (sindate < 20180801)
                {
                    return "3 上位";
                }
                return kogakuKbn switch
                {
                    "4" => "4 低所Ⅱ",
                    "5" => "5 低所Ⅰ",
                    _ when sindate < 20090101 => "6 特定収入",
                    "26" => "26 現役Ⅲ",
                    "27" => "27 現役Ⅱ",
                    "28" => "28 現役Ⅰ",
                    _ when isSpecialInsurance => "41 一般Ⅱ",
                    "3" => "3 上位",
                    "6" => "6 特定収入",
                    "17" => "17 上位[A]",
                    "18" => "18 一般[B]",
                    "19" => "19 低所[C]",
                    "29" => "29 区エ",
                    "30" => "30 区オ",
                    "41" => "41 一般Ⅱ",
                    _ => kogakuKbn?.ToString() ?? string.Empty

                };
            }
            else
            {
                if (sindate < 20150101)
                {
                    return kogakuKbn switch
                    {
                        "17" => "17 上位[A]",
                        "18" => "18 一般[B]",
                        "19" => "19 低所[C]",
                        _ => kogakuKbn?.ToString() ?? string.Empty
                    };
                }
                return kogakuKbn switch
                {
                    "3" => "3 上位",
                    "4" => "4 低所Ⅱ",
                    "5" => "5 低所Ⅰ",
                    "6" => "6 特定収入",
                    "17" => "17 上位[A]",
                    "18" => "18 一般[B]",
                    "19" => "19 低所[C]",
                    "26" => "26 区ア",
                    "27" => "27 区イ",
                    "28" => "28 区ウ",
                    "29" => "29 区エ",
                    "30" => "30 区オ",
                    "41" => "41 一般Ⅱ",
                    _ => kogakuKbn?.ToString() ?? string.Empty
                };
            }
        }

        public void DeleteKeyInsuranceList(int hpId, long ptId)
        {
            var key = GetCacheKey() + CacheKeyConstant.InsuranceList + "-" + hpId + "-" + ptId;
            if (_cache.KeyExists(key))
            {
                _cache.KeyDelete(key);
            }
        }
    }
}
