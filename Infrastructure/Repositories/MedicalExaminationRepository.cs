﻿
using Domain.Constant;
using Domain.Models.AuditLog;
using Domain.Models.CommonModel;
using Domain.Models.Diseases;
using Domain.Models.Ka;
using Domain.Models.Medical;
using Domain.Models.MedicalExamination;
using Domain.Models.MstItem;
using Domain.Models.OrdInfDetails;
using Domain.Models.OrdInfs;
using Domain.Models.SystemConf;
using Domain.Types;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Text;
using static Helper.Constants.OrderInfConst;

namespace Infrastructure.Repositories
{
    public class MedicalExaminationRepository : RepositoryBase, IMedicalExaminationRepository
    {
        private readonly ISystemConfRepository _systemConf;
        private readonly IMstItemRepository _mstItemRepository;
        private readonly ICommonRepository _commonRepository;

        public MedicalExaminationRepository(ITenantProvider tenantProvider, ISystemConfRepository systemConf, IMstItemRepository mstItemRepository, ICommonRepository commonRepository) : base(tenantProvider)
        {
            _systemConf = systemConf;
            _mstItemRepository = mstItemRepository;
            _commonRepository = commonRepository;
        }

        public List<CheckedOrderModel> IgakuTokusitu(int hpId, long raiinNo, int sinDate, int hokenId, int syosaisinKbn, List<PtDiseaseModel> ByomeiModelList, List<OrdInfDetailModel> allOdrInfDetail, bool isJouhou)
        {
            List<CheckedOrderModel> checkedOrderModelList = new();
            var igakuTokusituItem = allOdrInfDetail.FirstOrDefault(detail => detail.ItemCd == ItemCdConst.IgakuTokusitu || detail.ItemCd == ItemCdConst.IgakuTokusitu1);
            var reserveDetailId = NoTrackingDataContext.RaiinInfs.FirstOrDefault(x => x.HpId == hpId && x.RaiinNo == raiinNo)?.ReserveDetailId;
            var reserveType = NoTrackingDataContext.ReserveDetails.FirstOrDefault(x => x.ReserveDetailId == reserveDetailId)?.ReserveType;

            // 既に入力されている場合は不要
            if (igakuTokusituItem != null)
            {
                return checkedOrderModelList;
            }

            TenMst? tenMstModel = null;
            IpnNameMst? ipnNameMst = null;
            if (isJouhou || reserveType == 1)
            {
                if (sinDate >= 20220401)
                {
                    tenMstModel = FindTenMst(hpId, ItemCdConst.IgakuTokusitu1, sinDate);
                    if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                    {
                        return checkedOrderModelList;
                    }
                    ipnNameMst = NoTrackingDataContext.IpnNameMsts
                        .Where(e => 
                            e.IpnNameCd == tenMstModel.IpnNameCd && 
                            e.StartDate <= sinDate && 
                            e.EndDate >= sinDate &&
                            e.IsDeleted == DeleteTypes.None)
                        .OrderByDescending(e => e.SeqNo)
                        .FirstOrDefault();
                }
                else
                {
                    return checkedOrderModelList;
                }
            }
            else
            {
                tenMstModel = FindTenMst(hpId, ItemCdConst.IgakuTokusitu, sinDate);
                if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                {
                    return checkedOrderModelList;
                }
                ipnNameMst = NoTrackingDataContext.IpnNameMsts
                    .Where(e => 
                        e.IpnNameCd == tenMstModel.IpnNameCd && 
                        e.StartDate <= sinDate && 
                        e.EndDate >= sinDate && 
                        e.IsDeleted == DeleteTypes.None)
                    .OrderByDescending(e => e.SeqNo)
                    .FirstOrDefault();
            }

            // 初診の場合は算定不可
            if (syosaisinKbn == SyosaiConst.Syosin ||
                syosaisinKbn == SyosaiConst.Syosin2 ||
                syosaisinKbn == SyosaiConst.Unspecified)
            {
                return checkedOrderModelList;
            }

            // 電話再診の場合は算定不可
            if (syosaisinKbn == SyosaiConst.SaisinDenwa ||
                syosaisinKbn == SyosaiConst.SaisinDenwa2)
            {
                return checkedOrderModelList;
            }

            // 初診日から1カ月以内は算定不可
            // 背反設定されている場合は不可

            var byomeiCondition = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2002 && p.GrpEdaNo == 4)?.Val ?? 0;
            // 対象疾病の有無
            bool existByoMeiSpecial = ByomeiModelList
                                .Any(b => (byomeiCondition != 1 || b.SyubyoKbn == 1) &&
                                    b.SikkanKbn == SikkanKbnConst.Special &&
                                    (b.HokenPid == hokenId || b.HokenPid == 0) &&
                                    b.StartDate <= sinDate &&
                                    (b.TenkiKbn == TenkiKbnConst.Continued || b.TenkiDate > sinDate));
            if (existByoMeiSpecial)
            {
                var santeiKanren = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 0)?.Val ?? 0;
                bool santei = false;
                if (santeiKanren == 0)
                {
                    santei = false;
                }
                else if (santeiKanren == 1 && syosaisinKbn != SyosaiConst.None)
                {
                    santei = true;
                }
                var checkedContent = FormatSanteiMessage(tenMstModel.Name ?? string.Empty);

                var checkedOrderModel = new CheckedOrderModel(
                    CheckingType.MissingCalculate,
                    santei,
                    checkedContent,
                    tenMstModel.ItemCd,
                    tenMstModel.SinKouiKbn,
                    tenMstModel.Name ?? string.Empty,
                    0,
                    tenMstModel.OdrUnitName ?? string.Empty,
                    tenMstModel.ReceUnitName ?? string.Empty,
                    tenMstModel.IpnNameCd ?? string.Empty,
                    tenMstModel.CnvUnitName ?? string.Empty,
                    tenMstModel.DrugKbn,
                    tenMstModel.MasterSbt ?? string.Empty,
                    tenMstModel.YohoKbn,
                    tenMstModel.BuiKbn,
                    tenMstModel.IsAdopted,
                    tenMstModel.SenteiRyoyoKbn,
                    tenMstModel.CenterCd ?? string.Empty,
                    tenMstModel.YjCd ?? string.Empty,
                    tenMstModel.KohatuKbn,
                    ipnNameMst?.IpnName ?? string.Empty,
                    tenMstModel.IpnNameCd ?? string.Empty,
                    tenMstModel.DefaultVal,
                    tenMstModel.CmtColKeta1,
                    tenMstModel.CmtColKeta2,
                    tenMstModel.CmtColKeta3,
                    tenMstModel.CmtColKeta4,
                    tenMstModel.Kokuji1 ?? string.Empty,
                    tenMstModel.Kokuji2 ?? string.Empty,
                    tenMstModel.CmtCol1,
                    tenMstModel.CmtCol2,
                    tenMstModel.CmtCol3,
                    tenMstModel.CmtCol4,
                    tenMstModel.CenterCd ?? string.Empty,
                    tenMstModel.RousaiKbn,
                    tenMstModel.StartDate
                );

                checkedOrderModelList.Add(checkedOrderModel);

                return checkedOrderModelList;
            }

            bool existByoMeiOther = ByomeiModelList
                            .Any(b => (byomeiCondition != 1 || b.SyubyoKbn == 1) &&
                                b.SikkanKbn == SikkanKbnConst.Other &&
                                (b.HokenPid == hokenId || b.HokenPid == 0) &&
                                b.StartDate <= sinDate &&
                                (b.TenkiKbn == TenkiKbnConst.Continued || b.TenkiDate > sinDate));
            if (existByoMeiOther)
            {
                var checkedContent = FormatSanteiMessage(tenMstModel.Name ?? string.Empty);
                var checkedOrderModel = new CheckedOrderModel(CheckingType.MissingCalculate, false, checkedContent, tenMstModel.ItemCd, tenMstModel.SinKouiKbn, tenMstModel.Name ?? string.Empty, 0);

                checkedOrderModelList.Add(checkedOrderModel);

                return checkedOrderModelList;
            }
            return checkedOrderModelList;
        }

        public List<CheckedOrderModel> IgakuTokusituIsChecked(int hpId, int sinDate, int syosaisinKbn, List<CheckedOrderModel> checkedOrders, List<OrdInfDetailModel> allOdrInfDetail)
        {
            var result = new List<CheckedOrderModel>();
            if (syosaisinKbn == SyosaiConst.None)
            {
                bool containCdKbn = false;
                foreach (var detail in allOdrInfDetail)
                {
                    var tenMstModel = FindTenMst(hpId, detail.ItemCd, sinDate);
                    if (tenMstModel == null) continue;
                    if ((tenMstModel.CdKbn == "C" && tenMstModel.CdKbnno == 1 && tenMstModel.Kokuji2 == "1") || tenMstModel.ItemCd == "@Z")
                    {
                        var santeiKanren = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 0)?.Val ?? 0;
                        if (santeiKanren == 0)
                        {
                            containCdKbn = false;
                        }
                        else
                        {
                            containCdKbn = true;
                        }
                    }
                }
                foreach (var igaku in checkedOrders)
                {
                    if (igaku.ItemCd == ItemCdConst.IgakuTokusitu || igaku.ItemCd == ItemCdConst.IgakuTokusitu1)
                    {
                        igaku.ChangeSantei(containCdKbn);
                        break;
                    }
                }
            }

            result.AddRange(checkedOrders);

            return result;
        }

        public List<CheckedOrderModel> SihifuToku1(int hpId, long ptId, int sinDate, int hokenId, int syosaisinKbn, long raiinNo, long oyaRaiinNo, List<PtDiseaseModel> ByomeiModelList, List<OrdInfDetailModel> allOdrInfDetail, bool isJouhou)
        {
            var checkedOrderModelList = new List<CheckedOrderModel>();
            var sihifu1Item = allOdrInfDetail.FirstOrDefault(detail => detail.ItemCd == ItemCdConst.SiHifuToku1 || detail.ItemCd == ItemCdConst.SiHifuToku1JyohoTusin
                                                                    || detail.ItemCd == ItemCdConst.SiHifuToku2 || detail.ItemCd == ItemCdConst.SiHifuToku2JyohoTusin);


            // 既に入力されている場合は不要
            if (sihifu1Item != null)
            {
                return checkedOrderModelList;
            }

            TenMst? tenMstModel = null;
            IpnNameMst? ipnNameMst = null;
            if (isJouhou)
            {
                if (sinDate >= 20220401)
                {
                    tenMstModel = FindTenMst(hpId, ItemCdConst.SiHifuToku1JyohoTusin, sinDate);
                    if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                    {
                        return checkedOrderModelList;
                    }
                    ipnNameMst = NoTrackingDataContext.IpnNameMsts
                        .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                        .OrderByDescending(e => e.SeqNo)
                        .FirstOrDefault();
                }
                else
                {
                    return checkedOrderModelList;
                }
            }
            else
            {
                tenMstModel = FindTenMst(hpId, ItemCdConst.SiHifuToku1, sinDate);
                if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                {
                    return checkedOrderModelList;
                }
                ipnNameMst = NoTrackingDataContext.IpnNameMsts
                    .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                    .OrderByDescending(e => e.SeqNo)
                    .FirstOrDefault();
            }

            var hifukaSetting = NoTrackingDataContext.SystemGenerationConfs.FirstOrDefault(p => p.HpId == hpId
                    && p.GrpCd == 8001
                    && p.GrpEdaNo == 1
                    && p.StartDate <= sinDate
                    && p.EndDate >= sinDate)?.Val ?? 0;
            //皮膚科標榜
            if (hifukaSetting != 1)
            {
                return checkedOrderModelList;
            }

            // 対象疾患の有無
            bool existByoMeiSkin1 = ByomeiModelList
                          .Any(b => b.SikkanKbn == SikkanKbnConst.Skin1 &&
                              (b.HokenPid == hokenId || b.HokenPid == 0) &&
                              b.StartDate <= sinDate &&
                              (b.TenkiKbn == TenkiKbnConst.Continued || b.TenkiDate > sinDate));
            if (!existByoMeiSkin1)
            {
                return checkedOrderModelList;
            }

            // 初診の場合は算定不可
            if (syosaisinKbn == SyosaiConst.Syosin ||
                syosaisinKbn == SyosaiConst.Syosin2 ||
                syosaisinKbn == SyosaiConst.Unspecified)
            {
                return checkedOrderModelList;
            }

            // 電話再診の場合は算定不可
            if (syosaisinKbn == SyosaiConst.SaisinDenwa ||
                syosaisinKbn == SyosaiConst.SaisinDenwa2)
            {
                return checkedOrderModelList;
            }

            // 算定回数チェック（1日1回）: 同一診療内に
            int odrCountInDay = GetSameVisitOdrCountInDay(hpId, ptId, sinDate, raiinNo, oyaRaiinNo,
                new List<string>() { ItemCdConst.SiHifuToku1, ItemCdConst.SiHifuToku2 });
            if (odrCountInDay > 0)
            {
                return checkedOrderModelList;
            }

            // 算定回数チェック（月2回）
            // 同月診療日以前の診療内に、算定されている場合は不可
            int santeiCount = GetSanteiCountInMonth(hpId, ptId, sinDate, new List<string>() { ItemCdConst.SiHifuToku1, ItemCdConst.SiHifuToku2 });
            //（基本的に、月１ 回に限りだが、処方の内容に変更があった場合は、その都度算定できるため）
            if (santeiCount > 0)
            {
                return checkedOrderModelList;
            }

            // 背反設定されている場合は不可
            var santeiKanren = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 2)?.Val ?? 0;
            bool santei = false;
            if (santeiKanren == 0)
            {
                santei = false;
            }
            else if (santeiKanren == 1 && syosaisinKbn != SyosaiConst.None)
            {
                santei = true;
            }
            var checkedContent = FormatSanteiMessage(tenMstModel.Name ?? string.Empty);
            var checkedOrderModel = new CheckedOrderModel(
                    CheckingType.MissingCalculate,
                    santei,
                    checkedContent,
                    tenMstModel.ItemCd,
                    tenMstModel.SinKouiKbn,
                    tenMstModel.Name ?? string.Empty,
                    0,
                    tenMstModel.OdrUnitName ?? string.Empty,
                    tenMstModel.ReceUnitName ?? string.Empty,
                    tenMstModel.IpnNameCd ?? string.Empty,
                    tenMstModel.CnvUnitName ?? string.Empty,
                    tenMstModel.DrugKbn,
                    tenMstModel.MasterSbt ?? string.Empty,
                    tenMstModel.YohoKbn,
                    tenMstModel.BuiKbn,
                    tenMstModel.IsAdopted,
                    tenMstModel.SenteiRyoyoKbn,
                    tenMstModel.CenterCd ?? string.Empty,
                    tenMstModel.YjCd ?? string.Empty,
                    tenMstModel.KohatuKbn,
                    ipnNameMst?.IpnName ?? string.Empty,
                    tenMstModel.IpnNameCd ?? string.Empty,
                    tenMstModel.DefaultVal,
                    tenMstModel.CmtColKeta1,
                    tenMstModel.CmtColKeta2,
                    tenMstModel.CmtColKeta3,
                    tenMstModel.CmtColKeta4,
                    tenMstModel.Kokuji1 ?? string.Empty,
                    tenMstModel.Kokuji2 ?? string.Empty,
                    tenMstModel.CmtCol1,
                    tenMstModel.CmtCol2,
                    tenMstModel.CmtCol3,
                    tenMstModel.CmtCol4,
                    tenMstModel.CenterCd ?? string.Empty,
                    tenMstModel.RousaiKbn,
                    tenMstModel.StartDate
            );

            checkedOrderModelList.Add(checkedOrderModel);

            return checkedOrderModelList;
        }

        private TenMst FindTenMst(int hpId, string itemCd, int sinDate)
        {
            var entity = NoTrackingDataContext.TenMsts.FirstOrDefault(p =>
                   p.HpId == hpId &&
                   p.StartDate <= sinDate &&
                   p.EndDate >= sinDate &&
                   p.ItemCd == itemCd);

            return entity ?? new TenMst();
        }

        private int GetSameVisitOdrCountInDay(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo, IEnumerable<string> itemCd)
        {
            var raiinInfQuery = NoTrackingDataContext.RaiinInfs
               .Where(s => s.HpId == hpId && s.PtId == ptId && s.SinDate == sinDate && s.OyaRaiinNo == oyaRaiinNo && s.RaiinNo != raiinNo);
            var odrInfQuery = NoTrackingDataContext.OdrInfs
                .Where(o => o.HpId == hpId && o.PtId == ptId && o.RaiinNo == raiinNo && o.IsDeleted == 0);
            var odrInfDetailQuery = NoTrackingDataContext.OdrInfDetails
                .Where(o => o.HpId == hpId && o.PtId == ptId && o.RaiinNo == raiinNo && itemCd.Contains(o.ItemCd));
            var resultQuery = from raiinInf in raiinInfQuery.AsEnumerable()
                              join odrInf in odrInfQuery
                              on new { raiinInf.HpId, raiinInf.PtId, raiinInf.RaiinNo }
                              equals new { odrInf.HpId, odrInf.PtId, odrInf.RaiinNo }
                              join odrInfDetail in odrInfDetailQuery
                              on new { odrInf.HpId, odrInf.PtId, odrInf.RaiinNo, odrInf.RpNo, odrInf.RpEdaNo }
                              equals new { odrInfDetail.HpId, odrInfDetail.PtId, odrInfDetail.RaiinNo, odrInfDetail.RpNo, odrInfDetail.RpEdaNo } into odrDetailList
                              select new
                              {
                                  OdrDetailList = odrDetailList,
                              };
            var firstRecord = resultQuery.FirstOrDefault();
            int result = 0;
            if (firstRecord != null)
            {
                result = firstRecord.OdrDetailList.Count();
            }
            return result;
        }

        private int GetSanteiCountInMonth(int hpId, long ptId, int sinDate, IEnumerable<string> itemCd)
        {
            int sinYM = sinDate / 100;
            int sinDay = sinDate - sinYM * 100;
            var sinKouiCountQuery = NoTrackingDataContext.SinKouiCounts
                .Where(s => s.HpId == hpId && s.PtId == ptId && s.SinYm == sinYM && s.SinDay < sinDay);

            var sinKouiDetailQuery = NoTrackingDataContext.SinKouiDetails
                .Where(s => s.HpId == hpId && s.PtId == ptId && itemCd.Contains(s.ItemCd));

            var resultQuery = from sinKouiCount in sinKouiCountQuery.AsEnumerable()
                              join sinKouiDetail in sinKouiDetailQuery
                              on new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.RpNo, sinKouiCount.SinYm }
                              equals new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.RpNo, sinKouiDetail.SinYm }
                              select new
                              {
                                  SinKouiCount = sinKouiCount,
                              };
            return resultQuery.AsEnumerable().Sum(s => s.SinKouiCount.Count);
        }

        private string FormatSanteiMessage(string santeiItemName)
        {
            return $"\"{santeiItemName}\"を算定できる可能性があります。";

        }

        public List<CheckedOrderModel> SihifuToku2(int hpId, long ptId, int sinDate, int hokenId, int iBirthDay, long raiinNo, int syosaisinKbn, long oyaRaiinNo, List<PtDiseaseModel> byomeiModelList, List<OrdInfDetailModel> allOdrInfDetail, List<int> odrInfs, bool isJouhou)
        {
            var checkedOrderModelList = new List<CheckedOrderModel>();
            var sihifu2Item = allOdrInfDetail.FirstOrDefault(detail => detail.ItemCd == ItemCdConst.SiHifuToku1 || detail.ItemCd == ItemCdConst.SiHifuToku1JyohoTusin
                                                                    || detail.ItemCd == ItemCdConst.SiHifuToku2 || detail.ItemCd == ItemCdConst.SiHifuToku2JyohoTusin);

            // 既に入力されている場合は不要
            if (sihifu2Item != null)
            {
                return checkedOrderModelList;
            }

            var sihifu1Item = checkedOrderModelList.FirstOrDefault(i => i.ItemCd == ItemCdConst.SiHifuToku1);
            if (sihifu1Item != null)
            {
                return checkedOrderModelList;
            }

            TenMst? tenMstModel = null;
            IpnNameMst? ipnNameMst = null;
            if (isJouhou)
            {
                if (sinDate >= 20220401)
                {
                    tenMstModel = FindTenMst(hpId, ItemCdConst.SiHifuToku2JyohoTusin, sinDate);
                    if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                    {
                        return checkedOrderModelList;
                    }
                    ipnNameMst = NoTrackingDataContext.IpnNameMsts
                        .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                        .OrderByDescending(e => e.SeqNo)
                        .FirstOrDefault();
                }
                else
                {
                    return checkedOrderModelList;
                }
            }
            else
            {
                tenMstModel = FindTenMst(hpId, ItemCdConst.SiHifuToku2, sinDate);
                if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                {
                    return checkedOrderModelList;
                }
                ipnNameMst = NoTrackingDataContext.IpnNameMsts
                    .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                    .OrderByDescending(e => e.SeqNo)
                    .FirstOrDefault();
            }

            var hifukaSetting = NoTrackingDataContext.SystemGenerationConfs.FirstOrDefault(p => p.HpId == hpId
                    && p.GrpCd == 8001
                    && p.GrpEdaNo == 1
                    && p.StartDate <= sinDate
                    && p.EndDate >= sinDate)?.Val;
            //皮膚科標榜
            if (hifukaSetting != 1)
            {
                return checkedOrderModelList;
            }

            // 対象疾患の有無
            bool existByoMeiSkin2 = byomeiModelList
                          .Any(b => b.SikkanKbn == SikkanKbnConst.Skin2 &&
                              (b.HokenPid == hokenId || b.HokenPid == 0) &&
                              b.StartDate <= sinDate &&
                              (b.TenkiKbn == TenkiKbnConst.Continued || b.TenkiDate > sinDate)
                               //&&
                               //((!string.IsNullOrEmpty(b.Icd1012013) && b.Icd1012013.StartsWith("L20"))
                               //  || (!string.IsNullOrEmpty(b.Icd1022013) && b.Icd1022013.StartsWith("L20")))
                               );

            if (!existByoMeiSkin2)
            {
                return checkedOrderModelList;
            }

            bool existByoMeiSkin2WithoutL20 = byomeiModelList
                          .Any(b => b.SikkanKbn == SikkanKbnConst.Skin2 &&
                              (b.HokenPid == hokenId || b.HokenPid == 0) &&
                              b.StartDate <= sinDate &&
                              (b.TenkiKbn == TenkiKbnConst.Continued || b.TenkiDate > sinDate)
                               &&
                               !((!string.IsNullOrEmpty(b.Icd1012013) && b.Icd1012013.StartsWith("L20"))
                                 || (!string.IsNullOrEmpty(b.Icd1022013) && b.Icd1022013.StartsWith("L20")))
                               );

            if (!existByoMeiSkin2WithoutL20)
            {
                if (!odrInfs.Contains(23))
                {
                    return checkedOrderModelList;
                }
                int age = CIUtil.SDateToAge(iBirthDay, sinDate);
                if (age < 16)
                {
                    return checkedOrderModelList;
                }
            }

            // 初診の場合は算定不可
            if (syosaisinKbn == SyosaiConst.Syosin ||
                syosaisinKbn == SyosaiConst.Syosin2 ||
                syosaisinKbn == SyosaiConst.Unspecified)
            {
                return checkedOrderModelList;
            }

            // 電話再診の場合は算定不可
            if (syosaisinKbn == SyosaiConst.SaisinDenwa ||
                syosaisinKbn == SyosaiConst.SaisinDenwa2)
            {
                return checkedOrderModelList;
            }

            // 算定回数チェック（1日1回）: 同一診療内に
            int odrCountInDay = GetSameVisitOdrCountInDay(hpId, ptId, sinDate, raiinNo, oyaRaiinNo,
                new List<string>() { ItemCdConst.SiHifuToku1, ItemCdConst.SiHifuToku2 });
            if (odrCountInDay > 0)
            {
                return checkedOrderModelList;
            }

            // 算定回数チェック（月2回）
            // 同月診療日以前の診療内に、算定されている場合は不可
            int santeiCount = GetSanteiCountInMonth(hpId, ptId, sinDate, new List<string>() { ItemCdConst.SiHifuToku1, ItemCdConst.SiHifuToku2 });
            //（基本的に、月１ 回に限りだが、処方の内容に変更があった場合は、その都度算定できるため）
            if (santeiCount > 0)
            {
                return checkedOrderModelList;
            }

            // 背反設定されている場合は不可
            var santeiKanren = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 2)?.Val ?? 0;
            bool santei = false;
            if (santeiKanren == 0)
            {
                santei = false;
            }
            else if (santeiKanren == 1 && syosaisinKbn != SyosaiConst.None)
            {
                santei = true;
            }
            var checkedContent = FormatSanteiMessage(tenMstModel?.Name ?? string.Empty);
            var checkedOrderModel = new CheckedOrderModel(
                    CheckingType.MissingCalculate, 
                    santei, checkedContent, 
                    tenMstModel?.ItemCd ?? string.Empty, 
                    tenMstModel?.SinKouiKbn ?? 0, 
                    tenMstModel?.Name ?? string.Empty, 
                    0,
                    tenMstModel?.OdrUnitName ?? string.Empty,
                    tenMstModel?.ReceUnitName ?? string.Empty,
                    tenMstModel?.IpnNameCd ?? string.Empty,
                    tenMstModel?.CnvUnitName ?? string.Empty,
                    tenMstModel?.DrugKbn ?? 0,
                    tenMstModel?.MasterSbt ?? string.Empty,
                    tenMstModel?.YohoKbn ?? 0,
                    tenMstModel?.BuiKbn ?? 0,
                    tenMstModel?.IsAdopted ?? 0,
                    tenMstModel?.SenteiRyoyoKbn ?? 0,
                    tenMstModel?.CenterCd ?? string.Empty,
                    tenMstModel?.YjCd ?? string.Empty,
                    tenMstModel?.KohatuKbn ?? 0,
                    ipnNameMst?.IpnName ?? string.Empty,
                    tenMstModel?.IpnNameCd ?? string.Empty,
                    tenMstModel?.DefaultVal ?? 0,
                    tenMstModel?.CmtColKeta1 ?? 0,
                    tenMstModel?.CmtColKeta2 ?? 0,
                    tenMstModel?.CmtColKeta3 ?? 0,
                    tenMstModel?.CmtColKeta4 ?? 0,
                    tenMstModel?.Kokuji1 ?? string.Empty,
                    tenMstModel?.Kokuji2 ?? string.Empty,
                    tenMstModel?.CmtCol1 ?? 0,
                    tenMstModel?.CmtCol2 ?? 0,
                    tenMstModel?.CmtCol3 ?? 0,
                    tenMstModel?.CmtCol4 ?? 0,
                    tenMstModel?.CenterCd ?? string.Empty,
                    tenMstModel?.RousaiKbn ?? 0,
                    tenMstModel?.StartDate ?? 0
                );
            checkedOrderModelList.Add(checkedOrderModel);
            return checkedOrderModelList;
        }

        public List<CheckedOrderModel> IgakuTenkan(int hpId, int sinDate, int hokenId, int syosaisinKbn, List<PtDiseaseModel> ByomeiModelList, List<OrdInfDetailModel> allOdrInfDetail, bool isJouhou)
        {
            var checkedOrderModelList = new List<CheckedOrderModel>();
            var igakuTenkanItem = allOdrInfDetail.FirstOrDefault(detail => detail.ItemCd == ItemCdConst.IgakuTenkan || detail.ItemCd == ItemCdConst.IgakuTenkanJyohoTusin);

            // 既に入力されている場合は不要
            if (igakuTenkanItem != null)
            {
                return checkedOrderModelList;
            }

            TenMst? tenMstModel = null;
            IpnNameMst? ipnNameMst = null;
            if (isJouhou)
            {
                tenMstModel = FindTenMst(hpId, ItemCdConst.IgakuTenkanJyohoTusin, sinDate);
                if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                {
                    return checkedOrderModelList;
                }
                ipnNameMst = NoTrackingDataContext.IpnNameMsts
                    .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                    .OrderByDescending(e => e.SeqNo)
                    .FirstOrDefault();
            }
            else
            {
                tenMstModel = FindTenMst(hpId, ItemCdConst.IgakuTenkan, sinDate);
                if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                {
                    return checkedOrderModelList;
                }
                ipnNameMst = NoTrackingDataContext.IpnNameMsts
                    .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                    .OrderByDescending(e => e.SeqNo)
                    .FirstOrDefault();
            }


            // 小児科、神経科、神経内科、精神科、脳神経外科又は心療内科を標榜
            var kaMstList = GetKaMsts(hpId);
            // 09  小児科
            // 03  神経科
            // 04  神経内科
            // 02  精神科
            // 14  脳神経外科
            // 33  心療内科
            bool existReceKaCd = false;
            if (kaMstList.Any(kaMst => new List<string> { "09", "03", "04", "02", "14", "33" }.Contains(kaMst.ReceKaCd)))
            {
                existReceKaCd = true;
            }
            if (!existReceKaCd)
            {
                return checkedOrderModelList;
            }

            // 初診の場合は算定不可
            if (syosaisinKbn == SyosaiConst.Syosin ||
                syosaisinKbn == SyosaiConst.Syosin2 ||
                syosaisinKbn == SyosaiConst.Unspecified)
            {
                return checkedOrderModelList;
            }

            // 電話再診の場合は算定不可
            if (syosaisinKbn == SyosaiConst.SaisinDenwa ||
                syosaisinKbn == SyosaiConst.SaisinDenwa2)
            {
                return checkedOrderModelList;
            }

            // 算定回数チェック（1日1回、月2回）
            // 初診日から1カ月以内は算定不可（休日などの特例なし）
            // 背反設定されている場合は不可

            // 対象疾患の有無
            bool existByoMeiEpilepsy = ByomeiModelList
                                .Any(b => b.SyubyoKbn == 1 &&
                                    b.SikkanKbn == SikkanKbnConst.Epilepsy &&
                                    (b.HokenPid == hokenId || b.HokenPid == 0) &&
                                    b.StartDate <= sinDate &&
                                    (b.TenkiKbn == TenkiKbnConst.Continued || b.TenkiDate > sinDate));
            if (existByoMeiEpilepsy)
            {
                var santeiKanren = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 6)?.Val ?? 0;
                var santei = false;
                if (santeiKanren == 0)
                {
                    santei = false;
                }
                else if (santeiKanren == 1 && syosaisinKbn != SyosaiConst.None)
                {
                    santei = true;
                }
                var checkedContent = FormatSanteiMessage(tenMstModel.Name ?? string.Empty);
                var checkedOrderModel = new CheckedOrderModel(
                    CheckingType.MissingCalculate, 
                    santei, checkedContent, 
                    tenMstModel?.ItemCd ?? string.Empty, 
                    tenMstModel?.SinKouiKbn ?? 0, 
                    tenMstModel?.Name ?? string.Empty, 
                    0,
                    tenMstModel?.OdrUnitName ?? string.Empty,
                    tenMstModel?.ReceUnitName ?? string.Empty,
                    tenMstModel?.IpnNameCd ?? string.Empty,
                    tenMstModel?.CnvUnitName ?? string.Empty,
                    tenMstModel?.DrugKbn ?? 0,
                    tenMstModel?.MasterSbt ?? string.Empty,
                    tenMstModel?.YohoKbn ?? 0,
                    tenMstModel?.BuiKbn ?? 0,
                    tenMstModel?.IsAdopted ?? 0,
                    tenMstModel?.SenteiRyoyoKbn ?? 0,
                    tenMstModel?.CenterCd ?? string.Empty,
                    tenMstModel?.YjCd ?? string.Empty,
                    tenMstModel?.KohatuKbn ?? 0,
                    ipnNameMst?.IpnName ?? string.Empty,
                    tenMstModel?.IpnNameCd ?? string.Empty,
                    tenMstModel?.DefaultVal ?? 0,
                    tenMstModel?.CmtColKeta1 ?? 0,
                    tenMstModel?.CmtColKeta2 ?? 0,
                    tenMstModel?.CmtColKeta3 ?? 0,
                    tenMstModel?.CmtColKeta4 ?? 0,
                    tenMstModel?.Kokuji1 ?? string.Empty,
                    tenMstModel?.Kokuji2 ?? string.Empty,
                    tenMstModel?.CmtCol1 ?? 0,
                    tenMstModel?.CmtCol2 ?? 0,
                    tenMstModel?.CmtCol3 ?? 0,
                    tenMstModel?.CmtCol4 ?? 0,
                    tenMstModel?.CenterCd ?? string.Empty,
                    tenMstModel?.RousaiKbn ?? 0,
                    tenMstModel?.StartDate ?? 0
                    );

                checkedOrderModelList.Add(checkedOrderModel);

                return checkedOrderModelList;
            }

            bool existByoMeiOther = ByomeiModelList
            .Any(b => b.SyubyoKbn == 1 &&
                                b.SikkanKbn == SikkanKbnConst.Other &&
                                (b.HokenPid == hokenId || b.HokenPid == 0) &&
                                b.StartDate <= sinDate &&
                                (b.TenkiKbn == TenkiKbnConst.Continued || b.TenkiDate > sinDate));
            if (existByoMeiOther)
            {
                var checkedOrderModel = new CheckedOrderModel(CheckingType.MissingCalculate, false, FormatSanteiMessage(tenMstModel.Name ?? string.Empty), tenMstModel?.ItemCd ?? string.Empty, tenMstModel?.SinKouiKbn ?? 0, tenMstModel?.Name ?? string.Empty, 0);
                checkedOrderModelList.Add(checkedOrderModel);

                return checkedOrderModelList;
            }

            return checkedOrderModelList;
        }

        private List<KaMstModel> GetKaMsts(int hpId)
        {
            var entities = NoTrackingDataContext.KaMsts.Where(k => k.HpId == hpId && k.IsDeleted == 0).OrderBy(u => u.SortNo).ThenBy(u => u.KaId).ToList();
            List<KaMstModel> results = new();

            entities?.ForEach(entity =>
            {
                results.Add(new KaMstModel(entity.Id, entity.KaId, entity.SortNo, entity.ReceKaCd ?? string.Empty, entity.KaSname ?? string.Empty, entity.KaName ?? string.Empty, entity.YousikiKaCd ?? string.Empty));
            });

            return results;
        }

        public List<CheckedOrderModel> IgakuNanbyo(int hpId, int sinDate, int hokenId, int syosaisinKbn, List<PtDiseaseModel> ByomeiModelList, List<OrdInfDetailModel> allOdrInfDetail, bool isJouhou)
        {
            List<CheckedOrderModel> checkedOrderModelList = new();
            var igakuNanbyoItem = allOdrInfDetail.FirstOrDefault(detail => detail.ItemCd == ItemCdConst.IgakuNanbyo || detail.ItemCd == ItemCdConst.IgakuNanbyoJyohoTusin);

            // 既に入力されている場合は不要
            if (igakuNanbyoItem != null)
            {
                return checkedOrderModelList;
            }

            TenMst? tenMstModel = null;
            IpnNameMst? ipnNameMst = null;
            if (isJouhou)
            {
                if (sinDate >= 20220401)
                {
                    tenMstModel = FindTenMst(hpId, ItemCdConst.IgakuNanbyoJyohoTusin, sinDate);
                    if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                    {
                        return checkedOrderModelList;
                    }
                    ipnNameMst = NoTrackingDataContext.IpnNameMsts
                        .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                        .OrderByDescending(e => e.SeqNo)
                        .FirstOrDefault();
                }
                else
                {
                    return checkedOrderModelList;
                }
            }
            else
            {
                tenMstModel = FindTenMst(hpId, ItemCdConst.IgakuNanbyo, sinDate);
                if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                {
                    return checkedOrderModelList;
                }
                ipnNameMst = NoTrackingDataContext.IpnNameMsts
                    .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                    .OrderByDescending(e => e.SeqNo)
                    .FirstOrDefault();
            }

            // 初診の場合は算定不可
            if (syosaisinKbn == SyosaiConst.Syosin ||
                syosaisinKbn == SyosaiConst.Syosin2 ||
                syosaisinKbn == SyosaiConst.Unspecified)
            {
                return checkedOrderModelList;
            }

            // 電話再診の場合は算定不可
            if (syosaisinKbn == SyosaiConst.SaisinDenwa ||
                syosaisinKbn == SyosaiConst.SaisinDenwa2)
            {
                return checkedOrderModelList;
            }

            // 算定回数チェック（1日1回、月2回）
            // 初診日から1カ月以内は算定不可（休日などの特例なし）
            // 背反設定されている場合は不可

            // 対象疾患の有無
            bool existByoMeiSanteiGai = ByomeiModelList
                            .Any(b => b.SyubyoKbn == 1 &&
                                b.NanbyoCd == NanbyoConst.Gairai &&
                                (b.HokenPid == hokenId || b.HokenPid == 0) &&
                                b.StartDate <= sinDate &&
                                (b.TenkiKbn == TenkiKbnConst.Continued || b.TenkiDate > sinDate));
            if (existByoMeiSanteiGai)
            {
                var santeiKanren = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 7)?.Val;
                var santei = false;
                if (santeiKanren == 0)
                {
                    santei = false;
                }
                else if (santeiKanren == 1 && syosaisinKbn != SyosaiConst.None)
                {
                    santei = true;
                }
                var checkedContent = FormatSanteiMessage(tenMstModel.Name ?? string.Empty);
                var checkedOrderModel = new CheckedOrderModel(
                    CheckingType.MissingCalculate, 
                    santei, checkedContent, 
                    tenMstModel.ItemCd, 
                    tenMstModel.SinKouiKbn, 
                    tenMstModel.Name ?? String.Empty, 
                    0,
                    tenMstModel?.OdrUnitName ?? string.Empty,
                    tenMstModel?.ReceUnitName ?? string.Empty,
                    tenMstModel?.IpnNameCd ?? string.Empty,
                    tenMstModel?.CnvUnitName ?? string.Empty,
                    tenMstModel?.DrugKbn ?? 0,
                    tenMstModel?.MasterSbt ?? string.Empty,
                    tenMstModel?.YohoKbn ?? 0,
                    tenMstModel?.BuiKbn ?? 0,
                    tenMstModel?.IsAdopted ?? 0,
                    tenMstModel?.SenteiRyoyoKbn ?? 0,
                    tenMstModel?.CenterCd ?? string.Empty,
                    tenMstModel?.YjCd ?? string.Empty,
                    tenMstModel?.KohatuKbn ?? 0,
                    ipnNameMst?.IpnName ?? string.Empty,
                    tenMstModel?.IpnNameCd ?? string.Empty,
                    tenMstModel?.DefaultVal ?? 0,
                    tenMstModel?.CmtColKeta1 ?? 0,
                    tenMstModel?.CmtColKeta2 ?? 0,
                    tenMstModel?.CmtColKeta3 ?? 0,
                    tenMstModel?.CmtColKeta4 ?? 0,
                    tenMstModel?.Kokuji1 ?? string.Empty,
                    tenMstModel?.Kokuji2 ?? string.Empty,
                    tenMstModel?.CmtCol1 ?? 0,
                    tenMstModel?.CmtCol2 ?? 0,
                    tenMstModel?.CmtCol3 ?? 0,
                    tenMstModel?.CmtCol4 ?? 0,
                    tenMstModel?.CenterCd ?? string.Empty,
                    tenMstModel?.RousaiKbn ?? 0,
                    tenMstModel?.StartDate ?? 0
                );

                checkedOrderModelList.Add(checkedOrderModel);

                return checkedOrderModelList;
            }

            return checkedOrderModelList;
        }

        public List<CheckedOrderModel> InitPriorityCheckDetail(List<CheckedOrderModel> checkedOrderModelList)
        {
            bool igakuNanbyoChecked = checkedOrderModelList.Any(c => c.ItemCd == ItemCdConst.IgakuNanbyo && c.Santei);
            if (igakuNanbyoChecked)
            {
                var uncheckedList = checkedOrderModelList.FindAll(c => c.ItemCd == ItemCdConst.IgakuTokusitu ||
                c.ItemCd == ItemCdConst.SiHifuToku2 ||
                c.ItemCd == ItemCdConst.IgakuTenkan ||
                c.ItemCd == ItemCdConst.SiHifuToku1);
                foreach (var checkModel in uncheckedList)
                {
                    checkModel.ChangeSantei(false);
                }
            }

            bool igakuTenkanChecked = checkedOrderModelList.Any(c => c.ItemCd == ItemCdConst.IgakuTenkan && c.Santei);
            if (igakuTenkanChecked)
            {
                var uncheckedList = checkedOrderModelList.FindAll(c => c.ItemCd == ItemCdConst.IgakuTokusitu ||
                c.ItemCd == ItemCdConst.SiHifuToku2 ||
                c.ItemCd == ItemCdConst.SiHifuToku1);
                foreach (var checkModel in uncheckedList)
                {
                    checkModel.ChangeSantei(false);
                }
            }

            bool sihifuToku1Checked = checkedOrderModelList.Any(c => c.ItemCd == ItemCdConst.SiHifuToku1 && c.Santei);
            if (sihifuToku1Checked)
            {
                var uncheckedList = checkedOrderModelList.FindAll(c => c.ItemCd == ItemCdConst.IgakuTokusitu ||
                c.ItemCd == ItemCdConst.SiHifuToku2);
                foreach (var checkModel in uncheckedList)
                {
                    checkModel.ChangeSantei(false);
                }
            }

            bool igakuTokusituChecked = checkedOrderModelList.Any(c => c.ItemCd == ItemCdConst.IgakuTokusitu && c.Santei);
            if (igakuTokusituChecked)
            {
                var sihifuToku2 = checkedOrderModelList.FirstOrDefault(c => c.ItemCd == ItemCdConst.SiHifuToku2);
                if (sihifuToku2 != null)
                {
                    sihifuToku2.ChangeSantei(false);
                }
            }

            return checkedOrderModelList;
        }

        public List<CheckedOrderModel> TouyakuTokusyoSyoho(int hpId, int sinDate, int hokenId, List<PtDiseaseModel> ByomeiModelList, List<OrdInfDetailModel> allOdrInfDetail, List<OrdInfModel> allOdrInf)
        {
            var checkedOrderModelList = new List<CheckedOrderModel>();
            OrdInfDetailModel? touyakuTokuSyoSyohoItem = null;
            if (sinDate >= KaiseiDate.d20240601)
            {
                touyakuTokuSyoSyohoItem = allOdrInfDetail.FirstOrDefault(detail => detail.ItemCd == ItemCdConst.TouyakuTokuSyoSyoho ||
                                                                                   detail.ItemCd == ItemCdConst.TouyakuTokuSyoSyohosen);
            }
            else
            {
                touyakuTokuSyoSyohoItem = allOdrInfDetail.FirstOrDefault(detail => detail.ItemCd == ItemCdConst.TouyakuTokuSyo1Syoho
                                                                                || detail.ItemCd == ItemCdConst.TouyakuTokuSyo2Syoho
                                                                                || detail.ItemCd == ItemCdConst.TouyakuTokuSyo1Syohosen
                                                                                || detail.ItemCd == ItemCdConst.TouyakuTokuSyo2Syohosen);
            }

            // 既に入力されている場合は不要
            if (touyakuTokuSyoSyohoItem != null)
            {
                return checkedOrderModelList;
            }

            List<OrdInfModel> checkedOdrList;

            #region < 2024/06/01

            if (sinDate < KaiseiDate.d20240601)
            {
                string itemTokusyoCd2;
                string itemTokusyoCd1;
                int inoutKbn;

                var outDrug = allOdrInf.Where(o => o.IsDrug && o.InoutKbn == 1);
                if (outDrug.Any())
                {
                    // Contains OutDrug
                    checkedOdrList = allOdrInf.Where(o => o.IsDrug).ToList();
                    itemTokusyoCd2 = ItemCdConst.TouyakuTokuSyo2Syohosen;
                    itemTokusyoCd1 = ItemCdConst.TouyakuTokuSyo1Syohosen;
                    inoutKbn = 1;
                }
                else
                {
                    // Contains InDrug only
                    checkedOdrList = allOdrInf.Where(o => o.IsDrug && o.InoutKbn == 0).ToList();
                    if (!checkedOdrList.Any())
                    {
                        return checkedOrderModelList;
                    }
                    itemTokusyoCd2 = ItemCdConst.TouyakuTokuSyo2Syoho;
                    itemTokusyoCd1 = ItemCdConst.TouyakuTokuSyo1Syoho;
                    inoutKbn = 0;
                }

                CheckedOrderModel? checkedOdr = null;
                bool isCheckShuByomeiOnly2 = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2002 && p.GrpEdaNo == 2)?.Val == 1;
                bool isCheckTeikyoByomei2 = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2002 && p.GrpEdaNo == 3)?.Val == 1;
                bool isCheckShuByomeiOnly1 = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2002 && p.GrpEdaNo == 0)?.Val == 1;
                bool isCheckTeikyoByomei1 = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2002 && p.GrpEdaNo == 1)?.Val == 1;

                foreach (var odrInfDetail in checkedOdrList.Select(item => item.OrdInfDetails).ToList())
                {
                    var usageItem = odrInfDetail.FirstOrDefault(d => d.SinKouiKbn == 21);
                    var drugItems = odrInfDetail.Where(d => d.IsDrug);
                    if (usageItem != null && usageItem.Suryo >= 28)
                    {
                        foreach (var drug in drugItems)
                        {
                            var checkedMoreThan28DaysOdr = CheckByoMei(hpId, sinDate, hokenId, isCheckShuByomeiOnly2, isCheckTeikyoByomei2, itemTokusyoCd2, drug.ItemCd, inoutKbn, ByomeiModelList);
                            if (!(checkedMoreThan28DaysOdr.CheckingType == 0 && string.IsNullOrEmpty(checkedMoreThan28DaysOdr.ItemCd) && string.IsNullOrEmpty(checkedMoreThan28DaysOdr.ItemName) && string.IsNullOrEmpty(checkedMoreThan28DaysOdr.CheckingContent)))
                            {
                                // having item with usage day >= 28, just break
                                checkedOrderModelList.Add(checkedMoreThan28DaysOdr);
                                return checkedOrderModelList;
                            }
                        }
                    }
                    // just check item have suryo < 28 one time
                    if (checkedOdr != null) continue;
                    foreach (var drug in drugItems)
                    {
                        checkedOdr = CheckByoMei(hpId, sinDate, hokenId, isCheckShuByomeiOnly1, isCheckTeikyoByomei1, itemTokusyoCd1, drug.ItemCd, inoutKbn, ByomeiModelList);
                        if (!(checkedOdr.CheckingType == 0 && string.IsNullOrEmpty(checkedOdr.ItemCd) && string.IsNullOrEmpty(checkedOdr.ItemName) && string.IsNullOrEmpty(checkedOdr.CheckingContent)))
                        {
                            break;
                        }
                    }
                }
                if (checkedOdr != null && checkedOdr.CheckingType != 0 && !string.IsNullOrEmpty(checkedOdr.CheckingContent))
                {
                    checkedOrderModelList.Add(checkedOdr);
                    return checkedOrderModelList;
                }
            }

            #endregion

            #region >= 2024/06/01

            if (sinDate >= KaiseiDate.d20240601)
            {
                string itemTokusyoCd;
                int inoutKbn;

                var outDrug = allOdrInf.Where(o => o.IsDrug && o.InoutKbn == 1);
                if (outDrug.Any())
                {
                    // Contains OutDrug
                    checkedOdrList = allOdrInf.Where(o => o.IsDrug).ToList();
                    itemTokusyoCd = ItemCdConst.TouyakuTokuSyoSyohosen;
                    inoutKbn = 1;
                }
                else
                {
                    // Contains InDrug only
                    checkedOdrList = allOdrInf.Where(o => o.IsDrug && o.InoutKbn == 0).ToList();
                    if (!checkedOdrList.Any())
                    {
                        return checkedOrderModelList;
                    }
                    itemTokusyoCd = ItemCdConst.TouyakuTokuSyoSyoho;
                    inoutKbn = 0;
                }

                bool isCheckShuByomeiOnly = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2002 && p.GrpEdaNo == 5)?.Val == 1;
                bool isCheckTeikyoByomei = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2002 && p.GrpEdaNo == 6)?.Val == 1;

                foreach (var odrInfDetail in checkedOdrList.Select(item => item.OrdInfDetails).ToList())
                {
                    double refillCount = 1;
                    var refillItem = odrInfDetail.FirstOrDefault(d => d.ItemCd == ItemCdConst.Con_Refill);
                    if (refillItem != null)
                    {
                        refillCount = refillItem.Suryo;
                    }
                    var usageItem = odrInfDetail.FirstOrDefault(d => d.SinKouiKbn == 21);
                    var drugItems = odrInfDetail.Where(d => d.IsDrug);
                    if (usageItem != null && (usageItem.Suryo * refillCount) >= 28)
                    {
                        foreach (var drug in drugItems)
                        {
                            var checkedMoreThan28DaysOdr = CheckByoMei(hpId, sinDate, hokenId, isCheckShuByomeiOnly, isCheckTeikyoByomei, itemTokusyoCd, drug.ItemCd, inoutKbn, ByomeiModelList);
                            if (!(checkedMoreThan28DaysOdr.CheckingType == 0 && string.IsNullOrEmpty(checkedMoreThan28DaysOdr.ItemCd) && string.IsNullOrEmpty(checkedMoreThan28DaysOdr.ItemName) && string.IsNullOrEmpty(checkedMoreThan28DaysOdr.CheckingContent)))
                            {
                                // having item with usage day >= 28, just break
                                checkedOrderModelList.Add(checkedMoreThan28DaysOdr);
                                return checkedOrderModelList;
                            }
                        }
                    }
                }
            }

            #endregion

            // 算定回数チェック（1日1回、月2回）
            // 背反設定されている場合は不可

            return checkedOrderModelList;
        }

        public CheckedOrderModel CheckByoMei(int hpId, int sinDate, int hokenId, bool isCheckShuByomeiOnly, bool isCheckTeikyoByomei, string itemTokusyoCd, string itemCd, int inoutKbn, List<PtDiseaseModel> ByomeiModelList)
        {
            var tenMstModel = FindTenMst(hpId, itemTokusyoCd, sinDate);
            var ipnNameMst = NoTrackingDataContext.IpnNameMsts
                .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                .OrderByDescending(e => e.SeqNo)
                .FirstOrDefault();

            var byoMeiSpecialList = ByomeiModelList
                            .Where(b => (!isCheckShuByomeiOnly || b.SyubyoKbn == 1) &&
                                b.SikkanKbn == SikkanKbnConst.Special &&
                                (b.HokenPid == hokenId || b.HokenPid == 0) &&
                                b.StartDate <= sinDate &&
                                (b.TenkiKbn == TenkiKbnConst.Continued || b.TenkiDate > sinDate))
                            .ToList();
            if (byoMeiSpecialList.Count > 0)
            {
                bool isSantei = false;
                if (isCheckTeikyoByomei)
                {
                    var byomeiCdList = byoMeiSpecialList.Select(b => b.ByomeiCd);
                    var byomeiCdTenkiouList = GetByomeiCdFromTenkiou(hpId, itemCd, true);
                    isSantei = byomeiCdTenkiouList.Any(byomeiCd => byomeiCdList.Contains(byomeiCd));
                }
                else
                {
                    isSantei = true;
                }
                if (isSantei)
                {
                    var santei = false;
                    var touyaku = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 1)?.Val;
                    if (touyaku == 1)
                    {
                        santei = true;
                    }
                    var checkedContent = FormatSanteiMessage(tenMstModel.Name ?? string.Empty);
                    var checkedOrderModel = new CheckedOrderModel(CheckingType.MissingCalculate, santei, checkedContent, tenMstModel?.ItemCd ?? string.Empty, tenMstModel?.SinKouiKbn ?? 0, tenMstModel?.Name ?? string.Empty, inoutKbn);

                    return checkedOrderModel;
                }
            }

            var byoMeiOtherList = ByomeiModelList
                            .Where(b => (!isCheckShuByomeiOnly || b.SyubyoKbn == 1) &&
                                b.SikkanKbn == SikkanKbnConst.Other &&
                                (b.HokenPid == hokenId || b.HokenPid == 0) &&
                                b.StartDate <= sinDate &&
                                (b.TenkiKbn == TenkiKbnConst.Continued || b.TenkiDate > sinDate))
                            .ToList();
            if (byoMeiOtherList.Count > 0)
            {
                bool isSantei = false;
                if (isCheckTeikyoByomei)
                {
                    var byomeiCdList = byoMeiOtherList.Select(b => b.ByomeiCd);
                    var byomeiCdFromTenkiouList = GetByomeiCdFromTenkiou(hpId, itemCd, true);
                    isSantei = byomeiCdFromTenkiouList.Any(byomeiCdFromTenkiou => byomeiCdList.Contains(byomeiCdFromTenkiou));
                }
                else
                {
                    isSantei = true;
                }
                if (isSantei)
                {
                    var checkedContent = FormatSanteiMessage(tenMstModel.Name ?? string.Empty);
                    var checkedOrderModel = new CheckedOrderModel(
                        CheckingType.MissingCalculate, 
                        false, 
                        checkedContent, 
                        tenMstModel?.ItemCd ?? string.Empty, 
                        tenMstModel?.SinKouiKbn ?? 0, 
                        tenMstModel?.Name ?? string.Empty, 
                        inoutKbn,
                        tenMstModel?.OdrUnitName ?? string.Empty,
                        tenMstModel?.ReceUnitName ?? string.Empty,
                        tenMstModel?.IpnNameCd ?? string.Empty,
                        tenMstModel?.CnvUnitName ?? string.Empty,
                        tenMstModel?.DrugKbn ?? 0,
                        tenMstModel?.MasterSbt ?? string.Empty,
                        tenMstModel?.YohoKbn ?? 0,
                        tenMstModel?.BuiKbn ?? 0,
                        tenMstModel?.IsAdopted ?? 0,
                        tenMstModel?.SenteiRyoyoKbn ?? 0,
                        tenMstModel?.CenterCd ?? string.Empty,
                        tenMstModel?.YjCd ?? string.Empty,
                        tenMstModel?.KohatuKbn ?? 0,
                        ipnNameMst?.IpnName ?? string.Empty,
                        tenMstModel?.IpnNameCd ?? string.Empty,
                        tenMstModel?.DefaultVal ?? 0,
                        tenMstModel?.CmtColKeta1 ?? 0,
                        tenMstModel?.CmtColKeta2 ?? 0,
                        tenMstModel?.CmtColKeta3 ?? 0,
                        tenMstModel?.CmtColKeta4 ?? 0,
                        tenMstModel?.Kokuji1 ?? string.Empty,
                        tenMstModel?.Kokuji2 ?? string.Empty,
                        tenMstModel?.CmtCol1 ?? 0,
                        tenMstModel?.CmtCol2 ?? 0,
                        tenMstModel?.CmtCol3 ?? 0,
                        tenMstModel?.CmtCol4 ?? 0,
                        tenMstModel?.CenterCd ?? string.Empty,
                        tenMstModel?.RousaiKbn ?? 0,
                        tenMstModel?.StartDate ?? 0
                    );

                    return checkedOrderModel;
                }
            }
            return new CheckedOrderModel();
        }

        private List<string> GetByomeiCdFromTenkiou(int hpId, string itemCd, bool isFromCheckingView = false)
        {
            List<string> result;
            var teikyoByomeis = NoTrackingDataContext.TekiouByomeiMsts.Where(
                (x) => x.HpId == hpId && x.ItemCd == itemCd && (!isFromCheckingView || x.IsInvalidTokusyo != 1));
            var byomeiMsts = NoTrackingDataContext.ByomeiMsts.Where(
                (x) => x.HpId == hpId);
            var query = from teikyoByomei in teikyoByomeis
                        join byomeiMst in byomeiMsts on
                        teikyoByomei.ByomeiCd equals byomeiMst.ByomeiCd
                        select new
                        {
                            TeikyoByomei = teikyoByomei,
                            ByomeiMst = byomeiMst
                        };
            result = query.AsEnumerable()
                          .Select(x => x.TeikyoByomei.ByomeiCd)
                          .ToList();

            return result;
        }

        public List<CheckedOrderModel> ChikiHokatu(int hpId, long ptId, int userId, int sinDate, int primaryDoctor, int tantoId, List<OrdInfDetailModel> allOdrInfDetail, int syosaisinKbn)
        {
            var checkedOrderModelList = new List<CheckedOrderModel>();
            var tikiHokatu = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 8)?.Val ?? 0;

            var tikiHokatuItem = allOdrInfDetail.FirstOrDefault(detail => detail.ItemCd == ItemCdConst.SaisinTiikiHoukatu1
                                                                        || detail.ItemCd == ItemCdConst.SaisinTiikiHoukatu2
                                                                        || detail.ItemCd == ItemCdConst.SaisinNintiTiikiHoukatu1
                                                                        || detail.ItemCd == ItemCdConst.SaisinNintiTiikiHoukatu2);

            // 既に入力されている場合は不要
            if (tikiHokatuItem != null)
            {
                return checkedOrderModelList;
            }

            // 再診のとき、算定可
            if (syosaisinKbn != SyosaiConst.Saisin)
            {
                return checkedOrderModelList;
            }

            // 地域包括対象疾病の患者である
            var ptSanteiConfList = GetPtCalculationInfById(hpId, ptId, sinDate);
            List<string> santeiItemCds = new List<string>();

            var tiikiSanteiConf = ptSanteiConfList.FirstOrDefault(c => c.Item1 == 3 && c.Item2 == 1);
            if (tiikiSanteiConf != null)
            {
                //SystemSetting.TikiHokatu = 1 --> 加算１
                //                         = 0 --> 加算２
                //                         = 0 --> Default 加算２
                if (tikiHokatu == 1)
                {
                    santeiItemCds.Add(ItemCdConst.SaisinTiikiHoukatu1);
                }
                else
                {
                    santeiItemCds.Add(ItemCdConst.SaisinTiikiHoukatu2);
                }
            }

            var ninTiikiSanteiConf = ptSanteiConfList.FirstOrDefault(c => c.Item1 == 3 && c.Item2 == 2);
            if (ninTiikiSanteiConf != null)
            {
                //SystemSetting.TikiHokatu = 1 --> 加算１
                //                         = 0 --> 加算２
                //                         = 0 --> Default 加算２
                if (tikiHokatu == 1)
                {
                    santeiItemCds.Add(ItemCdConst.SaisinNintiTiikiHoukatu1);
                }
                else
                {
                    santeiItemCds.Add(ItemCdConst.SaisinNintiTiikiHoukatu2);
                }
            }

            if (santeiItemCds.Count == 0)
            {
                return checkedOrderModelList;
            }

            // 主治医の設定がある
            if (primaryDoctor == 0)
            {
                return checkedOrderModelList;
            }
            string userSName = NoTrackingDataContext.UserMsts.FirstOrDefault(u => u.HpId == hpId && u.UserId == userId && (sinDate <= 0 || u.StartDate <= sinDate && u.EndDate >= sinDate))?.Sname ?? string.Empty;
            if (string.IsNullOrEmpty(userSName))
            {
                return checkedOrderModelList;
            }

            if (primaryDoctor != tantoId)
            {
                return checkedOrderModelList;
            }

            var oshinDetails = allOdrInfDetail.Where(d => d.SinKouiKbn == 14);
            foreach (var detail in oshinDetails)
            {
                var tenMst = FindTenMst(hpId, detail.ItemCd, sinDate);
                //往診...TEN_MST.CD_KBN='C' and CD_KBNNO=0 and KOKUJI2=1
                if (tenMst.CdKbn == "C" && tenMst.CdKbnno == 0 && tenMst.Kokuji2 == "1")
                {
                    return checkedOrderModelList;
                }
                //在宅患者訪問診療料（Ⅰ）（Ⅱ）...TEN_MST.CD_KBN='C' and CD_KBNNO=0 and CD_EDANO in (0,2) and KOKUJI2=1
                if (tenMst.CdKbn == "C" && tenMst.CdKbnno == 1 && new List<int> { 0, 2 }.Contains(tenMst.CdEdano) && tenMst.Kokuji2 == "1")
                {
                    return checkedOrderModelList;
                }
            }

            var tikiHokatuJidoSantei = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 4)?.Val ?? 0;
            var ipnNameMsts = NoTrackingDataContext.IpnNameMsts.Where(e => e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None).ToList();
            foreach (var itemCd in santeiItemCds)
            {
                var tenMstModel = FindTenMst(hpId, itemCd, sinDate);
                if (string.IsNullOrEmpty(tenMstModel.ItemCd) && tenMstModel.HpId == 0)
                {
                    continue;
                }
                var ipnNameMst = ipnNameMsts.Where(e => e.IpnNameCd == tenMstModel.IpnNameCd).OrderByDescending(e => e.SeqNo).FirstOrDefault();

                var santei = false;
                if (tikiHokatuJidoSantei == 1)
                {
                    santei = true;
                }
                var checkedContent = FormatSanteiMessage(tenMstModel.Name ?? string.Empty);
                var checkedOrderModel = new CheckedOrderModel(
                    CheckingType.MissingCalculate, 
                    santei, checkedContent, 
                    tenMstModel?.ItemCd ?? string.Empty,
                    tenMstModel?.SinKouiKbn ?? 0, 
                    tenMstModel?.Name ?? string.Empty, 
                    0,
                    tenMstModel?.OdrUnitName ?? string.Empty,
                    tenMstModel?.ReceUnitName ?? string.Empty,
                    tenMstModel?.IpnNameCd ?? string.Empty,
                    tenMstModel?.CnvUnitName ?? string.Empty,
                    tenMstModel?.DrugKbn ?? 0,
                    tenMstModel?.MasterSbt ?? string.Empty,
                    tenMstModel?.YohoKbn ?? 0,
                    tenMstModel?.BuiKbn ?? 0,
                    tenMstModel?.IsAdopted ?? 0,
                    tenMstModel?.SenteiRyoyoKbn ?? 0,
                    tenMstModel?.CenterCd ?? string.Empty,
                    tenMstModel?.YjCd ?? string.Empty,
                    tenMstModel?.KohatuKbn ?? 0,
                    ipnNameMst?.IpnName ?? string.Empty,
                    tenMstModel?.IpnNameCd ?? string.Empty,
                    tenMstModel?.DefaultVal ?? 0,
                    tenMstModel?.CmtColKeta1 ?? 0,
                    tenMstModel?.CmtColKeta2 ?? 0,
                    tenMstModel?.CmtColKeta3 ?? 0,
                    tenMstModel?.CmtColKeta4 ?? 0,
                    tenMstModel?.Kokuji1 ?? string.Empty,
                    tenMstModel?.Kokuji2 ?? string.Empty,
                    tenMstModel?.CmtCol1 ?? 0,
                    tenMstModel?.CmtCol2 ?? 0,
                    tenMstModel?.CmtCol3 ?? 0,
                    tenMstModel?.CmtCol4 ?? 0,
                    tenMstModel?.CenterCd ?? string.Empty,
                    tenMstModel?.RousaiKbn ?? 0,
                    tenMstModel?.StartDate ?? 0
                );

                checkedOrderModelList.Add(checkedOrderModel);
            }

            return checkedOrderModelList;
        }

        private List<Tuple<int, int>> GetPtCalculationInfById(int hpId, long ptId, int sinDate)
        {
            return NoTrackingDataContext.PtSanteiConfs
                .Where(pt =>
                    pt.HpId == hpId && pt.PtId == ptId && pt.StartDate <= sinDate && pt.EndDate >= sinDate && pt.IsDeleted == 0)
                .AsEnumerable()
                .Select(item => new Tuple<int, int>(item.KbnNo, item.EdaNo)).ToList();
        }

        public List<CheckedOrderModel> YakkuZai(int hpId, long ptId, int sinDate, int birthDay, List<OrdInfDetailModel> allOdrInfDetail, List<OrdInfModel> allOdrInf)
        {
            var checkedOrderModelList = new List<CheckedOrderModel>();
            var yakkuzaiJoho = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 5)?.Val;

            var yakuzaiItem = allOdrInfDetail.FirstOrDefault(detail => detail.ItemCd == ItemCdConst.YakuzaiJoho);

            // 既に入力されている場合は不要
            if (yakuzaiItem != null)
            {
                return checkedOrderModelList;
            }

            var existInOrder = allOdrInf.Any(odr => odr.IsDrug && odr.InoutKbn == 0);
            if (!existInOrder)
            {
                return checkedOrderModelList;
            }

            /*var shonikaSetting = NoTrackingDataContext.SystemGenerationConfs.FirstOrDefault(p => p.HpId == hpId
                    && p.GrpCd == 8001
                    && p.GrpEdaNo == 0
                    && p.StartDate <= sinDate
                    && p.EndDate >= sinDate)?.Val;*/
            var shonikaSetting = GetHyoboSyounika(hpId);
            // 小児科外来診療料算定あり、3歳未満の場合は不可
            if (shonikaSetting == 1)
            {
                // 小児科外来診療料を算定しない
                var autoSanteiItem = FindAutoSanteiMst(hpId, ItemCdConst.IgakuSyouniGairaiSyosinKofuAri, sinDate);
                if (autoSanteiItem)
                {
                    // 3歳未満の場合は不可
                    int age = CIUtil.SDateToAge(birthDay, sinDate);
                    if (age > 0 && age <= 3)
                    {
                        return checkedOrderModelList;
                    }
                }
            }

            var tenMstModel = FindTenMst(hpId, ItemCdConst.YakuzaiJoho, sinDate);
            if (string.IsNullOrEmpty(tenMstModel.ItemCd))
            {
                return checkedOrderModelList;
            }
            var ipnNameMst = NoTrackingDataContext.IpnNameMsts
                .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                .OrderByDescending(e => e.SeqNo).FirstOrDefault();
            
            var tenMstTeiyoModel = FindTenMst(hpId, ItemCdConst.YakuzaiJohoTeiyo, sinDate);
            if (string.IsNullOrEmpty(tenMstTeiyoModel.ItemCd))
            {
                return checkedOrderModelList;
            }
            var ipnNameMstTeiyo = NoTrackingDataContext.IpnNameMsts
                .Where(e => e.IpnNameCd == tenMstTeiyoModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                .OrderByDescending(e => e.SeqNo).FirstOrDefault();

            bool santei = false;
            if (yakkuzaiJoho == 1 || yakkuzaiJoho == 2)
            {
                santei = true;
            }
            var checkedContent = FormatSanteiMessage(tenMstModel.Name ?? string.Empty);

            var checkedOrderModel = new CheckedOrderModel(
                CheckingType.MissingCalculate, 
                santei, 
                checkedContent, 
                tenMstModel?.ItemCd ?? string.Empty,
                tenMstModel?.SinKouiKbn ?? 0,
                tenMstModel?.Name ?? string.Empty, 
                0,
                tenMstModel?.OdrUnitName ?? string.Empty,
                tenMstModel?.ReceUnitName ?? string.Empty,
                tenMstModel?.IpnNameCd ?? string.Empty,
                tenMstModel?.CnvUnitName ?? string.Empty,
                tenMstModel?.DrugKbn ?? 0,
                tenMstModel?.MasterSbt ?? string.Empty,
                tenMstModel?.YohoKbn ?? 0,
                tenMstModel?.BuiKbn ?? 0,
                tenMstModel?.IsAdopted ?? 0,
                tenMstModel?.SenteiRyoyoKbn ?? 0,
                tenMstModel?.CenterCd ?? string.Empty,
                tenMstModel?.YjCd ?? string.Empty,
                tenMstModel?.KohatuKbn ?? 0,
                ipnNameMst?.IpnName ?? string.Empty,
                tenMstModel?.IpnNameCd ?? string.Empty,
                tenMstModel?.DefaultVal ?? 0,
                tenMstModel?.CmtColKeta1 ?? 0,
                tenMstModel?.CmtColKeta2 ?? 0,
                tenMstModel?.CmtColKeta3 ?? 0,
                tenMstModel?.CmtColKeta4 ?? 0,
                tenMstModel?.Kokuji1 ?? string.Empty,
                tenMstModel?.Kokuji2 ?? string.Empty,
                tenMstModel?.CmtCol1 ?? 0,
                tenMstModel?.CmtCol2 ?? 0,
                tenMstModel?.CmtCol3 ?? 0,
                tenMstModel?.CmtCol4 ?? 0,
                tenMstModel?.CenterCd ?? string.Empty,
                tenMstModel?.RousaiKbn ?? 0,
                tenMstModel?.StartDate ?? 0
            );

            checkedOrderModelList.Add(checkedOrderModel);

            santei = false;
            if (yakkuzaiJoho == 2)
            {
                santei = true;
            }
            checkedContent = FormatSanteiMessage(tenMstTeiyoModel.Name ?? string.Empty);

            var checkedOrderTeiyoModel = new CheckedOrderModel(
                CheckingType.MissingCalculate, 
                santei, 
                checkedContent, 
                tenMstTeiyoModel?.ItemCd ?? string.Empty, 
                tenMstTeiyoModel?.SinKouiKbn ?? 0, 
                tenMstTeiyoModel?.Name ?? string.Empty, 
                0,
                tenMstTeiyoModel?.OdrUnitName ?? string.Empty,
                tenMstTeiyoModel?.ReceUnitName ?? string.Empty,
                tenMstTeiyoModel?.IpnNameCd ?? string.Empty,
                tenMstTeiyoModel?.CnvUnitName ?? string.Empty,
                tenMstTeiyoModel?.DrugKbn ?? 0,
                tenMstTeiyoModel?.MasterSbt ?? string.Empty,
                tenMstTeiyoModel?.YohoKbn ?? 0,
                tenMstTeiyoModel?.BuiKbn ?? 0,
                tenMstTeiyoModel?.IsAdopted ?? 0,
                tenMstTeiyoModel?.SenteiRyoyoKbn ?? 0,
                tenMstTeiyoModel?.CenterCd ?? string.Empty,
                tenMstTeiyoModel?.YjCd ?? string.Empty,
                tenMstTeiyoModel?.KohatuKbn ?? 0,
                ipnNameMstTeiyo?.IpnName ?? string.Empty,
                tenMstTeiyoModel?.IpnNameCd ?? string.Empty,
                tenMstTeiyoModel?.DefaultVal ?? 0,
                tenMstTeiyoModel?.CmtColKeta1 ?? 0,
                tenMstTeiyoModel?.CmtColKeta2 ?? 0,
                tenMstTeiyoModel?.CmtColKeta3 ?? 0,
                tenMstTeiyoModel?.CmtColKeta4 ?? 0,
                tenMstTeiyoModel?.Kokuji1 ?? string.Empty,
                tenMstTeiyoModel?.Kokuji2 ?? string.Empty,
                tenMstTeiyoModel?.CmtCol1 ?? 0,
                tenMstTeiyoModel?.CmtCol2 ?? 0,
                tenMstTeiyoModel?.CmtCol3 ?? 0,
                tenMstTeiyoModel?.CmtCol4 ?? 0,
                tenMstTeiyoModel?.CenterCd ?? string.Empty,
                tenMstTeiyoModel?.RousaiKbn ?? 0,
                tenMstTeiyoModel?.StartDate ?? 0
            );
            checkedOrderModelList.Add(checkedOrderTeiyoModel);

            // 当月すでに1回以上算定されている場合、チェックOFFで表示する
            int santeiCount = GetSanteiCountInMonth(hpId, ptId, sinDate, new List<string>() { ItemCdConst.YakuzaiJoho, ItemCdConst.YakuzaiJohoTeiyo });
            //（基本的に、月１ 回に限りだが、処方の内容に変更があった場合は、その都度算定できるため）

            var result = new List<CheckedOrderModel>();
            if (santeiCount >= 1)
            {
                foreach (var checkModel in checkedOrderModelList)
                {
                    result.Add(checkModel.ChangeSantei(false));
                }
            }
            else
            {
                result.AddRange(checkedOrderModelList);
            }

            return result;
        }

        private bool FindAutoSanteiMst(int hpId, string itemCd, int sinDate)
        {
            var check = NoTrackingDataContext.AutoSanteiMsts.Any(e =>
                 e.HpId == hpId &&
                 e.ItemCd == itemCd &&
                 e.StartDate <= sinDate &&
                 e.EndDate >= sinDate);

            return check;
        }

        public List<CheckedOrderModel> SiIkuji(int hpId, int sinDate, int birthDay, List<OrdInfDetailModel> allOdrInfDetail, bool isJouhou, int syosaisinKbn)
        {
            var checkedOrderModelList = new List<CheckedOrderModel>();

            var siIkujiItem = allOdrInfDetail.FirstOrDefault(detail => detail.ItemCd == ItemCdConst.SiIkuji || detail.ItemCd == ItemCdConst.SiIkujiJyohoTusin);

            // 既に入力されている場合は不要
            if (siIkujiItem != null)
            {
                return checkedOrderModelList;
            }

            TenMst? tenMstModel = null;
            IpnNameMst? ipnNameMst = null;
            if (isJouhou)
            {
                if (sinDate >= 20220401)
                {
                    tenMstModel = FindTenMst(hpId, ItemCdConst.SiIkujiJyohoTusin, sinDate);
                    if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                    {
                        return checkedOrderModelList;
                    }
                    ipnNameMst = NoTrackingDataContext.IpnNameMsts
                        .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                        .OrderByDescending(e => e.SeqNo).FirstOrDefault();
                }
                else
                {
                    return checkedOrderModelList;
                }
            }
            else
            {
                tenMstModel = FindTenMst(hpId, ItemCdConst.SiIkuji, sinDate);
                if (string.IsNullOrEmpty(tenMstModel.ItemCd))
                {
                    return checkedOrderModelList;
                }
                ipnNameMst = NoTrackingDataContext.IpnNameMsts
                    .Where(e => e.IpnNameCd == tenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                    .OrderByDescending(e => e.SeqNo).FirstOrDefault();
            }

            /*var shonika = NoTrackingDataContext.SystemGenerationConfs.FirstOrDefault(p => p.HpId == hpId
                    && p.GrpCd == 8001
                    && p.GrpEdaNo == 0
                    && p.StartDate <= sinDate
                    && p.EndDate >= sinDate)?.Val ?? 0;*/
            var shonika = GetHyoboSyounika(hpId);
            // 小児科外来診療料算定あり、3歳未満の場合は不可
            if (shonika != 1)
            {
                // 小児科外来診療料算定しない
                return checkedOrderModelList;
            }
            var autoSanteiItem = FindAutoSanteiMst(hpId, ItemCdConst.IgakuSyouniGairaiSyosinKofuAri, sinDate);
            if (autoSanteiItem)
            {
                // 自動算定されるため、算定不可
                return checkedOrderModelList;
            }

            // ３歳未満の乳幼児
            int age = CIUtil.SDateToAge(birthDay, sinDate);
            if (age > 3)
            {
                return checkedOrderModelList;
            }

            // 初診時
            // 初診以上の場合は算定不可
            if (syosaisinKbn != SyosaiConst.Syosin &&
                syosaisinKbn != SyosaiConst.Syosin2)
            {
                return checkedOrderModelList;
            }

            // 背反設定されている場合は不可
            var checkedContent = FormatSanteiMessage(tenMstModel.Name ?? string.Empty);
            var checkedOrderModel = new CheckedOrderModel(
                CheckingType.MissingCalculate, 
                true, checkedContent, 
                tenMstModel?.ItemCd ?? string.Empty, 
                tenMstModel?.SinKouiKbn ?? 0, 
                tenMstModel?.Name ?? string.Empty, 
                0,
                tenMstModel?.OdrUnitName ?? string.Empty,
                tenMstModel?.ReceUnitName ?? string.Empty,
                tenMstModel?.IpnNameCd ?? string.Empty,
                tenMstModel?.CnvUnitName ?? string.Empty,
                tenMstModel?.DrugKbn ?? 0,
                tenMstModel?.MasterSbt ?? string.Empty,
                tenMstModel?.YohoKbn ?? 0,
                tenMstModel?.BuiKbn ?? 0,
                tenMstModel?.IsAdopted ?? 0,
                tenMstModel?.SenteiRyoyoKbn ?? 0,
                tenMstModel?.CenterCd ?? string.Empty,
                tenMstModel?.YjCd ?? string.Empty,
                tenMstModel?.KohatuKbn ?? 0,
                ipnNameMst?.IpnName ?? string.Empty,
                tenMstModel?.IpnNameCd ?? string.Empty,
                tenMstModel?.DefaultVal ?? 0,
                tenMstModel?.CmtColKeta1 ?? 0,
                tenMstModel?.CmtColKeta2 ?? 0,
                tenMstModel?.CmtColKeta3 ?? 0,
                tenMstModel?.CmtColKeta4 ?? 0,
                tenMstModel?.Kokuji1 ?? string.Empty,
                tenMstModel?.Kokuji2 ?? string.Empty,
                tenMstModel?.CmtCol1 ?? 0,
                tenMstModel?.CmtCol2 ?? 0,
                tenMstModel?.CmtCol3 ?? 0,
                tenMstModel?.CmtCol4 ?? 0,
                tenMstModel?.CenterCd ?? string.Empty,
                tenMstModel?.RousaiKbn ?? 0,
                tenMstModel?.StartDate ?? 0
            );

            checkedOrderModelList.Add(checkedOrderModel);

            return checkedOrderModelList;
        }

        public List<CheckedOrderModel> Zanyaku(int hpId, int sinDate, List<OrdInfDetailModel> allOdrInfDetail, List<OrdInfModel> allOrderInf)
        {
            var checkedOrderModelList = new List<CheckedOrderModel>();

            var existZanyakuItem = allOdrInfDetail.Any(detail => detail.ItemCd == ItemCdConst.ZanGigi || detail.ItemCd == ItemCdConst.ZanTeiKyo);

            // 既に入力されている場合は不要
            if (existZanyakuItem)
            {
                return checkedOrderModelList;
            }

            //院外処方オーダーがあるかチェック（算定外、自費含む）
            var existOutOrder = allOrderInf.Any(odr => odr.IsDrug && odr.InoutKbn == 1);
            if (!existOutOrder)
            {
                return checkedOrderModelList;
            }

            var zanyakuSetting = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2012 && p.GrpEdaNo == 0)?.Val;

            var zangigiTenMstModel = _mstItemRepository.GetTenMstInfo(hpId, ItemCdConst.ZanGigi, sinDate);
            if (zangigiTenMstModel != null)
            {
                var santei = zanyakuSetting == 1;
                var checkedContent = $"\"{zangigiTenMstModel.Name}\"を指示する。";
                var ipnNameMstSetting = NoTrackingDataContext.IpnNameMsts.Where(e => e.IpnNameCd == zangigiTenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                    .OrderByDescending(e => e.SeqNo).FirstOrDefault();

                var checkedOrderModelZangigi = new CheckedOrderModel(
                    CheckingType.Order, 
                    santei, 
                    checkedContent, 
                    zangigiTenMstModel?.ItemCd ?? string.Empty, 
                    zangigiTenMstModel?.SinKouiKbn ?? 0,
                    zangigiTenMstModel?.Name ?? string.Empty, 
                    1,
                    zangigiTenMstModel?.OdrUnitName ?? string.Empty,
                    zangigiTenMstModel?.ReceUnitName ?? string.Empty,
                    zangigiTenMstModel?.IpnNameCd ?? string.Empty,
                    zangigiTenMstModel?.CnvUnitName ?? string.Empty,
                    zangigiTenMstModel?.DrugKbn ?? 0,
                    zangigiTenMstModel?.MasterSbt ?? string.Empty,
                    zangigiTenMstModel?.YohoKbn ?? 0,
                    zangigiTenMstModel?.BuiKbn ?? 0,
                    zangigiTenMstModel?.IsAdopted ?? 0,
                    zangigiTenMstModel?.SenteiRyoyoKbn ?? 0,
                    zangigiTenMstModel?.CenterCd ?? string.Empty,
                    zangigiTenMstModel?.YjCd ?? string.Empty,
                    zangigiTenMstModel?.KohatuKbn ?? 0,
                    ipnNameMstSetting?.IpnName ?? string.Empty,
                    zangigiTenMstModel?.IpnNameCd ?? string.Empty,
                    zangigiTenMstModel?.DefaultValue ?? 0,
                    zangigiTenMstModel?.CmtColKeta1 ?? 0,
                    zangigiTenMstModel?.CmtColKeta2 ?? 0,
                    zangigiTenMstModel?.CmtColKeta3 ?? 0,
                    zangigiTenMstModel?.CmtColKeta4 ?? 0,
                    zangigiTenMstModel?.Kokuji1 ?? string.Empty,
                    zangigiTenMstModel?.Kokuji2 ?? string.Empty,
                    zangigiTenMstModel?.CmtCol1 ?? 0,
                    zangigiTenMstModel?.CmtCol2 ?? 0,
                    zangigiTenMstModel?.CmtCol3 ?? 0,
                    zangigiTenMstModel?.CmtCol4 ?? 0,
                    zangigiTenMstModel?.CenterCd ?? string.Empty,
                    zangigiTenMstModel?.RousaiKbn ?? 0,
                    zangigiTenMstModel?.StartDate ?? 0
                    );

                checkedOrderModelList.Add(checkedOrderModelZangigi);
            }

            var zanteikyotenMstModel = _mstItemRepository.GetTenMstInfo(hpId, ItemCdConst.ZanTeiKyo, sinDate);
            if (zanteikyotenMstModel != null)
            {
                var ipnNameMst = NoTrackingDataContext.IpnNameMsts.Where(e => e.IpnNameCd == zanteikyotenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                    .OrderByDescending(e => e.SeqNo).FirstOrDefault();

                var santei = zanyakuSetting == 2;
                var checkedContent = $"\"{zanteikyotenMstModel.Name}\"を指示する。";
                var checkedOrderModelZanTeikyo = new CheckedOrderModel(
                    CheckingType.Order, 
                    santei, 
                    checkedContent, 
                    zanteikyotenMstModel?.ItemCd ?? string.Empty, 
                    zanteikyotenMstModel?.SinKouiKbn ?? 0, 
                    zanteikyotenMstModel?.Name ?? string.Empty, 
                    1,
                    zanteikyotenMstModel?.OdrUnitName ?? string.Empty,
                    zanteikyotenMstModel?.ReceUnitName ?? string.Empty,
                    zanteikyotenMstModel?.IpnNameCd ?? string.Empty,
                    zanteikyotenMstModel?.CnvUnitName ?? string.Empty,
                    zanteikyotenMstModel?.DrugKbn ?? 0,
                    zanteikyotenMstModel?.MasterSbt ?? string.Empty,
                    zanteikyotenMstModel?.YohoKbn ?? 0,
                    zanteikyotenMstModel?.BuiKbn ?? 0,
                    zanteikyotenMstModel?.IsAdopted ?? 0,
                    zanteikyotenMstModel?.SenteiRyoyoKbn ?? 0,
                    zanteikyotenMstModel?.CenterCd ?? string.Empty,
                    zanteikyotenMstModel?.YjCd ?? string.Empty,
                    zanteikyotenMstModel?.KohatuKbn ?? 0,
                    ipnNameMst?.IpnName ?? string.Empty,
                    zanteikyotenMstModel?.IpnNameCd ?? string.Empty,
                    zanteikyotenMstModel?.DefaultValue ?? 0,
                    zanteikyotenMstModel?.CmtColKeta1 ?? 0,
                    zanteikyotenMstModel?.CmtColKeta2 ?? 0,
                    zanteikyotenMstModel?.CmtColKeta3 ?? 0,
                    zanteikyotenMstModel?.CmtColKeta4 ?? 0,
                    zanteikyotenMstModel?.Kokuji1 ?? string.Empty,
                    zanteikyotenMstModel?.Kokuji2 ?? string.Empty,
                    zanteikyotenMstModel?.CmtCol1 ?? 0,
                    zanteikyotenMstModel?.CmtCol2 ?? 0,
                    zanteikyotenMstModel?.CmtCol3 ?? 0,
                    zanteikyotenMstModel?.CmtCol4 ?? 0,
                    zanteikyotenMstModel?.CenterCd ?? string.Empty,
                    zanteikyotenMstModel?.RousaiKbn ?? 0,
                    zanteikyotenMstModel?.StartDate ?? 0
                    );

                checkedOrderModelList.Add(checkedOrderModelZanTeikyo);
            }

            return checkedOrderModelList;
        }

        public List<CheckedOrderModel> TrialIryoJyohoKibanCalculation(int hpId, long ptId, int sinDate, long raiinNo, List<OrdInfDetailModel> allOdrInfDetail)
        {
            List<CheckedOrderModel> checkingOrderModelList = new List<CheckedOrderModel>();

            if (sinDate >= KaiseiDate.d20240601)
            {
                return checkingOrderModelList;
            }

            var autoSanteiItem = FindAutoSanteiMst(hpId, ItemCdConst.SyosinIryoJyohoKiban1, sinDate);
            if (!autoSanteiItem)
            {
                return checkingOrderModelList;
            }

            var existAutoItem = allOdrInfDetail.Any(detail => ItemCdConst.IryoJyohoKibanList.Contains(detail.ItemCd));
            if (existAutoItem)
            {
                return checkingOrderModelList;
            }

            bool isExistFirstVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && x.Suryo == 1);
            bool isExistReturnVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && x.Suryo == 3);
            bool isGairaiRiha = CheckGairaiRiha(hpId, ptId, sinDate, raiinNo, allOdrInfDetail);
            if (isExistFirstVisit)
            {
                var firstVisitDevelopmentSystemEnhanceAdd1TenMstModel = _mstItemRepository.GetTenMstInfo(hpId, ItemCdConst.SyosinIryoJyohoKiban1, sinDate);
                if (firstVisitDevelopmentSystemEnhanceAdd1TenMstModel != null)
                {
                    var ipnNameMstFirstVisit = NoTrackingDataContext.IpnNameMsts
                        .Where(e => e.IpnNameCd == firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                        .OrderByDescending(e => e.SeqNo).FirstOrDefault();

                    CheckedOrderModel checkingOrderModel = new CheckedOrderModel(
                        CheckingType.MissingCalculate,
                        santei: true,
                        checkingContent: FormatSanteiMessage(firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.Name ?? string.Empty),
                        itemCd: firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.ItemCd,
                        sinKouiKbn: firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.SinKouiKbn,
                        itemName: firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.Name ?? string.Empty,
                        inOutKbn: 0,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.OdrUnitName ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.ReceUnitName ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.IpnNameCd ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CnvUnitName ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.DrugKbn,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.MasterSbt ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.YohoKbn,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.BuiKbn,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.IsAdopted,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.SenteiRyoyoKbn,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CenterCd ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.YjCd ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.KohatuKbn,
                        ipnNameMstFirstVisit?.IpnName ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.IpnNameCd ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.DefaultValue,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CmtColKeta1,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CmtColKeta2,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CmtColKeta3,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CmtColKeta4,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.Kokuji1 ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.Kokuji2 ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CmtCol1,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CmtCol2,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CmtCol3,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CmtCol4,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.CenterCd ?? string.Empty,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.RousaiKbn,
                        firstVisitDevelopmentSystemEnhanceAdd1TenMstModel.StartDate
                        );

                    checkingOrderModelList.Add(checkingOrderModel);
                }

                var medicalDevelopmentSystemEnhanceAdd1TenMstModel = _mstItemRepository.GetTenMstInfo(hpId, ItemCdConst.IgakuIryoJyohoKiban1, sinDate);
                if (medicalDevelopmentSystemEnhanceAdd1TenMstModel != null)
                {
                    var ipnNameMstMedical = NoTrackingDataContext.IpnNameMsts
                        .Where(e => e.IpnNameCd == medicalDevelopmentSystemEnhanceAdd1TenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                        .OrderByDescending(e => e.SeqNo).FirstOrDefault();

                    CheckedOrderModel checkingOrderModel = new CheckedOrderModel(
                        CheckingType.MissingCalculate,
                        santei: true,
                        checkingContent: FormatSanteiMessage(medicalDevelopmentSystemEnhanceAdd1TenMstModel.Name ?? string.Empty),
                        itemCd: medicalDevelopmentSystemEnhanceAdd1TenMstModel.ItemCd,
                        sinKouiKbn: medicalDevelopmentSystemEnhanceAdd1TenMstModel.SinKouiKbn,
                        itemName: medicalDevelopmentSystemEnhanceAdd1TenMstModel.Name ?? string.Empty,
                        inOutKbn: 0,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.OdrUnitName ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.ReceUnitName ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.IpnNameCd ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CnvUnitName ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.DrugKbn,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.MasterSbt ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.YohoKbn,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.BuiKbn,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.IsAdopted,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.SenteiRyoyoKbn,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CenterCd ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.YjCd ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.KohatuKbn,
                        ipnNameMstMedical?.IpnName ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.IpnNameCd ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.DefaultValue,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CmtColKeta1,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CmtColKeta2,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CmtColKeta3,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CmtColKeta4,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.Kokuji1 ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.Kokuji2 ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CmtCol1,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CmtCol2,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CmtCol3,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CmtCol4,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.CenterCd ?? string.Empty,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.RousaiKbn,
                        medicalDevelopmentSystemEnhanceAdd1TenMstModel.StartDate
                        );

                    checkingOrderModelList.Add(checkingOrderModel);
                }
            }
            else if (isExistReturnVisit || isGairaiRiha)
            {
                var visitDevelopmentSystemEnhanceAdd3TenMstModel = _mstItemRepository.GetTenMstInfo(hpId, ItemCdConst.SaisinIryoJyohoKiban3, sinDate);
                if (visitDevelopmentSystemEnhanceAdd3TenMstModel != null)
                {
                    var ipnNameMstVisitDev = NoTrackingDataContext.IpnNameMsts
                        .Where(e => e.IpnNameCd == visitDevelopmentSystemEnhanceAdd3TenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                        .OrderByDescending(e => e.SeqNo).FirstOrDefault();

                    CheckedOrderModel checkingOrderModel = new CheckedOrderModel(
                        CheckingType.MissingCalculate,
                        santei: true,
                        checkingContent: FormatSanteiMessage(visitDevelopmentSystemEnhanceAdd3TenMstModel.Name ?? string.Empty),
                        itemCd: visitDevelopmentSystemEnhanceAdd3TenMstModel.ItemCd,
                        sinKouiKbn: visitDevelopmentSystemEnhanceAdd3TenMstModel.SinKouiKbn,
                        itemName: visitDevelopmentSystemEnhanceAdd3TenMstModel.Name ?? string.Empty,
                        inOutKbn: 0,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.OdrUnitName ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.ReceUnitName ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.IpnNameCd ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CnvUnitName ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.DrugKbn,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.MasterSbt ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.YohoKbn,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.BuiKbn,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.IsAdopted,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.SenteiRyoyoKbn,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CenterCd ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.YjCd ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.KohatuKbn,
                        ipnNameMstVisitDev?.IpnName ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.IpnNameCd ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.DefaultValue,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CmtColKeta1,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CmtColKeta2,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CmtColKeta3,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CmtColKeta4,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.Kokuji1 ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.Kokuji2 ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CmtCol1,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CmtCol2,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CmtCol3,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CmtCol4,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.CenterCd ?? string.Empty,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.RousaiKbn,
                        visitDevelopmentSystemEnhanceAdd3TenMstModel.StartDate
                        );

                    checkingOrderModelList.Add(checkingOrderModel);
                }

                var returnVisitDevelopmentSystemEnhanceAdd3TenMstModel = _mstItemRepository.GetTenMstInfo(hpId, ItemCdConst.IgakuIryoJyohoKiban3, sinDate);
                if (returnVisitDevelopmentSystemEnhanceAdd3TenMstModel != null)
                {
                    var ipnNameMstReturnVisitDev = NoTrackingDataContext.IpnNameMsts
                        .Where(e => e.IpnNameCd == returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                        .OrderByDescending(e => e.SeqNo).FirstOrDefault();

                    CheckedOrderModel checkingOrderModel = new CheckedOrderModel(
                        CheckingType.MissingCalculate,
                        santei: true,
                        checkingContent: FormatSanteiMessage(returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.Name ?? string.Empty),
                        itemCd: returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.ItemCd,
                        sinKouiKbn: returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.SinKouiKbn,
                        itemName: returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.Name ?? string.Empty,
                        inOutKbn: 0,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.OdrUnitName ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.ReceUnitName ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.IpnNameCd ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CnvUnitName ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.DrugKbn,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.MasterSbt ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.YohoKbn,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.BuiKbn,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.IsAdopted,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.SenteiRyoyoKbn,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CenterCd ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.YjCd ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.KohatuKbn,
                        ipnNameMstReturnVisitDev?.IpnName ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.IpnNameCd ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.DefaultValue,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CmtColKeta1,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CmtColKeta2,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CmtColKeta3,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CmtColKeta4,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.Kokuji1 ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.Kokuji2 ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CmtCol1,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CmtCol2,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CmtCol3,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CmtCol4,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.CenterCd ?? string.Empty,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.RousaiKbn,
                        returnVisitDevelopmentSystemEnhanceAdd3TenMstModel.StartDate
                        );

                    checkingOrderModelList.Add(checkingOrderModel);
                }
            }
            return checkingOrderModelList;
        }

        private bool CheckGairaiRiha(int hpId, long ptId, int sinDate, long raiinNo, List<OrdInfDetailModel> allOdrInfDetail)
        {
            if (_systemConf.GetSettingValue(2016, 0, hpId) == 0)
            {
                return false;
            }

            if (allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && x.Suryo == 0))
            {
                // 既に存在
                if (allOdrInfDetail.Any(detail => detail.ItemCd == ItemCdConst.IgakuGairaiRiha1
                                                || detail.ItemCd == ItemCdConst.IgakuGairaiRiha2))
                {
                    return true;
                }

                // 外来リハビリテーション診療料１
                int lastDaySanteiRiha1 = GetLastDaySantei(hpId, ptId, sinDate, raiinNo, ItemCdConst.IgakuGairaiRiha1);
                if (lastDaySanteiRiha1 != 0)
                {
                    int tgtDay = CIUtil.SDateInc(lastDaySanteiRiha1, 6);
                    if (lastDaySanteiRiha1 <= sinDate && tgtDay >= sinDate)
                    {
                        return true;
                    }
                }

                // 外来リハビリテーション診療料２
                int lastDaySanteiRiha2 = GetLastDaySantei(hpId, ptId, sinDate, raiinNo, ItemCdConst.IgakuGairaiRiha2);
                if (lastDaySanteiRiha2 != 0)
                {
                    int tgtDay = CIUtil.SDateInc(lastDaySanteiRiha2, 13);
                    if (lastDaySanteiRiha2 <= sinDate && tgtDay >= sinDate)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public List<CheckedOrderModel> IryoDX(int hpId, long ptId, int sinDate, List<OrdInfDetailModel> allOdrInfDetail, int syosaisinKbn)
        {
            List<CheckedOrderModel> checkingOrderModelList = new List<CheckedOrderModel>();

            // 当該診療日が2024 / 06 / 01以降の自動算定に設定されている期間チェック
            if (sinDate < KaiseiDate.d20240601)
            {
                return checkingOrderModelList;
            }

            var listItemCd = new List<string>();
            listItemCd.AddRange(ItemCdConst.syosinIryoDxItemCds(sinDate));
            listItemCd.AddRange(ItemCdConst.igakuIryoDxItemCds(sinDate));
            listItemCd.AddRange(ItemCdConst.zaitakuIryoDxHoumonSinryoItemCds(sinDate));
            listItemCd.AddRange(ItemCdConst.zaitakuIryoDxZaiganItemCds(sinDate));
            listItemCd.AddRange(
                new List<string>
                {
                    ItemCdConst.ZaitakuIryoDXHoumonKango,
                    ItemCdConst.SonotaIryoDXHoumon
                }
            );

            // 項目がオーダーされているチェック
            var odrInfDetail = allOdrInfDetail.FirstOrDefault(item => listItemCd.Contains(item.ItemCd));
            if (odrInfDetail != null)
            {
                return checkingOrderModelList;
            }

            // reset list for autosantei check 
            listItemCd = new List<string>();

            if (sinDate >= KaiseiDate.d20250401)
            {
                // 111703670「医療ＤＸ推進体制整備加算１」の自動算定期間の場合
                if (FindAutoSanteiMst(hpId, ItemCdConst.SyosinIryoDX1_202504, sinDate))
                {
                    listItemCd.Add(ItemCdConst.SyosinIryoDX1_202504);
                    listItemCd.Add(ItemCdConst.IgakuIryoDX1_202504);
                }

                // 111703770「医療ＤＸ推進体制整備加算２」の自動算定期間の場合
                if (FindAutoSanteiMst(hpId, ItemCdConst.SyosinIryoDX2_202504, sinDate))
                {
                    listItemCd.Add(ItemCdConst.SyosinIryoDX2_202504);
                    listItemCd.Add(ItemCdConst.IgakuIryoDX2_202504);
                }

                // 111703870「医療ＤＸ推進体制整備加算３」の自動算定期間の場合
                if (FindAutoSanteiMst(hpId, ItemCdConst.SyosinIryoDX3_202504, sinDate))
                {
                    listItemCd.Add(ItemCdConst.SyosinIryoDX3_202504);
                    listItemCd.Add(ItemCdConst.IgakuIryoDX3_202504);
                }
            }

            if (sinDate >= KaiseiDate.d20241001)
            {
                // 111703470「医療ＤＸ推進体制整備加算４(～2025/3/31:医療ＤＸ加算１)」の自動算定期間の場合
                if (FindAutoSanteiMst(hpId, ItemCdConst.SyosinIryoDX1, sinDate))
                {
                    listItemCd.Add(ItemCdConst.SyosinIryoDX1);
                    listItemCd.Add(ItemCdConst.IgakuIryoDX1);
                }

                // 111703570「医療ＤＸ推進体制整備加算５(～2025/3/31:医療ＤＸ加算２)」の自動算定期間の場合
                if (FindAutoSanteiMst(hpId, ItemCdConst.SyosinIryoDX2, sinDate))
                {
                    listItemCd.Add(ItemCdConst.SyosinIryoDX2);
                    listItemCd.Add(ItemCdConst.IgakuIryoDX2);
                }
            }

            // 111703370「医療ＤＸ推進体制整備加算６(～2024/9/30:医療ＤＸ加算、2024/10/1～2025/3/31:医療ＤＸ加算３)」の自動算定に設定されている
            if (FindAutoSanteiMst(hpId, ItemCdConst.SyosinIryoDX, sinDate))
            {
                listItemCd.Add(ItemCdConst.SyosinIryoDX);
                listItemCd.Add(ItemCdConst.IgakuIryoDX);
            }

            // 同意ありチェック
            if (ExistOnlineConsent(ptId, sinDate, isCheckAgreeExist: true))
            {
                if (sinDate >= KaiseiDate.d20250401)
                {
                    // 114746310「在宅医療ＤＸ情報活用加算１」の自動算定に設定されている
                    if (FindAutoSanteiMst(hpId, ItemCdConst.ZaitakuIryoDXHoumonSinryo1, sinDate))
                    {
                        listItemCd.Add(ItemCdConst.ZaitakuIryoDXHoumonSinryo1);
                        listItemCd.Add(ItemCdConst.ZaitakuIryoDXZaigan1);
                    }
                }

                // 114706510「在宅医療ＤＸ情報活用加算２(～2025/3/31:在宅医療ＤＸ加算)」の自動算定に設定されている
                if (FindAutoSanteiMst(hpId, ItemCdConst.ZaitakuIryoDXHoumonSinryo, sinDate))
                {
                    listItemCd.Add(ItemCdConst.ZaitakuIryoDXHoumonSinryo);
                    listItemCd.Add(ItemCdConst.ZaitakuIryoDXZaigan);
                }

                // 114741310「訪問看護医療ＤＸ情報活用加算」の自動算定に設定されている
                if (FindAutoSanteiMst(hpId, ItemCdConst.ZaitakuIryoDXHoumonKango, sinDate))
                {
                    listItemCd.Add(ItemCdConst.ZaitakuIryoDXHoumonKango);
                    listItemCd.Add(ItemCdConst.SonotaIryoDXHoumon);
                }
            }

            if (listItemCd.Count == 0)
            {
                return checkingOrderModelList;
            }

            var santei = syosaisinKbn != SyosaiConst.None && syosaisinKbn != SyosaiConst.Jihi;
            foreach (var itemCd in listItemCd)
            {
                var tenMst = _mstItemRepository.GetTenMstInfo(hpId, itemCd, sinDate);
                if (tenMst != null)
                {
                    var ipnNameMst = NoTrackingDataContext.IpnNameMsts
                        .Where(e => e.IpnNameCd == tenMst.IpnNameCd && e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None)
                        .OrderByDescending(e => e.SeqNo).FirstOrDefault();

                    CheckedOrderModel checkingOrderModel = new CheckedOrderModel(
                            CheckingType.MissingCalculate,
                            santei: true,
                            checkingContent: FormatSanteiMessage(tenMst.Name ?? string.Empty),
                            itemCd: tenMst.ItemCd,
                            sinKouiKbn: tenMst.SinKouiKbn,
                            itemName: tenMst.Name ?? string.Empty,
                            inOutKbn: 0,
                            tenMst.OdrUnitName ?? string.Empty,
                            tenMst.ReceUnitName ?? string.Empty,
                            tenMst.IpnNameCd ?? string.Empty,
                            tenMst.CnvUnitName ?? string.Empty,
                            tenMst.DrugKbn,
                            tenMst.MasterSbt ?? string.Empty,
                            tenMst.YohoKbn,
                            tenMst.BuiKbn,
                            tenMst.IsAdopted,
                            tenMst.SenteiRyoyoKbn,
                            tenMst.CenterCd ?? string.Empty,
                            tenMst.YjCd ?? string.Empty,
                            tenMst.KohatuKbn,
                            ipnNameMst?.IpnName ?? string.Empty,
                            tenMst.IpnNameCd ?? string.Empty,
                            tenMst.DefaultValue,
                            tenMst.CmtColKeta1,
                            tenMst.CmtColKeta2,
                            tenMst.CmtColKeta3,
                            tenMst.CmtColKeta4,
                            tenMst.Kokuji1 ?? string.Empty,
                            tenMst.Kokuji2 ?? string.Empty,
                            tenMst.CmtCol1,
                            tenMst.CmtCol2,
                            tenMst.CmtCol3,
                            tenMst.CmtCol4,
                            tenMst.CenterCd ?? string.Empty,
                            tenMst.RousaiKbn,
                            tenMst.StartDate
                            );

                    checkingOrderModelList.Add(checkingOrderModel);
                }
            }

            return checkingOrderModelList;
        }

        public List<CheckedOrderModel> TrialGairaiZaitakuRiha(int hpId, long ptId, int sinDate, List<OrdInfDetailModel> allOdrInfDetail)
        {
            List<CheckedOrderModel> checkingOrderModelList = new List<CheckedOrderModel>();

            // 当該診療日が2024 / 06 / 01以降の自動算定に設定されている期間チェック
            if (sinDate < KaiseiDate.d20240601)
            {
                return checkingOrderModelList;
            }

            bool autoSanteiGairai = false;
            bool autoSanteiZaitaku = false;
            bool autoSanteiRehabilitation = false;

            // 113042070
            var todayOrderOutPatient = allOdrInfDetail.FirstOrDefault(p => p.ItemCd == ItemCdConst.GairaiTeishutsuKasan);
            if (todayOrderOutPatient == null)
            {
                autoSanteiGairai = FindAutoSanteiMst(hpId, ItemCdConst.GairaiTeishutsuKasan, sinDate);
            }

            // 114057970
            var autoSanteiHomeDetail = allOdrInfDetail.FirstOrDefault(p => p.ItemCd == ItemCdConst.ZaitakuTeishutsuKasanDoctor || p.ItemCd == ItemCdConst.ZaitakuTeishutsuKasanDoctorCancerFee);
            if (autoSanteiHomeDetail == null)
            {
                autoSanteiZaitaku = FindAutoSanteiMst(hpId, ItemCdConst.ZaitakuTeishutsuKasanDoctor, sinDate);
            }

            // 180066370
            var todayOrderRehabilitation = allOdrInfDetail.FirstOrDefault(p => p.ItemCd == ItemCdConst.RehabilitationZaitakuTeishutsuKasan);
            if (todayOrderRehabilitation == null)
            {
                autoSanteiRehabilitation = FindAutoSanteiMst(hpId, ItemCdConst.RehabilitationZaitakuTeishutsuKasan, sinDate);
            }

            if (autoSanteiGairai || autoSanteiZaitaku || autoSanteiRehabilitation)
            {
                GetTodayOdrInfDetail(ref allOdrInfDetail, hpId, sinDate);
            }

            var listItemCd = new List<string>();

            // 113042070 外来データ提出加算（生活習慣病管理料１・２）
            if (todayOrderOutPatient != null)
            {
                //WriteLogs("外来データ提出加算（生活習慣病管理料１・２）: オーダーされている");
            }
            else
            {
                if (autoSanteiGairai)
                {
                    var outPatientData = allOdrInfDetail.FirstOrDefault(p => p.MasterSbt == "S" && p.Kokuji2 != "7" && p.CdKbn == "B" && p.CdKbnNo == 1 && p.CdEdaNo == 3 &&
                                        (p.CdKouNo == 1 || p.CdKouNo == 6 || p.CdKouNo == 7));
                    if (outPatientData != null)
                    {
                        listItemCd.Add(ItemCdConst.GairaiTeishutsuKasan);
                    }
                }
            }

            // 114057970 在宅データ提出加算
            if (autoSanteiHomeDetail != null)
            {
                //WriteLogs("在宅データ提出加算（在医総管・施医総管）: オーダーされている");
            }
            else
            {
                if (autoSanteiZaitaku)
                {
                    var homePatientData = allOdrInfDetail.Where(p => p.MasterSbt == "S" && p.Kokuji2 != "7" && p.CdKbn == "C").ToList();
                    if (homePatientData.FirstOrDefault(p => p.CdKbnNo == 2 && (p.CdEdaNo == 0 || p.CdEdaNo == 2)) != null)
                    {
                        listItemCd.Add(ItemCdConst.ZaitakuTeishutsuKasanDoctor);
                    }
                    if (homePatientData.FirstOrDefault(p => p.CdKbnNo == 3 && p.CdEdaNo == 0) != null)
                    {
                        listItemCd.Add(ItemCdConst.ZaitakuTeishutsuKasanDoctorCancerFee);
                    }
                }
            }

            // 180066370 リハビリテーションデータ提出加算
            if (todayOrderRehabilitation != null)
            {
                //WriteLogs("リハビリテーションデータ提出加算: オーダーされている");
            }
            else
            {
                if (autoSanteiRehabilitation)
                {
                    var rehabilitationPatientData = allOdrInfDetail.FirstOrDefault(p => p.MasterSbt == "S" && p.Kokuji2 != "7" && p.CdKbn == "H" && !p.ItemCd.StartsWith("Z") &&
                                                    ((p.CdKbnNo == 0 && p.CdEdaNo == 0) ||
                                                     (p.CdKbnNo == 1 && p.CdEdaNo == 0) ||
                                                     (p.CdKbnNo == 1 && p.CdEdaNo == 2) ||
                                                     (p.CdKbnNo == 2 && p.CdEdaNo == 0) ||
                                                     (p.CdKbnNo == 3 && p.CdEdaNo == 0)));
                    if (rehabilitationPatientData != null)
                    {
                        listItemCd.Add(ItemCdConst.RehabilitationZaitakuTeishutsuKasan);
                    }
                }
            }

            var ipnNameMsts = NoTrackingDataContext.IpnNameMsts.Where(e => e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None).ToList();

            foreach (var itemCd in listItemCd)
            {
                var tenMst = _mstItemRepository.GetTenMstInfo(hpId, itemCd, sinDate);
                if (tenMst != null)
                {
                    var ipnNameMst = ipnNameMsts.FirstOrDefault(e => e.IpnNameCd == tenMst.IpnNameCd);
                    CheckedOrderModel checkingOrderModel = new CheckedOrderModel(
                            CheckingType.MissingCalculate,
                            santei: true,
                            checkingContent: FormatSanteiMessage(tenMst.Name ?? string.Empty),
                            itemCd: tenMst.ItemCd,
                            sinKouiKbn: tenMst.SinKouiKbn,
                            itemName: tenMst.Name ?? string.Empty,
                            inOutKbn: 0,
                            tenMst.OdrUnitName ?? string.Empty,
                            tenMst.ReceUnitName ?? string.Empty,
                            tenMst.IpnNameCd ?? string.Empty,
                            tenMst.CnvUnitName ?? string.Empty,
                            tenMst.DrugKbn,
                            tenMst.MasterSbt ?? string.Empty,
                            tenMst.YohoKbn,
                            tenMst.BuiKbn,
                            tenMst.IsAdopted,
                            tenMst.SenteiRyoyoKbn,
                            tenMst.CenterCd ?? string.Empty,
                            tenMst.YjCd ?? string.Empty,
                            tenMst.KohatuKbn,
                            ipnNameMst?.IpnName ?? string.Empty,
                            tenMst.IpnNameCd ?? string.Empty,
                            tenMst.DefaultValue,
                            tenMst.CmtColKeta1,
                            tenMst.CmtColKeta2,
                            tenMst.CmtColKeta3,
                            tenMst.CmtColKeta4,
                            tenMst.Kokuji1 ?? string.Empty,
                            tenMst.Kokuji2 ?? string.Empty,
                            tenMst.CmtCol1,
                            tenMst.CmtCol2,
                            tenMst.CmtCol3,
                            tenMst.CmtCol4,
                            tenMst.CenterCd ?? string.Empty,
                            tenMst.RousaiKbn,
                            tenMst.StartDate
                            );

                    checkingOrderModelList.Add(checkingOrderModel);
                }
            }

            return checkingOrderModelList;
        }

        public List<CheckedOrderModel> TrialIryoJyohoSyutoku(int hpId, long ptId, int sinDate, List<OrdInfDetailModel> allOdrInfDetail, List<OrdInfModel> odrInfItems)
        {
            List<CheckedOrderModel> checkingOrderModelList = new List<CheckedOrderModel>();
            if (sinDate < KaiseiDate.d20240601 ||
                sinDate >= KaiseiDate.d20241201) // 202412_算定支援_医療情報取得加算
            {
                return checkingOrderModelList;
            }

            // 自動算定期限チェック
            bool autoSanteiItem = FindAutoSanteiMst(hpId, ItemCdConst.SyosinIryojyohoSyutoku1, sinDate);
            if (!autoSanteiItem)
            {
                return checkingOrderModelList;
            }


            // 項目がオーダーされているチェック
            var allIryoJyohoItemList = new List<string> { ItemCdConst.SyosinIryojyohoSyutoku1, ItemCdConst.SyosinIryojyohoSyutoku2,
                                                    ItemCdConst.SaisinIryoJyohoSyutoku3, ItemCdConst.SaisinIryoJyohoSyutoku4,
                                                    ItemCdConst.IgakuIryoJyohoSyutoku1, ItemCdConst.IgakuIryoJyohoSyutoku2,
                                                    ItemCdConst.IgakuIryoJyohoSyutoku3, ItemCdConst.IgakuIryoJyohoSyutoku4 };
            var odrInfDetail = allOdrInfDetail.FirstOrDefault(item => allIryoJyohoItemList.Contains(item.ItemCd));
            if (odrInfDetail != null)
            {
                return checkingOrderModelList;
            }
            bool isExistFirstVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && x.Suryo == 1);
            bool isExistReturnVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && (x.Suryo == 3));
            bool isExistNoneVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && (x.Suryo == SyosaiConst.None));
            var listItemCdForTrialCalculate = new List<string>();
            // 初診
            if (isExistFirstVisit)
            {
                listItemCdForTrialCalculate = new List<string> { ItemCdConst.SyosinIryojyohoSyutoku1, ItemCdConst.IgakuIryoJyohoSyutoku1 };
            }
            // 再診 初再診なし
            else if (isExistReturnVisit || isExistNoneVisit)
            {
                listItemCdForTrialCalculate = new List<string> { ItemCdConst.SaisinIryoJyohoSyutoku3, ItemCdConst.IgakuIryoJyohoSyutoku3 };
            }

            var ipnNameMsts = NoTrackingDataContext.IpnNameMsts.Where(e => e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None).ToList();
            foreach (var itemCd in listItemCdForTrialCalculate)
            {
                var tenMst = _mstItemRepository.GetTenMstInfo(hpId, itemCd, sinDate);
                if (tenMst != null)
                {
                    var ipnNameMst = ipnNameMsts.FirstOrDefault(e => e.IpnNameCd == tenMst.IpnNameCd);
                    CheckedOrderModel checkingOrderModel = new CheckedOrderModel(
                            CheckingType.MissingCalculate,
                            santei: true,
                            checkingContent: FormatSanteiMessage(tenMst.Name ?? string.Empty),
                            itemCd: tenMst.ItemCd,
                            sinKouiKbn: tenMst.SinKouiKbn,
                            itemName: tenMst.Name ?? string.Empty,
                            inOutKbn: 0,
                            tenMst.OdrUnitName ?? string.Empty,
                            tenMst.ReceUnitName ?? string.Empty,
                            tenMst.IpnNameCd ?? string.Empty,
                            tenMst.CnvUnitName ?? string.Empty,
                            tenMst.DrugKbn,
                            tenMst.MasterSbt ?? string.Empty,
                            tenMst.YohoKbn,
                            tenMst.BuiKbn,
                            tenMst.IsAdopted,
                            tenMst.SenteiRyoyoKbn,
                            tenMst.CenterCd ?? string.Empty,
                            tenMst.YjCd ?? string.Empty,
                            tenMst.KohatuKbn,
                            ipnNameMst?.IpnName ?? string.Empty,
                            tenMst.IpnNameCd ?? string.Empty,
                            tenMst.DefaultValue,
                            tenMst.CmtColKeta1,
                            tenMst.CmtColKeta2,
                            tenMst.CmtColKeta3,
                            tenMst.CmtColKeta4,
                            tenMst.Kokuji1 ?? string.Empty,
                            tenMst.Kokuji2 ?? string.Empty,
                            tenMst.CmtCol1,
                            tenMst.CmtCol2,
                            tenMst.CmtCol3,
                            tenMst.CmtCol4,
                            tenMst.CenterCd ?? string.Empty,
                            tenMst.RousaiKbn,
                            tenMst.StartDate
                    );

                    checkingOrderModelList.Add(checkingOrderModel);
                }
            }

            return checkingOrderModelList;
        }

        public List<CheckedOrderModel> IryoJyohoSyutoku202412(int hpId, long ptId, int sinDate, List<OrdInfDetailModel> allOdrInfDetail, List<OrdInfModel> odrInfItems)
        {
            List<CheckedOrderModel> checkingOrderModelList = new List<CheckedOrderModel>();
            if (sinDate < KaiseiDate.d20241201) // 202412_算定支援_医療情報取得加算
            {
                return checkingOrderModelList;
            }

            // 自動算定期限チェック
            bool autoSanteiItem = FindAutoSanteiMst(hpId, ItemCdConst.SyosinIryojyohoSyutoku1, sinDate);
            if (!autoSanteiItem)
            {
                return checkingOrderModelList;
            }


            // 項目がオーダーされているチェック
            var allIryoJyohoItemList = new List<string> { ItemCdConst.SyosinIryojyohoSyutoku2, ItemCdConst.SaisinIryoJyohoSyutoku4,
                                                          ItemCdConst.IgakuIryoJyohoSyutoku2, ItemCdConst.IgakuIryoJyohoSyutoku4 };
            var odrInfDetail = allOdrInfDetail.FirstOrDefault(item => allIryoJyohoItemList.Contains(item.ItemCd));
            if (odrInfDetail != null)
            {
                return checkingOrderModelList;
            }
            bool isExistFirstVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && x.Suryo == 1);
            bool isExistReturnVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && (x.Suryo == 3));
            bool isExistNoneVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && (x.Suryo == SyosaiConst.None));
            var listItemCdForTrialCalculate = new List<string>();
            // 初診
            if (isExistFirstVisit)
            {
                listItemCdForTrialCalculate = new List<string> { ItemCdConst.SyosinIryojyohoSyutoku2, ItemCdConst.IgakuIryoJyohoSyutoku2 };
            }
            // 再診 初再診なし
            else if (isExistReturnVisit || isExistNoneVisit)
            {
                listItemCdForTrialCalculate = new List<string> { ItemCdConst.SaisinIryoJyohoSyutoku4, ItemCdConst.IgakuIryoJyohoSyutoku4 };
            }

            var ipnNameMsts = NoTrackingDataContext.IpnNameMsts.Where(e => e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == DeleteTypes.None).ToList();

            foreach (var itemCd in listItemCdForTrialCalculate)
            {
                var tenMst = _mstItemRepository.GetTenMstInfo(hpId, itemCd, sinDate);
                if (tenMst != null)
                {
                    var ipnNameMst = ipnNameMsts.FirstOrDefault(e => e.IpnNameCd == tenMst.IpnNameCd);

                    CheckedOrderModel checkingOrderModel = new CheckedOrderModel(
                            CheckingType.MissingCalculate,
                            santei: true,
                            checkingContent: FormatSanteiMessage(tenMst.Name ?? string.Empty),
                            itemCd: tenMst.ItemCd,
                            sinKouiKbn: tenMst.SinKouiKbn,
                            itemName: tenMst.Name ?? string.Empty,
                            inOutKbn: 0,
                            tenMst.OdrUnitName ?? string.Empty,
                            tenMst.ReceUnitName ?? string.Empty,
                            tenMst.IpnNameCd ?? string.Empty,
                            tenMst.CnvUnitName ?? string.Empty,
                            tenMst.DrugKbn,
                            tenMst.MasterSbt ?? string.Empty,
                            tenMst.YohoKbn,
                            tenMst.BuiKbn,
                            tenMst.IsAdopted,
                            tenMst.SenteiRyoyoKbn,
                            tenMst.CenterCd ?? string.Empty,
                            tenMst.YjCd ?? string.Empty,
                            tenMst.KohatuKbn,
                            ipnNameMst?.IpnName ?? string.Empty,
                            tenMst.IpnNameCd ?? string.Empty,
                            tenMst.DefaultValue,
                            tenMst.CmtColKeta1,
                            tenMst.CmtColKeta2,
                            tenMst.CmtColKeta3,
                            tenMst.CmtColKeta4,
                            tenMst.Kokuji1 ?? string.Empty,
                            tenMst.Kokuji2 ?? string.Empty,
                            tenMst.CmtCol1,
                            tenMst.CmtCol2,
                            tenMst.CmtCol3,
                            tenMst.CmtCol4,
                            tenMst.CenterCd ?? string.Empty,
                            tenMst.RousaiKbn,
                            tenMst.StartDate
                    );

                    checkingOrderModelList.Add(checkingOrderModel);
                }
            }

            return checkingOrderModelList;
        }

        public void GairaiZaitakuRiha(int hpId, long ptId, int sinDate, ref List<CheckedOrderModel> checkingOrderModelList, List<string> itemSantei)
        {
            // 当該診療日が2024 / 06 / 01以降の自動算定に設定されている期間チェック
            if (sinDate < KaiseiDate.d20240601)
            {
                return;
            }

            var listItemCd = new List<string>();

            // 113042070 外来データ提出加算（生活習慣病管理料１・２）
            if (itemSantei.Contains(ItemCdConst.GairaiTeishutsuKasan))
            {
                //生活習慣病管理料Ⅰ
                //生活習慣病管理料Ⅱ
                var outPatientData = NoTrackingDataContext.TenMsts
                    .Where(p => p.HpId == hpId &&
                       p.StartDate <= sinDate &&
                       p.EndDate >= sinDate &&
                       p.IsDeleted == DeleteTypes.None &&
                       p.MasterSbt == "S" &&
                       p.Kokuji2 != "7" &&
                       p.CdKbn == "B" &&
                       p.CdKbnno == 1 &&
                       p.CdEdano == 3 &&
                       (p.CdKouno == 1 || p.CdKouno == 6 || p.CdKouno == 7))
                    .Select(item => item.ItemCd)
                    .ToList();
                if (outPatientData == null || !itemSantei.Any(p => outPatientData.Any(x => x == p)))
                {
                    checkingOrderModelList.RemoveAll(x => x.ItemCd == ItemCdConst.GairaiTeishutsuKasan);
                }
            }
            else
            {
                checkingOrderModelList.RemoveAll(x => x.ItemCd == ItemCdConst.GairaiTeishutsuKasan);
            }

            //[114057970] 在宅データ提出加算（在医総管・施医総管）
            if (itemSantei.Contains(ItemCdConst.ZaitakuTeishutsuKasanDoctor))
            {
                //在宅時医学総合管理料
                var homePatientData = NoTrackingDataContext.TenMsts
                    .Where(p => p.HpId == hpId &&
                       p.StartDate <= sinDate &&
                       p.EndDate >= sinDate &&
                       p.IsDeleted == DeleteTypes.None &&
                       p.MasterSbt == "S" &&
                       p.Kokuji2 != "7" &&
                       p.CdKbn == "C" &&
                       p.CdKbnno == 2 &&
                       (p.CdEdano == 0 || p.CdEdano == 2))
                    .Select(item => item.ItemCd)
                    .ToList();
                if (homePatientData == null || !itemSantei.Any(p => homePatientData.Any(x => x == p)))
                {
                    checkingOrderModelList.RemoveAll(x => x.ItemCd == ItemCdConst.ZaitakuTeishutsuKasanDoctor);
                }
            }
            else
            {
                checkingOrderModelList.RemoveAll(x => x.ItemCd == ItemCdConst.ZaitakuTeishutsuKasanDoctor);
            }

            //[114060670] 在宅データ提出加算（在宅がん医療総合診療料））
            if (itemSantei.Contains(ItemCdConst.ZaitakuTeishutsuKasanDoctorCancerFee))
            {
                //在宅がん医療総合診療料
                var homePatientData = NoTrackingDataContext.TenMsts
                    .Where(p => p.HpId == hpId &&
                       p.StartDate <= sinDate &&
                       p.EndDate >= sinDate &&
                       p.IsDeleted == DeleteTypes.None &&
                       p.MasterSbt == "S" &&
                       p.Kokuji2 != "7" &&
                       p.CdKbn == "C" &&
                       p.CdKbnno == 3 &&
                       p.CdEdano == 0)
                    .Select(item => item.ItemCd)
                    .ToList();
                //在宅がん医療総合診療料
                if (homePatientData == null || !itemSantei.Any(p => homePatientData.Any(x => x == p)))
                {
                    checkingOrderModelList.RemoveAll(x => x.ItemCd == ItemCdConst.ZaitakuTeishutsuKasanDoctorCancerFee);
                }
            }
            else
            {
                checkingOrderModelList.RemoveAll(x => x.ItemCd == ItemCdConst.ZaitakuTeishutsuKasanDoctorCancerFee);
            }

            //[180066370] リハビリテーションデータ提出加算
            if (itemSantei.Contains(ItemCdConst.RehabilitationZaitakuTeishutsuKasan))
            {
                var rehabilitationPatientData = NoTrackingDataContext.TenMsts
                    .Where(p => p.HpId == hpId &&
                       p.StartDate <= sinDate &&
                       p.EndDate >= sinDate &&
                       p.IsDeleted == DeleteTypes.None &&
                       p.MasterSbt == "S" &&
                       p.Kokuji2 != "7" &&
                       p.CdKbn == "H" &&
                       !p.ItemCd.StartsWith("Z") &&
                       ((p.CdKbnno == 0 && p.CdEdano == 0) ||
                        (p.CdKbnno == 1 && p.CdEdano == 0) ||
                        (p.CdKbnno == 1 && p.CdEdano == 2) ||
                        (p.CdKbnno == 2 && p.CdEdano == 0) ||
                        (p.CdKbnno == 3 && p.CdEdano == 0)))
                    .Select(item => item.ItemCd)
                    .ToList();
                if (rehabilitationPatientData == null || !itemSantei.Any(p => rehabilitationPatientData.Any(x => x == p)))
                {
                    checkingOrderModelList.RemoveAll(x => x.ItemCd == ItemCdConst.RehabilitationZaitakuTeishutsuKasan);
                }
            }
            else
            {
                checkingOrderModelList.RemoveAll(x => x.ItemCd == ItemCdConst.RehabilitationZaitakuTeishutsuKasan);
            }
        }

        public void IryoJyohoSyutoku(int hpId, long ptId, int sinDate, ref List<CheckedOrderModel> checkingOrderModelList, List<string> itemSantei, List<OrdInfModel> odrInfItems, List<OrdInfDetailModel> allOdrInfDetail)
        {
            if (sinDate < KaiseiDate.d20240601 ||
                sinDate >= KaiseiDate.d20241201) // 202412_算定支援_医療情報取得加算
            {
                return;
            }

            // 項目がオーダーされているチェック
            // 手オーダーしている場合も、計算結果に医療情報取得加算が含まれるため、再チェック
            var allIryoJyohoItemList = new List<string> { ItemCdConst.SyosinIryojyohoSyutoku1, ItemCdConst.SyosinIryojyohoSyutoku2,
                                                    ItemCdConst.SaisinIryoJyohoSyutoku3, ItemCdConst.SaisinIryoJyohoSyutoku4,
                                                    ItemCdConst.IgakuIryoJyohoSyutoku1, ItemCdConst.IgakuIryoJyohoSyutoku2,
                                                    ItemCdConst.IgakuIryoJyohoSyutoku3, ItemCdConst.IgakuIryoJyohoSyutoku4 };
            if (checkingOrderModelList.Any(p => allIryoJyohoItemList.Contains(p.ItemCd)) == false)
            {
                return;
            }

            var listItemCd = new List<string>();
            bool existOnlineConsent = ExistOnlineConsent(ptId, sinDate, true);
            bool isExistFirstVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && x.Suryo == 1);
            bool isExistReturnVisit = allOdrInfDetail.Any(x => x.ItemCd == ItemCdConst.SyosaiKihon && (x.Suryo == 3 || x.Suryo == 0));

            // 初診
            if (isExistFirstVisit)
            {
                // [111703170] 医療情報取得加算１（初診）が算定された場合
                if (itemSantei.Contains(ItemCdConst.SyosinIryojyohoSyutoku1))
                {
                    listItemCd.Add(ItemCdConst.SyosinIryojyohoSyutoku2);
                }

                //  [113705270] 医療情報取得加算１（医学管理等）が算定された場合
                if (itemSantei.Contains(ItemCdConst.IgakuIryoJyohoSyutoku1))
                {
                    listItemCd.Add(ItemCdConst.IgakuIryoJyohoSyutoku2);
                }
            }
            // 再診 初再診なし
            else if (isExistReturnVisit)
            {
                // [112708870] 医療情報取得加算３（再診）が算定された場合
                if (itemSantei.Contains(ItemCdConst.SaisinIryoJyohoSyutoku3))
                {
                    listItemCd.Add(ItemCdConst.SaisinIryoJyohoSyutoku4);
                }

                //  [113705770] 医療情報取得加算３（医学管理等）が算定された場合
                if (itemSantei.Contains(ItemCdConst.IgakuIryoJyohoSyutoku3))
                {
                    listItemCd.Add(ItemCdConst.IgakuIryoJyohoSyutoku4);
                }
            }

            foreach (var itemCd in listItemCd)
            {
                var tenMst = FindTenMst(hpId, itemCd, sinDate);
                if (tenMst != null)
                {
                    CheckedOrderModel checkingOrderModel = new CheckedOrderModel(
                            CheckingType.MissingCalculate,
                            santei: true,
                            checkingContent: FormatSanteiMessage(tenMst.Name ?? string.Empty),
                            itemCd: tenMst.ItemCd,
                            sinKouiKbn: tenMst.SinKouiKbn,
                            itemName: tenMst.Name ?? string.Empty,
                            inOutKbn: 0
                            );

                    checkingOrderModelList.Add(checkingOrderModel);
                }
            }


            foreach (var item in checkingOrderModelList)
            {
                if (item.ItemCd == ItemCdConst.SyosinIryojyohoSyutoku1
                    || item.ItemCd == ItemCdConst.IgakuIryoJyohoSyutoku1
                    || item.ItemCd == ItemCdConst.SaisinIryoJyohoSyutoku3
                    || item.ItemCd == ItemCdConst.IgakuIryoJyohoSyutoku3)
                {
                    item.ChangeSantei(!existOnlineConsent);
                }
                else if (item.ItemCd == ItemCdConst.SyosinIryojyohoSyutoku2
                    || item.ItemCd == ItemCdConst.IgakuIryoJyohoSyutoku2
                    || item.ItemCd == ItemCdConst.SaisinIryoJyohoSyutoku4
                    || item.ItemCd == ItemCdConst.IgakuIryoJyohoSyutoku4)
                {
                    item.ChangeSantei(existOnlineConsent);
                }
            }
            return;
        }

        private void GetTodayOdrInfDetail(ref List<OrdInfDetailModel> allOdrInfDetail, int hpId, int sinDate)
        {
            foreach (var detail in allOdrInfDetail)
            {
                TenMst tenMst = FindTenMst(hpId, detail.ItemCd, sinDate);
                if (tenMst != null)
                {
                    detail.ChangeModel(tenMst.CdKbn ?? string.Empty, tenMst.CdKbnno, tenMst.CdEdano, tenMst.CdKouno, tenMst.MasterSbt ?? string.Empty);
                }
            }
        }

        private bool ExistOnlineConsent(long ptId, int sinDate, bool isCheckAll = false, bool isCheckAgreeExist = false)
        {
            var listExistHistory = NoTrackingDataContext.OnlineConfirmationHistories.Where(p => p.PtId == ptId).AsEnumerable().Where(p => CIUtil.DateTimeToInt(p.OnlineConfirmationDate) == sinDate).ToList();
            if (listExistHistory.Count > 0)
            {
                if (isCheckAgreeExist)
                {
                    var infoConsFlg = listExistHistory.Any(p => p.InfoConsFlg != null
                                           && p.InfoConsFlg.Contains("1"));
                    if (!infoConsFlg)
                    {
                        return ExistOnlineAgreedConsent(ptId, sinDate, isCheckAll);
                    }
                    return infoConsFlg;
                }
                else
                {
                    var infoConsAgreementFlg = listExistHistory.Any(p => p.InfoConsFlg != null
                                            && !p.InfoConsFlg.Contains("2")
                                            && p.InfoConsFlg.Contains("1"));
                    if (!infoConsAgreementFlg)
                    {
                        return ExistOnlineAgreedConsent(ptId, sinDate, isCheckAll);
                    }
                    return infoConsAgreementFlg;
                }
            }
            else
            {
                return ExistOnlineAgreedConsent(ptId, sinDate, isCheckAll);
            }
        }

        private bool ExistOnlineAgreedConsent(long ptId, int sinDate, bool isCheckAll = false)
        {
            List<int> listConsKbn = new List<int> { 1, 2, 3, 4 };
            DateTime earlyTime = CIUtil.IntToDateTime(sinDate, 000000).ToUniversalTime();
            DateTime lateTime = CIUtil.IntToDateTime(sinDate, 235959).ToUniversalTime();
            var query = NoTrackingDataContext.OnlineAgreedConsents.Where(o => o.HpId == o.HpId &&
                                                                           o.PtId == ptId &&
                                                                           listConsKbn.Contains(o.ConsKbn) &&
                                                                           o.ConsDate <= lateTime &&
                                                                           o.LimitDate >= earlyTime)
                                                                  .GroupBy(g => g.ConsKbn).Select(o => o.OrderByDescending(x => x.ProcessTime).First());

            if (isCheckAll)
            {
                var onlineAgreedConsentList = query.ToList();
                return onlineAgreedConsentList.Any() && onlineAgreedConsentList.All(o => o.ConsFlg == 1);
            }
            else
            {
                return query.Any(o => o.ConsFlg == 1);
            }
        }

        public (List<string>, List<SinKouiCountModel>) GetCheckedAfter327Screen(int hpId, long ptId, int sinDate, List<CheckedOrderModel> checkedTenMstResult, bool isTokysyoOrder, bool isTokysyosenOrder)
        {
            #region Checking
            // 特定疾患処方管理加算２（処方料）・算定
            List<string> msgs = new List<string>
            {
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty
            };
            var tokysyoItem = checkedTenMstResult.FirstOrDefault(d => d.ItemCd == ItemCdConst.TouyakuTokuSyo2Syoho);
            List<SinKouiCountModel> lastSanteiInMonth = new List<SinKouiCountModel>();
            if (tokysyoItem != null && tokysyoItem.Santei)
            {
                TenMst? touyakuTokuSyo1Syoho = null;
                // 当月すでに1回以上算定されている場合、警告を表示する
                lastSanteiInMonth = GetSinkouCountInMonth(hpId, ptId, sinDate, ItemCdConst.TouyakuTokuSyo1Syoho);
                if (lastSanteiInMonth.Count == 0)
                {
                    lastSanteiInMonth = GetSinkouCountInMonth(hpId, ptId, sinDate, ItemCdConst.TouyakuTokuSyo1Syohosen);
                    if (lastSanteiInMonth.Count > 0)
                    {
                        touyakuTokuSyo1Syoho = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo1Syohosen, sinDate);
                    }
                }
                else
                {
                    touyakuTokuSyo1Syoho = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo1Syoho, sinDate);
                }
                if (lastSanteiInMonth.Count > 0)
                {
                    var touyakuTokuSyo2Syoho = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo2Syoho, sinDate);
                    if (touyakuTokuSyo1Syoho != null && touyakuTokuSyo2Syoho != null)
                    {
                        msgs[0] = BuildMessage(touyakuTokuSyo1Syoho.Name ?? string.Empty, touyakuTokuSyo2Syoho.Name ?? string.Empty, SanteiDateFormat(lastSanteiInMonth));
                    }
                }
            }

            // 特定疾患処方管理加算２（処方料）・オーダー
            if (isTokysyoOrder)
            {
                TenMst? touyakuTokuSyo1Syoho = null;
                // 当月すでに1回以上算定されている場合、警告を表示する
                lastSanteiInMonth = GetSinkouCountInMonth(hpId, ptId, sinDate, ItemCdConst.TouyakuTokuSyo1Syoho);
                if (lastSanteiInMonth.Count == 0)
                {
                    lastSanteiInMonth = GetSinkouCountInMonth(hpId, ptId, sinDate, ItemCdConst.TouyakuTokuSyo1Syohosen);
                    if (lastSanteiInMonth.Count > 0)
                    {
                        touyakuTokuSyo1Syoho = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo1Syohosen, sinDate);
                    }
                }
                else
                {
                    touyakuTokuSyo1Syoho = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo1Syoho, sinDate);
                }

                if (lastSanteiInMonth.Count > 0)
                {
                    var touyakuTokuSyo2Syoho = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo2Syoho, sinDate);
                    if (touyakuTokuSyo1Syoho != null && touyakuTokuSyo2Syoho != null)
                    {
                        msgs[1] = BuildMessage(touyakuTokuSyo1Syoho.Name ?? string.Empty, touyakuTokuSyo2Syoho.Name ?? string.Empty, SanteiDateFormat(lastSanteiInMonth));
                    }
                }
            }

            // 特定疾患処方管理加算２（処方箋料）・算定
            var tokysyosenItem = checkedTenMstResult.FirstOrDefault(d => d.ItemCd == ItemCdConst.TouyakuTokuSyo2Syohosen);
            if (tokysyosenItem != null && tokysyosenItem.Santei)
            {
                TenMst? touyakuTokuSyo1Syohosen = null;
                // 当月すでに1回以上算定されている場合、警告を表示する
                lastSanteiInMonth = GetSinkouCountInMonth(hpId, ptId, sinDate, ItemCdConst.TouyakuTokuSyo1Syohosen);
                if (lastSanteiInMonth.Count == 0)
                {
                    lastSanteiInMonth = GetSinkouCountInMonth(hpId, ptId, sinDate, ItemCdConst.TouyakuTokuSyo1Syoho);
                    if (lastSanteiInMonth.Count > 0)
                    {
                        touyakuTokuSyo1Syohosen = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo1Syoho, sinDate);
                    }
                }
                else
                {
                    touyakuTokuSyo1Syohosen = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo1Syohosen, sinDate);
                }
                if (lastSanteiInMonth.Count > 0)
                {
                    var touyakuTokuSyo2Syohosen = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo2Syohosen, sinDate);
                    if (touyakuTokuSyo1Syohosen != null && touyakuTokuSyo2Syohosen != null)
                    {
                        msgs[2] = BuildMessage(touyakuTokuSyo1Syohosen?.Name ?? string.Empty, touyakuTokuSyo2Syohosen.Name ?? string.Empty, SanteiDateFormat(lastSanteiInMonth));
                    }
                }
            }

            // 特定疾患処方管理加算２（処方箋料）・オーダー
            if (isTokysyosenOrder)
            {
                TenMst? touyakuTokuSyo1Syohosen = null;
                // 当月すでに1回以上算定されている場合、警告を表示する
                lastSanteiInMonth = GetSinkouCountInMonth(hpId, ptId, sinDate, ItemCdConst.TouyakuTokuSyo1Syohosen);
                if (lastSanteiInMonth.Count == 0)
                {
                    lastSanteiInMonth = GetSinkouCountInMonth(hpId, ptId, sinDate, ItemCdConst.TouyakuTokuSyo1Syoho);
                    if (lastSanteiInMonth.Count > 0)
                    {
                        touyakuTokuSyo1Syohosen = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo1Syoho, sinDate);
                    }
                }
                else
                {
                    touyakuTokuSyo1Syohosen = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo1Syohosen, sinDate);
                }
                if (lastSanteiInMonth.Count > 0)
                {
                    var touyakuTokuSyo2Syohosen = FindTenMst(hpId, ItemCdConst.TouyakuTokuSyo2Syohosen, sinDate);
                    if (touyakuTokuSyo1Syohosen != null && touyakuTokuSyo2Syohosen != null)
                    {
                        msgs[3] = BuildMessage(touyakuTokuSyo1Syohosen.Name ?? string.Empty, touyakuTokuSyo2Syohosen.Name ?? string.Empty, SanteiDateFormat(lastSanteiInMonth));
                    }
                }
            }
            #endregion

            return new(msgs, lastSanteiInMonth);
        }

        string BuildMessage(string touyaku1Name, string touyaku2Name, string dateSantei)
        {
            StringBuilder msg = new StringBuilder();
            msg.Append("'");
            msg.Append(touyaku1Name);
            msg.Append("' ");
            msg.Append("が");
            msg.Append(dateSantei);
            msg.Append("に算定されているため、");
            msg.Append(Environment.NewLine);

            msg.Append("'");
            msg.Append(touyaku2Name);
            msg.Append("' ");
            msg.Append("を算定すると差額が発生します。");
            msg.Append(Environment.NewLine);

            msg.Append("'");
            msg.Append(touyaku2Name);
            msg.Append("' ");
            msg.Append("を算定しますか？");

            return msg.ToString();
        }

        string SanteiDateFormat(List<SinKouiCountModel> sinKouiCountList)
        {
            if (sinKouiCountList == null || sinKouiCountList.Count == 0)
            {
                return string.Empty;
            }
            var dateInt = sinKouiCountList.Select(d => d.SinDay + "日");
            return string.Join(", ", dateInt);
        }

        public List<SinKouiCountModel> GetSinkouCountInMonth(int hpId, long ptId, int sinDate, string itemCd)
        {
            int sinYM = sinDate / 100;
            int sinDay = sinDate - sinYM * 100;
            var sinKouiCountQuery = NoTrackingDataContext.SinKouiCounts
                .Where(s => s.HpId == hpId && s.PtId == ptId && s.SinYm == sinYM && s.SinDay < sinDay);

            var sinKouiDetailQuery = NoTrackingDataContext.SinKouiDetails
                .Where(s => s.HpId == hpId && s.PtId == ptId && itemCd == s.ItemCd);

            var resultQuery = from sinKouiCount in sinKouiCountQuery
                              join sinKouiDetail in sinKouiDetailQuery
                              on new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.RpNo, sinKouiCount.SinYm }
                              equals new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.RpNo, sinKouiDetail.SinYm }
                              select new
                              {
                                  SinKouiCount = sinKouiCount,
                              };
            var sinKouiCountList = resultQuery.AsEnumerable().Select(s => s.SinKouiCount);
            var santeiCount = sinKouiCountList.Sum(s => s.Count);
            if (santeiCount > 0)
            {
                return sinKouiCountList.OrderBy(s => s.SinDay).Select(d => new SinKouiCountModel(d.HpId, d.PtId, d.SinYm, d.SinDay, d.SinDay, d.RaiinNo, d.RpNo, d.SeqNo, d.Count)).ToList();
            }
            return new List<SinKouiCountModel>();
        }

        public Dictionary<string, DateTime> GetMaxAuditTrailLogDateForPrint(int hpId, long ptID, int sinDate, long raiinNo)
        {
            Dictionary<string, DateTime> result = new Dictionary<string, DateTime>();

            List<string> eventCds = new List<string>();
            eventCds.Add(EventCode.ReportInDrug);
            eventCds.Add(EventCode.ReportDrugNoteSeal);
            eventCds.Add(EventCode.ReportYakutai);
            eventCds.Add(EventCode.ReportDrugInf);
            eventCds.Add(EventCode.ReportOutDrug);
            eventCds.Add(EventCode.ReportOrderLabel);
            eventCds.Add(EventCode.ReportSijisen);

            var auditTrailLogs = NoTrackingDataContext.AuditTrailLogs.Where(x =>
                            (x.EventCd != null && eventCds.Contains(x.EventCd)) &&
                            x.HpId == hpId &&
                            x.PtId == ptID &&
                            x.SinDay == sinDate &&
                            x.RaiinNo == raiinNo).ToList();
            foreach (var eventCd in eventCds)
            {
                var eventAuditTrailLogs = auditTrailLogs.Where(a => a.EventCd == eventCd).ToList();
                var maxDate = eventAuditTrailLogs?.Count == 0 ? DateTime.MinValue : eventAuditTrailLogs?.Max(x => x.LogDate) ?? DateTime.MinValue;
                result.Add(eventCd, maxDate);
            }
            return result;
        }

        public void ReleaseResource()
        {
            _mstItemRepository.ReleaseResource();
            _systemConf.ReleaseResource();
            DisposeDataContext();
        }

        public long GetMaxRpNo(int hpId, long ptId, long raiinNo, int sinDate)
        {
            var odrListQuery = NoTrackingDataContext.OdrInfs
                .Where(odr => odr.HpId == hpId && odr.PtId == ptId && odr.RaiinNo == raiinNo && odr.SinDate == sinDate).ToList();
            if (odrListQuery.Any())
            {
                return odrListQuery.Max(odr => odr.RpNo);
            }
            return 0;
        }

        private int GetLastDaySantei(int hpId, long ptId, int sinDate, long raiinNo, string itemCd)
        {
            int result = 0;
            var sinKouiCountDiffDayQuery = NoTrackingDataContext.SinKouiCounts.Where(s => s.HpId == hpId && s.PtId == ptId && (s.SinYm * 100 + s.SinDay) < sinDate);
            var sinKouiDetailQuery = NoTrackingDataContext.SinKouiDetails.Where(s => s.HpId == hpId && s.PtId == ptId && s.ItemCd == itemCd);
            var resultDiffDayQuery = from sinKouiCount in sinKouiCountDiffDayQuery
                                     join sinKouiDetail in sinKouiDetailQuery
                                     on new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.RpNo, sinKouiCount.SinYm }
                                     equals new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.RpNo, sinKouiDetail.SinYm }
                                     select new
                                     {
                                         SinKouiCount = sinKouiCount,
                                     };
            var resultCountList = resultDiffDayQuery.ToList();
            if (resultCountList.Count > 0)
            {
                //当日を含めない
                result = resultCountList.Max(d => d.SinKouiCount.SinYm * 100 + d.SinKouiCount.SinDay);
            }
            else
            {
                //当日を含める
                var sinKouiCountSameDayQuery = NoTrackingDataContext.SinKouiCounts
                    .Where(s => s.HpId == hpId && s.PtId == ptId && (s.SinYm * 100 + s.SinDay) <= sinDate && s.RaiinNo != raiinNo);
                var resultSameDayQuery = from sinKouiCount in sinKouiCountSameDayQuery
                                         join sinKouiDetail in sinKouiDetailQuery
                                         on new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.RpNo, sinKouiCount.SinYm }
                                         equals new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.RpNo, sinKouiDetail.SinYm }
                                         select new
                                         {
                                             SinKouiCount = sinKouiCount,
                                         };
                resultCountList = resultSameDayQuery.ToList();
                if (resultCountList.Count > 0)
                {
                    result = resultCountList.Max(d => d.SinKouiCount.SinYm * 100 + d.SinKouiCount.SinDay);
                }
            }

            return result;
        }

        public List<AuditTrailLogModel> GetKensaAuditTrailLogs(int hpId, string eventCd, long ptID, int sinDate, long raiinNo, int isOperator, string operatorName)
        {
            var trailLogs = NoTrackingDataContext.AuditTrailLogs.Where(x =>
                                   x.HpId == hpId &&
                                   x.EventCd == eventCd &&
                                   x.PtId == ptID &&
                                   x.SinDay == sinDate &&
                                   x.RaiinNo == raiinNo &&
                                   x.IsOperator == isOperator &&
                                   x.OperatorName == operatorName);
            var detailLogs = NoTrackingDataContext.AuditTrailLogDetails.Where(x => x.HpId == hpId);
            var query = from trailLog in trailLogs
                        join detailLog in detailLogs on
                        trailLog.LogId equals detailLog.LogId
                        select new
                        {
                            TrailLog = trailLog,
                            Hosuke = detailLog.Hosoku
                        };
            var result = query.AsEnumerable().Select(x => new AuditTrailLogModel(x.TrailLog.LogId, x.TrailLog.LogDate, x.TrailLog.HpId, x.TrailLog.UserId, x.TrailLog.EventCd ?? string.Empty, x.TrailLog.PtId, x.TrailLog.SinDay, x.TrailLog.RaiinNo, x.TrailLog.Machine ?? string.Empty, x.Hosuke, x.TrailLog.IsOperator, x.TrailLog.OperatorName)).ToList();
            return result;
            // return query.AsEnumerable().Select(x => new AuditTrailLogModel(x.TrailLog.LogId, x.TrailLog.LogDate, x.TrailLog.HpId, x.TrailLog.UserId, x.TrailLog.EventCd ?? string.Empty, x.TrailLog.PtId, x.TrailLog.SinDay, x.TrailLog.RaiinNo, x.TrailLog.Machine ?? string.Empty, x.Hosuke, x.TrailLog.IsOperator, x.TrailLog.OperatorName)).ToList();
        }

        //Item1: InoutKbn
        //Item2: OdrKouiKbn
        //Item3: IsDeleted
        //Item4: Details (Item1: Itemcd, Item2: MasterSbt) 
        public List<KensaPrinterItemModel> GetContainerMstModels(int hpId, int sinDate, List<Tuple<int, int, int, List<Tuple<string, string>>>> orderInfs, bool defaultChecked)
        {
            var kensaLabelCheckInHospital = _systemConf.GetSettingValue(92009, 1, hpId);
            bool checkInHospital(string itemCd)
            {
                var orderKensaLabels = orderInfs.Where(x => x.Item3 == 0 && x.Item4.Any(detail => !string.IsNullOrEmpty(itemCd) &&
                                                                                                                   (detail.Item1.StartsWith("J") ||
                                                                                                                   detail.Item1.StartsWith("Z") ||
                                                                                                                   detail.Item2 == "S"))).ToList();
                return (kensaLabelCheckInHospital == 0 && !(!orderKensaLabels.Any(o => o.Item3 == 0 && (o.Item1 == 0 && o.Item2 >= 60 && o.Item2 <= 69) && o.Item4.Any(detail => detail.Item1 == itemCd)))) ||
                       (kensaLabelCheckInHospital == 1 && !(!orderKensaLabels.Any(o => o.Item3 == 0 && (o.Item1 == 1 && o.Item2 >= 60 && o.Item2 <= 69) && o.Item4.Any(detail => detail.Item1 == itemCd)))) ||
                       (!(!orderKensaLabels.Any(o => o.Item3 == 0 && !(o.Item2 >= 60 && o.Item2 <= 69) && o.Item4.Any(detail => detail.Item1 == itemCd)))) ||
                       kensaLabelCheckInHospital == 2;
            }

            List<string> itemcds = new();
            List<KensaPrinterItemModel> kensaItems = new();
            foreach (var order in orderInfs)
            {
                if (order.Item3 != 0) continue;
                var kensaItemCds = order.Item4.Where(x => !string.IsNullOrEmpty(x.Item1) &&
                                                                       (x.Item1.StartsWith("J") || x.Item1.StartsWith("Z") || x.Item2 == "S"));

                itemcds.AddRange(kensaItemCds.Select(x => x.Item1));
            }
            var tenmsts = NoTrackingDataContext.TenMsts.Where(x => x.HpId == hpId &&
                                                                                   x.StartDate <= sinDate &&
                                                                                   x.EndDate >= sinDate &&
                                                                                   x.KensaLabel > 0 &&
                                                                                   x.IsDeleted == DeleteTypes.None);
            var kensaMsts = NoTrackingDataContext.KensaMsts.Where(x => x.HpId == hpId && x.IsDelete == 0);
            var containerMsts = NoTrackingDataContext.ContainerMsts.Where(x => x.HpId == hpId);
            List<KensaPrinterItemModel> allItems = new List<KensaPrinterItemModel>();



            foreach (var itemcd in itemcds)
            {

                var query = (from tenmst in tenmsts
                             where tenmst.ItemCd == itemcd
                             join kensaMst in kensaMsts on
                             new { tenmst.KensaItemCd, tenmst.KensaItemSeqNo } equals
                             new { kensaMst.KensaItemCd, kensaMst.KensaItemSeqNo } into tenMstKensas
                             from tenMstKensa in tenMstKensas.DefaultIfEmpty()
                             join containerMst in containerMsts on
                             tenMstKensa.ContainerCd equals containerMst.ContainerCd into tenMstKensaContainers
                             from tenMstKensaContainer in tenMstKensaContainers.DefaultIfEmpty()
                             select new
                             {
                                 tenmst.ItemCd,
                                 Name = tenMstKensaContainer == null ? tenmst.Name ?? string.Empty : tenMstKensaContainer.ContainerName,
                                 ContainerName = tenMstKensaContainer == null ? string.Empty : tenMstKensaContainer.ContainerName,
                                 ContainerCd = tenMstKensaContainer == null ? 0 : tenMstKensaContainer.ContainerCd,
                                 tenmst.KensaLabel
                             }).ToList();
                allItems.AddRange(query.Select(x => new KensaPrinterItemModel(x.ItemCd, x.Name, x.ContainerName, x.KensaLabel, x.ContainerCd, defaultChecked)));
            }
            allItems = allItems.Where(mst => checkInHospital(mst.ItemCd)).ToList();
            var groupKensaExistContainerCds = allItems.Where(x => x.ContainerCd != 0).GroupBy(x => new { x.ContainerCd });
            foreach (var group in groupKensaExistContainerCds)
            {
                int maxKensaLabel = group.AsEnumerable().Any() ? group.AsEnumerable().Max(x => x.KensaLabel) : 0;
                var item = group.FirstOrDefault();
                item?.ChangeKensaLabel(maxKensaLabel);
                if (item != null)
                {
                    kensaItems.Add(item);
                }
            }

            var groupKensaNotExistContainercds = allItems.Where(x => x.ContainerCd == 0).GroupBy(x => x.ItemCd);
            foreach (var group in groupKensaNotExistContainercds)
            {
                var item = group.FirstOrDefault();
                if (item != null)
                {
                    kensaItems.Add(item);
                }
            }
            for (int i = 0; i < kensaItems.Count; i++)
            {
                if (i == 0)
                {
                    kensaItems[i].ChangeTextBoxBorderThickness(new Thickness(1, 0.5, 0, 0.5));
                    kensaItems[i].ChangeComboboxBorderThickness(new Thickness(1, 0, 0, 0));
                }
                else if (kensaItems.Count == i + 1)
                {
                    kensaItems[i].ChangeTextBoxBorderThickness(new Thickness(1, 1, 0, 0));
                    kensaItems[i].ChangeComboboxBorderThickness(new Thickness(1, 1, 0, 0));
                }
                else
                {
                    kensaItems[i].ChangeTextBoxBorderThickness(new Thickness(1, 1, 0, 0.5));
                    kensaItems[i].ChangeTextBoxBorderThickness(new Thickness(1, 1, 0, 0));
                }
                var odrInf = orderInfs.FirstOrDefault(o => o.Item4.Any(d => d.Item1 == kensaItems[i].ItemCd));
                if (odrInf == null) continue;
                kensaItems[i].ChangeInoutKbnOdrKouiKbn(odrInf.Item1, odrInf.Item2);
            }
            return kensaItems;
        }

        public bool SaveTreatmentDepartmentAndDoctor(int hpId, int userId, long ptId, long raiinNo, int treatmentDepartmentId, int tantoId)
        {
            var entity = TrackingDataContext.RaiinInfs.FirstOrDefault(item => item.HpId == hpId
                                                                            && item.PtId == ptId
                                                                            && item.RaiinNo == raiinNo
                                                                            && item.IsDeleted == DeleteTypes.None);
            if (entity != null)
            {
                entity.KaId = _commonRepository.GetKaIdByTreatmentDepartmentId(hpId, treatmentDepartmentId);
                entity.TreatmentDepartmentId = treatmentDepartmentId;
                entity.TantoId = tantoId;
                entity.UpdateDate = CIUtil.GetJapanDateTimeNow();
                entity.UpdateId = userId;
                return TrackingDataContext.SaveChanges() > 0;
            }
            return false;
        }

        private int GetHyoboSyounika(int hpId)
        {
            int ret = 0;

            List<KaMst> kaMstModels = NoTrackingDataContext.KaMsts.Where(p => p.HpId == hpId &&
                                                                                   p.IsDeleted == 0 &&
                                                                                   new List<string>() { "09", "17" }.Contains(p.ReceKaCd ?? string.Empty)
                                                                                   ).ToList();

            if (kaMstModels.Any())
            {
                ret = 1;
            }

            return ret;
        }
    }
}
