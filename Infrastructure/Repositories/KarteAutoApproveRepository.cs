using System.Linq.Dynamic.Core;
using Domain.Models.KarteApproval;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repositories;

public class KarteAutoApproveRepository : RepositoryBase, IKarteAutoApproveRepository
{

    public KarteAutoApproveRepository(ITenantProvider tenantProvider) : base(tenantProvider)
    {
    }

    public void AutoApproval()
    {
        DateTime nowUtc = DateTime.UtcNow;

        // Get all yesterday raiin info and map to new approval info entities
        var raiinInfs = NoTrackingDataContext.RaiinInfs
                .Where(item => item.IsDeleted == DeleteTypes.None && (
                                    item.CreateDate <= nowUtc ||
                                    item.UpdateDate <= nowUtc
                                ))
                .ToList();

        var raiinNoList = raiinInfs.Select(raiinInf => raiinInf.RaiinNo).Distinct();

        var validKarteEdition =
            TrackingDataContext.KarteEditions.Where(e => e.KarteStatus == 1 && raiinNoList.Contains(e.RaiinNo) && e.IsDeleted == DeleteTypes.None).ToList();


        // Get all yesterday approval info include both approved and have not approved
        var approvalInfoList = TrackingDataContext.ApprovalInfs
            .Where(item => item.UpdateDate <= nowUtc && item.UpdateDate <= nowUtc
            ).ToList();

        var updateApprovalInfList = new List<ApprovalInf>();
        foreach (var item in raiinInfs)
        {
            var existKarteEdition = validKarteEdition.Where(e =>  e.RaiinNo == item.RaiinNo && e.HpId == item.HpId).OrderByDescending(e => e.Edition).FirstOrDefault();
            if (existKarteEdition == null)
            {
                continue;
            }

            var existApprovalInfo =
                approvalInfoList.Where(a => a.HpId == item.HpId && a.RaiinNo == item.RaiinNo && a.SinDate == item.SinDate)
                    .OrderByDescending(a => a.UpdateDate).FirstOrDefault();

            // Add new when raiin info has no approval inf
            if (existApprovalInfo == null)
            {
                var addApprovalInf = TrackingDataContext.ApprovalInfs.Add(
                    new ApprovalInf() {
                        HpId = item.HpId,
                        PtId = item.PtId,
                        RaiinNo = item.RaiinNo,
                        SinDate = item.SinDate,
                        IsDeleted = DeleteTypes.None,
                        CreateId = item.TantoId,
                        CreateDate = CIUtil.GetJapanDateTimeNow()
                    });

                TrackingDataContext.SaveChanges();
                existKarteEdition.ApprovalId = addApprovalInf.Entity.Id;
                existKarteEdition.ApprovalDate = CIUtil.GetJapanDateTimeNow();
            }
            // Update deleted to 0 when raiin info has a deleted (waiting) approval inf
            else if (existApprovalInfo.IsDeleted == DeleteTypes.Deleted)
            {
                existApprovalInfo.IsDeleted = DeleteTypes.None;
                existApprovalInfo.UpdateId = item.TantoId;
                existApprovalInfo.UpdateDate = CIUtil.GetJapanDateTimeNow();
                updateApprovalInfList.Add(existApprovalInfo);
                existKarteEdition.ApprovalId = existApprovalInfo.Id;
                existKarteEdition.ApprovalDate = CIUtil.GetJapanDateTimeNow();
            }
            // If raiin inf has been approved, skip and go next
        }

        if (updateApprovalInfList.Any())
        {
            TrackingDataContext.ApprovalInfs.UpdateRange(updateApprovalInfList);
        }

        TrackingDataContext.SaveChanges();
    }

    public void ReleaseResource()
    {
        DisposeDataContext();
    }
}
