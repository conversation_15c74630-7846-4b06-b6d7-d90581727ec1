﻿using Domain.Models.Family;
using Domain.Models.KarteMedicalHistory;
using Helper.Common;
using Helper.Constants;
using Helper.Extension;
using Helper.Redis;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using System.Globalization;
using System.Linq;
using System.Text.Json;

namespace Infrastructure.Repositories.KarteMedicalHistory
{
    public class KarteMedicalHistoryRepository : RepositoryBase, IKarteMedicalHistoryRepository
    {
        private readonly string key;
        private readonly IDatabase _cache;
        private readonly IConfiguration _configuration;

        public KarteMedicalHistoryRepository(ITenantProvider tenantProvider, IConfiguration configuration) : base(tenantProvider)
        {
            key = GetDomainKey();
            _configuration = configuration;
            GetRedis();
            _cache = RedisConnectorHelper.Connection.GetDatabase();
        }

        public void GetRedis()
        {
            string connection = string.Concat(_configuration["Redis:RedisHost"], ":", _configuration["Redis:RedisPort"]);
            if (RedisConnectorHelper.RedisHost != connection)
            {
                RedisConnectorHelper.RedisHost = connection;
            }
        }

        public List<PtPregnancyRelatedModel> GetPregnantList(int hpId, long ptId)
        {
            List<PtPregnancyRelatedModel> PtPregnancyRelateds = NoTrackingDataContext.PtPregnancyRelateds.Where(x => x.PtId == ptId && x.HpId == hpId && x.IsDeleted == 0)
                                                                .AsEnumerable()
                                                                .OrderByDescending(x => x.CreateDate)
                                                                .Select(x => new PtPregnancyRelatedModel(
                                                                                 x.HpId,
                                                                                 x.PtId,
                                                                                 x.PregnancyStatus,
                                                                                 x.BreastfeedStatus,
                                                                                 x.IsDeleted
                                                                )).ToList();
            return PtPregnancyRelateds;
        }

        public List<PtSmokingRelatedModel> GetPtSocialHistoryList(int hpId, long ptId)
        {
            List<PtSmokingRelatedModel> ptSmokingRelateds;

            var patientBirthday = NoTrackingDataContext.PtInfs.Where(x => x.HpId == hpId && x.PtId == ptId)
                                                              .Select(p => p.Birthday)
                                                              .FirstOrDefault();

            ptSmokingRelateds = NoTrackingDataContext.PtSmokingRelateds.Where(x => x.PtId == ptId && x.HpId == hpId && x.IsDeleted == 0)
                                                            .AsEnumerable()
                                                            .OrderByDescending(x => x.CreateDate)
                                                            .Select(x =>
                                                            {
                                                                int totalSmokingDuration = getTotalSmokingDuration(patientBirthday, x.SmokingStartAge, x.SmokingEndAge);
                                                                return new PtSmokingRelatedModel(
                                                                    x.HpId,
                                                                    x.PtId,
                                                                    x.SmokingStatus,
                                                                    x.SmokingDetail,
                                                                    x.SmokingDailyCount,
                                                                    x.SmokingDuration,
                                                                    x.DrinkingFrequency,
                                                                    x.DrinkingAmount,
                                                                    x.DrinkingDetail,
                                                                    x.IsDeleted,
                                                                    x.SmokingStartAge,
                                                                    x.SmokingEndAge,
                                                                    getSmokingStartYear(patientBirthday, x.SmokingStartAge),
                                                                    getSmokingEndYear(patientBirthday, x.SmokingEndAge),
                                                                    totalSmokingDuration,
                                                                    getBrinkmanNumber(totalSmokingDuration, x.SmokingDailyCount),
                                                                    GetAge(patientBirthday)
                                                                );
                                                            }).ToList();
            return ptSmokingRelateds;
        }

        public List<PtFamilyRekiModel> GetFamilyList(int hpId, long ptId)
        {
            List<PtFamilyRekiModel> familyRekiModels = new List<PtFamilyRekiModel>();
            var ptFamilyRepo = NoTrackingDataContext.PtFamilyRekis.Where(item => item.HpId == hpId && item.PtId == ptId && item.IsDeleted == DeleteTypes.None)
                                                              .OrderByDescending(x => x.CreateDate)
                                                              .ToList();

            familyRekiModels = ptFamilyRepo.Select(data => new PtFamilyRekiModel(
                                               data.Id,
                                               data.ByomeiCd ?? string.Empty,
                                               data.Byomei ?? string.Empty,
                                               data.Cmt ?? string.Empty,
                                               data.SortNo,
                                               data.IsDeleted == DeleteTypes.None ? false : true,
                                               data.HpId,
                                               data.PtId,
                                               data.FamilyId,
                                               data.SeqNo,
                                               data.ByotaiCd ?? string.Empty,
                                               data.ZokugaraCd ?? string.Empty,
                                               data.ZokugaraElse ?? string.Empty
                                               )).ToList();

            return familyRekiModels;
        }

        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        private int getSmokingStartYear(int patientBirthday, int? smokingStartAge)
        {
            if (patientBirthday == 0 || smokingStartAge < 0)
            {
                return 0;
            }
            string WrkStr = patientBirthday.ToString("D8");
            DateTime.TryParseExact(WrkStr, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None,
                out DateTime BirthDate);
            return BirthDate.Year + (smokingStartAge ?? 0);
        }

        private int getSmokingEndYear(int patientBirthday, int? smokingEndAge)
        {
            if (patientBirthday == null || smokingEndAge < 0)
            {
                return 0;
            }

            DateTime currentDate = DateTime.Now;
            int sinDate = int.Parse(currentDate.ToString("yyyyMMdd"));
            int formattedDate = int.Parse(currentDate.ToString("MMdd"));
            string WrkStr = patientBirthday.ToString("D8");
            DateTime.TryParseExact(WrkStr, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime BirthDate);

            if (BirthDate.Month * 100 + BirthDate.Day <= formattedDate)
            {
                return BirthDate.Year + (smokingEndAge ?? CIUtil.SDateToAge(patientBirthday, sinDate));
            }
            else
            {
                return BirthDate.Year + (smokingEndAge ?? CIUtil.SDateToAge(patientBirthday, sinDate)) + 1;
            };
        }

        private int getTotalSmokingDuration(int patientBirthday, int? smokingStartAge, int? smokingEndAge)
        {
            DateTime currentDate = DateTime.Now;
            int sinDate = int.Parse(currentDate.ToString("yyyyMMdd"));
            if (patientBirthday == null || smokingStartAge < 0 || (smokingStartAge == null && smokingEndAge == null))
            {
                return 0;
            }

            if (smokingEndAge == null || smokingEndAge <= 0)
            {
                return CIUtil.SDateToAge(patientBirthday, sinDate) - (smokingStartAge ?? 0);
            }

            return (smokingEndAge ?? CIUtil.SDateToAge(patientBirthday, sinDate)) - (smokingStartAge ?? 0);
        }

        private long getBrinkmanNumber(int totalSmokingDuration, int numberOfCigarettesPerDay)
        {
            if (numberOfCigarettesPerDay == null || numberOfCigarettesPerDay <= 0 || totalSmokingDuration <= 0)
            {
                return 0;
            }

            return numberOfCigarettesPerDay.AsLong() * totalSmokingDuration.AsLong();
        }

        private int GetAge(int patientBirthday)
        {
            DateTime currentDate = DateTime.Now;
            int sinDate = int.Parse(currentDate.ToString("yyyyMMdd"));

            return CIUtil.SDateToAge(patientBirthday, sinDate);
        }
    }
}