﻿using Domain.Models.Family;
using Domain.Models.KarteMedicalHistory;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Redis;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;

namespace Infrastructure.Repositories.KarteMedicalHistory
{
    public class KarteMedicalHistoryFamilyRepository : RepositoryBase, IKarteMedicalHistoryFamilyRepository
    {
        private readonly string key;
        private readonly IDatabase _cache;
        private readonly IConfiguration _configuration;

        public KarteMedicalHistoryFamilyRepository(ITenantProvider tenantProvider, IConfiguration configuration) : base(tenantProvider)
        {
            key = GetDomainKey();
            _configuration = configuration;
            GetRedis();
            _cache = RedisConnectorHelper.Connection.GetDatabase();
        }

        public void GetRedis()
        {
            string connection = string.Concat(_configuration["Redis:RedisHost"], ":", _configuration["Redis:RedisPort"]);
            if (RedisConnectorHelper.RedisHost != connection)
            {
                RedisConnectorHelper.RedisHost = connection;
            }
        }


        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        public bool SavePtFamilies(int userId, int hpId, long ptId, List<PtFamilyRekiModel> ptFamilyRekis)
        {
            var listId = ptFamilyRekis.Select(item => item.Id).ToList();
            var listFamilyDB = TrackingDataContext.PtFamilyRekis.Where(item => item.HpId == hpId && listId.Contains(item.Id) && item.IsDeleted != DeleteTypes.Deleted);

            foreach (var familyModel in ptFamilyRekis)
            {
                if (familyModel.Id <= 0 && !familyModel.IsDeleted)
                {
                    var ptFamilyEntity = ConvertToNewPtFamilyReki(hpId, userId, familyModel);
                    TrackingDataContext.PtFamilyRekis.Add(ptFamilyEntity);
                    TrackingDataContext.SaveChanges();
                }
                else
                {
                    var ptFamilyEntity = listFamilyDB.FirstOrDefault(item => item.Id == familyModel.Id);
                    if (ptFamilyEntity == null)
                    {
                        continue;
                    }
                    ptFamilyEntity.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    ptFamilyEntity.UpdateId = userId;
                    if (familyModel.IsDeleted)
                    {
                        ptFamilyEntity.IsDeleted = DeleteTypes.Deleted;
                        continue;
                    }
                    ptFamilyEntity.ZokugaraCd = familyModel.ZokugaraCd;
                    ptFamilyEntity.SortNo = familyModel.SortNo;
                    ptFamilyEntity.FamilyId = familyModel.FamilyId;
                    ptFamilyEntity.Cmt = familyModel.Cmt;
                    ptFamilyEntity.Byomei = familyModel.Byomei;
                    ptFamilyEntity.ZokugaraCd = familyModel.ZokugaraCd;
                    ptFamilyEntity.ZokugaraElse = familyModel.ZokugaraElse;
                    ptFamilyEntity.ByomeiCd = familyModel.ByomeiCd;
                    ptFamilyEntity.ByotaiCd = familyModel.ByotaiCd;
                }
            }
            TrackingDataContext.SaveChanges();
            return true;
        }

        private PtFamilyReki ConvertToNewPtFamilyReki(int hpId, int userId, PtFamilyRekiModel model)
        {
            PtFamilyReki ptFamilyReki = new();
            ptFamilyReki.FamilyId = 0;
            ptFamilyReki.HpId = hpId;
            ptFamilyReki.PtId = model.PtId;
            ptFamilyReki.ZokugaraCd = model.ZokugaraCd;
            ptFamilyReki.SortNo = model.SortNo;
            ptFamilyReki.Id = 0;
            ptFamilyReki.Cmt = model.Cmt;
            ptFamilyReki.CreateDate = CIUtil.GetJapanDateTimeNow();
            ptFamilyReki.CreateId = userId;
            ptFamilyReki.UpdateDate = CIUtil.GetJapanDateTimeNow();
            ptFamilyReki.UpdateId = userId;
            ptFamilyReki.IsDeleted = DeleteTypes.None;
            ptFamilyReki.ZokugaraElse = model.ZokugaraElse;
            ptFamilyReki.Byomei = model.Byomei;
            ptFamilyReki.ByomeiCd = model.ByomeiCd;
            ptFamilyReki.ByotaiCd = model.ByotaiCd;
            return ptFamilyReki;
        }
    }
}