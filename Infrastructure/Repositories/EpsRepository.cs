﻿using Domain.Constant;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Globalization;
using Domain.Enum;
using Domain.Models.Eps;
using Domain.Models.Eps;
using Domain.Models.Eps.CheckErrorPreRegistration;
using Domain.Models.EpsDispensing;
using Domain.Models.EpsPrescription;
using Domain.Models.Insurance;
using Domain.Models.PatientInfor;
using Domain.Models.SystemConf;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Extension;
using Helper.Mapping;
using Helper.Redis;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using System.Globalization;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using PtHokenPatternModel = Domain.Models.Receipt.Recalculation.PtHokenPatternModel;
using Domain.Models.EpsDispensingReq;
using Domain.Models.EpsReq;
using Domain.Models.EpsReference;
using Microsoft.AspNetCore.Mvc;
using Domain.Constant;
using Domain.Models.Insurance.AIChat;
using System.Runtime.InteropServices;
using Npgsql;

namespace Infrastructure.Repositories
{
    public class EpsRepository : RepositoryBase, IEpsRepository
    {

        private readonly string key;
        private readonly IDatabase _cache;
        private readonly IConfiguration _configuration;
        private readonly ISystemConfRepository _systemConfRepository;

        public EpsRepository(ITenantProvider tenantProvider, IConfiguration configuration,
            ISystemConfRepository systemConfRepository) : base(tenantProvider)
        {
            key = GetDomainKey();
            _configuration = configuration;
            GetRedis();
            _cache = RedisConnectorHelper.Connection.GetDatabase();
            _systemConfRepository = systemConfRepository;
        }

        public void GetRedis()
        {
            string connection =
                string.Concat(_configuration["Redis:RedisHost"], ":", _configuration["Redis:RedisPort"]);
            if (RedisConnectorHelper.RedisHost != connection)
            {
                RedisConnectorHelper.RedisHost = connection;
            }
        }

        public ValidateBeforePrintingModel ValidateBeforePrinting(int hpId, long ptId, long raiinNo, int sinDate, int userId)
        {
            var raiinInf = NoTrackingDataContext.RaiinInfs.FirstOrDefault(r => r.HpId == hpId && r.PtId == ptId && r.RaiinNo == raiinNo && r.SinDate == sinDate);
            if (raiinInf == null)
            {
                return new ValidateBeforePrintingModel();
            }
            RaiinInfModel raiinInfModel = new RaiinInfModel(
                raiinInf.HpId,
                raiinInf.PtId,
                raiinInf.SinDate,
                raiinInf.RaiinNo,
                raiinInf.HokenPid,
                raiinInf.PrintEpsReference,
                raiinInf.PrescriptionIssueType
            );

            var epsPrescriptions = NoTrackingDataContext.EpsPrescriptions
                .Where(e => e.HpId == hpId && e.PtId == ptId && e.RaiinNo == raiinNo && e.SinDate == sinDate && e.Status == 0)
                .Select(e => new EpsPrescriptionModel(
                    e.HpId,
                    e.PtId,
                    e.RaiinNo,
                    e.SeqNo,
                    e.RefileCount,
                    e.SinDate,
                    e.HokensyaNo,
                    e.Kigo,
                    e.Bango,
                    e.EdaNo,
                    e.KohiFutansyaNo,
                    e.KohiJyukyusyaNo,
                    e.PrescriptionId,
                    e.AccessCode,
                    e.IssueType,
                    e.PrescriptionDocument,
                    e.Status,
                    e.DeletedReason,
                    e.DeletedDate,
                    e.KaId,
                    e.TantoId,
                    e.CreateDate,
                    e.CreateId,
                    e.UpdateDate,
                    e.UpdateId,
                    new()
                )).ToList();



            var epsPrescriptionIds = epsPrescriptions.Select(e => e.PrescriptionId);
            var epsDispensings = NoTrackingDataContext.EpsDispensings.Where(e => e.HpId == hpId && e.PtId == ptId && epsPrescriptionIds.Contains(e.PrescriptionId) && e.IsDeleted == DeleteTypes.None);
            foreach (var epsPrescription in epsPrescriptions)
            {
                foreach (var epsDispensing in epsDispensings)
                {
                    if (epsPrescription.PrescriptionId == epsDispensing.PrescriptionId)
                    {
                        epsPrescription.EpsDispensingModel.Add(Mapper.Map(epsDispensing, new EpsDispensingModel()));
                    }
                }
            }

            var epsReferences = NoTrackingDataContext.EpsReferences
                .Where(e => e.HpId == hpId && e.PtId == ptId && e.RaiinNo == raiinNo && e.SinDate == sinDate)
                .Select(e => new EpsReferenceModel(
                    e.HpId,
                    e.PtId,
                    e.RaiinNo,
                    e.SinDate,
                    e.PrescriptionId,
                    e.PrescriptionReferenceInformation,
                    e.CreateDate,
                    e.CreateId,
                    e.CreateMachine
                )).ToList();

            var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns
                .Join(NoTrackingDataContext.OdrInfs, h => new { h.HpId, h.PtId, h.HokenPid }, o => new { o.HpId, o.PtId, o.HokenPid }, (h, o) => new { h, o })
                .Where(x => x.o.RaiinNo == raiinNo)
                .Select(x => new PtHokenPtn(
                    x.h.PtId,
                    x.h.HokenPid,
                    x.h.SeqNo,
                    x.h.HokenKbn,
                    x.h.HokenSbtCd,
                    x.h.HokenId,
                    x.h.Kohi1Id,
                    x.h.Kohi2Id,
                    x.h.Kohi3Id,
                    x.h.Kohi4Id,
                    x.h.HokenMemo ?? string.Empty,
                    x.h.StartDate,
                    x.h.EndDate
                )).ToList();

            List<string> checkInsurances = new()
            {
                "1",
                "2",
                "3",
                "4"
            };

            var notPublicInsurances = ptHokenPatterns.Where(x => checkInsurances.Any(prefix => x.HokenSbtCd.ToString().StartsWith(prefix)))
                                                     .ToList();
            var publicInsurances = ptHokenPatterns.Where(x => x.HokenSbtCd == 5).ToList();

            List<HokenInfModel> hokenInfModels = new();
            foreach (var notPublicInsurance in notPublicInsurances)
            {
                var hokenInf = NoTrackingDataContext.PtHokenInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId && x.HokenId == notPublicInsurance.HokenId) ?? new();
                hokenInfModels.Add(new HokenInfModel(hokenInf.PtId, hokenInf.HokenId, hokenInf.SeqNo, hokenInf.HokenNo, hokenInf.HokenEdaNo, hokenInf.HokenKbn, hokenInf.HokensyaNo ?? string.Empty, hokenInf.Kigo ?? string.Empty, hokenInf.Bango ?? string.Empty, hokenInf.EdaNo ?? string.Empty, hokenInf.HonkeKbn, hokenInf.KogakuKbn));
            }

            List<KohiInfDto> ptKohis = new();
            foreach (var publicInsurance in publicInsurances)
            {
                var ptKohi = NoTrackingDataContext.PtKohis.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId && x.HokenId == publicInsurance.HokenId) ?? new();
                ptKohis.Add(new KohiInfDto(ptKohi.FutansyaNo ?? string.Empty, ptKohi.JyukyusyaNo ?? string.Empty, ptKohi.HokenId, publicInsurance.HokenKbn));
            }

            foreach (var epsPrescription in epsPrescriptions)
            {
                List<int> hokenKbnInfs = hokenInfModels.Where(x => x.HokensyaNo == epsPrescription.HokensyaNo && x.Kigo == epsPrescription.Kigo && x.Bango == x.Bango && x.EdaNo == epsPrescription.EdaNo)
                                                       .Select(x => x.HokenKbn)
                                                       .Distinct()
                                                       .ToList();
                if (hokenKbnInfs.Count() != 0)
                {
                    epsPrescription.ChangeHokenKbns(hokenKbnInfs);
                }

                List<int> hokenKbnKohis = ptKohis.Where(x => x.FutansyaNo == epsPrescription.KohiFutansyaNo && x.JyukyusyaNo == epsPrescription.KohiJyukyusyaNo)
                                                 .Select(x => x.HokenKbn)
                                                 .Distinct()
                                                 .ToList();
                if (hokenKbnKohis.Count() != 0)
                {
                    epsPrescription.ChangeHokenKbns(hokenKbnKohis);
                }
            }

            return new ValidateBeforePrintingModel(raiinInfModel, epsPrescriptions, epsReferences, ptHokenPatterns);
        }

        public InsuranceModel GetInsuranceInf(int hpId, long ptId, long raiinNo, int sinDate)
        {
            var raiinInf = NoTrackingDataContext.RaiinInfs.FirstOrDefault(r => r.HpId == hpId && r.PtId == ptId && r.RaiinNo == raiinNo && r.SinDate == sinDate);
            if (raiinInf == null)
            {
                return new InsuranceModel();
            }
            RaiinInfModel raiinInfModel = new RaiinInfModel(
                raiinInf.HpId,
                raiinInf.PtId,
                raiinInf.SinDate,
                raiinInf.RaiinNo,
                raiinInf.HokenPid,
                raiinInf.PrintEpsReference,
                raiinInf.PrescriptionIssueType
            );

            var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns
                .Where(pattern => 
                    pattern.HpId == hpId
                    && pattern.PtId == ptId
                    && pattern.IsDeleted == 0
                    && pattern.StartDate <= sinDate
                    && pattern.EndDate >= sinDate
                ).ToList();

            List<int> hokenIds = ptHokenPatterns.Select(pattern => pattern.HokenId).Distinct().ToList();
            List<int> kohiIds = ptHokenPatterns.SelectMany(pattern => new List<int> { pattern.Kohi1Id, pattern.Kohi2Id, pattern.Kohi3Id, pattern.Kohi4Id }).Where(id => id > 0).Distinct().ToList();

            var ptHokenInfs = NoTrackingDataContext.PtHokenInfs
                .Where(hoken => 
                    hoken.HpId == hpId
                    && hoken.PtId == ptId
                    && hokenIds.Contains(hoken.HokenId)
                    && hoken.IsDeleted == 0
                    && hoken.StartDate <= sinDate
                    && hoken.EndDate >= sinDate
                )
                .ToDictionary(hoken => hoken.HokenId, hoken => new InsurancePtHokenInfModel(
                    hoken.HpId,
                    hoken.PtId,
                    hoken.HokenId,
                    hoken.SeqNo,
                    hoken.HokenNo,
                    hoken.EdaNo,
                    hoken.HokenEdaNo,
                    hoken.HokensyaNo,
                    hoken.Kigo,
                    hoken.Bango,
                    hoken.HonkeKbn,
                    hoken.HokenKbn,
                    hoken.Houbetu,
                    hoken.HokensyaName,
                    hoken.HokensyaPost,
                    hoken.HokensyaAddress,
                    hoken.HokensyaTel,
                    hoken.KeizokuKbn,
                    hoken.SikakuDate,
                    hoken.KofuDate,
                    hoken.StartDate,
                    hoken.EndDate,
                    hoken.Rate,
                    hoken.Gendogaku,
                    hoken.KogakuKbn,
                    hoken.KogakuType,
                    hoken.TokureiYm1,
                    hoken.TokureiYm2,
                    hoken.TasukaiYm,
                    hoken.SyokumuKbn,
                    hoken.GenmenKbn,
                    hoken.GenmenRate,
                    hoken.GenmenGaku,
                    hoken.Tokki1,
                    hoken.Tokki2,
                    hoken.Tokki3,
                    hoken.Tokki4,
                    hoken.Tokki5,
                    hoken.RousaiKofuNo,
                    hoken.RousaiSaigaiKbn,
                    hoken.RousaiJigyosyoName,
                    hoken.RousaiPrefName,
                    hoken.RousaiCityName,
                    hoken.RousaiSyobyoDate,
                    hoken.RousaiSyobyoCd,
                    hoken.RousaiRoudouCd,
                    hoken.RousaiKantokuCd,
                    hoken.RousaiReceCount,
                    hoken.RyoyoStartDate,
                    hoken.RyoyoEndDate,
                    hoken.JibaiHokenName,
                    hoken.JibaiHokenTanto,
                    hoken.JibaiHokenTel,
                    hoken.JibaiJyusyouDate,
                    hoken.IsDeleted,
                    hoken.CreateDate,
                    hoken.CreateId,
                    hoken.CreateMachine,
                    hoken.UpdateDate,
                    hoken.UpdateId,
                    hoken.UpdateMachine,
                    hoken.InsuredName
                ));

            var ptKohis = NoTrackingDataContext.PtKohis
                .Where(kohi => 
                    kohi.HpId == hpId
                    && kohi.PtId == ptId
                    && kohiIds.Contains(kohi.HokenId)
                    && kohi.IsDeleted == 0
                    && kohi.StartDate <= sinDate
                    && kohi.EndDate >= sinDate
                )
                .ToDictionary(kohi => kohi.HokenId, kohi => new InsurancePtKohiModel(
                    kohi.HpId,
                    kohi.PtId,
                    kohi.HokenId,
                    kohi.SeqNo,
                    kohi.PrefNo,
                    kohi.HokenNo,
                    kohi.HokenEdaNo,
                    kohi.FutansyaNo ?? string.Empty,
                    kohi.JyukyusyaNo ?? string.Empty,
                    kohi.TokusyuNo ?? string.Empty,
                    kohi.SikakuDate,
                    kohi.KofuDate,
                    kohi.StartDate,
                    kohi.EndDate,
                    kohi.Rate,
                    kohi.GendoGaku,
                    kohi.IsDeleted,
                    kohi.CreateDate,
                    kohi.CreateId,
                    kohi.CreateMachine ?? string.Empty,
                    kohi.UpdateDate,
                    kohi.UpdateId,
                    kohi.UpdateMachine ?? string.Empty,
                    kohi.HokenSbtKbn,
                    kohi.Houbetu ?? string.Empty,
                    kohi.Birthday
                ));

            List<InsurancePtHokenPatternModel> ptHokenPatternModels = new List<InsurancePtHokenPatternModel>();
            foreach (var ptHokenPattern in ptHokenPatterns)
            {
                InsurancePtHokenPatternModel ptHokenPatternModel = new InsurancePtHokenPatternModel(
                    ptHokenPattern.PtId,
                    ptHokenPattern.HokenPid,
                    ptHokenPattern.SeqNo,
                    ptHokenPattern.HokenKbn,
                    ptHokenPattern.HokenSbtCd,
                    ptHokenPattern.HokenId,
                    ptHokenPattern.Kohi1Id,
                    ptHokenPattern.Kohi2Id,
                    ptHokenPattern.Kohi3Id,
                    ptHokenPattern.Kohi4Id,
                    ptHokenPattern.HokenMemo ?? string.Empty,
                    ptHokenPattern.StartDate,
                    ptHokenPattern.EndDate,
                    ptHokenInfs.GetValueOrDefault(ptHokenPattern.HokenId),
                    ptKohis.GetValueOrDefault(ptHokenPattern.Kohi1Id),
                    ptKohis.GetValueOrDefault(ptHokenPattern.Kohi2Id),
                    ptKohis.GetValueOrDefault(ptHokenPattern.Kohi3Id),
                    ptKohis.GetValueOrDefault(ptHokenPattern.Kohi4Id)
                );
                ptHokenPatternModels.Add(ptHokenPatternModel);
            }

            // Điều kiện: chưa thực hiện confirm online vào ngày sin date đó
            // Không có giá trị online_confirmation_date=<sin_date> and confirmation_type>=1 trong online_confirmation_history
            DateTime sinDateTime = DateTime.ParseExact(sinDate.ToString(), "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal).ToUniversalTime();

            var onlineConfirmInf = NoTrackingDataContext.OnlineConfirmationHistories
                .Where(o => 
                    o.HpId == hpId 
                    && o.PtId == ptId 
                    && o.ConfirmationType >= 1
                    && o.OnlineConfirmationDate.Date == sinDateTime.Date
                )
                .FirstOrDefault();
            bool wasConfirmedOnline = onlineConfirmInf != null;
            
            return new InsuranceModel(
                raiinInfModel,
                ptHokenPatternModels,
                wasConfirmedOnline
            );
        }

        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        public PreRegistrationCheckingModel? GetPreRegistrationCheckingData(int hpId, long ptId, long raiinNo,
    int sinDate, List<int> statusList, List<EpsOrderInfModel> odrInfs)
        {
            var result = new PreRegistrationCheckingModel
            {
                EpsPrescriptionModel = new List<EpsPrescriptionModel>(),
                OdrInfs = new List<EpsOrderInfModel>()
            };

            var epsPrescriptionsQuery = NoTrackingDataContext.EpsPrescriptions
                .Where(e => e.HpId == hpId &&
                            e.PtId == ptId &&
                            (raiinNo == 0 || e.RaiinNo == raiinNo) &&
                            (sinDate == 0 || e.SinDate == sinDate));

            if (statusList.Any())
            {
                epsPrescriptionsQuery = epsPrescriptionsQuery.Where(e => statusList.Contains(e.Status));
            }
            else
            {
                epsPrescriptionsQuery = epsPrescriptionsQuery.Where(e => e.Status == 0);
            }

            var epsPrescriptions = epsPrescriptionsQuery.ToList();

            result.EpsPrescriptionModel = Mapper.Map<EpsPrescription, EpsPrescriptionModel>(epsPrescriptions);

            var epsPrescriptionIds = epsPrescriptions.Select(e => e.PrescriptionId).ToList();

            var epsDispensings = NoTrackingDataContext.EpsDispensings
                .Where(e => e.HpId == hpId &&
                            e.PtId == ptId &&
                            epsPrescriptionIds.Contains(e.PrescriptionId) &&
                            e.IsDeleted == DeleteTypes.None)
                .ToList();

            var dispensingsByPrescriptionId = epsDispensings
                .GroupBy(d => d.PrescriptionId)
                .ToDictionary(g => g.Key, g => g.Select(d => Mapper.Map(d, new EpsDispensingModel())).ToList());

            foreach (var epsPrescription in result.EpsPrescriptionModel)
            {
                if (dispensingsByPrescriptionId.TryGetValue(epsPrescription.PrescriptionId, out var models))
                {
                    epsPrescription.EpsDispensingModel = models;
                }
            }

            if (odrInfs?.Any() != true) return result;

            var hokenPids = odrInfs.Select(o => o.HokenPid).Distinct().ToList();

            var allPtHokenPatterns = NoTrackingDataContext.PtHokenPatterns
                .Where(p => p.HpId == hpId &&
                            p.PtId == ptId &&
                            hokenPids.Contains(p.HokenPid) &&
                            p.HokenSbtCd > 0 &&
                            p.IsDeleted == DeleteTypes.None)
                .ToList();

            var hokenPatternDict = allPtHokenPatterns.ToDictionary(p => p.HokenPid);
            var hokenIds = allPtHokenPatterns.Select(p => p.HokenId).Distinct().ToList();

            var allPtHokenInfs = hokenIds.Any()
                ? NoTrackingDataContext.PtHokenInfs
                    .Where(p => p.HpId == hpId &&
                                p.PtId == ptId &&
                                hokenIds.Contains(p.HokenId) &&
                                p.IsDeleted == DeleteTypes.None)
                    .ToDictionary(p => p.HokenId)
                : new Dictionary<int, PtHokenInf>();

            var allKohiIds = allPtHokenPatterns
                .SelectMany(p => new[] { p.Kohi1Id, p.Kohi2Id, p.Kohi3Id, p.Kohi4Id })
                .Where(id => id > 0)
                .Distinct()
                .ToList();

            var kohiDict = allKohiIds.Any()
                ? NoTrackingDataContext.PtKohis
                    .Where(p => p.HpId == hpId &&
                                p.PtId == ptId &&
                                allKohiIds.Contains(p.HokenId) &&
                                p.IsDeleted == DeleteTypes.None)
                    .GroupBy(k => k.HokenId)
                    .ToDictionary(g => g.Key, g => g.ToList())
                : new Dictionary<int, List<PtKohi>>();

            var externalMedicineItemCds = NoTrackingDataContext.TenMsts
                .Where(t => t.HpId == hpId &&
                            (t.DrugKbn > 0 || t.MasterSbt == "Y" || t.MasterSbt == "T" || t.ItemCd.StartsWith("Z")) &&
                            t.IsDeleted == DeleteTypes.None)
                .Select(t => t.ItemCd)
                .ToHashSet();

            var allOdrInfDetails = new List<OdrInfDetail>();
            var odrDetailDict = new Dictionary<string, OdrInfDetail>();

            if (raiinNo > 0)
            {
                var rpCombinations = odrInfs.Select(o => new { o.RpNo, o.RpEdaNo }).Distinct().ToList();

                allOdrInfDetails = NoTrackingDataContext.OdrInfDetails
                    .Where(o => o.HpId == hpId &&
                                o.RaiinNo == raiinNo &&
                                o.SyohoKbn == 3)
                    .ToList()
                    .Where(o => rpCombinations.Any(rp => rp.RpNo == o.RpNo && rp.RpEdaNo == o.RpEdaNo))
                    .ToList();

                odrDetailDict = allOdrInfDetails.ToDictionary(o => $"{o.RpNo}_{o.RpEdaNo}");
            }

            var itemCds = allOdrInfDetails.Select(o => o.ItemCd).Where(cd => !string.IsNullOrEmpty(cd)).Distinct().ToList();
            var ipnCds = allOdrInfDetails.Select(o => o.IpnCd).Where(cd => !string.IsNullOrEmpty(cd)).Distinct().ToList();

            var ipnKasanExcludeItemSet = itemCds.Any()
                ? NoTrackingDataContext.ipnKasanExcludeItems
                    .Where(u => u.StartDate <= sinDate &&
                                u.EndDate >= sinDate &&
                                itemCds.Contains(u.ItemCd))
                    .Select(u => u.ItemCd)
                    .ToHashSet()
                : new HashSet<string>();

            var ipnKasanExcludeSet = ipnCds.Any()
                ? NoTrackingDataContext.ipnKasanExcludes
                    .Where(u => u.StartDate <= sinDate &&
                                u.EndDate >= sinDate &&
                                ipnCds.Contains(u.IpnNameCd))
                    .Select(u => u.IpnNameCd)
                    .ToHashSet()
                : new HashSet<string>();

            string[] hokenSbtCdList = { "1", "2", "3", "4", "5" };
            int[] odrKouiKbnList = { 21, 22, 23, 28 };
            var odrKouiKbnSet = odrKouiKbnList.ToHashSet();

            foreach (var odrInf in odrInfs)
            {
                if (hokenPatternDict.TryGetValue(odrInf.HokenPid, out var ptHokenPattern))
                {
                    var hokenSbtFirstChar = ptHokenPattern.HokenSbtCd.ToString()[0].ToString();
                    if (hokenSbtCdList.Contains(hokenSbtFirstChar))
                    {
                        odrInf.PtHokenPatternModel = Mapper.Map(ptHokenPattern, new PtHokenPatternModel());

                        if (hokenSbtFirstChar != "5")
                        {
                            if (allPtHokenInfs.TryGetValue(ptHokenPattern.HokenId, out var ptHokenInfs))
                            {
                                odrInf.HokenInfModel = Mapper.Map(ptHokenInfs, new HokenInfModel());
                            }
                        }
                        else
                        {
                            int[] listKohiId = {
                        ptHokenPattern.Kohi1Id, ptHokenPattern.Kohi2Id,
                        ptHokenPattern.Kohi3Id, ptHokenPattern.Kohi4Id
                    };

                            var ptKohis = listKohiId
                                .Where(id => id > 0 && kohiDict.ContainsKey(id))
                                .SelectMany(id => kohiDict[id])
                                .ToList();

                            if (ptKohis.Any())
                            {
                                odrInf.KohiInfModel = ptKohis.Select(ptKohi => Mapper.Map(ptKohi, new KohiInfModel())).ToList();
                            }
                        }
                    }
                }

                odrInf.OrderDetailsNotContainMedicine = new List<EpsOrdInfDetailModel>();
                if (odrKouiKbnSet.Contains(odrInf.OdrKouiKbn) && odrInf.InoutKbn == 1)
                {
                    odrInf.OrderDetailsNotContainMedicine = odrInf.OrderDetails
                        .Where(d => !externalMedicineItemCds.Contains(d.ItemCd))
                        .Select(d => Mapper.Map(d, new EpsOrdInfDetailModel()))
                        .ToList();
                }

                var detailKey = $"{odrInf.RpNo}_{odrInf.RpEdaNo}";
                if (odrDetailDict.TryGetValue(detailKey, out var odrInfDetail))
                {
                    if (string.IsNullOrEmpty(odrInfDetail.IpnName) ||
                        ipnKasanExcludeItemSet.Contains(odrInfDetail.ItemCd) ||
                        ipnKasanExcludeSet.Contains(odrInfDetail.IpnCd))
                    {
                        odrInf.OrderContainsCommonNameNotInCommonNameMedicines = true;
                    }
                }

                result.OdrInfs.Add(odrInf);
            }

            var onlineConfirmInf = NoTrackingDataContext.OnlineConfirmationHistories
                .Where(o => o.HpId == hpId && o.PtId == ptId && o.ConfirmationType >= 1).ToList()
                .Where(o => o.OnlineConfirmationDate.ToString("yyyyMMdd") == sinDate.ToString()).ToList();
            if (onlineConfirmInf.Any())
            {
                result.WasConfirmedOnline = true;
            }

            return result;
        }

        public (List<EpsDispensingModel>, List<EpsPrescriptionModel>) GetDispensingInfList(int hpId, long ptId)
        {
            List<EpsDispensingModel> epsDispensingModels = new List<EpsDispensingModel>();
            List<EpsPrescriptionModel> epsPrescriptionModels = new List<EpsPrescriptionModel>();
            var epsDispensings = NoTrackingDataContext.EpsDispensings
                .Where(x => x.HpId == hpId && x.PtId == ptId
                            && x.ResultType != 1 
                            && x.ResultType != 3
                            && x.IsDeleted == DeleteTypes.None
                ).ToList();
            var epsPrescriptions = NoTrackingDataContext.EpsPrescriptions
                .Where(x => x.HpId == hpId && x.PtId == ptId).ToList();

            if (epsDispensings.Any())
            {
                epsDispensingModels = Mapper.Map<EpsDispensing, EpsDispensingModel>(epsDispensings);
            }
            if (epsPrescriptions.Any())
            {
                epsPrescriptionModels = Mapper.Map<EpsPrescription, EpsPrescriptionModel>(epsPrescriptions);
            }

            return (epsDispensingModels, epsPrescriptionModels);
        }

        public bool UpdatePrescriptionStatus(int hpId, long ptId, long raiinNo, long seqNo, int status, int userId,
            int? deletedReason = null)
        {
            var epsPrescription = TrackingDataContext.EpsPrescriptions
                .FirstOrDefault(x => x.HpId == hpId &&
                                     x.PtId == ptId &&
                                     x.RaiinNo == raiinNo &&
                                     x.SeqNo == seqNo);
            if (epsPrescription == null) return false;
            epsPrescription.Status = status;
            if (deletedReason != null) epsPrescription.DeletedReason = deletedReason.Value;
            epsPrescription.UpdateDate = CIUtil.GetJapanDateTimeNow();
            epsPrescription.UpdateId = userId;
            epsPrescription.UpdateMachine = CIUtil.GetComputerName();
            epsPrescription.CreateDate = epsPrescription.CreateDate.ToUniversalTime();
            TrackingDataContext.EpsPrescriptions.Update(epsPrescription);
            TrackingDataContext.SaveChanges();
            return true;
        }

        public bool UpdatePrescriptionStatus(int hpId, long ptId, long raiinNo, int status, int userId,
            int? deletedReason = null)
        {
            var epsPrescriptions = TrackingDataContext.EpsPrescriptions
                .Where(x => x.HpId == hpId &&
                            x.PtId == ptId &&
                            x.RaiinNo == raiinNo)
                .ToList();

            foreach (var epsPrescription in epsPrescriptions)
            {
                if (epsPrescription == null) return false;
                if (deletedReason != null) epsPrescription.DeletedReason = deletedReason.Value;
                epsPrescription.Status = status;
                epsPrescription.UpdateDate = CIUtil.GetJapanDateTimeNow();
                epsPrescription.UpdateId = userId;
                epsPrescription.UpdateMachine = CIUtil.GetComputerName();
                epsPrescription.CreateDate = epsPrescription.CreateDate.ToUniversalTime();
            }

            TrackingDataContext.EpsPrescriptions.UpdateRange(epsPrescriptions);
            TrackingDataContext.SaveChanges();
            return true;
        }

        public bool CreateEpsReference(int hpId, int userId, long ptId, long raiinNo, int sinDate,
            string prescriptionId, string prescriptionReferenceInformation)
        {
            var epsReference = new EpsReference()
            {
                HpId = hpId,
                PtId = ptId,
                RaiinNo = raiinNo,
                SinDate = sinDate,
                PrescriptionId = prescriptionId,
                PrescriptionReferenceInformation = prescriptionReferenceInformation,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId,
                CreateMachine = CIUtil.GetComputerName(),
            };

            TrackingDataContext.EpsReferences.Add(epsReference);
            TrackingDataContext.SaveChanges();
            return true;
        }

        public List<string> GetPrescriptionIdList(int hpId, int userId, long ptId, long raiinNo,
            List<int> prescriptionStatus, int? issueType, int? refillCount)
        {
            var isExistedRaiinNo = NoTrackingDataContext.RaiinInfs
                .FirstOrDefault(x => x.HpId == hpId &&
                                     x.RaiinNo == raiinNo &&
                                     x.IsDeleted == DeleteTypes.None);

            if (isExistedRaiinNo == null) return new List<string>();

            var prescriptionIds = NoTrackingDataContext.EpsPrescriptions
                .Where(x => x.HpId == hpId &&
                            x.PtId == ptId &&
                            x.RaiinNo == raiinNo &&
                            prescriptionStatus.Contains(x.Status) &&
                            !string.IsNullOrEmpty(x.PrescriptionId) &&
                            ((issueType == null && refillCount == null) || (x.IssueType != issueType) ||
                             (x.RefileCount != refillCount)))
                .Select(x => x.PrescriptionId)
                .ToList();
            return prescriptionIds;
        }

        public List<SaveEpsPrescriptionInfoModel> SavePrescriptionInfo(int userId, int hpId, List<SaveEpsPrescriptionInfoModel> models)
        {
            List<EpsPrescription> addNewList = new List<EpsPrescription>();
            List<EpsPrescription> updateList = new List<EpsPrescription>();
            foreach (var model in models)
            {
                var epsPrescription = TrackingDataContext.EpsPrescriptions.FirstOrDefault(e => e.HpId == hpId
                    && e.PtId == model.PtId
                    && e.RaiinNo == model.RaiinNo
                    && e.SeqNo == model.SeqNo);

                if (epsPrescription != null)
                {
                    epsPrescription.Status = model.Status;
                    epsPrescription.DeletedReason = model.DeletedReason;
                    epsPrescription.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    epsPrescription.IssueType = model.IssueType;
                    epsPrescription.UpdateId = userId;
                    epsPrescription.PrescriptionId = model.PrescriptionId;
                    epsPrescription.AccessCode = model.AccessCode;
                    updateList.Add(epsPrescription);
                }
                else
                {
                    EpsPrescription epsPrescriptionAdd = new EpsPrescription();

                    var raiinInf = NoTrackingDataContext.RaiinInfs.FirstOrDefault(item =>
                        item.HpId == model.HpId &&
                        item.PtId == model.PtId &&
                        item.RaiinNo == model.RaiinNo &&
                        item.IsDeleted == DeleteTypes.None);

                    epsPrescriptionAdd.HpId = hpId;
                    epsPrescriptionAdd.PtId = model.PtId;
                    epsPrescriptionAdd.RaiinNo = model.RaiinNo;
                    epsPrescriptionAdd.SeqNo = model.SeqNo;
                    epsPrescriptionAdd.SinDate = model.SinDate;
                    epsPrescriptionAdd.RefileCount = model.RefileCount;
                    epsPrescriptionAdd.HokensyaNo = model.HokensyaNo;
                    epsPrescriptionAdd.Kigo = model.Kigo;
                    epsPrescriptionAdd.Bango = model.Bango;
                    epsPrescriptionAdd.EdaNo = model.EdaNo;
                    epsPrescriptionAdd.KohiFutansyaNo = model.KohiFutansyaNo;
                    epsPrescriptionAdd.KohiJyukyusyaNo = model.KohiJyukyusyaNo;
                    epsPrescriptionAdd.IssueType = model.IssueType;
                    epsPrescriptionAdd.PrescriptionDocument = model.PrescriptionDocument;
                    epsPrescriptionAdd.Status = (sbyte)EpsEnum.PrescriptionStatus.Registered;
                    epsPrescriptionAdd.DeletedReason = (sbyte)EpsEnum.DeletedReason.NotDeleted;
                    if (raiinInf != null)
                    {
                        epsPrescriptionAdd.KaId = raiinInf.KaId;
                        epsPrescriptionAdd.TantoId = raiinInf.TantoId;
                    }

                    epsPrescriptionAdd.CreateDate = CIUtil.GetJapanDateTimeNow();
                    epsPrescriptionAdd.CreateId = userId;
                    epsPrescriptionAdd.CreateMachine = CIUtil.GetComputerName();
                    epsPrescriptionAdd.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    epsPrescriptionAdd.UpdateId = userId;
                    epsPrescriptionAdd.UpdateMachine = CIUtil.GetComputerName();
                    addNewList.Add(epsPrescriptionAdd);
                }
            }

            if (addNewList.Any())
            {
                TrackingDataContext.EpsPrescriptions.AddRange(addNewList);
            }

            if (updateList.Any())
            {
                TrackingDataContext.EpsPrescriptions.UpdateRange(updateList);
            }

            TrackingDataContext.SaveChanges();
            return Mapper.Map<EpsPrescription, SaveEpsPrescriptionInfoModel>(addNewList);
        }

        public bool SaveDispensingResultList(int hpId, int userId, SaveDispensingResultListModels models)
        {
            var strategy = TrackingDataContext.Database.CreateExecutionStrategy();
            List<SaveDispensingResultListModelItems> resultList = models.SaveDispensingResultListModelItems;
            resultList = resultList.OrderBy(x => x.UpdateDateTime).ToList();

            var isSuccess = false;
            strategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction(System.Data.IsolationLevel.Serializable);
                try
                {
                    // Truy vấn dữ liệu mà không theo dõi entity
                    var epsDispensing = NoTrackingDataContext.EpsDispensings
                        .Where(x => x.HpId == hpId && x.IsDeleted == 0)
                        .ToList();

                    var lisAllPrescriptions = NoTrackingDataContext.EpsPrescriptions
                        .Where(x => x.HpId == hpId)
                        .ToList();

                    var epsDispensingReqs = NoTrackingDataContext.EpsDispensingReqs
                        .Where(x => new[] { 0, 1 }.Contains(x.Status))
                        .ToList();

                    var epsDispensingReqAll = NoTrackingDataContext.EpsDispensingReqs
                        .ToList();

                    foreach (var dis in resultList)
                    {
                        bool isValidResult = dis.DispensingResultListType == (int)(EpsEnum.EpsDispensingResultType.ValidResult);
                        bool isInvalidResult = dis.DispensingResultListType == (int)(EpsEnum.EpsDispensingResultType.InvalidResult);
                        bool isPrescriptionCollectedOrInProgress = dis.DispensingResultListType ==
                                                                  (int)(EpsEnum.EpsDispensingResultType.PrescriptionCollected) ||
                                                                  dis.DispensingResultListType ==
                                                                  (int)(EpsEnum.EpsDispensingResultType.DispensingInProgress);

                        bool hasValidDispensingResultId = epsDispensing.Any(x => x.DispensingResultId == dis.DispensingResultId);
                        bool hasPrescription = epsDispensing.Any(x => x.PrescriptionId == dis.PrescriptionId);

                        bool hasValidDispensingResultIdInReq = epsDispensingReqs.Any(x => x.DispensingResultId == dis.DispensingResultId);
                        bool hasValidDispensingResultIdInReqAll = epsDispensingReqAll.Any(x => x.DispensingResultId == dis.DispensingResultId);

                        if (isValidResult)
                        {
                            if (!hasValidDispensingResultId)
                            {
                                if (hasPrescription)
                                {
                                    // update EPS_DISPENSING.IS_DELETED=1   
                                    var epsToUpdate = epsDispensing
                                        .Where(x => x.DispensingResultId != dis.DispensingResultId
                                                    && x.IsDeleted == 0
                                                    && x.PrescriptionId == dis.PrescriptionId)
                                        .ToList();
                                    foreach (var ep in epsToUpdate)
                                    {
                                        ep.IsDeleted = 1;
                                        ep.UpdateDate = CIUtil.GetJapanDateTimeNow();
                                        ep.UpdateId = userId;
                                        ep.UpdateMachine = CIUtil.GetComputerName();
                                    }

                                    TrackingDataContext.UpdateRange(epsToUpdate);
                                    AddEpsDispensing(hpId, userId, dis, lisAllPrescriptions);

                                    // check xem có record nào link với DISPENSING_RESULT_ID trong EPS_DISPENSING_REQ(STATUS in (0,1))
                                    if (!hasValidDispensingResultIdInReq)
                                    {
                                        AddEpsDispensingReq(hpId, userId, dis);
                                    }
                                }
                                else
                                {
                                    AddEpsDispensing(hpId, userId, dis, lisAllPrescriptions);
                                    // check xem có record nào link với DISPENSING_RESULT_ID trong EPS_DISPENSING_REQ(STATUS in (0,1))
                                    if (!hasValidDispensingResultIdInReq)
                                    {
                                        AddEpsDispensingReq(hpId, userId, dis);
                                    }
                                }
                            }
                        }

                        if (isInvalidResult)
                        {
                            if (hasValidDispensingResultId)
                            {
                                var epsToUpdate = epsDispensing
                                    .Where(x => x.DispensingResultId == dis.DispensingResultId
                                                && x.IsDeleted == 0)
                                    .ToList();
                                foreach (var ep in epsToUpdate)
                                {
                                    ep.IsDeleted = 1;
                                    ep.UpdateDate = CIUtil.GetJapanDateTimeNow();
                                    ep.UpdateId = userId;
                                    ep.UpdateMachine = CIUtil.GetComputerName();
                                }

                                TrackingDataContext.UpdateRange(epsToUpdate);
                                AddEpsDispensing(hpId, userId, dis, lisAllPrescriptions);
                            }

                            if (hasValidDispensingResultIdInReqAll)
                            {
                                var epsRemove = epsDispensingReqAll.Where(x => x.DispensingResultId == dis.DispensingResultId)
                                    .ToList();
                                TrackingDataContext.RemoveRange(epsRemove);
                            }
                        }

                        if (isPrescriptionCollectedOrInProgress)
                        {
                            if (hasPrescription)
                            {
                                // update EPS_DISPENSING.IS_DELETED=1   
                                var epsToUpdate = epsDispensing
                                    .Where(x => x.IsDeleted == 0
                                                && x.PrescriptionId == dis.PrescriptionId)
                                    .ToList();
                                foreach (var ep in epsToUpdate)
                                {
                                    ep.IsDeleted = 1;
                                    ep.UpdateDate = CIUtil.GetJapanDateTimeNow();
                                    ep.UpdateId = userId;
                                    ep.UpdateMachine = CIUtil.GetComputerName();
                                }

                                TrackingDataContext.UpdateRange(epsToUpdate);
                                AddEpsDispensing(hpId, userId, dis, lisAllPrescriptions);
                            }
                            else
                            {
                                AddEpsDispensing(hpId, userId, dis, lisAllPrescriptions);
                            }
                        }
                    }

                    // Add into epsDispensingList
                    AddEpsDispensingList(hpId, userId, models);
                    transaction.Commit();
                    isSuccess = true;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message);
                    transaction.Rollback();
                }
            });
            return isSuccess;
        }

        private void DetachExistingEpsDispensingReqEntity(EpsDispensingReq entity)
        {
            var entry = TrackingDataContext.Entry(entity);
            if (entry.State == EntityState.Detached)
                return;
            entry.State = EntityState.Detached;
        }

        private void AddEpsDispensing(int hpId, int userId, SaveDispensingResultListModelItems dis,
            List<EpsPrescription> epsPrescriptions)
        {
            var epsDispensingAdd = new EpsDispensing();
            var prescription = epsPrescriptions.FirstOrDefault(d => d.PrescriptionId == dis.PrescriptionId);
            epsDispensingAdd.Id = GetMaxIdEpsDispensing() + 1;
            epsDispensingAdd.HpId = hpId;
            epsDispensingAdd.PrescriptionId = dis.PrescriptionId;
            epsDispensingAdd.EpsUpdateDateTime =
                CIUtil.SetKindUtc(CIUtil.StrDateToDate(dis.UpdateDateTime.ToString(), "yyyyMMddHHmmss"));
            epsDispensingAdd.DispensingResultId = dis.DispensingResultId;

            epsDispensingAdd.PtId = prescription?.PtId ?? 0;
            epsDispensingAdd.HokensyaNo = prescription?.HokensyaNo ?? String.Empty;
            epsDispensingAdd.Kigo = prescription?.Kigo ?? String.Empty;
            epsDispensingAdd.Bango = prescription?.Bango ?? String.Empty;
            epsDispensingAdd.EdaNo = prescription?.EdaNo ?? String.Empty;
            epsDispensingAdd.KohiFutansyaNo = prescription?.KohiFutansyaNo ?? String.Empty;
            epsDispensingAdd.KohiJyukyusyaNo = prescription?.KohiJyukyusyaNo ?? String.Empty;
            epsDispensingAdd.ResultType = dis.DispensingResultListType;
            epsDispensingAdd.MessageFlg = dis.MessageFlg;
            epsDispensingAdd.CancelReason = dis.CancelReason;
            epsDispensingAdd.IsDeleted = DeleteTypes.None;
            epsDispensingAdd.CreateDate = CIUtil.GetJapanDateTimeNow();
            epsDispensingAdd.CreateId = userId;
            epsDispensingAdd.CreateMachine = CIUtil.GetComputerName();
            epsDispensingAdd.UpdateDate = CIUtil.GetJapanDateTimeNow();
            epsDispensingAdd.UpdateId = userId;
            epsDispensingAdd.UpdateMachine = CIUtil.GetComputerName();
            TrackingDataContext.Add(epsDispensingAdd);
            TrackingDataContext.SaveChanges();
        }

        private void AddEpsDispensingReq(int hpId, int userId, SaveDispensingResultListModelItems dis)
        {
            var epsDispensingReqAdd = new EpsDispensingReq();
            epsDispensingReqAdd.Id = GetMaxIdEpsDispensingReq() + 1;
            epsDispensingReqAdd.HpId = hpId;
            epsDispensingReqAdd.DispensingResultId = dis.DispensingResultId;
            epsDispensingReqAdd.Status = 0;
            epsDispensingReqAdd.CreateDate = CIUtil.GetJapanDateTimeNow();
            epsDispensingReqAdd.CreateId = userId;
            epsDispensingReqAdd.CreateMachine = CIUtil.GetComputerName();
            DetachExistingEpsDispensingReqEntity(epsDispensingReqAdd);
            TrackingDataContext.Add(epsDispensingReqAdd);
            TrackingDataContext.SaveChanges();
        }

        public long GetMaxIdEpsDispensingReq()
        {
            var nextId = TrackingDataContext.Database
                .SqlQueryRaw<long>("SELECT nextval('eps_dispensing_req_id_seq') AS \"Value\"")
                .FirstOrDefault();

            return nextId;
        }

        public long GetMaxIdEpsDispensing()
        {
            var epsDispensingId = TrackingDataContext.Database
                .SqlQueryRaw<long>("SELECT NEXTVAL('eps_dispensing_id_seq') AS \"Value\"")
                .FirstOrDefault();

            return epsDispensingId;
        }

        private void AddEpsDispensingList(int hpId, int userId, SaveDispensingResultListModels models)
        {
            var epsDispensingListAdd = new EpsDispensingList();
            epsDispensingListAdd.HpId = hpId;
            epsDispensingListAdd.StartDate = models.DispensingResultListStartDate;
            epsDispensingListAdd.EndDate = models.DispensingResultListEndDate;
            epsDispensingListAdd.CreateDate = CIUtil.GetJapanDateTimeNow();
            epsDispensingListAdd.CreateId = userId;
            epsDispensingListAdd.CreateMachine = CIUtil.GetComputerName();
            TrackingDataContext.Add(epsDispensingListAdd);
            TrackingDataContext.SaveChanges();
        }

        public List<TodayHokenOdrInfModel> GetOdrInfs(int hpId, long PtId, long raiinNo, int sinDate)
        {
            var odrInfListQuery = NoTrackingDataContext.OdrInfs
                .Where(odr => odr.HpId == hpId && odr.PtId == PtId && odr.RaiinNo == raiinNo &&
                              odr.SinDate == sinDate &&
                              odr.OdrKouiKbn != 10 && odr.IsDeleted == 0).ToList();
            List<OdrInf> AllOdrInfs = odrInfListQuery.ToList();

            var odrInfDetailQuery = NoTrackingDataContext.OdrInfDetails
                .Where(odrDetail => odrDetail.HpId == hpId
                                    && odrDetail.PtId == PtId
                                    && odrDetail.RaiinNo == raiinNo
                                    && odrDetail.SinDate == sinDate
                                    && odrDetail.ItemCd != ItemCdConst.JikanKihon
                                    && odrDetail.ItemCd != ItemCdConst.SyosaiKihon).ToList();

            var tenMstQuery = NoTrackingDataContext.TenMsts
                .Where(e => e.HpId == hpId && e.StartDate <= sinDate && e.EndDate >= sinDate).ToList();

            var kensaMstQuery = NoTrackingDataContext.KensaMsts
                .Where(p => p.HpId == hpId && p.IsDelete == DeleteTypes.None).ToList();

            var ipnKasanExcludeQuery = NoTrackingDataContext.ipnKasanExcludes
                .Where(u => u.StartDate <= sinDate && u.EndDate >= sinDate).ToList();

            var ipnKasanExcludeItemQuery = NoTrackingDataContext.ipnKasanExcludeItems
                .Where(u => u.StartDate <= sinDate && u.EndDate >= sinDate).ToList();

            var ipnMinYakkaMstQuery = NoTrackingDataContext.IpnMinYakkaMsts
                .Where(e => e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == 0).ToList();

            var detailJoinTenMstQuery = from odrDetail in odrInfDetailQuery
                join odrInf in odrInfListQuery on new { odrDetail.RpNo, odrDetail.RpEdaNo } equals new
                    { odrInf.RpNo, odrInf.RpEdaNo }
                join tenMst in tenMstQuery on odrDetail.ItemCd equals tenMst.ItemCd into tenMstList
                select new
                {
                    OdrDetail = odrDetail,
                    TenMst = tenMstList.FirstOrDefault()
                };
            var detailWithTenMstQuery = from odrDetail in detailJoinTenMstQuery
                join ipnKensa in ipnKasanExcludeQuery
                    on odrDetail.OdrDetail.IpnCd equals ipnKensa.IpnNameCd into ipnKasanExcludeList
                join ipnKensaItem in ipnKasanExcludeItemQuery
                    on odrDetail.OdrDetail.ItemCd equals ipnKensaItem.ItemCd into ipnKasanExcludeItemList
                join ipnMinYakka in ipnMinYakkaMstQuery
                    on odrDetail.OdrDetail.IpnCd equals ipnMinYakka.IpnNameCd into ipnMinYakkaList
                select new
                {
                    OdrDetail = odrDetail.OdrDetail,
                    KensaItemCd = odrDetail.TenMst == null ? "" : odrDetail.TenMst.KensaItemCd,
                    Ten = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.Ten,
                    HandanGrpKbn = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.HandanGrpKbn,
                    MasterSbt = odrDetail.TenMst == null ? "" : odrDetail.TenMst.MasterSbt,
                    CmtCol1 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtCol1,
                    CmtCol2 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtCol2,
                    CmtCol3 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtCol3,
                    CmtCol4 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtCol4,
                    CmtColKeta1 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtColKeta1,
                    CmtColKeta2 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtColKeta2,
                    CmtColKeta3 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtColKeta3,
                    CmtColKeta4 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtColKeta4,
                    IsGetYakka = ipnKasanExcludeList.FirstOrDefault() == null &&
                                 ipnKasanExcludeItemList.FirstOrDefault() == null,
                    Yakka = ipnMinYakkaList.FirstOrDefault(),
                    IsDeletedTenMst = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.IsDeleted,
                    SanteiItemCd = odrDetail.TenMst == null ? "" : odrDetail.TenMst.SanteiItemCd,
                };

            var detailWithKensaMstQuery = from detailWithTenMst in detailWithTenMstQuery
                join kensaMst in kensaMstQuery on detailWithTenMst.KensaItemCd equals kensaMst.KensaItemCd into
                    kensaMstList
                from kensaMst in kensaMstList.DefaultIfEmpty()
                select new
                {
                    OdrDetail = detailWithTenMst.OdrDetail,
                    Ten = detailWithTenMst.Ten,
                    HandanGrpKbn = detailWithTenMst.HandanGrpKbn,
                    MasterSbt = detailWithTenMst.MasterSbt,
                    CmtCol1 = detailWithTenMst.CmtCol1,
                    CmtCol2 = detailWithTenMst.CmtCol2,
                    CmtCol3 = detailWithTenMst.CmtCol3,
                    CmtCol4 = detailWithTenMst.CmtCol4,
                    CmtColKeta1 = detailWithTenMst.CmtColKeta1,
                    CmtColKeta2 = detailWithTenMst.CmtColKeta2,
                    CmtColKeta3 = detailWithTenMst.CmtColKeta3,
                    CmtColKeta4 = detailWithTenMst.CmtColKeta4,
                    IsGetYakka = detailWithTenMst.IsGetYakka,
                    Yakka = detailWithTenMst.Yakka,
                    KensaMst = kensaMst,
                    IsDeletedTenMst = detailWithTenMst.IsDeletedTenMst,
                    SanteiItemCd = detailWithTenMst.SanteiItemCd
                };

            List<TodayOdrInfDetailModel> AllOdrInfDetails = detailWithKensaMstQuery
                .Select(e => new TodayOdrInfDetailModel(e.OdrDetail, e.SanteiItemCd, e.MasterSbt))
                .ToList();

            // Find By Hoken
            var hokenOdrInfs = AllOdrInfs
                .GroupBy(odr => odr.HokenPid)
                .Select(grp => grp.FirstOrDefault())
                .ToList();
            List<TodayHokenOdrInfModel> result = new List<TodayHokenOdrInfModel>();

            foreach (OdrInf hokenOdrInf in hokenOdrInfs)
            {
                // Find By Group
                List<TodayGroupOdrInfModel> groupOdrInfModels = new List<TodayGroupOdrInfModel>();
                var groupOdrInfs = AllOdrInfs.Where(odr => odr.HokenPid == hokenOdrInf.HokenPid)
                    .GroupBy(odr => new
                    {
                        odr.HokenPid,
                        odr.InoutKbn,
                        odr.SyohoSbt,
                        odr.SikyuKbn,
                        odr.TosekiKbn,
                        odr.SanteiKbn
                    })
                    .Select(grp => grp.FirstOrDefault())
                    .ToList();

                foreach (OdrInf groupOdrInf in groupOdrInfs)
                {
                    // Find By RP
                    List<TodayOdrInfModel> rpOdrInfModels = new List<TodayOdrInfModel>();
                    var rpOdrInfs = AllOdrInfs.Where(odrInf => odrInf.HokenPid == hokenOdrInf.HokenPid
                                                               && odrInf.InoutKbn == groupOdrInf.InoutKbn
                                                               && odrInf.SyohoSbt == groupOdrInf.SyohoSbt
                                                               && odrInf.SikyuKbn == groupOdrInf.SikyuKbn
                                                               && odrInf.TosekiKbn == groupOdrInf.TosekiKbn
                                                               && odrInf.SanteiKbn == groupOdrInf.SanteiKbn)
                        .ToList();

                    foreach (OdrInf rpOdrInf in rpOdrInfs)
                    {
                        // Find OdrInfDetail
                        var odrInfDetails = AllOdrInfDetails
                            .Where(detail =>
                                detail.OdrInfDetail.RpNo == rpOdrInf.RpNo &&
                                detail.OdrInfDetail.RpEdaNo == rpOdrInf.RpEdaNo)
                            .ToList();
                        ;
                        rpOdrInfModels.Add(new TodayOdrInfModel(rpOdrInf, odrInfDetails));
                    }

                    TodayGroupOdrInfModel groupOdrInfModel = new TodayGroupOdrInfModel(rpOdrInfModels);
                    groupOdrInfModels.Add(groupOdrInfModel);
                }

                TodayHokenOdrInfModel todayHokenOdrInfModel = new TodayHokenOdrInfModel(groupOdrInfModels);

                result.Add(todayHokenOdrInfModel);
            }

            return result;
        }

        public List<Domain.Models.Eps.CheckErrorPreRegistration.PtHokenPatternModel> FindPtHokenPatternList(int hpId,
            long ptId, int sinDay, bool isGetDeleted = false)
        {
            string funcName = nameof(FindPtHokenPatternList);
            List<Domain.Models.Eps.CheckErrorPreRegistration.PtHokenPatternModel> ptHokenPatternList =
                new List<Domain.Models.Eps.CheckErrorPreRegistration.PtHokenPatternModel>();

            // PtInf
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(pt =>
                pt.HpId == hpId && pt.PtId == ptId && pt.IsDelete == 0);

            if (ptInf == null) return ptHokenPatternList;

            var listPtHokenPattern = NoTrackingDataContext.PtHokenPatterns
                .Where(pattern => pattern.HpId == hpId
                                  && pattern.PtId == ptId
                                  && (pattern.IsDeleted == 0 || isGetDeleted)).ToList();

            if (listPtHokenPattern == null || listPtHokenPattern.Count <= 0) return ptHokenPatternList;

            var ptHokenInfRepos = NoTrackingDataContext.PtHokenInfs
                .Where(hoken => hoken.HpId == hpId
                                && hoken.PtId == ptId
                                && (hoken.IsDeleted == 0 || isGetDeleted));

            var predicateHokenInf =
                CreatePtHokenInfExpression(listPtHokenPattern.Select(item => item.HokenId).ToList());

            List<PtHokenInf> listPtHokenInf = new List<PtHokenInf>();
            if (predicateHokenInf != null)
            {
                listPtHokenInf = ptHokenInfRepos.Where(predicateHokenInf).ToList();
            }

            var ptKohiRepos = NoTrackingDataContext.PtKohis
                .Where(kohi => kohi.HpId == hpId
                               && kohi.PtId == ptId
                               && (kohi.IsDeleted == 0 || isGetDeleted));

            var predicateKohi = CreatePtKohiExpression(listPtHokenPattern);

            List<PtKohi> listPtKohi = new List<PtKohi>();
            if (predicateKohi != null)
            {
                listPtKohi = ptKohiRepos.Where(predicateKohi).ToList();
            }

            var predicateHokenMst = CreateHokenMstExpression(listPtHokenInf, listPtKohi);

            if (predicateHokenMst == null) return ptHokenPatternList;

            foreach (var ptHokenPattern in listPtHokenPattern)
            {
                var ptHokenInf = listPtHokenInf.FirstOrDefault(hk => hk.HokenId == ptHokenPattern.HokenId);
                var ptKohi1 = listPtKohi.FirstOrDefault(kohi => kohi.HokenId == ptHokenPattern.Kohi1Id);
                var ptKohi2 = listPtKohi.FirstOrDefault(kohi => kohi.HokenId == ptHokenPattern.Kohi2Id);
                var ptKohi3 = listPtKohi.FirstOrDefault(kohi => kohi.HokenId == ptHokenPattern.Kohi3Id);
                var ptKohi4 = listPtKohi.FirstOrDefault(kohi => kohi.HokenId == ptHokenPattern.Kohi4Id);

                ptHokenPatternList.Add(new Domain.Models.Eps.CheckErrorPreRegistration.PtHokenPatternModel(sinDay,
                    ptInf.Birthday, ptHokenPattern,
                    ptHokenInf == null
                        ? null
                        : CreatePtHokenInfModel(ptHokenInf, sinDay),
                    ptKohi1 == null
                        ? null
                        : CreatePtKohiModel(ptKohi1, sinDay),
                    ptKohi2 == null
                        ? null
                        : CreatePtKohiModel(ptKohi2, sinDay),
                    ptKohi3 == null
                        ? null
                        : CreatePtKohiModel(ptKohi3, sinDay),
                    ptKohi4 == null
                        ? null
                        : CreatePtKohiModel(ptKohi4, sinDay)
                ));
            }

            return ptHokenPatternList;
        }

        public Domain.Models.Eps.CheckErrorPreRegistration.PtHokenPatternModel FindPtHokenPatternById(int hpId,
            long ptId, int sinDay, int patternId = 0, long raiinNo = 0,
            bool isGetDeleted = false)
        {
            string funcName = nameof(FindPtHokenPatternById);
            Domain.Models.Eps.CheckErrorPreRegistration.PtHokenPatternModel hokenPattern = null;

            // PtInf
            PtInf ptInf =
                NoTrackingDataContext.PtInfs.FirstOrDefault(
                    pt => pt.HpId == hpId && pt.PtId == ptId && pt.IsDelete == 0);
            if (patternId == 0 && raiinNo != 0)
            {
                RaiinInf raiinInf = NoTrackingDataContext.RaiinInfs.FirstOrDefault(u =>
                    u.RaiinNo == raiinNo && u.SinDate == sinDay && u.IsDeleted == DeleteTypes.None);
                if (raiinInf == null) return hokenPattern;
                patternId = raiinInf.HokenPid;
            }

            if (ptInf == null) return hokenPattern;

            var ptHokenPattern = NoTrackingDataContext.PtHokenPatterns
                .Where(pattern => pattern.HpId == hpId
                                  && pattern.PtId == ptId
                                  && pattern.HokenPid == patternId
                                  && (pattern.IsDeleted == 0 || isGetDeleted)).FirstOrDefault();

            if (ptHokenPattern == null) return hokenPattern;

            var ptHokenInf = NoTrackingDataContext.PtHokenInfs
                .FirstOrDefault(hoken => hoken.HpId == hpId
                                         && hoken.PtId == ptId
                                         && hoken.HokenId == ptHokenPattern.HokenId
                                         && (hoken.IsDeleted == 0 || isGetDeleted));

            var ptKohiQueryRepos = NoTrackingDataContext.PtKohis
                .Where(kohi => kohi.HpId == hpId
                               && kohi.PtId == ptId
                               && (kohi.IsDeleted == 0 || isGetDeleted));

            var predicateKohi = CreatePtKohiExpression(new List<int>()
            {
                ptHokenPattern.Kohi1Id,
                ptHokenPattern.Kohi2Id,
                ptHokenPattern.Kohi3Id,
                ptHokenPattern.Kohi4Id
            });

            List<PtKohi> listPtKohi = new List<PtKohi>();
            if (predicateKohi != null)
            {
                listPtKohi = ptKohiQueryRepos.Where(predicateKohi).ToList();
            }

            if (ptHokenInf == null && listPtKohi.Count <= 0) return hokenPattern;

            var predicateHokenMst = CreateHokenMstExpression(new List<PtHokenInf>() { ptHokenInf }, listPtKohi);

            if (predicateHokenMst == null) return hokenPattern;

            var ptKohi1 = listPtKohi.Where(item => item.HokenId == ptHokenPattern.Kohi1Id).FirstOrDefault();
            var ptKohi2 = listPtKohi.Where(item => item.HokenId == ptHokenPattern.Kohi2Id).FirstOrDefault();
            var ptKohi3 = listPtKohi.Where(item => item.HokenId == ptHokenPattern.Kohi3Id).FirstOrDefault();
            var ptKohi4 = listPtKohi.Where(item => item.HokenId == ptHokenPattern.Kohi4Id).FirstOrDefault();

            hokenPattern = new Domain.Models.Eps.CheckErrorPreRegistration.PtHokenPatternModel(sinDay, ptInf.Birthday,
                ptHokenPattern,
                ptHokenInf == null
                    ? null
                    : CreatePtHokenInfModel(ptHokenInf, sinDay),
                ptKohi1 == null
                    ? null
                    : CreatePtKohiModel(ptKohi1, sinDay),
                ptKohi2 == null
                    ? null
                    : CreatePtKohiModel(ptKohi2, sinDay),
                ptKohi3 == null
                    ? null
                    : CreatePtKohiModel(ptKohi3, sinDay),
                ptKohi4 == null
                    ? null
                    : CreatePtKohiModel(ptKohi4, sinDay)
            );

            return hokenPattern;
        }

        public ReceptionModel GetRaiinInfByRaiinNo(int hpId, long ptId, int sindate, long raiinNo)
        {
            RaiinInf raiinInf = null;
            if (raiinNo > 0)
            {
                raiinInf = NoTrackingDataContext.RaiinInfs.FirstOrDefault(p =>
                    p.HpId == hpId && p.PtId == ptId && p.SinDate == sindate
                    && p.RaiinNo == raiinNo && p.IsDeleted == DeleteTypes.None);
            }

            return new ReceptionModel(raiinInf ?? new RaiinInf() { PtId = ptId, SinDate = sindate });
        }

        public List<string> GetIpnKasanMst(int hpId, int sinDate, List<string> ipnCdList)
        {
            return NoTrackingDataContext.IpnKasanMsts.Where(x => x.StartDate <= sinDate
                                                                 && x.EndDate >= sinDate
                                                                 && ipnCdList.Contains(x.IpnNameCd))
                .Select(x => x.IpnNameCd)
                .ToList();
        }

        public List<string> GetIpnKasanExclude(int hpId, int sinDate, List<string> ipnCdList)
        {
            return NoTrackingDataContext.ipnKasanExcludes.Where(x => x.StartDate <= sinDate
                                                                     && x.EndDate >= sinDate
                                                                     && ipnCdList.Contains(x.IpnNameCd))
                .Select(x => x.IpnNameCd)
                .ToList();
        }

        private Expression<Func<PtKohi, bool>> CreatePtKohiExpression(List<int> listKohiId)
        {
            var param = Expression.Parameter(typeof(PtKohi));
            Expression expression = null;

            if (listKohiId != null && listKohiId.Count > 0)
            {
                foreach (var kohiId in listKohiId)
                {
                    if (kohiId > 0)
                    {
                        var valHokenId = Expression.Constant(kohiId);
                        var memberHokenId = Expression.Property(param, nameof(PtKohi.HokenId));
                        Expression expressionHokenId = Expression.Equal(valHokenId, memberHokenId);

                        expression = expression == null
                            ? expressionHokenId
                            : Expression.Or(expression, expressionHokenId);
                    }
                }
            }

            return expression != null
                ? Expression.Lambda<Func<PtKohi, bool>>(body: expression, parameters: param)
                : null;
        }

        public PtKohiModel CreatePtKohiModel(PtKohi eKohiInf, int sinDay)
        {
            PtKohiModel kohiInfModel = null;
            if (eKohiInf != null)
            {
                kohiInfModel = new PtKohiModel(sinDay, eKohiInf);
            }

            return kohiInfModel;
        }

        public PtHokenInfModel CreatePtHokenInfModel(PtHokenInf ePtHokenInf, int sinDay)
        {
            PtHokenInfModel hokenInfModel = null;
            if (ePtHokenInf != null)
            {
                hokenInfModel = new PtHokenInfModel(sinDay, ePtHokenInf);
            }

            return hokenInfModel;
        }

        private Expression<Func<HokenMst, bool>> CreateHokenMstExpression(List<PtHokenInf> listPtHokenInf,
            List<PtKohi> listPtKohi)
        {
            var param = Expression.Parameter(typeof(HokenMst));
            Expression expression = null;

            CreateHokenMstExpression(listPtHokenInf, ref expression, ref param);
            CreateHokenMstExpression(listPtKohi, ref expression, ref param);

            return expression != null
                ? Expression.Lambda<Func<HokenMst, bool>>(body: expression, parameters: param)
                : null;
        }

        private void CreateHokenMstExpression(List<PtKohi> listPtKohi, ref Expression expression,
            ref ParameterExpression param)
        {
            if (listPtKohi != null && listPtKohi.Count > 0)
            {
                foreach (var item in listPtKohi)
                {
                    if (item != null)
                    {
                        var valHokenNo = Expression.Constant(item.HokenNo);
                        var memberHokenNo = Expression.Property(param, nameof(HokenMst.HokenNo));

                        var valHokenEdaNo = Expression.Constant(item.HokenEdaNo);
                        var memberHokenEdaNo = Expression.Property(param, nameof(HokenMst.HokenEdaNo));

                        var expressionKohi = Expression.And(Expression.Equal(valHokenNo, memberHokenNo),
                            Expression.Equal(valHokenEdaNo, memberHokenEdaNo));

                        expression = expression == null ? expressionKohi : Expression.Or(expression, expressionKohi);
                    }
                }
            }
        }

        private void CreateHokenMstExpression(List<PtHokenInf> listPtHokenInf, ref Expression expression,
            ref ParameterExpression param)
        {
            if (listPtHokenInf != null)
            {
                foreach (var item in listPtHokenInf)
                {
                    if (item != null)
                    {
                        var valHokenNo = Expression.Constant(item.HokenNo);
                        var memberHokenNo = Expression.Property(param, nameof(HokenMst.HokenNo));

                        var valHokenEdaNo = Expression.Constant(item.HokenEdaNo);
                        var memberHokenEdaNo = Expression.Property(param, nameof(HokenMst.HokenEdaNo));

                        var expressionHoken = Expression.And(Expression.Equal(valHokenNo, memberHokenNo),
                            Expression.Equal(valHokenEdaNo, memberHokenEdaNo));

                        expression = expression == null ? expressionHoken : Expression.Or(expression, expressionHoken);
                    }
                }
            }
        }

        private Expression<Func<PtKohi, bool>>? CreatePtKohiExpression(List<PtHokenPattern> listPtHokenPattern)
        {
            var param = Expression.Parameter(typeof(PtKohi));
            Expression expression = null;

            if (listPtHokenPattern != null && listPtHokenPattern.Count > 0)
            {
                foreach (var pattern in listPtHokenPattern)
                {
                    if (pattern.PtId > 0)
                    {
                        CreatePtKohiExpression(new List<int>()
                        {
                            pattern.Kohi1Id,
                            pattern.Kohi2Id,
                            pattern.Kohi3Id,
                            pattern.Kohi4Id
                        }, ref expression, ref param);
                    }
                }
            }

            return expression != null
                ? Expression.Lambda<Func<PtKohi, bool>>(body: expression, parameters: param)
                : null;
        }

        private void CreatePtKohiExpression(List<int> listKohiId, ref Expression expression,
            ref ParameterExpression param)
        {
            if (listKohiId != null && listKohiId.Count > 0)
            {
                foreach (var kohiId in listKohiId)
                {
                    if (kohiId > 0)
                    {
                        var valHokenId = Expression.Constant(kohiId);
                        var memberHokenId = Expression.Property(param, nameof(PtKohi.HokenId));
                        Expression expressionHokenId = Expression.Equal(valHokenId, memberHokenId);

                        expression = expression == null
                            ? expressionHokenId
                            : Expression.Or(expression, expressionHokenId);
                    }
                }
            }
        }

        private Expression<Func<PtHokenInf, bool>>? CreatePtHokenInfExpression(List<int> listHokenId)
        {
            var param = Expression.Parameter(typeof(PtHokenInf));
            Expression expression = null;

            if (listHokenId != null && listHokenId.Count > 0)
            {
                foreach (var hokenId in listHokenId)
                {
                    if (hokenId > 0)
                    {
                        var valHokenId = Expression.Constant(hokenId);
                        var memberHokenId = Expression.Property(param, nameof(PtHokenInf.HokenId));
                        Expression expressionHokenId = Expression.Equal(valHokenId, memberHokenId);

                        expression = expression == null
                            ? expressionHokenId
                            : Expression.Or(expression, expressionHokenId);
                    }
                }
            }

            return expression != null
                ? Expression.Lambda<Func<PtHokenInf, bool>>(body: expression, parameters: param)
                : null;
        }

        public PrescriptionInfListModel GetPrescriptionInfList(int hpId, long patientNum, int startSinDate,
            int endSinDate, int startDispensingDate, int endDispensingDate)
        {
            PrescriptionInfListModel result = new PrescriptionInfListModel();
            List<EpsPrescriptionInfModel> epsPrescriptionInfModels = new List<EpsPrescriptionInfModel>();
            int systemDate = CIUtil.DateTimeToInt(DateTime.Now);

            var requestPtInf = TrackingDataContext.PtInfs.FirstOrDefault(p =>
                patientNum > 0 && p.PtNum == patientNum && p.HpId == hpId && p.IsDelete == DeleteTypes.None);
            if (patientNum > 0 && requestPtInf == null)
            {
                return result;
            }

            var lastDispensingList = NoTrackingDataContext.EpsDispensingLists.Where(l => l.HpId == hpId)
                .OrderByDescending(l => l.EndDate).FirstOrDefault();
            if (lastDispensingList != null)
            {
                result.LastDispensingListEndDate = lastDispensingList.EndDate;
            }

            var ptId = requestPtInf?.PtId ?? 0;
            var epsDispensingQuery = NoTrackingDataContext.EpsDispensings.Where(x => x.HpId == hpId &&
                (ptId == 0 ? true : x.PtId == ptId) &&
                x.IsDeleted == 0 &&
                (startDispensingDate == 0 && endDispensingDate == 0
                    ? true
                    : (startDispensingDate <= x.DispensingDate && x.DispensingDate <= endDispensingDate))).ToList();
            var kaMstsQuery = NoTrackingDataContext.KaMsts.Where(x => x.HpId == hpId && x.IsDeleted == 0).ToList();
            var userMstQuery = NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId &&
                                                                         x.IsDeleted == 0 &&
                                                                         x.StartDate <= systemDate &&
                                                                         x.EndDate >= systemDate).ToList();
            var ptInfQuery = NoTrackingDataContext.PtInfs.Where(x => x.HpId == hpId &&
                                                                     x.IsDelete == 0 &&
                                                                     (ptId == 0 ? true : x.PtId == ptId)).ToList();
            var epsPrescriptionQuery = NoTrackingDataContext.EpsPrescriptions.Where(x =>
                x.HpId == hpId && (ptId == 0 ? true : x.PtId == ptId) && (startSinDate == 0 && endSinDate == 0
                    ? true
                    : (startSinDate <= x.SinDate && x.SinDate <= endSinDate))).ToList();

            var nullablePrescriptionIds = epsDispensingQuery.Where(x => string.IsNullOrEmpty(x.PrescriptionId));
            var valuablePrescriptionIds = epsDispensingQuery.Where(x => !string.IsNullOrEmpty(x.PrescriptionId));

            var epsData = (from nullablePrescriptionId in nullablePrescriptionIds
                join ptInf in ptInfQuery on
                    new { nullablePrescriptionId.HpId, nullablePrescriptionId.PtId } equals new
                        { ptInf.HpId, ptInf.PtId }
                select new
                {
                    Eps = nullablePrescriptionId,
                    PtInf = ptInf
                }).ToList();
            epsPrescriptionInfModels = epsData.Select(x => new EpsPrescriptionInfModel(null,
                    Mapper.Map(x.PtInf, new PatientInforModel()), Mapper.Map(x.Eps, new EpsDispensingModel()), "", ""))
                .ToList();
            var prescriptionJoinQuery = from prescription in epsPrescriptionQuery
                join kaMst in kaMstsQuery on
                    new { prescription.HpId, prescription.KaId } equals new { kaMst.HpId, kaMst.KaId } into joinKaMsts
                from subKaMst in joinKaMsts.DefaultIfEmpty()
                join userMst in userMstQuery on
                    new { prescription.HpId, UserId = prescription.TantoId } equals new { userMst.HpId, userMst.UserId }
                    into joinUserMsts
                from subUserMst in joinUserMsts.DefaultIfEmpty()
                select new
                {
                    prescription,
                    KaSName = subKaMst == null ? string.Empty : subKaMst.KaSname,
                    TantoName = subUserMst == null ? string.Empty : subUserMst.Sname
                };

            var epsData1 = (from valuablePrescriptionId in valuablePrescriptionIds
                join ptInf in ptInfQuery on valuablePrescriptionId.PtId equals ptInf.PtId
                join prescription in prescriptionJoinQuery on
                    new { valuablePrescriptionId.PtId, valuablePrescriptionId.PrescriptionId }
                    equals new { prescription.prescription.PtId, prescription.prescription.PrescriptionId }
                    into joinPrescriptions
                from subPrescription in joinPrescriptions.Take(1).DefaultIfEmpty()
                select new
                {
                    Dispensing = valuablePrescriptionId,
                    Prescription = subPrescription == null ? null : subPrescription.prescription,
                    PtInf = ptInf,
                    KaSName = subPrescription == null ? string.Empty : subPrescription.KaSName,
                    TantoName = subPrescription == null ? string.Empty : subPrescription.TantoName
                }).ToList();
            epsPrescriptionInfModels.AddRange(epsData1.Select(x =>
                new EpsPrescriptionInfModel(Mapper.Map(x.Prescription, new EpsPrescriptionModel()),
                    Mapper.Map(x.PtInf, new PatientInforModel()), Mapper.Map(x.Dispensing, new EpsDispensingModel()),
                    x.KaSName, x.TantoName)));
            List<string> keys = epsPrescriptionInfModels.Where(x => x.EpsPrescription != null).Select(x =>
                x.EpsPrescription.PtId + "," + x.EpsPrescription.RaiinNo + "," + x.EpsPrescription.SeqNo).ToList();
            var prescriptionNoRelationshipJoinQuery = (from prescription in epsPrescriptionQuery
                join ptInf in ptInfQuery on
                    new { prescription.HpId, prescription.PtId } equals new { ptInf.HpId, ptInf.PtId }
                join kaMst in kaMstsQuery on
                    new { prescription.HpId, prescription.KaId } equals new { kaMst.HpId, kaMst.KaId } into joinKaMsts
                from subKaMst in joinKaMsts.DefaultIfEmpty()
                join userMst in userMstQuery on
                    new { prescription.HpId, UserId = prescription.TantoId } equals new { userMst.HpId, userMst.UserId }
                    into joinUserMsts
                from subUserMst in joinUserMsts.DefaultIfEmpty()
                where !keys.Any(x => x == (prescription.PtId + "," + prescription.RaiinNo + "," + prescription.SeqNo))
                select new
                {
                    prescription,
                    PtInf = ptInf,
                    KaSName = subKaMst == null ? string.Empty : subKaMst.KaSname,
                    TantoName = subUserMst == null ? string.Empty : subUserMst.Sname
                }).ToList();
            epsPrescriptionInfModels.AddRange(prescriptionNoRelationshipJoinQuery.Select(x =>
                new EpsPrescriptionInfModel(Mapper.Map(x.prescription, new EpsPrescriptionModel()),
                    Mapper.Map(x.PtInf, new PatientInforModel()), null, x.KaSName, x.TantoName)));
            if (ptId == 0)
            {
                if (startDispensingDate == 0 && (endDispensingDate == 0 || endDispensingDate == 99999999))
                {
                    epsPrescriptionInfModels = epsPrescriptionInfModels
                        .Where(x => x.SinDate >= startSinDate && x.SinDate <= endSinDate).ToList();
                }
                else if (startSinDate == 0 && (endSinDate == 0 || endSinDate == 99999999))
                {
                    epsPrescriptionInfModels = epsPrescriptionInfModels.Where(x =>
                        x.DispensingDate >= startDispensingDate && x.DispensingDate <= endDispensingDate).ToList();
                }
            }

            result.EpsPrescriptionModels = epsPrescriptionInfModels;
            return result;
        }
        
        public bool UpdatePrescriptionStatusByIds(int inputDataHpId, long inputDataPtId, long inputDataRaiinNo,
            List<UpdatePrescriptionStatusModel> inputDataUpdatePrescriptionStatusModels)
        {
            if (!inputDataUpdatePrescriptionStatusModels.Any())
            {
                return false;
            }

            var epsPrescriptions = TrackingDataContext.EpsPrescriptions.Where(p => p.HpId == inputDataHpId && p.PtId == inputDataPtId && p.RaiinNo == inputDataRaiinNo).ToList();

            foreach (var epsPrescription in epsPrescriptions)
            {
                foreach (var prescriptionModel in inputDataUpdatePrescriptionStatusModels)
                {
                    if (epsPrescription.PrescriptionId == prescriptionModel.PrescriptionId)
                    {
                        epsPrescription.Status = prescriptionModel.Status;
                        epsPrescription.UpdateDate = CIUtil.GetJapanDateTimeNow();

                        if (prescriptionModel.DeletedReason != null)
                        {
                            epsPrescription.DeletedReason = prescriptionModel.DeletedReason.Value;
                        }
                        if (prescriptionModel.DeletedDate != null)
                        {
                            if (prescriptionModel.DeletedDate == DateTime.MinValue) epsPrescription.DeletedDate = null;
                            else epsPrescription.DeletedDate = prescriptionModel.DeletedDate.Value;
                        }
                    }
                }
            }
            TrackingDataContext.SaveChanges();
            return true;
        }

        public bool GetEpsDispensingByResultType(int hpId, long ptId, long raiinNo, int sinDate,  List<int> listResultType)
        {
            var epsPrescription = NoTrackingDataContext.EpsPrescriptions.Where(x => x.PtId == ptId && x.RaiinNo == raiinNo && x.SinDate == sinDate).ToList();

                // .(item => item.HpId == _hpId && item.PtId == ptId && item.RaiinNo == raiinNo && item.SinDate == sinDate);
            var epsDispensing = NoTrackingDataContext.EpsDispensings.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == 0 && listResultType.Contains(x.ResultType));
            var query = from prescription in epsPrescription
                        join dispensing in epsDispensing on prescription.PrescriptionId equals dispensing.PrescriptionId
                        select new
                        {
                            ResultType = dispensing.ResultType,
                            UpdateDate = prescription.UpdateDate
                        };
            return query.FirstOrDefault() != null;
        }

        public EpsReqModel? UpsertEpsReq(int hpId, long ptId, long raiinNo, int sinDate, int reqDate, long dateSeqNo, string arbitraryFileIdentifier, string prescriptionId, string dispensingResultId, int reqType, int status, string resultCode, string resultMessage, string result, int userId)
        {
            var strategy = TrackingDataContext.Database.CreateExecutionStrategy();

            return strategy.Execute(() =>
            {
                int maxRetry = 3;
                for (int attempt = 0; attempt < maxRetry; attempt++)
                {
                    using var transaction = TrackingDataContext.Database
                        .BeginTransaction(System.Data.IsolationLevel.Serializable);

                    try
                    {
                        EpsReq? epsReq = null;

                        if (dateSeqNo > 0)
                        {
                            epsReq = TrackingDataContext.EpsReqs.FirstOrDefault(x =>
                                x.HpId == hpId &&
                                x.DateSeqNo == dateSeqNo &&
                                x.ReqDate == reqDate
                            );

                            if (epsReq == null) return null;
                        }

                        if (epsReq == null)
                        {
                            if (dateSeqNo <= 0)
                            {
                                dateSeqNo = (TrackingDataContext.EpsReqs
                                                .Where(x => x.HpId == hpId && x.ReqDate == reqDate)
                                                .Max(x => (long?)x.DateSeqNo) ?? 0) + 1;
                            }

                            epsReq = new EpsReq
                            {
                                HpId = hpId,
                                PtId = ptId,
                                RaiinNo = raiinNo,
                                SinDate = sinDate,
                                ReqDate = reqDate,
                                DateSeqNo = dateSeqNo,
                                ArbitraryFileIdentifier = arbitraryFileIdentifier,
                                PrescriptionId = prescriptionId,
                                DispensingResultId = dispensingResultId,
                                ReqType = reqType,
                                Status = status,
                                ResultCode = resultCode,
                                ResultMessage = resultMessage,
                                Result = result,
                                CreateDate = CIUtil.GetJapanDateTimeNow(),
                                CreateId = userId,
                                CreateMachine = CIUtil.GetComputerName(),
                                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                                UpdateId = userId,
                                UpdateMachine = CIUtil.GetComputerName(),
                            };

                            TrackingDataContext.EpsReqs.Add(epsReq);
                            TrackingDataContext.SaveChanges();

                            transaction.Commit();
                        }
                        else
                        {
                            epsReq.Status = status;
                            epsReq.ResultCode = resultCode;
                            epsReq.ResultMessage = resultMessage;
                            epsReq.Result = result;
                            epsReq.UpdateId = userId;
                            epsReq.UpdateMachine = CIUtil.GetComputerName();
                            epsReq.UpdateDate = CIUtil.GetJapanDateTimeNow();

                            TrackingDataContext.SaveChanges();
                            transaction.Commit();
                        }

                        return new EpsReqModel(
                            epsReq.HpId,
                            epsReq.ReqDate,
                            epsReq.DateSeqNo,
                            epsReq.ArbitraryFileIdentifier,
                            epsReq.PtId,
                            epsReq.SinDate,
                            epsReq.RaiinNo,
                            epsReq.PrescriptionId,
                            epsReq.DispensingResultId,
                            epsReq.ReqType,
                            epsReq.Status,
                            epsReq.ResultCode,
                            epsReq.ResultMessage,
                            epsReq.Result,
                            epsReq.CreateDate,
                            epsReq.CreateId,
                            epsReq.CreateMachine,
                            epsReq.UpdateDate,
                            epsReq.UpdateId,
                            epsReq.UpdateMachine
                        );
                    }
                    catch (PostgresException ex) when (ex.SqlState == "40001")
                    {
                        transaction.Rollback();

                        if (attempt == maxRetry - 1)
                            throw;
                    }
                }

                throw new Exception("Unexpected execution flow.");
            });
        }

        public bool UpdateEpsDispensings(int hpId, List<EpsDispensingModel> epsDispensingModels, bool notAcceptedAtPharmacy, bool preparedAtPharmacy, bool collectedOrDispensedByPharmacy, bool cancelledPrescription, int userId)
        {
            if (!epsDispensingModels.Any())
            {
                return false;
            }

            var prescriptionIdsRequest = epsDispensingModels.Select(d => d.PrescriptionId).Distinct();
            var dispensingResultIdsRequest = epsDispensingModels.Select(d => d.DispensingResultId).Distinct();
            var epsDispensingsByPrescriptionIds = NoTrackingDataContext.EpsDispensings.Where(d => d.HpId == hpId && prescriptionIdsRequest.Contains(d.PrescriptionId) && d.IsDeleted == DeleteTypes.None).ToList();
            var epsDispensingsByResultIds = NoTrackingDataContext.EpsDispensings.Where(d => d.HpId == hpId && dispensingResultIdsRequest.Contains(d.DispensingResultId) && d.IsDeleted == DeleteTypes.None).ToList();
            var dispensingResultIdsActually = epsDispensingsByResultIds.Select(d => d.DispensingResultId).Distinct();
            var epsPrescriptionByPrescriptionIds = NoTrackingDataContext.EpsPrescriptions.Where(d => d.HpId == hpId && prescriptionIdsRequest.Contains(d.PrescriptionId)).ToList();

            var deletedList = new List<EpsDispensing>();
            var addNewList = new List<EpsDispensingModel>();
            if (notAcceptedAtPharmacy)
            {
                if (epsDispensingsByPrescriptionIds.Any())
                {
                    deletedList.AddRange(epsDispensingsByPrescriptionIds);
                }
            } 
            else if (preparedAtPharmacy)
            {
                foreach (var epsDispensingModel in epsDispensingModels)
                {
                    if (!dispensingResultIdsActually.Contains(epsDispensingModel.DispensingResultId))
                    {
                        var existingDispensings =
                            epsDispensingsByPrescriptionIds.Where(
                                d => d.PrescriptionId == epsDispensingModel.PrescriptionId);
                        if (existingDispensings.Any())
                        {
                            deletedList.AddRange(existingDispensings);
                        }
                        addNewList.Add(epsDispensingModel);
                    }
                }
            }
            else if (collectedOrDispensedByPharmacy)
            {
                if (epsDispensingsByPrescriptionIds.Any())
                {
                    deletedList.AddRange(epsDispensingsByPrescriptionIds);
                }
                addNewList.AddRange(epsDispensingModels);
            }
            else if (cancelledPrescription)
            {
                if (epsDispensingsByPrescriptionIds.Any())
                {
                    foreach (var prescription in epsPrescriptionByPrescriptionIds)
                    {
                        prescription.Status = (sbyte)EpsEnum.PrescriptionStatus.Cancelled; // 取消済み
                        prescription.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        prescription.UpdateId = userId;
                    }
                    TrackingDataContext.EpsPrescriptions.UpdateRange(epsPrescriptionByPrescriptionIds);
                    deletedList.AddRange(epsDispensingsByPrescriptionIds);
                } 
            }
            else
            {
                return false;
            }

            if (deletedList.Any())
            {
                foreach (var epsDispensing in deletedList)
                {
                    epsDispensing.IsDeleted = DeleteTypes.Deleted;
                    epsDispensing.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    epsDispensing.UpdateId = userId;
                }

                try
                {
                    TrackingDataContext.UpdateRange(deletedList);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    throw;
                }
            }

            if (addNewList.Any())
            {
                foreach (var epsDispensingModel in addNewList)
                {
                    epsDispensingModel.HpId = hpId;
                    epsDispensingModel.CreateId = userId;
                    epsDispensingModel.CreateDate = CIUtil.GetJapanDateTimeNow();
                    epsDispensingModel.UpdateId = userId;
                    epsDispensingModel.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    epsDispensingModel.UpdateMachine = CIUtil.GetComputerName();
                    epsDispensingModel.PrescriptionId = epsDispensingModel.PrescriptionId ?? string.Empty;
                    epsDispensingModel.DispensingResultId = epsDispensingModel.DispensingResultId ?? string.Empty;
                    epsDispensingModel.HokensyaNo = epsDispensingModel.HokensyaNo ?? string.Empty;
                    epsDispensingModel.Kigo = epsDispensingModel.Kigo ?? string.Empty;
                    epsDispensingModel.Bango = epsDispensingModel.Bango ?? string.Empty;
                    epsDispensingModel.EdaNo = epsDispensingModel.EdaNo ?? string.Empty;
                    epsDispensingModel.ReceptionPharmacyName = epsDispensingModel.ReceptionPharmacyName ?? string.Empty;
                    epsDispensingModel.CancelReason = epsDispensingModel.CancelReason ?? string.Empty;
                    epsDispensingModel.KohiFutansyaNo = epsDispensingModel.KohiFutansyaNo ?? string.Empty;
                    epsDispensingModel.KohiJyukyusyaNo = epsDispensingModel.KohiJyukyusyaNo ?? string.Empty;
                    epsDispensingModel.EpsUpdateDateTime = CIUtil.GetJapanDateTimeNow();
                }
                TrackingDataContext.EpsDispensings.AddRange(Mapper.Map<EpsDispensingModel, EpsDispensing>(addNewList));
            }

            try
            {
                TrackingDataContext.SaveChanges();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
            string finalKey = key + CacheKeyConstant.IsProcessUpdate;
            if (_cache.KeyExists(finalKey))
            {
                _cache.KeyDelete(finalKey);
            }
            return true;
        }

        public (List<EpsPrescriptionModel>, bool) PreCheckOldPrescription(int hpId, long ptId, long raiinNo, int sinDate)
        {
            bool isPreCheckOldPrescription = false;
            List<EpsPrescriptionModel> result = new List<EpsPrescriptionModel>();
            var isContainLastVisit = IsContainLastVisit(hpId, ptId, raiinNo, sinDate);

            if (isContainLastVisit)
            {
                var listInformationIsNotConfirm = CheckInformationIsNotConfirm(hpId, ptId);

                if (listInformationIsNotConfirm)
                {
                    if (listInformationIsNotConfirm)
                    {
                        result.AddRange(GetInformationIsNotConfirm(hpId, ptId, sinDate));
                    }
                    isPreCheckOldPrescription = true;
                }
            }

            return (result, isPreCheckOldPrescription);
        }

        private bool IsContainLastVisit(int hpId, long ptId, long raiinNo, int sinDate)
        {
            var currentRaiinInf = NoTrackingDataContext.RaiinInfs.Where(r => r.HpId == hpId &&
                                                                             r.PtId == ptId &&
                                                                             r.RaiinNo == raiinNo &&
                                                                             r.IsDeleted == DeleteTypes.None &&
                                                                             r.SinDate == sinDate).FirstOrDefault();
            if (currentRaiinInf == null)
            {
                return false;
            }
            return NoTrackingDataContext.RaiinInfs.Where(r => r.HpId == hpId &&
                                                              r.PtId == ptId &&
                                                              r.IsDeleted == DeleteTypes.None &&
                                                              ((r.SinDate < currentRaiinInf.SinDate) ||
                                                              (r.SinDate == currentRaiinInf.SinDate && string.Compare(r.UketukeTime, currentRaiinInf.UketukeTime) <= 0)) &&
                                                              r.OyaRaiinNo != currentRaiinInf.RaiinNo).Count() > 0;
        }

        private bool CheckInformationIsNotConfirm(int hpId, long ptId)
        {
            return NoTrackingDataContext.EpsDispensings.Where(d => d.HpId == hpId &&
                                                                   d.PtId == ptId &&
                                                                   d.IsDeleted == DeleteTypes.None &&
                                                                   d.MessageFlg == 2).Count() > 0;
        }

        public List<EpsPrescriptionModel> GetInformationIsNotConfirm(int hpId, long ptId, int sinDate)
        {
            var listOldDispensing = NoTrackingDataContext.EpsDispensings.Where(d => d.HpId == hpId && d.PtId == ptId && d.IsDeleted == DeleteTypes.None && d.MessageFlg == 2);
            var ptInfQuery = NoTrackingDataContext.PtInfs.Where(x => x.HpId == hpId && x.IsDelete == 0 && x.PtId == ptId);
            var kaMstsQuery = NoTrackingDataContext.KaMsts.Where(x => x.HpId == hpId && x.IsDeleted == 0);
            var userMstQuery = NoTrackingDataContext.UserMsts.Where(x => x.HpId == hpId && x.IsDeleted == 0 && x.StartDate <= sinDate && x.EndDate >= sinDate);
            var listOldPrescription = NoTrackingDataContext.EpsPrescriptions.Where(p => p.HpId == hpId && p.PtId == ptId);

            var prescriptionJoinQuery = (from prescription in listOldPrescription
                                         join ptInf in ptInfQuery on
                                         new { prescription.HpId, prescription.PtId } equals new { ptInf.HpId, ptInf.PtId }
                                         join kaMst in kaMstsQuery on
                                         new { prescription.HpId, prescription.KaId } equals new { kaMst.HpId, kaMst.KaId } into joinKaMsts
                                         from subKaMst in joinKaMsts.DefaultIfEmpty()
                                         join userMst in userMstQuery on
                                         new { prescription.HpId, UserId = prescription.TantoId } equals new { userMst.HpId, userMst.UserId } into joinUserMsts
                                         from subUserMst in joinUserMsts.DefaultIfEmpty()
                                         select new
                                         {
                                             prescription,
                                             PtInf = ptInf,
                                             KaSName = subKaMst == null ? string.Empty : subKaMst.KaSname,
                                             TantoName = subUserMst == null ? string.Empty : subUserMst.Sname
                                         });

            var epsData1 = (from valuablePrescriptionId in listOldDispensing
                            join ptInf in ptInfQuery on
                            new { valuablePrescriptionId.HpId, valuablePrescriptionId.PtId } equals new { ptInf.HpId, ptInf.PtId }
                            join prescription in prescriptionJoinQuery on
                            new { valuablePrescriptionId.HpId, valuablePrescriptionId.PtId, valuablePrescriptionId.PrescriptionId } equals new { prescription.prescription.HpId, prescription.prescription.PtId, prescription.prescription.PrescriptionId } into joinPrescriptions
                            from subPrescription in joinPrescriptions.DefaultIfEmpty()
                            select new
                            {
                                Dispensing = valuablePrescriptionId,
                                Prescription = subPrescription == null ? new() : subPrescription.prescription,
                                PtInf = ptInf,
                                KaSName = subPrescription == null ? string.Empty : subPrescription.KaSName,
                                TantoName = subPrescription == null ? string.Empty : subPrescription.TantoName
                            }).ToList();

            return epsData1.Select(x => new EpsPrescriptionModel(hpId,
                                                                 x.Prescription?.PtId ?? 0,
                                                                 x.Prescription?.RaiinNo ?? 0,
                                                                 x.Prescription?.SeqNo ?? 0,
                                                                 x.Prescription?.RefileCount ?? 0,
                                                                 x.Prescription?.SinDate ?? 0,
                                                                 x.Prescription?.HokensyaNo ?? string.Empty,
                                                                 x.Prescription?.Kigo ?? string.Empty,
                                                                 x.Prescription?.Bango ?? string.Empty,
                                                                 x.Prescription?.EdaNo ?? string.Empty,
                                                                 x.Prescription?.KohiFutansyaNo ?? string.Empty,
                                                                 x.Prescription?.KohiJyukyusyaNo ?? string.Empty,
                                                                 x.Prescription?.PrescriptionId ?? string.Empty,
                                                                 x.Prescription?.AccessCode ?? string.Empty,
                                                                 x.Prescription?.IssueType ?? 0,
                                                                 x.Prescription?.PrescriptionDocument ?? string.Empty,
                                                                 x.Prescription?.Status ?? 0,
                                                                 x.Prescription?.DeletedReason ?? 0,
                                                                 x.Prescription?.DeletedDate ?? new(),
                                                                 x.Prescription?.KaId ?? 0,
                                                                 x.Prescription?.TantoId ?? 0,
                                                                 x.Prescription?.CreateDate ?? new(),
                                                                 x.Prescription?.CreateId ?? 0,
                                                                 x.Prescription?.UpdateDate ?? new(),
                                                                 x.Prescription?.UpdateId ?? 0,
                                                                 new PatientInforModel(x.PtInf.PtNum, x.PtInf.Name ?? string.Empty),
                                                                 new EpsDispensingModel(x.Dispensing.HokensyaNo, x.Dispensing.Bango, x.Dispensing.Kigo, x.Dispensing.EdaNo, x.Dispensing.KohiFutansyaNo, x.Dispensing.KohiJyukyusyaNo, x.Dispensing.DispensingTimes, x.Dispensing.ResultType, x.Dispensing.ReceptionPharmacyName, x.Dispensing.DispensingDate, x.Dispensing.EpsUpdateDateTime, x.Dispensing.MessageFlg),
                                                                 x.KaSName, x.TantoName)).ToList();
        }
        
        public YohoMst? GetYohoMst(string yohoCd, int hpId)
        {
            return NoTrackingDataContext.YohoMsts.FirstOrDefault(y => y.HpId == hpId && y.YohoCd == yohoCd);
        }
        
        public bool updateDispensingByResponse(int inputDataHpId, List<EpsDispensingModel> inputDataEpsDispensings, List<EpsDispensingReqModel> inputDataEpsDispensingReqs)
        {
            var strategy = TrackingDataContext.Database.CreateExecutionStrategy();

            var isSuccess = false;
            strategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    var dispensingResultIds = inputDataEpsDispensingReqs.Select(r => r.DispensingResultId).ToList();
                    var epsDispensingReqList =
                        TrackingDataContext.EpsDispensingReqs.Where(r => dispensingResultIds.Contains(r.DispensingResultId));
                    var epsDispensingIds = inputDataEpsDispensings.Select(d => d.DispensingResultId).ToList();
                    var epsDispensingList =
                        TrackingDataContext.EpsDispensings.Where(d => epsDispensingIds.Contains(d.DispensingResultId));

                    foreach (var inputDataEpsDispensingReq in inputDataEpsDispensingReqs)
                    {
                        var epsDispensingReq = epsDispensingReqList.FirstOrDefault(r =>
                            r.DispensingResultId == inputDataEpsDispensingReq.DispensingResultId);
                        if (epsDispensingReq != null)
                        {
                            epsDispensingReq.Status = inputDataEpsDispensingReq.Status;
                            epsDispensingReq.ErrorMessage = inputDataEpsDispensingReq.ErrorMessage;
                        }
                    }
                    
                    foreach (var inputDataEpsDispensing in inputDataEpsDispensings)
                    {
                        var epsDispensing = epsDispensingList.FirstOrDefault(r =>
                            r.DispensingResultId == inputDataEpsDispensing.DispensingResultId);
                        if (epsDispensing != null)
                        {
                            if (!string.IsNullOrEmpty(inputDataEpsDispensing.DispensingDocument))
                            {
                                epsDispensing.DispensingDocument = inputDataEpsDispensing.DispensingDocument;
                            }
                            if (!string.IsNullOrEmpty(inputDataEpsDispensing.ReceptionPharmacyName))
                            {
                                epsDispensing.ReceptionPharmacyName = inputDataEpsDispensing.ReceptionPharmacyName;
                            }
                            if (inputDataEpsDispensing.DispensingDate > 0)
                            {
                                epsDispensing.DispensingDate = inputDataEpsDispensing.DispensingDate;
                            }
                            if (inputDataEpsDispensing.DispensingTimes > 0)
                            {
                                epsDispensing.DispensingTimes = inputDataEpsDispensing.DispensingTimes;
                            }
                            if (inputDataEpsDispensing.PtId > 0)
                            {
                                epsDispensing.PtId = inputDataEpsDispensing.PtId;
                            }
                            if (!string.IsNullOrEmpty(inputDataEpsDispensing.HokensyaNo))
                            {
                                epsDispensing.HokensyaNo = inputDataEpsDispensing.HokensyaNo;
                            }
                            if (!string.IsNullOrEmpty(inputDataEpsDispensing.Kigo))
                            {
                                epsDispensing.Kigo = inputDataEpsDispensing.Kigo;
                            }
                            if (!string.IsNullOrEmpty(inputDataEpsDispensing.Bango))
                            {
                                epsDispensing.Bango = inputDataEpsDispensing.Bango;
                            }
                            if (!string.IsNullOrEmpty(inputDataEpsDispensing.EdaNo))
                            {
                                epsDispensing.EdaNo = inputDataEpsDispensing.EdaNo;
                            }
                            if (!string.IsNullOrEmpty(inputDataEpsDispensing.KohiFutansyaNo))
                            {
                                epsDispensing.KohiFutansyaNo = inputDataEpsDispensing.KohiFutansyaNo;
                            }
                            if (!string.IsNullOrEmpty(inputDataEpsDispensing.KohiJyukyusyaNo))
                            {
                                epsDispensing.KohiJyukyusyaNo = inputDataEpsDispensing.KohiJyukyusyaNo;
                            }
                            epsDispensing.IsDeleted = inputDataEpsDispensing.IsDeleted;
                            epsDispensing.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        }
                    }

                    TrackingDataContext.SaveChanges();
                    transaction.Commit();
                    isSuccess = true;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message);
                    transaction.Rollback();
                }
            });
            return isSuccess;
        }

        public void ClearTrashReference()
        {
            var now = CIUtil.GetJapanDateTimeNow();

            var configDaysDict = NoTrackingDataContext.SystemConfs
                .Where(c => c.GrpCd == 100040 && c.GrpEdaNo == 6)
                .ToDictionary(c => c.HpId, c => (int)Math.Floor(c.Val));

            var expiredReferences = TrackingDataContext.EpsReferences.ToList()
                .Where(reference =>
                    reference.CreateDate.AddDays(
                        configDaysDict.TryGetValue(reference.HpId, out var configDays)
                            ? configDays
                            : EpsConstant.DAYS_TO_CLEAR_EPS_REF
                    ) < now
                ).ToList();

            if (expiredReferences.Any())
            {
                TrackingDataContext.EpsReferences.RemoveRange(expiredReferences);
                TrackingDataContext.SaveChanges();
            }
        }
        
        public bool UpsertDispensingInf(int inputDataHpId, List<EpsDispensingModel> inputDataEpsDispensingModels)
        {
            var strategy = TrackingDataContext.Database.CreateExecutionStrategy();

            var isSuccess = false;
            strategy.Execute(() =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    var dispensingIds = inputDataEpsDispensingModels.Select(r => r.Id).ToList();
                    var epsDispensingList =
                        TrackingDataContext.EpsDispensings.Where(d => dispensingIds.Contains(d.Id));

                    foreach (var inputDataEpsDispensing in inputDataEpsDispensingModels)
                    {
                        inputDataEpsDispensing.PrescriptionId = inputDataEpsDispensing.PrescriptionId ?? string.Empty;
                        inputDataEpsDispensing.DispensingResultId = inputDataEpsDispensing.DispensingResultId ?? string.Empty;
                        inputDataEpsDispensing.HokensyaNo = inputDataEpsDispensing.HokensyaNo ?? string.Empty;
                        inputDataEpsDispensing.Kigo = inputDataEpsDispensing.Kigo ?? string.Empty;
                        inputDataEpsDispensing.Bango = inputDataEpsDispensing.Bango ?? string.Empty;
                        inputDataEpsDispensing.EdaNo = inputDataEpsDispensing.EdaNo ?? string.Empty;
                        inputDataEpsDispensing.ReceptionPharmacyName = inputDataEpsDispensing.ReceptionPharmacyName ?? string.Empty;
                        inputDataEpsDispensing.CancelReason = inputDataEpsDispensing.CancelReason ?? string.Empty;
                        inputDataEpsDispensing.KohiFutansyaNo = inputDataEpsDispensing.KohiFutansyaNo ?? string.Empty;
                        inputDataEpsDispensing.KohiJyukyusyaNo = inputDataEpsDispensing.KohiJyukyusyaNo ?? string.Empty;
                        inputDataEpsDispensing.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        var epsDispensing = epsDispensingList.FirstOrDefault(r =>
                            r.Id == inputDataEpsDispensing.Id);
                        if (epsDispensing != null)
                        {
                            epsDispensing.MessageFlg = inputDataEpsDispensing.MessageFlg;
                            epsDispensing.DispensingDocument = inputDataEpsDispensing.DispensingDocument;
                            epsDispensing.ReceptionPharmacyName = inputDataEpsDispensing.ReceptionPharmacyName;
                            epsDispensing.DispensingDate = inputDataEpsDispensing.DispensingDate;
                            epsDispensing.DispensingTimes = inputDataEpsDispensing.DispensingTimes;
                            epsDispensing.IsDeleted = inputDataEpsDispensing.IsDeleted;
                            epsDispensing.UpdateDate = CIUtil.GetJapanDateTimeNow();
                        }
                        else
                        {
                            TrackingDataContext.EpsDispensings.AddRange(Mapper.Map(inputDataEpsDispensing, new EpsDispensing()));
                        }
                    }

                    TrackingDataContext.SaveChanges();
                    transaction.Commit();
                    isSuccess = true;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message);
                    transaction.Rollback();
                }
            });
            return isSuccess;
        }

        public (bool UpdateHoken, bool UpdateKohi, bool PrescriptionMatched, long PtId) CheckCsvHokenInf(int inputDataHpId, string inputDataPrescriptionId, string inputDataHokensyaNo,
            string inputDataKigo, string inputDataBango, string inputDataEdaNo, string inputDataFutansyaNo,
            string inputDataJyukyusyaNo)
        {
            var prescriptionMatched = false;
            var updateHoken = false;
            var updateKohi = false;
            long ptId = 0;

            var epsDispensing = NoTrackingDataContext.EpsDispensings
                .Where(d => d.HpId == inputDataHpId && d.PrescriptionId == inputDataPrescriptionId).ToList();

            if (epsDispensing.Any())
            {
                var epsPrescription = NoTrackingDataContext.EpsPrescriptions
                    .FirstOrDefault(p => p.HpId == inputDataHpId && p.PrescriptionId == inputDataPrescriptionId);
                if (epsPrescription != null)
                {
                    prescriptionMatched = true;
                }

                if (!string.IsNullOrEmpty(inputDataHokensyaNo) && !(inputDataHokensyaNo.Substring(0, 2) == "12" && inputDataHokensyaNo.Length == 8))
                {
                    var hokenInf = NoTrackingDataContext.PtHokenInfs.FirstOrDefault(h => h.HokensyaNo == inputDataHokensyaNo && h.Kigo == inputDataKigo && h.Bango == inputDataBango && h.EdaNo == inputDataEdaNo && h.IsDeleted == DeleteTypes.None);
                    if (hokenInf != null)
                    {
                        updateHoken = true;
                        ptId = hokenInf.PtId;
                    }
                }

                if (!string.IsNullOrEmpty(inputDataHokensyaNo) && inputDataHokensyaNo.Substring(0, 2) == "12" && inputDataHokensyaNo.Length == 8)
                {
                    var hokiInf = NoTrackingDataContext.PtKohis.FirstOrDefault(h => h.FutansyaNo == inputDataFutansyaNo && h.JyukyusyaNo == inputDataJyukyusyaNo && h.IsDeleted == DeleteTypes.None);
                    if (hokiInf != null)
                    {
                        updateKohi = true;
                        ptId = hokiInf.PtId;
                    }
                }
            }
            
            return (updateHoken, updateKohi, prescriptionMatched, ptId);
        }

        public (DateTime, DateTime, DateTime, DateTime) GetPrintSettingFlag(int hpId, long ptId, long raiinNo)
        {
            DateTime logDateExportSijisen = DateTime.MinValue;
            DateTime logDateOutdrug = DateTime.MinValue;
            DateTime logDateIndrug = DateTime.MinValue;
            DateTime logDateExportDrugInfo = DateTime.MinValue;
            var checkExportSijisen = NoTrackingDataContext.AuditTrailLogs.Where(x => x.HpId == hpId && x.PtId == ptId && x.RaiinNo == raiinNo && x.EventCd == EventCode.ReportSijisen);
            var checkOutdrug = NoTrackingDataContext.AuditTrailLogs.Where(x => x.HpId == hpId && x.PtId == ptId && x.RaiinNo == raiinNo && x.EventCd == EventCode.ReportOutDrug);
            var checkIndrug = NoTrackingDataContext.AuditTrailLogs.Where(x => x.HpId == hpId && x.PtId == ptId && x.RaiinNo == raiinNo && x.EventCd == EventCode.ReportInDrug);
            var checkExportDrugInfo = NoTrackingDataContext.AuditTrailLogs.Where(x => x.HpId == hpId && x.PtId == ptId && x.RaiinNo == raiinNo && x.EventCd == EventCode.ReportDrugInf);

            if (checkExportSijisen.Any())
            {
                checkExportSijisen = checkExportSijisen.OrderByDescending(x => x.LogDate);
                logDateExportSijisen = checkExportSijisen.FirstOrDefault()?.LogDate ?? DateTime.MinValue;
            }

            if (checkOutdrug.Any())
            {
                checkOutdrug = checkOutdrug.OrderByDescending(x => x.LogDate);
                logDateOutdrug = checkOutdrug.FirstOrDefault()?.LogDate ?? DateTime.MinValue;
            }

            if (checkIndrug.Any())
            {
                checkIndrug = checkIndrug.OrderByDescending(x => x.LogDate);
                logDateIndrug = checkIndrug.FirstOrDefault()?.LogDate ?? DateTime.MinValue;
            }

            if (checkExportDrugInfo.Any())
            {
                checkExportDrugInfo = checkExportDrugInfo.OrderByDescending(x => x.LogDate);
                logDateExportDrugInfo = checkExportDrugInfo.FirstOrDefault()?.LogDate ?? DateTime.MinValue;
            }
            return (logDateExportSijisen, logDateOutdrug, logDateIndrug, logDateExportDrugInfo);
        }
    }
}
