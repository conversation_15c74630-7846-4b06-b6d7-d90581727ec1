using Domain.Models.KensaCenterPartnership;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repositories;

public class KensaCenterPartnershipRepository : RepositoryBase, IKensaCenterPartnershipRepository
{
    public KensaCenterPartnershipRepository(ITenantProvider tenantProvider) : base(tenantProvider)
    {
    }

    public void ReleaseResource()
    {
        DisposeDataContext();
    }

    /*
     * 検査会社(common_kensa_center_mst)と契約情報(kensa_mst_partnership)を取得
     * 契約情報は hpId で絞り込んだものを取得
     ＊ 未契約の検査会社は hpId が null になっている
     * centerCd, kyeWord があれば、それで絞り込む
     */
    public List<KensaCenterPartnershipModel> GetKensaCenterPartnership(int hpId, string? centerCd)
    {
        var currentDate = CIUtil.DateTimeToInt(DateTime.UtcNow);
        var allPartnerships = NoTrackingDataContext.KensaCenterPartnership
            .Where(p => p.HpId == hpId && p.IsDeleted == 0)
            .ToList();

        var allCenters = NoTrackingDataContext.CommonKensaCenterMst.ToList();

        var latestPartnerships = allPartnerships
            .GroupBy(p => p.CenterCd)
            .Select(g => g.OrderByDescending(p => p.StartDate).First())
            .ToDictionary(p => p.CenterCd);

        // kensa_center_partnership に登録されていない common_kensa_center_mst のデータも取得する
        var query = from center in allCenters
                    let partnership = latestPartnerships.ContainsKey(center.CenterCd)
                        ? latestPartnerships[center.CenterCd]
                        : null
                    select new
                    {
                        center,
                        partnership
                    };

        if (!string.IsNullOrEmpty(centerCd))
        {
            query = query.Where(
                p => centerCd.Contains(p.center.CenterCd)
            );
        }

        var result = new List<KensaCenterPartnershipModel>();
        foreach (var record in query)
        {
            if (record.partnership == null)
            {
                result.Add(new KensaCenterPartnershipModel(
                    hpId: null,
                    centerCd: record.center.CenterCd,
                    startDate: null,
                    endDate: null,
                    masterUpdateDate: null,
                    centerKey: record.center.CenterKey,
                    centerName: record.center.CenterName,
                    dspCenterName: record.center.DspCenterName
                ));
            }
            else
            {
                result.Add(new KensaCenterPartnershipModel(
                    hpId: record.partnership.HpId,
                    centerCd: record.center.CenterCd,
                    startDate: record.partnership.StartDate,
                    endDate: record.partnership.EndDate,
                    masterUpdateDate: record.partnership.MasterUpdateDate,
                    centerKey: record.center.CenterKey,
                    centerName: record.center.CenterName,
                    dspCenterName: record.center.DspCenterName
                ));
            }
        }

        if (string.IsNullOrEmpty(centerCd))
        {
            result = result.OrderByDescending(x => x.StartDate ?? 0).ToList();
        }
        return result;
    }

    /*
     * クリニックと検査会社の契約を登録する
     */
    public KensaCenterPartnershipModel RegisterKensaCenterPartnership(int hpId, string centerCd, int startDate, int endDate)
    {
        var exists = TrackingDataContext.KensaCenterPartnership
            .Any(p => p.HpId == hpId && p.CenterCd == centerCd && p.IsDeleted == 0);
        if (exists)
        {
            throw new KensaCenterPartnershipSpecialException();
        }

        var newPartnership = new KensaCenterPartnership()
        {
            HpId = hpId,
            CenterCd = centerCd,
            StartDate = startDate,
            EndDate = endDate,
            IsDeleted = 0,
            MasterUpdateDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Now),
            CreateDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Now),
            CreateId = 0,
            UpdateDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Now),
            UpdateId = 0
        };

        try
        {
            TrackingDataContext.KensaCenterPartnership.Add(newPartnership);
            TrackingDataContext.SaveChanges();
        }
        catch (Exception ex)
        {
            var innerMessage = ex.InnerException?.Message ?? "No additional details.";
            throw new Exception($"kensa_center_partnership の保存中にエラーが発生しました: {innerMessage}", ex);
        }
        Console.WriteLine("after save");

        // common_kensa_item_mst（CommonCenterKensaMst）からcenter_cdが一致するレコードを取得し、ten_mstにコピー
        CopyCommonKensaItemsToTenMst(hpId, centerCd, startDate, null, null);

        return GetKensaCenterPartnership(hpId, centerCd).FirstOrDefault();
    }

    /*
     * 契約期間の更新
     * hp_id, center_cd, start_date が複合主キーとして設定されてるため、そのままでは開始日を更新できない
     * よって、既存の契約を物理削除して新しい契約を登録する
     */
    public KensaCenterPartnershipModel UpdateKensaCenterPartnership(int hpId, string centerCd, int oldStartDate, int startDate, int endDate)
    {
        var partnership = TrackingDataContext.KensaCenterPartnership.FirstOrDefault(
            p => p.CenterCd == centerCd &&
            p.HpId == hpId &&
            p.StartDate == oldStartDate &&
            p.IsDeleted == 0
        );
        if (partnership == null)
        {
            throw new Exception("指定された kensa_center_partnership のデータが見つかりません。");
        }

        TrackingDataContext.KensaCenterPartnership.Remove(partnership);

        try
        {
            TrackingDataContext.SaveChanges();
        }
        catch (Exception ex)
        {
            var innerMessage = ex.InnerException?.Message ?? "No additional details.";
            throw new Exception($"kensa_center_partnership の更新中にエラーが発生しました: {innerMessage}", ex);
        }

        return RegisterKensaCenterPartnership(hpId, centerCd, startDate, endDate);
    }

    /*
     * 契約解除（論理削除）
     */
    public bool UnregisterKensaCenterPartnership(int hpId, string centerCd, int startDate)
    {
        // 仕様が変わって複数契約を持てるようになった場合は create_date で絞り込む必要がある。
        var partnership = TrackingDataContext.KensaCenterPartnership.FirstOrDefault(
            p => p.CenterCd == centerCd && p.HpId == hpId && p.StartDate == startDate && p.IsDeleted == 0);
        if (partnership == null)
        {
            throw new Exception($"kensa_center_partnership のデータが見つかりません。");
        }

        partnership.IsDeleted = 1;

        try
        {
            TrackingDataContext.SaveChanges();
        }
        catch (DbUpdateConcurrencyException)
        {
            throw new KensaCenterPartnershipSpecialException();
        }
        catch (Exception ex)
        {
            var innerMessage = ex.InnerException?.Message ?? "No additional details.";
            throw new Exception($"kensa_center_partnership の論理削除中にエラーが発生しました: {innerMessage}", ex);
        }

        return true;
    }



    public List<KensaCenterPartnershipModel> GetKensaCenterPartnershipBySinDate(int hpId, int sinDate)
    {
        var tenMsts = NoTrackingDataContext.TenMsts.Where(e =>
            e.HpId == hpId &&
            !string.IsNullOrEmpty(e.CenterCd) &&
            e.CenterCd != "000" &&
            e.StartDate <= sinDate &&
            sinDate <= e.EndDate &&
            e.IsDeleted == DeleteTypes.None);
        var kensaCenterPartnerships = NoTrackingDataContext.KensaCenterPartnership
            .Where(e => e.HpId == hpId && e.StartDate <= sinDate && sinDate <= e.EndDate && e.IsDeleted == DeleteTypes.None);

        var join = (
                from kensaCenterPartnership in kensaCenterPartnerships
                join tenMst in tenMsts on
                    new { kensaCenterPartnership.HpId, kensaCenterPartnership.CenterCd } equals
                    new { tenMst.HpId, tenMst.CenterCd }
                select new
                {
                    kensaCenterPartnership
                }
            ).ToList();

        if (join == null) return new();
        return join.Select(e => new KensaCenterPartnershipModel(e.kensaCenterPartnership.HpId, e.kensaCenterPartnership.CenterCd, e.kensaCenterPartnership.StartDate, e.kensaCenterPartnership.EndDate)).ToList();
    }

    public void UpdateKensaCenterPartnershipMstUpdateDate(int hpId)
    {
        var currentDate = CIUtil.DateTimeToInt(DateTime.Now);

        var partnerships = TrackingDataContext.KensaCenterPartnership
            .Where(x => x.HpId == hpId &&
                       x.StartDate <= currentDate &&
                       (x.EndDate == 0 || currentDate <= x.EndDate) &&
                       x.IsDeleted == DeleteTypes.None)
            .ToList();

        partnerships.ForEach(x =>
        {
            var beforeMasterUpdateDate = x.MasterUpdateDate;
            x.MasterUpdateDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Now);
            x.UpdateDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Now);
            x.UpdateId = 0;
            try
            {
                // 他端末との競合を避けるため、先にmaster_update_dateを更新しておく
                TrackingDataContext.SaveChanges();
            }
            catch (Exception ex)
            {
                var innerMessage = ex.InnerException?.Message ?? "No additional details.";
                throw new Exception($"kensa_center_partnershipの更新中にエラーが発生しました: {innerMessage}", ex);
            }
            CopyCommonKensaItemsToTenMst(hpId, x.CenterCd, x.StartDate, x.CreateDate, beforeMasterUpdateDate);
        });
    }

    private void CopyCommonKensaItemsToTenMst(int hpId, string centerCd, int startDate, DateTime? createDate, DateTime? masterUpdateDate)
    {
        var commonKensaItems = NoTrackingDataContext.CommonCenterItemMst
            .Where(x => x.CenterCd == centerCd && (masterUpdateDate == null || x.UpdateDate >= masterUpdateDate))
            .ToList();

        if (!commonKensaItems.Any())
        {
            return;
        }

        foreach (var commonItem in commonKensaItems)
        {
            var existingTenMst = TrackingDataContext.TenMsts
                .FirstOrDefault(x => x.HpId == hpId &&
                                   x.CenterCd == commonItem.CenterCd &&
                                   x.ItemCd == commonItem.ItemCd &&
                                   x.StartDate == commonItem.StartDate &&
                                   x.IsDeleted == 0);

            if (existingTenMst == null)
            {
                var newTenMst = CreateNewTenMst(hpId, commonItem);
                if (newTenMst != null)
                {
                    newTenMst.HpId = hpId;
                    TrackingDataContext.TenMsts.Add(newTenMst);
                }
            }
            else
            {
                SetTenMstProperties(hpId, commonItem, existingTenMst);
                existingTenMst.UpdateDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Now);
                existingTenMst.UpdateId = 0;
                TrackingDataContext.TenMsts.Update(existingTenMst);
            }
        }
        try
        {
            TrackingDataContext.SaveChanges();
        }
        catch (Exception ex)
        {
            var partnership = TrackingDataContext.KensaCenterPartnership.FirstOrDefault(x =>
                x.HpId == hpId &&
                x.CenterCd == centerCd &&
                x.StartDate == startDate &&
                x.CreateDate == createDate &&
                x.IsDeleted == DeleteTypes.None);

            if (partnership != null && masterUpdateDate != null)
            {
                partnership.MasterUpdateDate = masterUpdateDate.Value;
                partnership.UpdateDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Now);
                partnership.UpdateId = 0;
                try
                {
                    TrackingDataContext.SaveChanges();
                }
                catch (Exception ex2)
                {
                    var innerMessage2 = ex2.InnerException?.Message ?? "No additional details.";
                    throw new Exception($"kensa_center_partnershipのMasterUpdateDate復旧中にエラーが発生しました: {innerMessage2}", ex2);
                }
            }
            var innerMessage = ex.InnerException?.Message ?? "No additional details.";
            throw new Exception($"ten_mst の更新中にエラーが発生しました: {innerMessage}", ex);
        }
    }

    private TenMst? CreateNewTenMst(int hpId, CommonCenterItemMst commonItem)
    {
        var newTenMst = new TenMst
        {
            HpId = hpId,
            OdrUnitName = "",
            IsAdopted = 0,
            KensaLabel = 0,
            IsDeleted = DeleteTypes.None,
            CreateDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Now),
            CreateId = 0,
            UpdateDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Now),
            UpdateId = 0
        };

        SetTenMstProperties(hpId, commonItem, newTenMst);
        return newTenMst;
    }

    private void SetTenMstProperties(int hpId, CommonCenterItemMst commonItem, TenMst trackingTenMst)
    {
        SetCommonProperties(trackingTenMst, commonItem);

        if (commonItem.SanteiItemCd != ItemCdConst.NoSantei)
        {
            // 審定項目の場合、既存のten_mstから値を取得して設定
            var existingTenMst = NoTrackingDataContext.TenMsts.FirstOrDefault(x =>
                x.HpId == hpId &&
                x.ItemCd == commonItem.SanteiItemCd &&
                x.StartDate <= commonItem.StartDate &&
                x.EndDate >= commonItem.StartDate &&
                x.IsDeleted == DeleteTypes.None);
            if (existingTenMst == null)
            {
                return;
            }
            SetPropertiesForSanteiTenMst(trackingTenMst, existingTenMst);
        }
        else
        {
            // 審定項目以外の場合、デフォルト値を設定
            SetPropertiesForNotSanteiTenMst(trackingTenMst, commonItem);
        }
    }

    private void SetCommonProperties(TenMst trackingTenMst, CommonCenterItemMst commonItem)
    {
        trackingTenMst.ItemCd = commonItem.ItemCd;
        trackingTenMst.StartDate = commonItem.StartDate;
        trackingTenMst.EndDate = commonItem.EndDate ?? 0;
        trackingTenMst.Name = commonItem.Name;
        trackingTenMst.KanaName1 = commonItem.KanaName;
        trackingTenMst.SanteiItemCd = commonItem.SanteiItemCd;
        trackingTenMst.KensaItemCd = commonItem.KensaItemCd;
        trackingTenMst.CenterCd = commonItem.CenterCd;
        trackingTenMst.UpdateDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Now);
        trackingTenMst.UpdateId = 0;
    }

    private void SetPropertiesForNotSanteiTenMst(TenMst trackingTenMst, CommonCenterItemMst commonItem)
    {
        trackingTenMst.MasterSbt = "S";
        trackingTenMst.SinKouiKbn = 61;
        trackingTenMst.KanaName2 = "";
        trackingTenMst.KanaName3 = "";
        trackingTenMst.KanaName4 = "";
        trackingTenMst.KanaName5 = "";
        trackingTenMst.KanaName6 = "";
        trackingTenMst.KanaName7 = "";
        trackingTenMst.RyosyuName = "";
        trackingTenMst.ReceName = commonItem.Name;
        trackingTenMst.TenId = 1;
        trackingTenMst.Ten = 0;
        trackingTenMst.ReceUnitCd = "";
        trackingTenMst.ReceUnitName = "";
        trackingTenMst.OdrUnitName = "";
        trackingTenMst.CnvUnitName = "";
        trackingTenMst.OdrTermVal = 0;
        trackingTenMst.CnvTermVal = 0;
        trackingTenMst.DefaultVal = 0;
        trackingTenMst.KoukiKbn = 0;
        trackingTenMst.HokatuKensa = 0;
        trackingTenMst.ByomeiKbn = 0;
        trackingTenMst.Igakukanri = 0;
        trackingTenMst.JitudayCount = 0;
        trackingTenMst.Jituday = 0;
        trackingTenMst.DayCount = 0;
        trackingTenMst.DrugKanrenKbn = 0;
        trackingTenMst.KizamiId = 0;
        trackingTenMst.KizamiMin = 0;
        trackingTenMst.KizamiMax = 0;
        trackingTenMst.KizamiVal = 0;
        trackingTenMst.KizamiTen = 0;
        trackingTenMst.KizamiErr = 0;
        trackingTenMst.MaxCount = 0;
        trackingTenMst.MaxCountErr = 0;
        trackingTenMst.TyuCd = "0";
        trackingTenMst.TyuSeq = "0";
        trackingTenMst.TusokuAge = 0;
        trackingTenMst.MinAge = "";
        trackingTenMst.MaxAge = "";
        trackingTenMst.AgeCheck = 0;
        trackingTenMst.TimeKasanKbn = 0;
        trackingTenMst.FutekiKbn = 0;
        trackingTenMst.FutekiSisetuKbn = 0;
        trackingTenMst.SyotiNyuyojiKbn = 0;
        trackingTenMst.LowWeightKbn = 0;
        trackingTenMst.HandanKbn = 0;
        trackingTenMst.HandanGrpKbn = 0;
        trackingTenMst.TeigenKbn = 0;
        trackingTenMst.SekituiKbn = 0;
        trackingTenMst.KeibuKbn = 0;
        trackingTenMst.AutoHougouKbn = 0;
        trackingTenMst.GairaiKanriKbn = 0;
        trackingTenMst.TusokuTargetKbn = 0;
        trackingTenMst.HokatuKbn = 0;
        trackingTenMst.TyoonpaNaisiKbn = 0;
        trackingTenMst.AutoFungoKbn = 0;
        trackingTenMst.TyoonpaGyokoKbn = 0;
        trackingTenMst.GazoKasan = 0;
        trackingTenMst.KansatuKbn = 0;
        trackingTenMst.MasuiKbn = 0;
        trackingTenMst.FukubikuNaisiKasan = 0;
        trackingTenMst.FukubikuKotunanKasan = 0;
        trackingTenMst.MasuiKasan = 0;
        trackingTenMst.MoniterKasan = 0;
        trackingTenMst.ToketuKasan = 0;
        trackingTenMst.TenKbnNo = "";
        trackingTenMst.ShortstayOpe = 0;
        trackingTenMst.BuiKbn = 0;
        trackingTenMst.Sisetucd1 = 0;
        trackingTenMst.Sisetucd2 = 0;
        trackingTenMst.Sisetucd3 = 0;
        trackingTenMst.Sisetucd4 = 0;
        trackingTenMst.Sisetucd5 = 0;
        trackingTenMst.Sisetucd6 = 0;
        trackingTenMst.Sisetucd7 = 0;
        trackingTenMst.Sisetucd8 = 0;
        trackingTenMst.Sisetucd9 = 0;
        trackingTenMst.Sisetucd10 = 0;
        trackingTenMst.AgekasanMin1 = "";
        trackingTenMst.AgekasanMax1 = "";
        trackingTenMst.AgekasanCd1 = "";
        trackingTenMst.AgekasanMin2 = "";
        trackingTenMst.AgekasanMax2 = "";
        trackingTenMst.AgekasanCd2 = "";
        trackingTenMst.AgekasanMin3 = "";
        trackingTenMst.AgekasanMax3 = "";
        trackingTenMst.AgekasanCd3 = "";
        trackingTenMst.AgekasanMin4 = "";
        trackingTenMst.AgekasanMax4 = "";
        trackingTenMst.AgekasanCd4 = "";
        trackingTenMst.KensaCmt = 0;
        trackingTenMst.MadokuKbn = 0;
        trackingTenMst.SinkeiKbn = 0;
        trackingTenMst.SeibutuKbn = 0;
        trackingTenMst.ZoueiKbn = 0;
        trackingTenMst.DrugKbn = 0;
        trackingTenMst.ZaiKbn = 0;
        trackingTenMst.Capacity = 0;
        trackingTenMst.KohatuKbn = 0;
        trackingTenMst.TokuzaiAgeKbn = 0;
        trackingTenMst.SansoKbn = 0;
        trackingTenMst.TokuzaiSbt = 0;
        trackingTenMst.MaxPrice = 0;
        trackingTenMst.MaxTen = 0;
        trackingTenMst.SyukeiSaki = "0";
        trackingTenMst.CdKbn = "";
        trackingTenMst.CdSyo = 0;
        trackingTenMst.CdBu = 0;
        trackingTenMst.CdKbnno = 0;
        trackingTenMst.CdEdano = 0;
        trackingTenMst.CdKouno = 0;
        trackingTenMst.KokujiKbn = "";
        trackingTenMst.KokujiSyo = 0;
        trackingTenMst.KokujiBu = 0;
        trackingTenMst.KokujiKbnNo = 0;
        trackingTenMst.KokujiEdaNo = 0;
        trackingTenMst.KokujiKouNo = 0;
        trackingTenMst.Kokuji1 = "";
        trackingTenMst.Kokuji2 = "";
        trackingTenMst.KohyoJun = 0;
        trackingTenMst.YjCd = "";
        trackingTenMst.YakkaCd = "";
        trackingTenMst.SyusaiSbt = 0;
        trackingTenMst.SyohinKanren = "";
        trackingTenMst.UpdDate = 0;
        trackingTenMst.DelDate = 0;
        trackingTenMst.KeikaDate = 0;
        trackingTenMst.KokujiBetuno = 0;
        trackingTenMst.KokujiKbnno = 0;
        trackingTenMst.RousaiKbn = 0;
        trackingTenMst.SisiKbn = 0;
        trackingTenMst.ShotCnt = 0;
        trackingTenMst.IsNosearch = 0;
        trackingTenMst.IsNodspPaperRece = 0;
        trackingTenMst.IsNodspRece = 0;
        trackingTenMst.IsNodspRyosyu = 0;
        trackingTenMst.IsNodspKarte = 0;
        trackingTenMst.JihiSbt = 0;
        trackingTenMst.KazeiKbn = 0;
        trackingTenMst.YohoKbn = 0;
        trackingTenMst.IpnNameCd = "";
        trackingTenMst.FukuyoRise = 0;
        trackingTenMst.FukuyoMorning = 0;
        trackingTenMst.FukuyoDaytime = 0;
        trackingTenMst.FukuyoNight = 0;
        trackingTenMst.FukuyoSleep = 0;
        trackingTenMst.IsNodspYakutai = 0;
        trackingTenMst.ZaikeiPoint = 0;
        trackingTenMst.SuryoRoundupKbn = 0;
        trackingTenMst.KouseisinKbn = 0;
        trackingTenMst.ChusyaDrugSbt = 0;
        trackingTenMst.KensaFukusuSantei = 0;
        trackingTenMst.SanteiItemCd = "9999999999";
        trackingTenMst.SanteigaiKbn = 1;
        trackingTenMst.KensaItemCd = "0";
        trackingTenMst.KensaItemSeqNo = 0;
        trackingTenMst.RenkeiCd1 = "0";
        trackingTenMst.RenkeiCd2 = "0";
        trackingTenMst.SaiketuKbn = 0;
        trackingTenMst.CmtKbn = 0;
        trackingTenMst.CmtCol1 = 0;
        trackingTenMst.CmtColKeta1 = 0;
        trackingTenMst.CmtCol2 = 0;
        trackingTenMst.CmtColKeta2 = 0;
        trackingTenMst.CmtCol3 = 0;
        trackingTenMst.CmtColKeta3 = 0;
        trackingTenMst.CmtCol4 = 0;
        trackingTenMst.CmtColKeta4 = 0;
        trackingTenMst.SelectCmtId = 0;
        trackingTenMst.CreateMachine = "";
        trackingTenMst.UpdateMachine = "";
        trackingTenMst.CmtSbt = 0;
        trackingTenMst.GairaiKansen = 0;
        trackingTenMst.JibiAgeKasan = 0;
        trackingTenMst.JibiSyonikokin = 0;
        trackingTenMst.YohoCd = "";
        trackingTenMst.YohoHosokuKbn = 0;
        trackingTenMst.YohoHosokuRec = 0;
        trackingTenMst.BaseUp1 = "";
        trackingTenMst.BaseUp2 = "";
        trackingTenMst.SaiseizoKasan = "";
        trackingTenMst.SaiseizoKiki = "";
        trackingTenMst.IsDeleted = DeleteTypes.None;
        trackingTenMst.TyokiItemCd = "";
        trackingTenMst.SenteiRyoyoKbn = 0;
        trackingTenMst.SenteiRyoyoYakka = 0;
    }

    private void SetPropertiesForSanteiTenMst(TenMst trackingTenMst, TenMst tenMst)
    {
        trackingTenMst.MasterSbt = tenMst.MasterSbt;
        trackingTenMst.SinKouiKbn = tenMst.SinKouiKbn;
        trackingTenMst.KanaName2 = tenMst.KanaName2;
        trackingTenMst.KanaName3 = tenMst.KanaName3;
        trackingTenMst.KanaName4 = tenMst.KanaName4;
        trackingTenMst.KanaName5 = tenMst.KanaName5;
        trackingTenMst.KanaName6 = tenMst.KanaName6;
        trackingTenMst.KanaName7 = tenMst.KanaName7;
        trackingTenMst.RyosyuName = tenMst.RyosyuName;
        trackingTenMst.ReceName = tenMst.ReceName;
        trackingTenMst.TenId = tenMst.TenId;
        trackingTenMst.Ten = tenMst.Ten;
        trackingTenMst.ReceUnitCd = tenMst.ReceUnitCd;
        trackingTenMst.ReceUnitName = tenMst.ReceUnitName;
        trackingTenMst.CnvUnitName = tenMst.CnvUnitName;
        trackingTenMst.OdrTermVal = tenMst.OdrTermVal;
        trackingTenMst.CnvTermVal = tenMst.CnvTermVal;
        trackingTenMst.DefaultVal = tenMst.DefaultVal;
        trackingTenMst.KoukiKbn = tenMst.KoukiKbn;
        trackingTenMst.HokatuKensa = tenMst.HokatuKensa;
        trackingTenMst.ByomeiKbn = tenMst.ByomeiKbn;
        trackingTenMst.Igakukanri = tenMst.Igakukanri;
        trackingTenMst.JitudayCount = tenMst.JitudayCount;
        trackingTenMst.Jituday = tenMst.Jituday;
        trackingTenMst.DayCount = tenMst.DayCount;
        trackingTenMst.DrugKanrenKbn = tenMst.DrugKanrenKbn;
        trackingTenMst.KizamiId = tenMst.KizamiId;
        trackingTenMst.KizamiMin = tenMst.KizamiMin;
        trackingTenMst.KizamiMax = tenMst.KizamiMax;
        trackingTenMst.KizamiVal = tenMst.KizamiVal;
        trackingTenMst.KizamiTen = tenMst.KizamiTen;
        trackingTenMst.KizamiErr = tenMst.KizamiErr;
        trackingTenMst.MaxCount = tenMst.MaxCount;
        trackingTenMst.MaxCountErr = tenMst.MaxCountErr;
        trackingTenMst.TyuCd = tenMst.TyuCd;
        trackingTenMst.TyuSeq = tenMst.TyuSeq;
        trackingTenMst.MaxAge = tenMst.MaxAge;
        trackingTenMst.AgeCheck = tenMst.AgeCheck;
        trackingTenMst.TimeKasanKbn = tenMst.TimeKasanKbn;
        trackingTenMst.FutekiKbn = tenMst.FutekiKbn;
        trackingTenMst.FutekiSisetuKbn = tenMst.FutekiSisetuKbn;
        trackingTenMst.SyotiNyuyojiKbn = tenMst.SyotiNyuyojiKbn;
        trackingTenMst.LowWeightKbn = tenMst.LowWeightKbn;
        trackingTenMst.HandanKbn = tenMst.HandanKbn;
        trackingTenMst.HandanGrpKbn = tenMst.HandanGrpKbn;
        trackingTenMst.TeigenKbn = tenMst.TeigenKbn;
        trackingTenMst.SekituiKbn = tenMst.SekituiKbn;
        trackingTenMst.KeibuKbn = tenMst.KeibuKbn;
        trackingTenMst.AutoHougouKbn = tenMst.AutoHougouKbn;
        trackingTenMst.GairaiKanriKbn = tenMst.GairaiKanriKbn;
        trackingTenMst.TusokuTargetKbn = tenMst.TusokuTargetKbn;
        trackingTenMst.HokatuKbn = tenMst.HokatuKbn;
        trackingTenMst.TyoonpaNaisiKbn = tenMst.TyoonpaNaisiKbn;
        trackingTenMst.AutoFungoKbn = tenMst.AutoFungoKbn;
        trackingTenMst.TyoonpaGyokoKbn = tenMst.TyoonpaGyokoKbn;
        trackingTenMst.GazoKasan = tenMst.GazoKasan;
        trackingTenMst.KansatuKbn = tenMst.KansatuKbn;
        trackingTenMst.MasuiKbn = tenMst.MasuiKbn;
        trackingTenMst.FukubikuNaisiKasan = tenMst.FukubikuNaisiKasan;
        trackingTenMst.FukubikuKotunanKasan = tenMst.FukubikuKotunanKasan;
        trackingTenMst.MasuiKasan = tenMst.MasuiKasan;
        trackingTenMst.MoniterKasan = tenMst.MoniterKasan;
        trackingTenMst.ToketuKasan = tenMst.ToketuKasan;
        trackingTenMst.TenKbnNo = tenMst.TenKbnNo;
        trackingTenMst.ShortstayOpe = tenMst.ShortstayOpe;
        trackingTenMst.BuiKbn = tenMst.BuiKbn;
        trackingTenMst.Sisetucd1 = tenMst.Sisetucd1;
        trackingTenMst.Sisetucd2 = tenMst.Sisetucd2;
        trackingTenMst.Sisetucd3 = tenMst.Sisetucd3;
        trackingTenMst.Sisetucd4 = tenMst.Sisetucd4;
        trackingTenMst.Sisetucd5 = tenMst.Sisetucd5;
        trackingTenMst.Sisetucd6 = tenMst.Sisetucd6;
        trackingTenMst.Sisetucd7 = tenMst.Sisetucd7;
        trackingTenMst.Sisetucd8 = tenMst.Sisetucd8;
        trackingTenMst.Sisetucd9 = tenMst.Sisetucd9;
        trackingTenMst.Sisetucd10 = tenMst.Sisetucd10;
        trackingTenMst.AgekasanMin1 = tenMst.AgekasanMin1;
        trackingTenMst.AgekasanMax1 = tenMst.AgekasanMax1;
        trackingTenMst.AgekasanCd1 = tenMst.AgekasanCd1;
        trackingTenMst.AgekasanMin2 = tenMst.AgekasanMin2;
        trackingTenMst.AgekasanMax2 = tenMst.AgekasanMax2;
        trackingTenMst.AgekasanCd2 = tenMst.AgekasanCd2;
        trackingTenMst.AgekasanMin3 = tenMst.AgekasanMin3;
        trackingTenMst.AgekasanMax3 = tenMst.AgekasanMax3;
        trackingTenMst.AgekasanCd3 = tenMst.AgekasanCd3;
        trackingTenMst.AgekasanMin4 = tenMst.AgekasanMin4;
        trackingTenMst.AgekasanMax4 = tenMst.AgekasanMax4;
        trackingTenMst.AgekasanCd4 = tenMst.AgekasanCd4;
        trackingTenMst.KensaCmt = tenMst.KensaCmt;
        trackingTenMst.MadokuKbn = tenMst.MadokuKbn;
        trackingTenMst.SinkeiKbn = tenMst.SinkeiKbn;
        trackingTenMst.SeibutuKbn = tenMst.SeibutuKbn;
        trackingTenMst.ZoueiKbn = tenMst.ZoueiKbn;
        trackingTenMst.DrugKbn = tenMst.DrugKbn;
        trackingTenMst.ZaiKbn = tenMst.ZaiKbn;
        trackingTenMst.Capacity = tenMst.Capacity;
        trackingTenMst.KohatuKbn = tenMst.KohatuKbn;
        trackingTenMst.TokuzaiAgeKbn = tenMst.TokuzaiAgeKbn;
        trackingTenMst.SansoKbn = tenMst.SansoKbn;
        trackingTenMst.TokuzaiSbt = tenMst.TokuzaiSbt;
        trackingTenMst.MaxPrice = tenMst.MaxPrice;
        trackingTenMst.MaxTen = tenMst.MaxTen;
        trackingTenMst.SyukeiSaki = tenMst.SyukeiSaki;
        trackingTenMst.CdKbn = tenMst.CdKbn;
        trackingTenMst.CdSyo = tenMst.CdSyo;
        trackingTenMst.CdBu = tenMst.CdBu;
        trackingTenMst.CdKbnno = tenMst.CdKbnno;
        trackingTenMst.CdEdano = tenMst.CdEdano;
        trackingTenMst.CdKouno = tenMst.CdKouno;
        trackingTenMst.KokujiKbn = tenMst.KokujiKbn;
        trackingTenMst.KokujiSyo = tenMst.KokujiSyo;
        trackingTenMst.KokujiBu = tenMst.KokujiBu;
        trackingTenMst.KokujiKbnNo = tenMst.KokujiKbnNo;
        trackingTenMst.KokujiEdaNo = tenMst.KokujiEdaNo;
        trackingTenMst.KokujiKouNo = tenMst.KokujiKouNo;
        trackingTenMst.Kokuji1 = tenMst.Kokuji1;
        trackingTenMst.Kokuji2 = tenMst.Kokuji2;
        trackingTenMst.KohyoJun = tenMst.KohyoJun;
        trackingTenMst.YjCd = tenMst.YjCd;
        trackingTenMst.YakkaCd = tenMst.YakkaCd;
        trackingTenMst.SyusaiSbt = tenMst.SyusaiSbt;
        trackingTenMst.SyohinKanren = tenMst.SyohinKanren;
        trackingTenMst.UpdDate = tenMst.UpdDate;
        trackingTenMst.DelDate = tenMst.DelDate;
        trackingTenMst.KeikaDate = tenMst.KeikaDate;
        trackingTenMst.KokujiBetuno = tenMst.KokujiBetuno;
        trackingTenMst.KokujiKbnno = tenMst.KokujiKbnno;
        trackingTenMst.RousaiKbn = tenMst.RousaiKbn;
        trackingTenMst.SisiKbn = tenMst.SisiKbn;
        trackingTenMst.ShotCnt = tenMst.ShotCnt;
        trackingTenMst.IsNosearch = tenMst.IsNosearch;
        trackingTenMst.IsNodspPaperRece = tenMst.IsNodspPaperRece;
        trackingTenMst.IsNodspRece = tenMst.IsNodspRece;
        trackingTenMst.IsNodspRyosyu = tenMst.IsNodspRyosyu;
        trackingTenMst.IsNodspKarte = tenMst.IsNodspKarte;
        trackingTenMst.JihiSbt = tenMst.JihiSbt;
        trackingTenMst.KazeiKbn = tenMst.KazeiKbn;
        trackingTenMst.YohoKbn = tenMst.YohoKbn;
        trackingTenMst.IpnNameCd = tenMst.IpnNameCd;
        trackingTenMst.FukuyoRise = tenMst.FukuyoRise;
        trackingTenMst.FukuyoMorning = tenMst.FukuyoMorning;
        trackingTenMst.FukuyoDaytime = tenMst.FukuyoDaytime;
        trackingTenMst.FukuyoNight = tenMst.FukuyoNight;
        trackingTenMst.FukuyoSleep = tenMst.FukuyoSleep;
        trackingTenMst.IsNodspYakutai = tenMst.IsNodspYakutai;
        trackingTenMst.ZaikeiPoint = tenMst.ZaikeiPoint;
        trackingTenMst.SuryoRoundupKbn = tenMst.SuryoRoundupKbn;
        trackingTenMst.KouseisinKbn = tenMst.KouseisinKbn;
        trackingTenMst.ChusyaDrugSbt = tenMst.ChusyaDrugSbt;
        trackingTenMst.KensaFukusuSantei = tenMst.KensaFukusuSantei;
        trackingTenMst.SanteigaiKbn = tenMst.SanteigaiKbn;
        trackingTenMst.KensaItemSeqNo = tenMst.KensaItemSeqNo;
        trackingTenMst.RenkeiCd1 = tenMst.RenkeiCd1;
        trackingTenMst.RenkeiCd2 = tenMst.RenkeiCd2;
        trackingTenMst.SaiketuKbn = tenMst.SaiketuKbn;
        trackingTenMst.CmtKbn = tenMst.CmtKbn;
        trackingTenMst.CmtCol1 = tenMst.CmtCol1;
        trackingTenMst.CmtColKeta1 = tenMst.CmtColKeta1;
        trackingTenMst.CmtCol2 = tenMst.CmtCol2;
        trackingTenMst.CmtColKeta2 = tenMst.CmtColKeta2;
        trackingTenMst.CmtCol3 = tenMst.CmtCol3;
        trackingTenMst.CmtColKeta3 = tenMst.CmtColKeta3;
        trackingTenMst.CmtCol4 = tenMst.CmtCol4;
        trackingTenMst.CmtColKeta4 = tenMst.CmtColKeta4;
        trackingTenMst.SelectCmtId = tenMst.SelectCmtId;
        trackingTenMst.CreateMachine = tenMst.CreateMachine;
        trackingTenMst.UpdateMachine = tenMst.UpdateMachine;
        trackingTenMst.CmtSbt = tenMst.CmtSbt;
        trackingTenMst.GairaiKansen = tenMst.GairaiKansen;
        trackingTenMst.JibiAgeKasan = tenMst.JibiAgeKasan;
        trackingTenMst.JibiSyonikokin = tenMst.JibiSyonikokin;
        trackingTenMst.YohoCd = tenMst.YohoCd;
        trackingTenMst.YohoHosokuKbn = tenMst.YohoHosokuKbn;
        trackingTenMst.YohoHosokuRec = tenMst.YohoHosokuRec;
        trackingTenMst.BaseUp1 = tenMst.BaseUp1;
        trackingTenMst.BaseUp2 = tenMst.BaseUp2;
        trackingTenMst.SaiseizoKasan = tenMst.SaiseizoKasan;
        trackingTenMst.SaiseizoKiki = tenMst.SaiseizoKiki;
        trackingTenMst.IsDeleted = tenMst.IsDeleted;
        trackingTenMst.TyokiItemCd = tenMst.TyokiItemCd;
        trackingTenMst.SenteiRyoyoKbn = tenMst.SenteiRyoyoKbn;
        trackingTenMst.SenteiRyoyoYakka = tenMst.SenteiRyoyoYakka;
    }
}

public class KensaCenterPartnershipSpecialException : Exception
{
    public KensaCenterPartnershipSpecialException()
        : base("cannot process KensaCenterPartnership.") { }
}
