using Helper.Constants;
using Helper.Redis;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using System.Text.Json;
using Domain.Models.CustomButtonConf;
using Helper.Extension;
using Entity.Tenant;
using Infrastructure.Constants;
using Microsoft.EntityFrameworkCore;
using Amazon.S3;

namespace Infrastructure.Repositories
{
    public class CustomButtonConfRepository : RepositoryBase, ICustomButtonConfRepository
    {
        private readonly string key;
        private readonly IDatabase _cache;
        private readonly IConfiguration _configuration;
        private readonly IAmazonS3Service _amazonS3Service;

        public CustomButtonConfRepository(ITenantProvider tenantProvider, IConfiguration configuration,
            IAmazonS3Service amazonS3Service) : base(
            tenantProvider)
        {
            key = GetDomainKey();
            _configuration = configuration;
            GetRedis();
            _cache = RedisConnectorHelper.Connection.GetDatabase();
            _amazonS3Service = amazonS3Service;
        }

        public void GetRedis()
        {
            string connection =
                string.Concat(_configuration["Redis:RedisHost"], ":", _configuration["Redis:RedisPort"]);
            if (RedisConnectorHelper.RedisHost != connection)
            {
                RedisConnectorHelper.RedisHost = connection;
            }
        }

       public List<CustomButtonConfModel> GetCustomButtonConfList(int hpId, int ptId, long raiinNo)
        {
            List<CustomButtonConfModel> result;
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(p => p.HpId == hpId && p.PtId == ptId && p.IsDelete == 0);
            var raiinInf = NoTrackingDataContext.RaiinInfs.FirstOrDefault(item =>
                item.HpId == hpId &&
                item.PtId == ptId &&
                item.RaiinNo == raiinNo &&
                item.IsDeleted == DeleteTypes.None);

            // If exist cache, get data from cache then return data
            var finalKey = key + CacheKeyConstant.CustomButtonConfGetList;

            if (_cache.KeyExists(finalKey))
            {
                var cacheString = _cache.StringGet(finalKey).AsString();
                result = !string.IsNullOrEmpty(cacheString)
                    ? JsonSerializer.Deserialize<List<CustomButtonConfModel>>(cacheString) ?? new()
                    : new();
            }
            else
            {
                // If not, get data from database
                var customButtonConfModels = NoTrackingDataContext.CustomButtonConfs
                    .Where(x => x.HpId == hpId)
                    .OrderBy(x => x.Sort).ToList();
                result = (from cus in customButtonConfModels
                    select new CustomButtonConfModel
                    (
                        cus.HpId,
                        cus.Id,
                        cus.Name,
                        cus.IsUrl,
                        cus.Path,
                        cus.WorkDir,
                        cus.Pattern,
                        cus.Sort,
                        cus.FileName,
                        null,
                        ReplacePatternInUrl(cus.Pattern, ptInf, raiinInf)
                    )).ToList();
                var tasks = result.Select(async file =>
                {
                    file.UrlImage = await _amazonS3Service.GetPreSignedUrlsAsync(file.Filename, -1, file.HpId);
                });
                Task.WhenAll(tasks).Wait();
                // Set data to new cache
                var json = JsonSerializer.Serialize(result);
                _cache.StringSet(finalKey, json);
            }

            return result;
        }

        public CustomButtonConfModel GetDetailCustomButtonConfList(int id, int hpId, int ptId, long raiinNo)
        {
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(p => p.HpId == hpId && p.PtId == ptId && p.IsDelete == 0);
            var raiinInf = NoTrackingDataContext.RaiinInfs.FirstOrDefault(item =>
                item.HpId == hpId &&
                item.PtId == ptId &&
                item.RaiinNo == raiinNo &&
                item.IsDeleted == DeleteTypes.None);
            var customButtonConfModel = NoTrackingDataContext.CustomButtonConfs
                .Where(x => x.HpId == hpId && x.Id == id).FirstOrDefault();
            var imageUrl = _amazonS3Service.GetPreSignedUrlsAsync(customButtonConfModel.FileName, -1, hpId);
            return new CustomButtonConfModel
            (
                customButtonConfModel.HpId,
                customButtonConfModel.Id,
                customButtonConfModel.Name,
                customButtonConfModel.IsUrl,
                customButtonConfModel.Path ?? string.Empty,
                customButtonConfModel.WorkDir ?? string.Empty,
                customButtonConfModel.Pattern ?? string.Empty,
                customButtonConfModel.Sort,
                customButtonConfModel.FileName ?? string.Empty,
                imageUrl.GetAwaiter().GetResult(),
                ReplacePatternInUrl(customButtonConfModel.Pattern, ptInf, raiinInf)
            );
        }


        public bool CheckExistCustomButtonConf(long id, int hpId)
        {
            var exists = NoTrackingDataContext.CustomButtonConfs
                .AsNoTracking()
                .Any(cb => cb.Id == id && cb.HpId == hpId);

            return exists;
        }

        public long SaveCustomButtonConf(CustomButtonConfModel customButtonConfModel)
        {
            if (!IsInvalidInputId(customButtonConfModel.HpId)) return 0;

            return SaveCustomButtonConfItems(customButtonConfModel);
        }

        public bool IsInvalidInputId(int hpId)
        {
            if (!NoTrackingDataContext.HpInfs.Any(x => x.HpId == hpId)) return false;
            return true;
        }

        private long SaveCustomButtonConfItems(CustomButtonConfModel customButtonConfModel)
        {
            if (customButtonConfModel.Id <= 0)
            {
                var customButtonConfEntity = ConvertToNewCustomButtonConf(customButtonConfModel);
                TrackingDataContext.CustomButtonConfs.Add(customButtonConfEntity);
                string finalKey = key + CacheKeyConstant.CustomButtonConfGetList;
                if (_cache.KeyExists(finalKey))
                {
                    _cache.KeyDelete(finalKey);
                }

                TrackingDataContext.SaveChanges();
                return customButtonConfEntity.Id;
            }
            else
            {
                UpdateCustomButton(customButtonConfModel);
                string finalKey = key + CacheKeyConstant.CustomButtonConfGetList;
                if (_cache.KeyExists(finalKey))
                {
                    _cache.KeyDelete(finalKey);
                }
                return customButtonConfModel.Id;
            }
        }

        private void UpdateCustomButton(CustomButtonConfModel customButtonConfModel)
        {
            var customButtonConfEntity = TrackingDataContext.CustomButtonConfs.Where(item =>
                item.HpId == customButtonConfModel.HpId && item.Id == customButtonConfModel.Id).FirstOrDefault();
            var originFileName = customButtonConfEntity?.FileName;
            var actualFileName = customButtonConfModel.Id + customButtonConfModel.Filename;
            if ((!string.IsNullOrEmpty(originFileName) &&
                 string.IsNullOrEmpty(actualFileName)) ||
                (!string.IsNullOrEmpty(originFileName) && originFileName != actualFileName))
            {
                var strategy = TrackingDataContext.Database.CreateExecutionStrategy();

                strategy.Execute(() =>
                {
                    using var transaction = TrackingDataContext.Database.BeginTransaction();
                    bool response = true;
                    string keyAws = $"{customButtonConfModel.HpId}/{ConfigConstant.Custombutton_image}/" + originFileName;
                    try
                    {
                        UpdateCustomButtonWithoutRemoveImage(customButtonConfEntity, customButtonConfModel);
                        if (_amazonS3Service.ObjectExistsAsync(keyAws).GetAwaiter().GetResult())
                        {
                            response = _amazonS3Service.DeleteObjectIfExistsAsync(customButtonConfModel.HpId,
                                originFileName).GetAwaiter().GetResult();
                        }

                        if (response)
                        {
                            TrackingDataContext.SaveChanges();
                            transaction.Commit();
                        }
                        else
                        {
                            transaction.Rollback();
                        }
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        throw;
                    }
                });
            }
            else
            {
                if (customButtonConfEntity != null)
                    UpdateCustomButtonWithoutRemoveImage(customButtonConfEntity, customButtonConfModel);
                TrackingDataContext.CustomButtonConfs.Update(customButtonConfEntity);
                TrackingDataContext.SaveChanges();
            }
        }

        private void UpdateCustomButtonWithoutRemoveImage(CustomButtonConf customButtonConfEntity,
            CustomButtonConfModel customButtonConfModel)
        {
            customButtonConfEntity.Id = customButtonConfModel.Id;
            customButtonConfEntity.HpId = customButtonConfModel.HpId;
            customButtonConfEntity.Name = customButtonConfModel.Name;
            customButtonConfEntity.IsUrl = customButtonConfModel.IsUrl;
            customButtonConfEntity.Path = customButtonConfModel.Path;
            customButtonConfEntity.WorkDir = customButtonConfModel.Workdir;
            customButtonConfEntity.Pattern = customButtonConfModel.Pattern;
            customButtonConfEntity.FileName = string.IsNullOrEmpty(customButtonConfModel.Filename)
                ? String.Empty
                : customButtonConfModel.Id + customButtonConfModel.Filename;
        }

        private CustomButtonConf ConvertToNewCustomButtonConf(CustomButtonConfModel model)
        {
            CustomButtonConf customButtonConf = new();
            customButtonConf.Id = 0;
            customButtonConf.HpId = model.HpId;
            customButtonConf.Name = model.Name;
            customButtonConf.IsUrl = model.IsUrl;
            customButtonConf.Path = model.Path;
            customButtonConf.WorkDir = model.Workdir;
            customButtonConf.Pattern = model.Pattern;
            customButtonConf.Sort = GetMaxSort() + 1;
            customButtonConf.FileName = string.IsNullOrEmpty(model.Filename)
                ? String.Empty : GetMaxId() + 1 + model.Filename;
            return customButtonConf;
        }

        public long GetMaxId()
        {
            var allCustomButton = TrackingDataContext.Database.SqlQueryRaw<long>("SELECT NEXTVAL(' \"custom_button_conf_seq\"')").ToList();
            var nextCustombutton = allCustomButton?.FirstOrDefault() ?? 1;
            return nextCustombutton;
        }

        public int? GetMaxSort()
        {
            return TrackingDataContext.CustomButtonConfs
                .AsNoTracking()
                .Select(c => c.Sort)
                .AsEnumerable()
                .DefaultIfEmpty(0)
                .Max();
        }

        private  string ReplacePatternInUrl(string originUrl, PtInf? ptInf, RaiinInf? raiinInf)
        {
            // Tạo dictionary để lưu trữ các tham số và giá trị tương ứng
            var replacements = new Dictionary<string, string>
            {
                { "patientid", ptInf != null ? ptInf.PtNum.ToString() :  "patientid"},
                { "patientid_z", ptInf != null ? ptInf.PtNum.ToString().PadLeft(9, '0') : "patientid_z" }, // 9 số, thêm số 0 phía trước
                { "patientid_z10", ptInf != null ? ptInf.PtNum.ToString().PadLeft(10, '0') : "patientid_z10" }, // 10 số, thêm số 0 phía trước
                { "patientid_z2", ptInf != null ? ptInf.PtNum.ToString().PadLeft(2, '0') :"patientid_z2" },  // 2 số
                { "patientid_z3", ptInf != null ? ptInf.PtNum.ToString().PadLeft(3, '0') : "patientid_z3" },  // 3 số
                { "patientid_z4", ptInf != null ? ptInf.PtNum.ToString().PadLeft(4, '0') : "patientid_z4"},  // 4 số
                { "patientid_z5", ptInf != null ? ptInf.PtNum.ToString().PadLeft(5, '0') : "patientid_z5"},  // 5 số
                { "patientid_z6", ptInf != null ? ptInf.PtNum.ToString().PadLeft(6, '0') : "patientid_z6"},  // 6 số
                { "patientid_z7", ptInf != null ? ptInf.PtNum.ToString().PadLeft(7, '0') : "patientid_z7"},  // 7 số
                { "patientid_z8", ptInf != null ? ptInf.PtNum.ToString().PadLeft(8, '0') : "patientid_z8"},  // 8 số
                { "kanjiname_s", ptInf != null ? ptInf.Name.Contains(" ") ? ptInf.Name.Split(" ")[0] : ptInf.Name : "kanjiname_s"}, // Lay phan trước space
                { "kanjiname_m", ptInf != null ? ptInf.Name.Contains(" ") ? ptInf.Name.Split(" ")[1] : ptInf.Name :  "kanjiname_m"}, // Lấy phần sau space
                { "kananame_s", ptInf != null ? ptInf.KanaName.Contains(" ") ? ptInf.KanaName.Split(" ")[0] : ptInf.KanaName : "kananame_s"}, // Kana name trước space
                { "kananame_m", ptInf != null ? ptInf.KanaName.Contains(" ") ? ptInf.KanaName.Split(" ")[1] : ptInf.KanaName : "kananame_m"}, // Kana name sau space
                { "sex", ptInf != null ? ptInf.Sex.ToString() : "sex"}, // Chuyển giới tính
                { "birthday", ptInf != null ? ptInf.Birthday.ToString() : "birthday"},  // Ngày sinh
                { "calendate", raiinInf != null ? raiinInf.SinDate.ToString() : "calendate"}, // Sinh nhật hoặc ngày khác
                { "sysdate", DateTime.Now.ToString("yyyyMMdd") }, // Ngày hệ thống
                { "sysdate_2", DateTime.Now.ToString("yyyyMMdd") }, // Ngày hệ thống (tương tự)
                { "sysdatetime", DateTime.Now.ToString("yyyyMMddHHmmss") }, // Ngày và giờ hệ thống
                { "ka_id", raiinInf != null ? raiinInf.KaId.ToString() : "ka_id" } // Department ID
            };

            // Thay thế các tham số trong pattern với giá trị từ dictionary
            foreach (var item in replacements)
            {
                if (originUrl.Contains(item.Key))
                {
                    originUrl = originUrl.Replace("\"" + item.Key + "\"", item.Value);
                }
            }

            // Trả về URL đã thay thế
            return originUrl;
        }
    

        public bool DeleteCustomButtonConf(long id, int hpId)
        {
            try
            {
                var record = TrackingDataContext.CustomButtonConfs.FirstOrDefault(cb => cb.Id == id && cb.HpId == hpId);

                if (record == null)
                {
                    return false;
                }

                TrackingDataContext.CustomButtonConfs.Remove(record);
                var isSaveChange = TrackingDataContext.SaveChanges() > 0;

                if (isSaveChange)
                {
                    bool response = true;
                    string keyAws = $"{hpId}/{ConfigConstant.Custombutton_image}/" + record.FileName;
                    //Remove image from S3
                    if (!string.IsNullOrEmpty(record.FileName) && _amazonS3Service.ObjectExistsAsync(keyAws).GetAwaiter().GetResult())
                    {
                        response = _amazonS3Service.DeleteObjectIfExistsAsync(hpId, record.FileName).GetAwaiter().GetResult();
                    }

                    if (response)
                    {
                        string finalKey = key + CacheKeyConstant.CustomButtonConfGetList;
                        if (_cache.KeyExists(finalKey))
                        {
                            _cache.KeyDelete(finalKey);
                        }
                    }
                }
                return isSaveChange;
            }
            catch (AmazonS3Exception s3Ex)
            {
                Console.WriteLine($"S3 Error deleting objects in folder: {s3Ex.Message}");
                return true;
            }
            catch (Exception ex) 
            {
                Console.WriteLine($"Error: {ex.Message}");
                return false;
            }
        }
        
        public bool UpdateSortCustomButtonConf(List<CustomButtonConfModel> customButtonConfModels)
        {
            var updateListCustomButtonConf = new List<CustomButtonConf>();
            foreach (var item in customButtonConfModels)
            { 
                var customButtonConf = NoTrackingDataContext.CustomButtonConfs
                    .FirstOrDefault(cus => cus.Id == item.Id && cus.HpId == item.HpId);
                if (customButtonConf != null)
                {
                    customButtonConf.Sort = item.Sort;
                    updateListCustomButtonConf.Add(customButtonConf);
                }
            }
            if (updateListCustomButtonConf.Any()){
                TrackingDataContext.CustomButtonConfs.UpdateRange(updateListCustomButtonConf);
            }

            TrackingDataContext.SaveChanges();
            string finalKey = key + CacheKeyConstant.CustomButtonConfGetList;
            if (_cache.KeyExists(finalKey))
            {
                _cache.KeyDelete(finalKey);
            } 
            return true;
        }

    public void ReleaseResource()
        {
            DisposeDataContext();
        }
    }
}