﻿using Domain.Enum;
using Domain.Models.AgentSetting;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Enum;
using Helper.Extension;
using Helper.Mapping;
using Helper.Redis;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using System.Text.Json;

namespace Infrastructure.Repositories
{
    public class AgentsettingRepository : RepositoryBase, IAgentSettingRepository
    {
        private readonly IDatabase _cache;
        private readonly string getAgentSettingKey;
        private readonly IConfiguration _configuration;
        public AgentsettingRepository(ITenantProvider tenantProvider, IConfiguration configuration) : base(tenantProvider)
        {
            _configuration = configuration;
            getAgentSettingKey  = GetDomainKey() + CacheKeyConstant.AgentSetting;
            GetRedis();
            _cache = RedisConnectorHelper.Connection.GetDatabase();
        }

        public void GetRedis()
        {
            string connection = string.Concat(_configuration["Redis:RedisHost"], ":", _configuration["Redis:RedisPort"]);
            if (RedisConnectorHelper.RedisHost != connection)
            {
                RedisConnectorHelper.RedisHost = connection;
            }
        }

        private List<AgentSettingModel> ReloadCache(int hpId, string keySystemConfig)
        {
            var result = NoTrackingDataContext.AgentSettings
                                        .Where(item => item.HpId == hpId)
                                        .ToList();
            var json = JsonSerializer.Serialize(result);
            _cache.StringSet(keySystemConfig, json);

            return Mapper.Map<AgentSetting, AgentSettingModel>(result);
        }

        private List<AgentSettingModel> ReadCache(string keySystemConfig)
        {
            var results = _cache.StringGet(keySystemConfig);
            var json = results.AsString();
            var datas = !string.IsNullOrEmpty(json) ? JsonSerializer.Deserialize<List<AgentSettingModel>>(json) : new();
            return datas ?? new();
        }

        private List<AgentSettingModel> GetData(int hpId)
        {
            var keyAgentSetting = getAgentSettingKey + "_" + hpId;
            List<AgentSettingModel> result;
            if (!_cache.KeyExists(keyAgentSetting))
            {
                result = ReloadCache(hpId, keyAgentSetting);
            }
            else
            {
                result = ReadCache(keyAgentSetting);
            }

            return result;
        }

        public bool SaveAgentSetting(int hpId, string host, string port, int isCommon)
        {
            var agentSetting = new AgentSettingModel()
            {
                HpId = hpId,
                Host = host,
                Port = port,
                IsCommon = (sbyte)isCommon
            };

            var existedAgentSetting = TrackingDataContext.AgentSettings.FirstOrDefault(x => x.HpId == hpId && x.Host == agentSetting.Host);

            var existedCommonAgent = TrackingDataContext.AgentSettings.FirstOrDefault(x => x.HpId == hpId && x.IsCommon == (sbyte)AgentSettingEnum.Common);

            if (existedAgentSetting == null)
            {
                CreateNewAgentSetting(hpId, agentSetting);
            }
            else
            {
                UpdateAgentSetting(hpId, existedAgentSetting, agentSetting);
            }

            if (agentSetting.IsCommon == (sbyte)AgentSettingEnum.Common &&
                (existedAgentSetting == null || existedAgentSetting != existedCommonAgent))
            {
                if (existedCommonAgent != null)
                {
                    existedCommonAgent.IsCommon = (sbyte)AgentSettingEnum.Individual;
                    existedCommonAgent.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    TrackingDataContext.Update(existedCommonAgent);
                }
            }

            var isSaveChange = TrackingDataContext.SaveChanges();
            if (isSaveChange > 0)
            {
                var keyAgentSetting = getAgentSettingKey + "_" + hpId;
                if (_cache.KeyExists(keyAgentSetting))
                {
                    _cache.KeyDelete(keyAgentSetting);
                }
            }
            return true;
        }

        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        public AgentSettingModel GetAgentSetting(int hpId, string host)
        {
            var agentSettings = GetData(hpId);
            var agentSetting = agentSettings.FirstOrDefault(x => x.HpId == hpId && x.Host == host);
            var commonAgentSetting = agentSettings.FirstOrDefault(x => x.HpId == hpId && x.IsCommon == (sbyte)AgentSettingEnum.Common);

            if (agentSetting == null) return new AgentSettingModel()
            {
                HpId = hpId,
                Host = host,
                CommonHost = commonAgentSetting is null ? string.Empty : commonAgentSetting.Host,
                IsCommon = (sbyte)AgentSettingEnum.Individual,
                Port = "0",
                SettingId = 0
            };

            var result = Mapper.Map(agentSetting, new AgentSettingModel());
            result.CommonHost = commonAgentSetting is null ? string.Empty : commonAgentSetting.Host;
            return result;
        }

        public List<AgentSettingModel> GetAgentSettingList(int hpId, bool onlyCommon)
        {

            var query = NoTrackingDataContext.AgentSettings.Where(x => x.HpId == hpId);

            if (onlyCommon)
            {
                query = query.Where(x => x.IsCommon == (sbyte)AgentSettingEnum.Common);
            }

            var agentSettings = query.AsEnumerable()
                                     .DistinctBy(x => x.Port)
                                     .ToList();
            return Mapper.Map<AgentSetting, AgentSettingModel>(agentSettings);
        }

        private void UpdateAgentSetting(int hpId, AgentSetting agentSetting, AgentSettingModel newAgentSetting)
        {
            agentSetting.UpdateDate = CIUtil.GetJapanDateTimeNow();
            agentSetting.Port = newAgentSetting.Port;
            agentSetting.IsCommon = newAgentSetting.IsCommon;
            TrackingDataContext.Update(agentSetting);
        }

        private void CreateNewAgentSetting(int hpId, AgentSettingModel agentSetting)
        {
            var newAgentSetting = new AgentSetting()
            {
                Port = agentSetting.Port,
                Host = agentSetting.Host,
                HpId = hpId,
                SettingId = 0,
                IsCommon = agentSetting.IsCommon,
                UpdateDate = CIUtil.GetJapanDateTimeNow()
            };
            TrackingDataContext.Add(newAgentSetting);
            agentSetting.SettingId = newAgentSetting.SettingId;
        }
    }
}
