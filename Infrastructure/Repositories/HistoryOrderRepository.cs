using System.Runtime.InteropServices;
using System.Text.Json;
using Domain.Models.HistoryOrder;
using Domain.Models.Insurance;
using Domain.Models.InsuranceInfor;
using Domain.Models.KarteFilterMst;
using Domain.Models.KarteInf;
using Domain.Models.KarteInfs;
using Domain.Models.OrdInfDetails;
using Domain.Models.OrdInfs;
using Domain.Models.RainListTag;
using Domain.Models.Receipt.Recalculation;
using Domain.Models.Reception;
using Entity.Tenant;
using Helper.Constants;
using Infrastructure.Base;
using Infrastructure.Converter;
using Infrastructure.Interfaces;
using Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Collections.Concurrent;

namespace Infrastructure.Repositories
{
    public class HistoryOrderRepository : RepositoryBase, IHistoryOrderRepository
    {
        private readonly IUserInfoService _userInfoService;
        private readonly IKaService _kaService;
        private readonly IInsuranceRepository _insuranceRepository;
        private readonly IRaiinListTagRepository _raiinListTagRepository;
        private readonly IKarteInfRepository _karteInfRepository;
        private readonly ITenantProvider _tenantProvider;
        private readonly ITenantProvider _tenantProvider1;
        private readonly ITenantProvider _tenantProvider2;
        private readonly ITenantProvider _tenantProvider3;
        private readonly ITenantProvider _tenantProvider4;
        private readonly ITenantProvider _tenantProvider5;
        private readonly ITenantProvider _tenantProvider6;
        private readonly ITenantProvider _tenantProvider7;

        public HistoryOrderRepository(ITenantProvider tenantProvider, IUserInfoService userInfoService, IKaService kaService, IInsuranceRepository insuranceRepository, IRaiinListTagRepository raiinListTagRepository, IKarteInfRepository karteInfRepository, ITenantProvider tenantProvider1, ITenantProvider tenantProvider2, ITenantProvider tenantProvider3, ITenantProvider tenantProvider4, ITenantProvider tenantProvider5, ITenantProvider tenantProvider6, ITenantProvider tenantProvider7) : base(tenantProvider)
        {
            _tenantProvider = tenantProvider;
            _tenantProvider1 = tenantProvider1;
            _tenantProvider2 = tenantProvider2;
            _tenantProvider3 = tenantProvider3;
            _tenantProvider4 = tenantProvider4;
            _tenantProvider5 = tenantProvider5;
            _tenantProvider6 = tenantProvider6;
            _tenantProvider7 = tenantProvider7;
            _userInfoService = userInfoService;
            _insuranceRepository = insuranceRepository;
            _raiinListTagRepository = raiinListTagRepository;
            _kaService = kaService;
            _karteInfRepository = karteInfRepository;
        }

        public KarteFilterMstModel GetFilter(int hpId, int userId, int filterId)
        {
            var filterMstData = NoTrackingDataContext.KarteFilterMsts.FirstOrDefault(u => u.HpId == hpId && u.UserId == userId && u.FilterId == filterId && u.IsDeleted == 0);
            if (filterMstData == null)
            {
                return new KarteFilterMstModel(hpId, userId);
            }

            var filterDetailList = NoTrackingDataContext.KarteFilterDetails
            .Where(item => item.HpId == hpId && item.UserId == userId && item.FilterId == filterId)
            .ToList();

            var isBookMarkChecked = filterDetailList.FirstOrDefault(detail => detail.FilterId == filterId && detail.FilterItemCd == 1 && detail.FilterEdaNo == 0 && detail.Val == 1) != null;
            var listHokenId = filterDetailList.Where(detail => detail.FilterId == filterId && detail.FilterItemCd == 3 && detail.Val == 1).Select(item => item.FilterEdaNo).ToList();
            var listKaId = filterDetailList.Where(detail => detail.FilterId == filterId && detail.FilterItemCd == 4 && detail.Val == 1).Select(item => item.FilterEdaNo).ToList();
            var listUserId = filterDetailList.Where(detail => detail.FilterId == filterId && detail.FilterItemCd == 2 && detail.Val == 1).Select(item => item.FilterEdaNo).ToList();

            var detailModel = new KarteFilterDetailModel(hpId, userId, filterId, isBookMarkChecked, listHokenId, listKaId, listUserId);

            var result = new KarteFilterMstModel(
                hpId,
                userId,
                filterId,
                filterMstData.FilterName ?? string.Empty,
                filterMstData.SortNo,
                filterMstData.AutoApply,
                filterMstData.IsDeleted,
                detailModel);

            return result;
        }

        private IEnumerable<RaiinInf> GenerateRaiinListQuery(int hpId, int userId, long ptId, int filterId, int isDeleted, List<Tuple<long, bool>> raiinNos, List<int>? treatmentDepartmentIds = null, List<int>? tantoIds = null, string? keyWord = null)
        {
            KarteFilterMstModel karteFilter = GetFilter(hpId, userId, filterId);
            List<int> hokenPidListByCondition = GetHokenPidListByCondition(hpId, ptId, isDeleted, karteFilter);
            var raiinGets = raiinNos.Where(r => r.Item2).Select(r => r.Item1).Distinct().ToList();
            var raiinNoAll = raiinNos.Select(r => r.Item1).Distinct().ToList();

            //Filter RaiinInf by condition.
            IQueryable<RaiinInf> raiinInfListQueryable = NoTrackingDataContext.RaiinInfs
                .Where(r => r.HpId == hpId &&
                            r.PtId == ptId &&
                            // r.Status >= 3 &&
                            (r.IsDeleted == DeleteTypes.None || isDeleted == 1 || (r.IsDeleted != DeleteTypes.Confirm && isDeleted == 2)) &&
                            hokenPidListByCondition.Contains(r.HokenPid) &&
                            (karteFilter.IsAllDepartment || karteFilter.ListDepartmentCode.Contains(r.KaId)) &&
                            (karteFilter.IsAllDoctor || karteFilter.ListDoctorCode.Contains(r.TantoId)))
                .Join(NoTrackingDataContext.KarteEditions,
                    r => r.RaiinNo,
                    k => k.RaiinNo,
                    (r, k) => r);

            IQueryable<RaiinInf> raiinInfEnumerable;
            if (karteFilter.OnlyBookmark)
            {
                raiinInfEnumerable = from raiinInf in raiinInfListQueryable
                                     join raiinTag in NoTrackingDataContext.RaiinListTags.Where(r => r.HpId == hpId && r.PtId == ptId && r.IsDeleted == 0 && r.TagNo != 0 && !raiinNoAll.Contains(r.RaiinNo))
                                      on raiinInf.RaiinNo equals raiinTag.RaiinNo
                                     select raiinInf;

                var raiinInfEnumerableFE = from raiinInf in raiinInfListQueryable where raiinGets.Contains(raiinInf.RaiinNo) select raiinInf;
                raiinInfEnumerable = raiinInfEnumerable.Union(raiinInfEnumerableFE);
            }
            else
            {
                raiinInfEnumerable = raiinInfListQueryable.Select(r => r);
            }

            // filter by treatmentDepartmentId
            if (treatmentDepartmentIds != null && treatmentDepartmentIds.Any())
            {
                raiinInfEnumerable = raiinInfEnumerable.Where(r => treatmentDepartmentIds.Contains(r.TreatmentDepartmentId));
            }

            // filter by tantoIds
            if (tantoIds != null && tantoIds.Any())
            {
                raiinInfEnumerable = raiinInfEnumerable.Where(r => tantoIds.Contains(r.TantoId));
            }

            // filter by keyWord
            if (!string.IsNullOrEmpty(keyWord))
            {
                // karteInfs: text column
                // odrInfs: rpName column
                // odrInfDetails: itemName column
                // filingInfs: dsp_file_name column
                var raiinNoKarteInfs = NoTrackingDataContext.KarteInfs.Where(k => k.HpId == hpId && k.PtId == ptId && k.IsDeleted == DeleteTypes.None && k.Text != null && k.Text.Contains(keyWord)).Select(k => k.RaiinNo).ToList();
                var raiinNoOdrInfDetails = NoTrackingDataContext.OdrInfDetails.Where(od => od.HpId == hpId && od.PtId == ptId && od.ItemName != null && od.ItemName.Contains(keyWord)).Select(od => od.RaiinNo).ToList();
                var raiinNoFilings = NoTrackingDataContext.FilingInf.Where(f => f.HpId == hpId && f.PtId == ptId && f.IsDeleted == DeleteTypes.None && f.DspFileName != null && f.DspFileName.Contains(keyWord)).Select(f => f.RaiinNo).ToList();
                var allRaiinNos = raiinNoKarteInfs.Union(raiinNoOdrInfDetails).Union(raiinNoFilings);
                raiinInfEnumerable = raiinInfEnumerable.Where(r => allRaiinNos.Contains(r.RaiinNo));
                //raiinInfEnumerable = raiinInfEnumerable.Where(r =>
                //    NoTrackingDataContext.KarteInfs.Any(k =>
                //        k.RaiinNo == r.RaiinNo && k.IsDeleted == DeleteTypes.None && k.Text != null && k.Text.Contains(keyWord)
                //    ) ||
                //    // NoTrackingDataContext.OdrInfs.Any(o =>
                //    //     o.RaiinNo == r.RaiinNo && o.IsDeleted == DeleteTypes.None && o.RpName != null && o.RpName.Contains(keyWord)
                //    // ) ||
                //    NoTrackingDataContext.OdrInfDetails
                //        .Join(
                //            NoTrackingDataContext.OdrInfs,
                //                d => new { d.HpId, d.RaiinNo, d.RpNo, d.RpEdaNo, d.Edition },
                //                o => new { o.HpId, o.RaiinNo, o.RpNo, o.RpEdaNo, o.Edition },
                //                (d, o) => new { d, o }
                //            )
                //        .Any(j =>
                //            j.d.RaiinNo == r.RaiinNo &&
                //            j.o.IsDeleted == DeleteTypes.None &&
                //            j.d.ItemName != null &&
                //            j.d.ItemName.Contains(keyWord)
                //        ) ||
                //    NoTrackingDataContext.FilingInf.Any(f =>
                //        f.RaiinNo == r.RaiinNo && f.IsDeleted == DeleteTypes.None && f.DspFileName != null && f.DspFileName.Contains(keyWord)
                //    )
                //);
            }

            return raiinInfEnumerable.AsEnumerable();
        }

        private IEnumerable<RaiinInf> GenerateRaiinListQuery(int hpId, long ptId, int startDate, int endDate, int isDeleted, bool inCludeDraf)
        {
            List<int> hokenPidListByCondition = NoTrackingDataContext.PtHokenPatterns
                                                .Where(p => p.HpId == hpId &&
                                                            p.PtId == ptId &&
                                                            (p.IsDeleted == 0 || isDeleted > 0))
                                                .Select(p => p.HokenPid)
                                                .ToList();

            //Filter RaiinInf by condition.
            IQueryable<RaiinInf> raiinInfListQueryable = NoTrackingDataContext.RaiinInfs
                .Where(r => r.HpId == hpId &&
                            r.PtId == ptId &&
                            // r.Status >= 3 &&
                            (r.IsDeleted == DeleteTypes.None || isDeleted == 1 || (r.IsDeleted != DeleteTypes.Confirm && isDeleted == 2)) &&
                            hokenPidListByCondition.Contains(r.HokenPid) &&
                            startDate <= r.SinDate &&
                            r.SinDate <= endDate);
            var karteEdition = NoTrackingDataContext.KarteEditions.Where(k => k.HpId == hpId && k.PtId == ptId && k.IsDeleted == DeleteTypes.None && startDate <= k.SinDate && k.SinDate <= endDate && (inCludeDraf || k.KarteStatus == 1));

            var result = raiinInfListQueryable.Join(karteEdition,
                        r => new { r.HpId, r.PtId, r.RaiinNo },
                        k => new { k.HpId, k.PtId, k.RaiinNo },
                        (r, k) => r
                        ).ToList();

            return result.Select(r => r);
        }

        private (IQueryable<RaiinInf> raiinInfs, IQueryable<OdrInf> allOdrInfs) GetRaiinInfs(int hpId, long ptId, int sinDate, int odrKouiKbn, int grpKouiKbn)
        {
            IQueryable<RaiinInf> query;
            IQueryable<OdrInf> allOdrInfs;
            IQueryable<RaiinInf> allRaiinInfs;

            var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                             item.PtId == ptId &&
                                                                             item.IsDeleted == DeleteTypes.None &&
                                                                             (sinDate <= 0 || item.SinDate == sinDate));

            var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                     item.PtId == ptId &&
                                                                                     item.IsDeleted == DeleteTypes.None &&
                                                                                     item.KarteStatus == KarteStatusConst.Official);

            var raiinInfs = from raiinInf in listRaiinInf
                            join karteEdition in listKarteEdition on
                                raiinInf.RaiinNo equals karteEdition.RaiinNo
                            select raiinInf;

            //get by odrKouiKbn
            if (odrKouiKbn > 0)
            {
                allOdrInfs = NoTrackingDataContext.OdrInfs
                                       .Where(p => p.HpId == hpId
                                                   && p.PtId == ptId
                                                   && p.OdrKouiKbn != 10
                                                   && ((odrKouiKbn == 20 || odrKouiKbn == 28) ?
                                                        (p.OdrKouiKbn == 20 || p.OdrKouiKbn == 28 || p.OdrKouiKbn == 100 || p.OdrKouiKbn == 101) :
                                                    odrKouiKbn == 30 ?
                                                    (p.OdrKouiKbn == 30 || p.OdrKouiKbn == 34) :
                                                    (odrKouiKbn == 60 ?
                                                    (p.OdrKouiKbn == 60 || p.OdrKouiKbn == 64) :
                                                    p.OdrKouiKbn == odrKouiKbn))
                                                    && (sinDate <= 0 || p.SinDate == sinDate)
                                                    && p.IsDeleted == DeleteTypes.None);

                query = from raiinInf in raiinInfs
                        join odrInf in allOdrInfs
                        on raiinInf.RaiinNo equals odrInf.RaiinNo
                        select raiinInf;
            }

            //get by groupKouiKbn
            else
            {
                allOdrInfs = NoTrackingDataContext.OdrInfs
                                       .Where(p => p.HpId == hpId
                                                   && p.PtId == ptId
                                                   && p.OdrKouiKbn != 10
                                                   && p.IsDeleted == DeleteTypes.None
                                                   && (sinDate <= 0 || p.SinDate == sinDate));
                if (grpKouiKbn == 14 || (grpKouiKbn >= 68 && grpKouiKbn < 70) || (grpKouiKbn >= 95 && grpKouiKbn < 99))
                {
                    allOdrInfs = allOdrInfs.Where(p => p.OdrKouiKbn == grpKouiKbn);
                }
                else
                {
                    allOdrInfs = allOdrInfs.Where(p => (grpKouiKbn == 20 ? (p.OdrKouiKbn / 10 == grpKouiKbn / 10 || p.OdrKouiKbn == 100 || p.OdrKouiKbn == 101) :
                                                    p.OdrKouiKbn / 10 == grpKouiKbn / 10) &&
                                                    p.OdrKouiKbn != 14 && !(p.OdrKouiKbn >= 68 && p.OdrKouiKbn < 70) && !(p.OdrKouiKbn >= 95 && p.OdrKouiKbn < 99));
                }

                query = from raiinInf in raiinInfs
                        join odrInf in allOdrInfs
                        on raiinInf.RaiinNo equals odrInf.RaiinNo
                        select raiinInf;
            }
            allRaiinInfs = query.Distinct();
            return (allRaiinInfs, allOdrInfs);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="hpId"></param>
        /// <param name="userId"></param>
        /// <param name="ptId"></param>
        /// <param name="sinDate"></param>
        /// <param name="currentIndex"></param>
        /// <param name="filterId"></param>
        /// <param name="keyWord"></param>
        /// <param name="isDeleted"></param>
        /// <param name="searchType"></param>
        /// 0: order and karte
        /// 1: only karte
        /// 2: only order
        /// <param name="isDescending"></param>
        /// <returns></returns>
        public (int, ReceptionModel) Search(int hpId, int userId, long ptId, int sinDate, int currentIndex, int filterId, int isDeleted, string keyWord, int searchType, bool isNext, List<Tuple<long, bool>> raiinNos)
        {
            IEnumerable<RaiinInf> raiinInfEnumerable = GenerateRaiinListQuery(hpId, userId, ptId, filterId, isDeleted, raiinNos);
            List<long> raiinNoList;

            if (isNext)
            {
                raiinNoList = raiinInfEnumerable.OrderByDescending(r => r.SinDate)
                                                .ThenByDescending(r => r.UketukeTime)
                                                .ThenByDescending(r => r.RaiinNo)
                                                .Skip(currentIndex + 1)
                                                .Select(r => r.RaiinNo)
                                                .ToList();
            }
            else
            {
                raiinNoList = raiinInfEnumerable.OrderByDescending(r => r.SinDate)
                                                .ThenByDescending(r => r.UketukeTime)
                                                .ThenByDescending(r => r.RaiinNo)
                                                .Take(currentIndex)
                                                .Select(r => r.RaiinNo)
                                                .ToList();
            }

            (int, ReceptionModel) GenerateResult(long raiinNo)
            {
                RaiinInf? raiinInf = NoTrackingDataContext.RaiinInfs.FirstOrDefault(r => r.HpId == hpId && r.PtId == ptId && r.RaiinNo == raiinNo);

                if (raiinInf == null)
                {
                    return (0, new ReceptionModel());
                }

                int index = 0;

                if (isNext)
                {
                    index = currentIndex + raiinNoList.IndexOf(raiinNo) + 1;
                }
                else
                {
                    index = Math.Max(0, currentIndex - (raiinNoList.Count - raiinNoList.IndexOf(raiinNo)));
                }
                return (index, Reception.FromRaiinInf(raiinInf));
            }

            long raiinNoByKarte = 0;
            long raiinNoByOrder = 0;
            if (searchType == 0 || searchType == 1)
            {
                raiinNoByKarte = SearchKarte(hpId, ptId, isDeleted, raiinNoList, keyWord, isNext);
            }

            if (searchType == 0 || searchType == 2)
            {
                raiinNoByOrder = SearchOrder(hpId, ptId, isDeleted, raiinNoList, keyWord, isNext);
            }

            if (raiinNoByKarte == 0 && raiinNoByOrder == 0)
            {
                return (-1, new ReceptionModel());
            }

            if (raiinNoByKarte == 0)
            {
                return GenerateResult(raiinNoByOrder);
            }

            if (raiinNoByOrder == 0)
            {
                return GenerateResult(raiinNoByKarte);
            }

            long foundRaiinNo = isNext ? Math.Max(raiinNoByKarte, raiinNoByOrder) : Math.Min(raiinNoByKarte, raiinNoByOrder);

            return GenerateResult(foundRaiinNo);
        }

        public (int, int, List<HistoryOrderModel>) GetList(int hpId, int userId, long ptId, int sinDate, int offset, int limit, List<Tuple<long, bool>> raiinNos, int isShowApproval = 0, int type = 0, string? keyWord = null, List<int>? treatmentDepartmentIds = null, bool hasSOAP = false, List<int>? odrKouiKbns = null, List<int>? tantoIds = null, List<InsuranceSummaryModel>? insuranceModels = null)
        {
            int filterId = 0; // bypass filter
            IEnumerable<RaiinInf> raiinInfEnumerableCheckOdrInf = GenerateRaiinListQuery(hpId, userId, ptId, filterId, 0, raiinNos, treatmentDepartmentIds, tantoIds, keyWord);
            IEnumerable<RaiinInf> raiinInfEnumerable;
            if (type == 0)
            {
                raiinInfEnumerableCheckOdrInf = raiinInfEnumerableCheckOdrInf.OrderByDescending(r => r.SinDate)
                .ThenByDescending(r => r.UketukeTime)
                .ThenByDescending(r => r.RaiinNo);

                HashSet<long> validSoap = new();
                if (hasSOAP)
                {
                    validSoap = NoTrackingDataContext.KarteInfs.Where(i => i.PtId == ptId && i.IsDeleted == DeleteTypes.None).Select(i => i.RaiinNo).Distinct().ToHashSet();
                }
                raiinInfEnumerable = raiinInfEnumerableCheckOdrInf
                   .Where(r => (!hasSOAP || validSoap.Contains(r.RaiinNo)));
                var validRaiinNos = NoTrackingDataContext.OdrInfs
                .Where(o => o.PtId == ptId && o.OdrKouiKbn != 10 && o.IsDeleted == 0 && (odrKouiKbns == null || !odrKouiKbns.Any() || odrKouiKbns.Contains(o.OdrKouiKbn)))
                .Select(o => o.RaiinNo)
                .Distinct()
                .ToHashSet();
                raiinInfEnumerable = raiinInfEnumerable
              .Where(r => odrKouiKbns == null || !odrKouiKbns.Any() || validRaiinNos.Contains(r.RaiinNo));
            }
            else if (type == 1)
            {
                raiinInfEnumerable = raiinInfEnumerableCheckOdrInf;
            }
            else
            {
                return (0, 0, new());
            }
            int totalCount = raiinInfEnumerable.Count();
            List<long> allRaiinNos = raiinInfEnumerable.Select(r => r.RaiinNo).ToList();
            // // get raiinInfList order by karateStatus in karteEdition
            // var query = from raiin in raiinInfEnumerable
            //             join karte in NoTrackingDataContext.KarteEditions
            //             on raiin.RaiinNo equals karte.RaiinNo into joined
            //             from karte in joined.DefaultIfEmpty()
            //             select new
            //             {
            //                 raiin,
            //                 KarteStatus = karte == null ? int.MinValue : karte.KarteStatus
            //             };

            List<RaiinInf> raiinInfList = new List<RaiinInf>();
            List<RaiinInf> raiinInfListCheckOdrInf = new List<RaiinInf>();
            var raiinInfListQuery = raiinInfEnumerable
                .OrderByDescending(r => r.SinDate)
                .ThenByDescending(r => r.UketukeTime)
                .ThenByDescending(r => r.RaiinNo);

            if (limit > 0)
            {
                ///offset: total number of records skipped
                raiinInfList = raiinInfListQuery.Skip(offset).Take(limit).ToList();
                raiinInfListCheckOdrInf = raiinInfEnumerableCheckOdrInf.Skip(offset).Take(limit).ToList();
            }
            else
            {
                raiinInfList = raiinInfListQuery.ToList();
                raiinInfListCheckOdrInf = raiinInfEnumerableCheckOdrInf.ToList();
            }

            int totalKeyWordOdrCount = 0;
            if (!string.IsNullOrEmpty(keyWord))
            {
                int keyWordKarteCount = CountKeyWordMatchedInKarteInfs(keyWord, hpId, ptId, allRaiinNos);
                // int keywordMatchInOdrInfsCount = CountKeyWordMatchedInOdrInfs(keyWord, hpId, ptId, 0, type, allRaiinNos, odrKouiKbns);
                int keywordMatchInOdrDetailsCount = CountKeyWordMatchedInOdrInfDetails(keyWord, hpId, ptId, 0, allRaiinNos);
                int keywordMatchInFillingInfCount = CountKeyWordMatchedInFillingInf(keyWord, hpId, ptId, allRaiinNos);
                totalKeyWordOdrCount = keyWordKarteCount + keywordMatchInOdrDetailsCount + keywordMatchInFillingInfCount;
            }

            (totalCount, List<HistoryOrderModel> historyOrderModelList) = GetList(hpId, ptId, sinDate, raiinInfList, raiinInfListCheckOdrInf, totalCount, 0, isShowApproval, type, keyWord, hasSOAP, odrKouiKbns, insuranceModels);
            return (totalCount, totalKeyWordOdrCount, historyOrderModelList);
        }

        public (int totalCount, int totalKeyWordMatched, List<HistoryOrderModel> historyOrderModelList) GetList(int hpId, long ptId, int sinDate, int startDate, int endDate, int isDeleted, int isShowApproval = 0, int type = 0, string? keyWord = null, bool inCludeDraf = false, List<InsuranceSummaryModel>? insuranceModels = null, bool isCheckedJihi = true)
        {
            IEnumerable<RaiinInf> raiinInfEnumerable = GenerateRaiinListQuery(hpId, ptId, startDate, endDate, isDeleted, inCludeDraf);
            int totalCount = raiinInfEnumerable.Count();
            List<RaiinInf> raiinInfList = type == 0 ? raiinInfEnumerable.OrderByDescending(r => r.SinDate).ThenByDescending(r => r.UketukeTime).ThenByDescending(r => r.RaiinNo).ToList() : raiinInfEnumerable.OrderBy(r => r.SinDate).ThenBy(r => r.RaiinNo).ToList();
            (totalCount, List<HistoryOrderModel> historyOrderModelList) = GetList(hpId, ptId, sinDate, raiinInfList, new(), totalCount, isDeleted, isShowApproval, type, keyWord, false, null, insuranceModels, isCheckedJihi: isCheckedJihi);
            return (totalCount, 0, historyOrderModelList);
        }

        public (int, List<HistoryOrderVersionModel>) GetListVersion(long raiinNo, int hpId, int userId, long ptId, int sinDate, int offset, int limit, int type = 0, bool includeDraft = false)
        {
            RaiinInf? raiinInf = NoTrackingDataContext.RaiinInfs
                .Where(r => r.HpId == hpId &&
                            r.PtId == ptId &&
                            r.RaiinNo == raiinNo)
                .FirstOrDefault();

            if (raiinInf == null)
            {
                return (0, new List<HistoryOrderVersionModel>());
            }

            var delKarteEditionQuery = NoTrackingDataContext.DelKarteEditions
                .AsEnumerable()
                .Where(r => r.HpId == hpId &&
                            r.PtId == ptId &&
                            r.RaiinNo == raiinNo &&
                            (includeDraft || r.KarteStatus > 0)
                        )
                .GroupJoin(
                    NoTrackingDataContext.UserMsts,
                    delKarteEdition => new { delKarteEdition.HpId, delKarteEdition.ApprovalId },
                    approver => new { approver.HpId, ApprovalId = approver.UserId },
                    (delKarteEdition, approvers) => new { delKarteEdition, approvers }
                )
                .SelectMany(
                    joined => joined.approvers.DefaultIfEmpty(),
                    (joined, approver) => new { joined.delKarteEdition, approver }
                )
                .GroupJoin(
                    NoTrackingDataContext.UserMsts,
                    result => new { result.delKarteEdition.HpId, UpdateId = result.delKarteEdition.UpdateId },
                    updater => new { updater.HpId, UpdateId = updater.UserId },
                    (result, updaters) => new { result.delKarteEdition, result.approver, updaters }
                )
                .SelectMany(
                    result => result.updaters.DefaultIfEmpty(),
                    (result, updater) => new DelKarteEditionModel(
                        result.delKarteEdition.HpId,
                        result.delKarteEdition.PtId,
                        result.delKarteEdition.RaiinNo,
                        result.delKarteEdition.Edition,
                        result.delKarteEdition.KarteStatus,
                        result.delKarteEdition.IsDeleted,
                        result.delKarteEdition.CreateDate,
                        result.delKarteEdition.CreateId,
                        result.delKarteEdition.UpdateDate,
                        result.delKarteEdition.UpdateId,
                        result.delKarteEdition.ApprovalDate,
                        result.delKarteEdition.ApprovalId,
                        result.approver != null ? result.approver.Name ?? string.Empty : string.Empty,
                        updater != null ? updater.Name ?? string.Empty : string.Empty
                    )
                );

            if (limit > 0)
            {
                delKarteEditionQuery = delKarteEditionQuery.Skip(offset).Take(limit);
            }
            delKarteEditionQuery = delKarteEditionQuery.OrderByDescending(r => r.Edition);

            List<DelKarteEditionModel> delKarteEditionModels = delKarteEditionQuery.ToList();

            Dictionary<int, DelKarteInfModel> allDelKarteInfModels = NoTrackingDataContext.DelKarteInfs
                .Where(r => r.HpId == hpId && r.RaiinNo == raiinNo)
                .OrderBy(r => r.Edition)
                .ToDictionary(
                    r => r.Edition,
                    r => new DelKarteInfModel(
                    r.HpId,
                    r.Edition,
                    r.RaiinNo,
                    r.KarteKbn,
                    r.SeqNo,
                    r.PtId,
                    r.SinDate,
                    r.Text ?? string.Empty,
                    r.IsDeleted,
                    System.Text.Encoding.UTF8.GetString(r.RichText ?? Array.Empty<byte>()),
                    r.CreateDate,
                    r.UpdateDate
                ));

            List<InsuranceModel> insuranceModelList = _insuranceRepository.GetInsuranceList(hpId, ptId, sinDate, true);
            List<RaiinListTagModel> tagModelList = _raiinListTagRepository.GetList(hpId, ptId, new List<long> { raiinNo });
            List<FileInfModel> listKarteFile = _karteInfRepository.GetListKarteFileInf(hpId, ptId, raiinNo, type);

            Dictionary<long, List<DelOrdInfModel>> allDelOrderInfList = GetDelOrderInfList(hpId, ptId, new List<long> { raiinNo }, type, null);
            var header = GetDelOrderInfJikanAndShinList(hpId, ptId, new List<long> { raiinNo });
            var jikanAndShinList = header.details;
            var otherHeaders = header.odrInfs;

            Dictionary<int, string> treamtmentDepartmentList = GetTreatmentDepartmentsListByIds(hpId, new List<int> { raiinInf.TreatmentDepartmentId });

            ReceptionModel receptionModel = Reception.FromRaiinInf(raiinInf);

            allDelOrderInfList.TryGetValue(raiinNo, out List<DelOrdInfModel>? orderInfListTemp);
            List<DelOrdInfModel>? delOrderInfListOfRaiinNo = orderInfListTemp ?? new();

            int treatmentDepartmentId = raiinInf.TreatmentDepartmentId;
            string treatmentDepartmentTitle = treamtmentDepartmentList.ContainsKey(treatmentDepartmentId) ? treamtmentDepartmentList[treatmentDepartmentId] : string.Empty;

            string tantoName = _userInfoService.GetNameById(raiinInf.TantoId);
            string tantoFullName = _userInfoService.GetFullNameById(raiinInf.TantoId);
            string kaName = _kaService.GetNameById(raiinInf.KaId);

            RaiinListTagModel tagModel = tagModelList.FirstOrDefault(t => t.RaiinNo == raiinNo) ?? new RaiinListTagModel();
            var approveInfs = GetApproveInf(hpId, ptId, true, new List<long> { raiinNo });
            var approveInf = approveInfs.Any() ? approveInfs.FirstOrDefault(a => a.RaiinNo == raiinNo) : new();

            List<HistoryOrderVersionModel> historyOrderModelList = new List<HistoryOrderVersionModel>();

            foreach (DelKarteEditionModel delKarteEdition in delKarteEditionModels)
            {
                var otherHeader = otherHeaders.FirstOrDefault(o => o.RaiinNo == delKarteEdition.RaiinNo && o.PtId == delKarteEdition.PtId && o.HpId == delKarteEdition.HpId && o.Edition == delKarteEdition.Edition);
                InsuranceModel insuranceModel = insuranceModelList.FirstOrDefault(i => i.HokenPid == otherHeader?.HokenPid) ?? new InsuranceModel();
                List<DelKarteInfModel> delKarteInfModels = new();
                // get delKarteInfModels by edition
                allDelKarteInfModels.TryGetValue(delKarteEdition.Edition, out DelKarteInfModel? delKarteInfModel);
                if (delKarteInfModel != null)
                {
                    delKarteInfModels.Add(delKarteInfModel);
                }
                var jikanKbn = jikanAndShinList.FirstOrDefault(i => i.RaiinNo == delKarteEdition.RaiinNo && i.Edition == delKarteEdition.Edition && i.ItemCd == "@JIKAN")?.Suryo;
                var syosaisinKbn = jikanAndShinList.FirstOrDefault(i => i.RaiinNo == delKarteEdition.RaiinNo && i.Edition == delKarteEdition.Edition && i.ItemCd == "@SHIN")?.Suryo;
                List<DelOrdInfModel>? delOrderInfList = delOrderInfListOfRaiinNo.Where(o => o.Edition == delKarteEdition.Edition).ToList();

                historyOrderModelList.Add(new HistoryOrderVersionModel(
                    receptionModel,
                    insuranceModel,
                    delOrderInfList,
                    delKarteInfModels,
                    treatmentDepartmentId,
                    treatmentDepartmentTitle,
                    kaName,
                    tantoName,
                    tantoFullName,
                    tagModel.TagNo,
                    approveInf?.DisplayApprovalInfo ?? string.Empty,
                    jikanKbn ?? 0,
                    syosaisinKbn ?? 0,
                    listKarteFile.Where(file =>
                    (file.CreateDate <= delKarteEdition.UpdateDate && !file.IsDelete) ||
                        (file.IsDelete && file.CreateDate <= delKarteEdition.UpdateDate && file.UpdateDate >= delKarteEdition.UpdateDate)).ToList(),
                    delKarteEdition
                ));
            }

            return (delKarteEditionModels.Count, historyOrderModelList);
        }

        public Dictionary<long, List<HistoryOrderVersionModel>> GetListVersionAllRaiinNo(int hpId, long ptId, int sinDate, int type = 0, bool includeDraft = false)
        {
            var raiinInfs = NoTrackingDataContext.RaiinInfs
                .Where(r => r.HpId == hpId && r.PtId == ptId)
                .ToList();

            var raiinNos = raiinInfs.Select(r => r.RaiinNo).ToList();

            if (!raiinNos.Any())
            {
                return new Dictionary<long, List<HistoryOrderVersionModel>>();
            }

            var delKarteEditionQuery = NoTrackingDataContext.DelKarteEditions
                .AsEnumerable()
                .Where(r => r.HpId == hpId &&
                            r.PtId == ptId &&
                            raiinNos.Contains(r.RaiinNo) &&
                            (includeDraft || r.KarteStatus > 0))
                .GroupJoin(
                    NoTrackingDataContext.UserMsts,
                    delKarteEdition => new { delKarteEdition.HpId, delKarteEdition.ApprovalId },
                    approver => new { approver.HpId, ApprovalId = approver.UserId },
                    (delKarteEdition, approvers) => new { delKarteEdition, approvers }
                )
                .SelectMany(
                    joined => joined.approvers.DefaultIfEmpty(),
                    (joined, approver) => new { joined.delKarteEdition, approver }
                )
                .GroupJoin(
                    NoTrackingDataContext.UserMsts,
                    result => new { result.delKarteEdition.HpId, UpdateId = result.delKarteEdition.UpdateId },
                    updater => new { updater.HpId, UpdateId = updater.UserId },
                    (result, updaters) => new { result.delKarteEdition, result.approver, updaters }
                )
                .SelectMany(
                    result => result.updaters.DefaultIfEmpty(),
                    (result, updater) => new DelKarteEditionModel(
                        result.delKarteEdition.HpId,
                        result.delKarteEdition.PtId,
                        result.delKarteEdition.RaiinNo,
                        result.delKarteEdition.Edition,
                        result.delKarteEdition.KarteStatus,
                        result.delKarteEdition.IsDeleted,
                        result.delKarteEdition.CreateDate,
                        result.delKarteEdition.CreateId,
                        result.delKarteEdition.UpdateDate,
                        result.delKarteEdition.UpdateId,
                        result.delKarteEdition.ApprovalDate,
                        result.delKarteEdition.ApprovalId,
                        result.approver != null ? result.approver.Name ?? string.Empty : string.Empty,
                        updater != null ? updater.Name ?? string.Empty : string.Empty
                    )
                );

            List<DelKarteEditionModel> delKarteEditionModels = delKarteEditionQuery.ToList();

            List<DelKarteInfModel> allDelKarteInfModels = NoTrackingDataContext.DelKarteInfs
                .Where(r => r.HpId == hpId && raiinNos.Contains(r.RaiinNo))
                .Select(r => new DelKarteInfModel(
                    r.HpId,
                    r.Edition,
                    r.RaiinNo,
                    r.KarteKbn,
                    r.SeqNo,
                    r.PtId,
                    r.SinDate,
                    r.Text ?? string.Empty,
                    r.IsDeleted,
                    System.Text.Encoding.UTF8.GetString(r.RichText ?? Array.Empty<byte>()),
                    r.CreateDate,
                    r.UpdateDate
                ))
                .ToList();

            var insuranceModels = _insuranceRepository.GetInsuranceList(hpId, ptId, sinDate, true);
            var tagModelList = _raiinListTagRepository.GetList(hpId, ptId, raiinNos);
            var listKarteFile = _karteInfRepository.GetListKarteFileVersion(hpId, ptId, raiinNos, false);

            var allDelOrderInfList = GetDelOrderInfList(hpId, ptId, raiinNos, type, null);
            var header = GetDelOrderInfJikanAndShinList(hpId, ptId, raiinNos);
            var jikanAndShinList = header.details;
            var otherHeaders = header.odrInfs;
            var treamtmentDepartmentIds = raiinInfs.Select(r => r.TreatmentDepartmentId).Distinct().ToList();
            var treatmentDepartmentList = GetTreatmentDepartmentsListByIds(hpId, treamtmentDepartmentIds);

            var result = new Dictionary<long, List<HistoryOrderVersionModel>>();

            foreach (var raiinNo in raiinNos)
            {
                var relatedEditions = delKarteEditionModels.Where(d => d.RaiinNo == raiinNo).ToList();
                var relatedRaiinInf = raiinInfs.FirstOrDefault(r => r.RaiinNo == raiinNo);

                if (relatedRaiinInf == null || !relatedEditions.Any())
                {
                    continue;
                }

                var receptionModel = Reception.FromRaiinInf(relatedRaiinInf);



                var tagModel = tagModelList.FirstOrDefault(t => t.RaiinNo == raiinNo) ?? new RaiinListTagModel();

                allDelOrderInfList.TryGetValue(raiinNo, out List<DelOrdInfModel>? delOrderInfListOfRaiinNo);
                delOrderInfListOfRaiinNo ??= new List<DelOrdInfModel>();

                int treatmentDepartmentId = relatedRaiinInf.TreatmentDepartmentId;
                string treatmentDepartmentTitle = treatmentDepartmentList.ContainsKey(treatmentDepartmentId)
                                                ? treatmentDepartmentList[treatmentDepartmentId]
                                                : string.Empty;

                string tantoName = _userInfoService.GetNameById(relatedRaiinInf.TantoId);
                string tantoFullName = _userInfoService.GetFullNameById(relatedRaiinInf.TantoId);
                string kaName = _kaService.GetNameById(relatedRaiinInf.KaId);

                var approveInfs = GetApproveInf(hpId, ptId, true, new List<long> { raiinNo });
                var approveInf = approveInfs.Any() ? approveInfs.FirstOrDefault(a => a.RaiinNo == raiinNo) : new();

                List<HistoryOrderVersionModel> historyOrderModelList = new List<HistoryOrderVersionModel>();

                foreach (DelKarteEditionModel delKarteEdition in relatedEditions)
                {
                    var otherHeader = otherHeaders.FirstOrDefault(o => o.RaiinNo == delKarteEdition.RaiinNo && o.PtId == delKarteEdition.PtId && o.HpId == delKarteEdition.HpId && o.Edition == delKarteEdition.Edition);
                    var insuranceModel = insuranceModels.FirstOrDefault(i => i.HokenPid == otherHeader?.HokenPid)
                                  ?? new InsuranceModel();
                    // get delKarteInfModels by edition
                    List<DelKarteInfModel> delKarteInfModels = allDelKarteInfModels.Where(d => d.Edition == delKarteEdition.Edition && d.RaiinNo == delKarteEdition.RaiinNo).ToList();

                    List<DelOrdInfModel>? delOrderInfList = delOrderInfListOfRaiinNo.Where(o => o.Edition == delKarteEdition.Edition && o.RaiinNo == delKarteEdition.RaiinNo).ToList();
                    var jikanKbn = jikanAndShinList.FirstOrDefault(i => i.RaiinNo == delKarteEdition.RaiinNo && i.Edition == delKarteEdition.Edition && i.ItemCd == "@JIKAN")?.Suryo;
                    var syosaisinKbn = jikanAndShinList.FirstOrDefault(i => i.RaiinNo == delKarteEdition.RaiinNo && i.Edition == delKarteEdition.Edition && i.ItemCd == "@SHIN")?.Suryo;
                    historyOrderModelList.Add(new HistoryOrderVersionModel(
                        receptionModel,
                        insuranceModel,
                        delOrderInfList,
                        delKarteInfModels,
                        treatmentDepartmentId,
                        treatmentDepartmentTitle,
                        kaName,
                        tantoName,
                        tantoFullName,
                        tagModel.TagNo,
                        approveInf?.DisplayApprovalInfo ?? string.Empty,
                        jikanKbn ?? 0,
                        syosaisinKbn ?? 0,
                        listKarteFile.Where(file =>
                        (file.CreateDate <= delKarteEdition.UpdateDate && !file.IsDelete) ||
                        (file.IsDelete && file.CreateDate <= delKarteEdition.UpdateDate && file.UpdateDate >= delKarteEdition.UpdateDate)
                        ).ToList(),
                        delKarteEdition
                    ));
                }

                result[raiinNo] = historyOrderModelList;
            }

            return result;
        }

        //type = 1 for print, type = 0 for history
        private (int totalCount, List<HistoryOrderModel> historyOrderModelList) GetList(int hpId, long ptId, int sinDate, List<RaiinInf> raiinInfList, List<RaiinInf> raiinInfListCheckOdrInf, int totalCount, int isDeleted, int isShowApproval, int type, string? keyWord = null, bool hasSOAP = false, List<int>? odrKouiKbns = null, List<InsuranceSummaryModel>? insuranceModels = null, bool isCheckedJihi = true)
        {
            if (!raiinInfList.Any())
            {
                return (0, new List<HistoryOrderModel>());
            }
            var raiinNos = new List<long>();
            List<long> raiinNoList = raiinInfList.Select(r => r.RaiinNo).ToList();
            List<long> raiinNoListCheckOdrInf = raiinInfListCheckOdrInf.Select(r => r.RaiinNo).ToList();
            List<Task> tasks = new();

            var taskKarteInfList = Task.Run(() =>
            {
                return GetKarteInfList(hpId, ptId, isDeleted, raiinNoList);
            });
            tasks.Add(taskKarteInfList);

            Task<Dictionary<long, List<OrdInfModel>>>? taskAllOrderInfList = null;
            if (type == 0)
            {
                taskAllOrderInfList = Task.Run(() =>
                {
                    return GetOrderInfList(hpId, ptId, isDeleted, raiinNoListCheckOdrInf, type, null, keyWord, odrKouiKbns);
                });
            }
            else if (type == 1)
            {
                taskAllOrderInfList = Task.Run(() =>
                {
                    return GetOrderInfList(hpId, ptId, isDeleted, raiinNoList, type, null, keyWord, odrKouiKbns);
                });
            }
            if (taskAllOrderInfList != null)
            {
                tasks.Add(taskAllOrderInfList);
            }

            var taskKarteEdition = Task.Run(() =>
            {
                return GetKarteEditions(hpId, ptId, raiinNoList);
            });
            tasks.Add(taskKarteEdition);

            var tasktreamtmentDepartmentList = Task.Run(() =>
            {
                return GetTreatmentDepartmentsListByIds(hpId, raiinInfList.Select(r => r.TreatmentDepartmentId).Distinct().ToList());
            });
            tasks.Add(tasktreamtmentDepartmentList);

            //var taskRaiinListTag = Task.Run(() =>
            //{
            //    return _raiinListTagRepository.GetList(hpId, ptId, raiinNoList);
            //});
            //tasks.Add(taskRaiinListTag);
            var taskKarteFileList = Task.Run(() =>
            {
                return _karteInfRepository.GetListKarteFile(hpId, ptId, raiinNoList, isDeleted != 0, type);
            });
            tasks.Add(taskKarteFileList);

            ConcurrentBag<HistoryOrderModel> historyOrderModelList = new ConcurrentBag<HistoryOrderModel>();
            var taskApproveInf = Task.Run(() =>
            {
                var approveInfs = (isShowApproval == 1 || isShowApproval == 2) ? GetApproveInf(hpId, ptId, isShowApproval == 2, raiinNoList) : new List<ApproveInfModel>();
                return approveInfs;
            });
            tasks.Add(taskApproveInf);

            Task.WaitAll(tasks.ToArray());
            var allKarteEditions = taskKarteEdition.Result;
            var allKarteInfList = taskKarteInfList.Result;
            //var tagModelList = taskRaiinListTag.Result;
            var listKarteFile = taskKarteFileList.Result;
            var approveInfs = taskApproveInf.Result;
            var treamtmentDepartmentList = tasktreamtmentDepartmentList.Result;
            var allOrderInfList = taskAllOrderInfList?.Result ?? new();

            Parallel.ForEach(raiinNoList, raiinNo =>
            {
                RaiinInf? raiinInf = raiinInfList.FirstOrDefault(r => r.RaiinNo == raiinNo);
                if (raiinInf == null)
                {
                    return;
                }

                ReceptionModel receptionModel = Reception.FromRaiinInf(raiinInf);
                List<KarteInfModel> karteInfModels = allKarteInfList.Where(r => r.RaiinNo == raiinNo).ToList() ?? new();

                // hasSOAP: true -> get records that have karte_inf not empty. false -> get all records
                if (hasSOAP && !karteInfModels.Any())
                {
                    return;
                }

                allOrderInfList.TryGetValue(raiinNo, out List<OrdInfModel>? orderInfListTemp);
                List<OrdInfModel>? orderInfList = orderInfListTemp ?? new();

                if (!isCheckedJihi && orderInfList.Any())
                {
                    orderInfList = orderInfList.Where(p => p.SanteiKbn != 2).ToList();
                }

                allKarteEditions.TryGetValue(raiinNo, out KarteEditionModel? karteEdition);

                var insuranceModel = insuranceModels?.FirstOrDefault(i => i.HokenPid == raiinInf.HokenPid) ?? new InsuranceSummaryModel();
                RaiinListTagModel tagModel = new();
                List<FileInfModel> listKarteFileModel = listKarteFile.Where(item => item.RaiinNo == raiinNo).ToList();
                string tantoName = _userInfoService.GetNameById(raiinInf.TantoId);
                string tantoFullName = _userInfoService.GetFullNameById(raiinInf.TantoId);
                string kaName = _kaService.GetNameById(raiinInf.KaId);
                var approveInf = approveInfs.Any() ? approveInfs.FirstOrDefault(a => a.RaiinNo == raiinNo) : new();

                int treatmentDepartmentId = raiinInf.TreatmentDepartmentId;
                string treatmentDepartmentTitle = treamtmentDepartmentList.ContainsKey(treatmentDepartmentId) ? treamtmentDepartmentList[treatmentDepartmentId] : string.Empty;

                historyOrderModelList.Add(new HistoryOrderModel(receptionModel,
                                                                insuranceModel,
                                                                orderInfList,
                                                                karteInfModels,
                                                                treatmentDepartmentId,
                                                                treatmentDepartmentTitle,
                                                                kaName,
                                                                tantoName,
                                                                tantoFullName,
                                                                tagModel.TagNo,
                                                                approveInf?.DisplayApprovalInfo ?? string.Empty,
                                                                listKarteFileModel.Where(file => (file.CreateDate <= karteEdition?.UpdateDate && !file.IsDelete) || (file.IsDelete && file.CreateDate <= karteEdition?.UpdateDate && file.UpdateDate >= karteEdition?.UpdateDate)).ToList(),
                                                                karteEdition ?? new()));
            });

            return (totalCount, historyOrderModelList.OrderByDescending(r => r.SinDate)
                .ThenByDescending(r => r.UketukeTime)
                .ThenByDescending(r => r.RaiinNo).ToList());
        }

        private Dictionary<int, string> GetTreatmentDepartmentsListByIds(int hpId, List<int> treatmentDepartmentIds)
        {
            var treatmentDepartmentList = _tenantProvider3.CreateNewNoTrackingDataContext().TreatmentDepartments
                .Where(r => r.HospitalId == hpId && treatmentDepartmentIds.Contains(r.TreatmentDepartmentId))
                .Select(r => new { r.TreatmentDepartmentId, r.Title })
                .ToDictionary(r => r.TreatmentDepartmentId, r => r.Title);

            return treatmentDepartmentList;
        }

        private Dictionary<long, KarteEditionModel> GetKarteEditions(int hpId, long ptId, List<long> raiinNoList)
        {
            var karteEditions = _tenantProvider2.CreateNewNoTrackingDataContext().KarteEditions
                .Where(k => k.HpId == hpId && k.PtId == ptId && raiinNoList.Contains(k.RaiinNo))
                .ToList();

            List<int> updateUserIds = karteEditions.Select(k => k.UpdateId).ToList();
            List<int> approvalUserIds = karteEditions.Select(k => k.ApprovalId).ToList();
            List<int> userIds = updateUserIds.Union(approvalUserIds).ToList();
            var users = NoTrackingDataContext.UserMsts
                .Where(
                    u => u.HpId == hpId && userIds.Contains(u.UserId)
                )
                .ToList();
            Dictionary<int, string> userNames = users.ToDictionary(u => u.UserId, u => u.Name ?? string.Empty);

            return karteEditions.ToDictionary(
                k => k.RaiinNo,
                k => new KarteEditionModel(
                    k.HpId,
                    k.PtId,
                    k.RaiinNo,
                    k.Edition,
                    k.KarteStatus,
                    k.IsDeleted,
                    k.CreateDate,
                    k.CreateId,
                    k.UpdateDate,
                    k.UpdateId,
                    k.ApprovalDate,
                    k.ApprovalId,
                    userNames.ContainsKey(k.UpdateId) ? userNames[k.UpdateId] : string.Empty,
                    userNames.ContainsKey(k.ApprovalId) ? userNames[k.ApprovalId] : string.Empty
                )
            );
        }


        public (int totalCount, List<HistoryOrderModel> historyOrderModels) GetOrdersForOneOrderSheetGroup(int hpId, long ptId, int odrKouiKbn, int grpKouiKbn, int sinDate, int offset, int limit)
        {
            var raiinInfs = GetRaiinInfs(hpId, ptId, sinDate, odrKouiKbn, grpKouiKbn);
            IEnumerable<RaiinInf> raiinInfEnumerable = raiinInfs.raiinInfs;

            int totalCount = raiinInfEnumerable.Count();
            List<RaiinInf> raiinInfList = raiinInfEnumerable.OrderByDescending(r => r.SinDate).ThenByDescending(r => r.UketukeTime).ThenByDescending(r => r.RaiinNo).Skip(offset).Take(limit).ToList();

            if (!raiinInfList.Any())
            {
                return (0, new List<HistoryOrderModel>());
            }

            List<long> raiinNoList = raiinInfList.Select(r => r.RaiinNo).ToList();
            var odrInfs = raiinInfs.allOdrInfs.AsEnumerable().Where(o => raiinNoList.Contains(o.RaiinNo)).ToList();

            Dictionary<long, List<OrdInfModel>> allOrderInfList = GetOrderInfList(hpId, ptId, 0, raiinNoList, 0, odrInfs);

            Dictionary<long, KarteEditionModel> allKarteEditions = GetKarteEditions(hpId, ptId, raiinNoList);

            Dictionary<int, string> treamtmentDepartmentList = GetTreatmentDepartmentsListByIds(hpId, raiinInfList.Select(r => r.TreatmentDepartmentId).Distinct().ToList());

            List<InsuranceSummaryModel> insuranceModelList = _insuranceRepository.GetInsuranceSummaryList(hpId, ptId, sinDate, true);

            List<HistoryOrderModel> historyOrderModelList = new List<HistoryOrderModel>();
            foreach (long raiinNo in raiinNoList)
            {
                RaiinInf? raiinInf = raiinInfList.FirstOrDefault(r => r.RaiinNo == raiinNo);
                if (raiinInf == null)
                {
                    continue;
                }

                ReceptionModel receptionModel = Reception.FromRaiinInf(raiinInf);
                allOrderInfList.TryGetValue(raiinNo, out List<OrdInfModel>? orderInfListTemp);
                List<OrdInfModel> orderInfList = orderInfListTemp ?? new();

                allKarteEditions.TryGetValue(raiinNo, out KarteEditionModel? karteEdition);

                var insuranceModel = insuranceModelList.FirstOrDefault(i => i.HokenPid == raiinInf.HokenPid) ?? new InsuranceSummaryModel();

                string tantoName = _userInfoService.GetNameById(raiinInf.TantoId);
                string tantoFullName = _userInfoService.GetFullNameById(raiinInf.TantoId);
                string kaName = _kaService.GetNameById(raiinInf.KaId);

                int treatmentDepartmentId = raiinInf.TreatmentDepartmentId;
                string treatmentDepartmentTitle = treamtmentDepartmentList.ContainsKey(treatmentDepartmentId) ? treamtmentDepartmentList[treatmentDepartmentId] : string.Empty;

                historyOrderModelList.Add(new HistoryOrderModel(receptionModel, insuranceModel, orderInfList, new(), treatmentDepartmentId, treatmentDepartmentTitle, kaName, tantoName, tantoFullName, 0, string.Empty, new(), karteEdition ?? new()));
            }

            return (totalCount, historyOrderModelList);
        }

        //flag == 0 : get for accounting
        //flag == 1 : get for one rp in todayorder
        public List<HistoryOrderModel> GetListByRaiin(int hpId, int userId, long ptId, int sinDate, int filterId, int isDeleted, long raiin, byte flag, List<Tuple<long, bool>> raiinNos, int isShowApproval)
        {
            IEnumerable<RaiinInf> raiinInfEnumerable = GenerateRaiinListQuery(hpId, userId, ptId, filterId, isDeleted, raiinNos);

            var oyaRaiinNo = NoTrackingDataContext.RaiinInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId && x.SinDate == sinDate && x.RaiinNo == raiin && x.IsDeleted == 0);
            if (oyaRaiinNo == null || (oyaRaiinNo.Status < RaiinState.ConsultationCompleted && flag != 1))
            {
                return new List<HistoryOrderModel>();
            }

            raiinInfEnumerable = raiinInfEnumerable.Where(x => x.OyaRaiinNo == oyaRaiinNo.OyaRaiinNo && (flag != 1 || x.RaiinNo == oyaRaiinNo.RaiinNo));

            List<RaiinInf> raiinInfList = raiinInfEnumerable.OrderByDescending(r => r.SinDate).ThenByDescending(r => r.UketukeTime).ThenByDescending(r => r.RaiinNo).ToList();

            if (!raiinInfList.Any())
            {
                return new();
            }

            List<long> raiinNoList = raiinInfList.Select(r => r.RaiinNo).ToList();

            List<KarteInfModel> allKarteInfList = GetKarteInfList(hpId, ptId, isDeleted, raiinNoList);
            Dictionary<long, List<OrdInfModel>> allOrderInfList = GetOrderInfList(hpId, ptId, isDeleted, raiinNoList, 1);

            Dictionary<long, KarteEditionModel> allKarteEditions = GetKarteEditions(hpId, ptId, raiinNoList);

            List<InsuranceModel> insuranceModelList = _insuranceRepository.GetInsuranceList(hpId, ptId, sinDate, true);
            List<RaiinListTagModel> tagModelList = _raiinListTagRepository.GetList(hpId, ptId, raiinNoList);
            List<FileInfModel> listKarteFile = _karteInfRepository.GetListKarteFile(hpId, ptId, raiinNoList, isDeleted != 0, 0);
            List<HistoryOrderModel> historyOrderModelList = new List<HistoryOrderModel>();
            var approveInfs = (isShowApproval == 1 || isShowApproval == 2) ? GetApproveInf(hpId, ptId, isShowApproval == 2, raiinNoList) : new List<ApproveInfModel>();
            foreach (long raiinNo in raiinNoList)
            {
                RaiinInf? raiinInf = raiinInfList.FirstOrDefault(r => r.RaiinNo == raiinNo);
                if (raiinInf == null)
                {
                    continue;
                }

                ReceptionModel receptionModel = Reception.FromRaiinInf(raiinInf);
                List<KarteInfModel> karteInfModels = allKarteInfList.Where(r => r.RaiinNo == raiinNo).ToList() ?? new();
                allOrderInfList.TryGetValue(raiinNo, out List<OrdInfModel>? orderInfListTemp);

                allKarteEditions.TryGetValue(raiinNo, out KarteEditionModel? karteEdition);

                List<OrdInfModel>? orderInfList = orderInfListTemp?.Where(o => o.OdrKouiKbn != 10).ToList() ?? new();
                var headerOrders = orderInfListTemp?.Where(o => o.OdrKouiKbn == 10).OrderByDescending(o => o.CreateDate).ToList() ?? new();

                InsuranceModel insuranceModel = insuranceModelList.FirstOrDefault(i => i.HokenPid == raiinInf.HokenPid) ?? new InsuranceModel();
                RaiinListTagModel tagModel = tagModelList.FirstOrDefault(t => t.RaiinNo == raiinNo) ?? new RaiinListTagModel();
                List<FileInfModel> listKarteFileModel = listKarteFile.Where(item => item.RaiinNo == raiinNo).ToList();
                string tantoName = _userInfoService.GetNameById(raiinInf.TantoId);
                string tantoFullName = _userInfoService.GetFullNameById(raiinInf.TantoId);
                string kaName = _kaService.GetNameById(raiinInf.KaId);
                var approveInf = approveInfs?.Count() > 0 ? approveInfs.FirstOrDefault(a => a.RaiinNo == raiinNo) : new();

                var headerOrderModels = new List<HeaderOrderModel>();
                foreach (var headerOrder in headerOrders)
                {
                    var insurance = insuranceModelList.FirstOrDefault(i => i.HokenPid == headerOrder.HokenPid) ?? new InsuranceModel();
                    var hokenPattentName = insurance.HokenName;
                    var updateName = string.IsNullOrEmpty(headerOrder.UpdateName) ? headerOrder.CreateName : headerOrder.UpdateName;
                    var displaycreateDate = headerOrder.CreateDate.ToString("yyyy/MM/dd HH:mm");
                    var syosaiKbn = headerOrder.OrdInfDetails.FirstOrDefault(od => od.ItemCd == ItemCdConst.SyosaiKihon)?.Suryo;
                    var jikanKbn = headerOrder.OrdInfDetails.FirstOrDefault(od => od.ItemCd == ItemCdConst.JikanKihon)?.Suryo;
                    var headerOrderModel = new HeaderOrderModel(syosaiKbn ?? 0, jikanKbn ?? 0, hokenPattentName, displaycreateDate, updateName, headerOrder.IsDeleted);
                    headerOrderModels.Add(headerOrderModel);
                }

                historyOrderModelList.Add(new HistoryOrderModel(receptionModel, insuranceModel, orderInfList, karteInfModels, kaName, tantoName, tantoFullName, tagModel.TagNo, approveInf?.DisplayApprovalInfo ?? string.Empty, listKarteFileModel, headerOrderModels, karteEdition ?? new()));
            }

            return historyOrderModelList;
        }

        public bool CheckExistedFilter(int hpId, int userId, int filterId)
        {
            return NoTrackingDataContext.KarteFilterMsts.Any(u => u.HpId == hpId && u.UserId == userId && u.FilterId == filterId && u.IsDeleted == 0);
        }

        public long GetHistoryIndex(int hpId, long ptId, long raiinNo, int userId, int filterId, int isDeleted, List<Tuple<long, bool>> raiinNos)
        {
            var raiinInfs = GenerateRaiinListQuery(hpId, userId, ptId, filterId, isDeleted, raiinNos)
                                                .OrderByDescending(r => r.SinDate)
                                                .ThenByDescending(r => r.UketukeTime)
                                                .ThenByDescending(r => r.RaiinNo).Select(r => r.RaiinNo).ToList();
            var index = raiinInfs.IndexOf(raiinNo);
            return index;
        }

        public List<SinKouiListModel> GetSinkouiList(int hpId, long ptId, List<int> sinDateList, List<long> raiinNoList, List<int> mainPidList)
        {
            var sinkouis = NoTrackingDataContext.SinKouis.Where(p => p.HpId == hpId && p.PtId == ptId && p.IsDeleted == 0);
            var sinkouiCounts = NoTrackingDataContext.SinKouiCounts.Where(p => p.HpId == hpId && p.PtId == ptId);
            var sinkouiDetails = NoTrackingDataContext.SinKouiDetails.Where(p => p.HpId == hpId && p.PtId == ptId && p.IsDeleted == 0);
            var tenMsts = NoTrackingDataContext.TenMsts.Where(p => p.HpId == hpId && p.IsDeleted == DeleteTypes.None);

            var sinKouiJoinSinKouiCountquery = from sinkoui in sinkouis
                                               join sinkouiCount in sinkouiCounts
                                               on new { sinkoui.RpNo, sinkoui.SeqNo } equals new { sinkouiCount.RpNo, sinkouiCount.SeqNo }
                                               select new
                                               {
                                                   Sinkoui = sinkoui,
                                                   SinKouiCount = sinkouiCount
                                               };

            var sinKouiCountJoinDetailQuery = from sinKouiJoinSinKouiCount in sinKouiJoinSinKouiCountquery
                                              join sinKouiDetail in sinkouiDetails
                                              on new { sinKouiJoinSinKouiCount.SinKouiCount.RpNo, sinKouiJoinSinKouiCount.SinKouiCount.SeqNo }
                                              equals new { sinKouiDetail.RpNo, sinKouiDetail.SeqNo }
                                              select new
                                              {
                                                  Sinkoui = sinKouiJoinSinKouiCount.Sinkoui,
                                                  SinKouiCount = sinKouiJoinSinKouiCount.SinKouiCount,
                                                  SinKouiDetail = sinKouiDetail
                                              };
            var joinTenMstQuery = from sinKouiCountJoinDetail in sinKouiCountJoinDetailQuery
                                  join tenMst in tenMsts
                                  on sinKouiCountJoinDetail.SinKouiDetail.ItemCd equals tenMst.ItemCd into tempTenMstList
                                  join userMst in NoTrackingDataContext.UserMsts.Where(item => item.HpId == hpId && item.IsDeleted == 0)
                                  on sinKouiCountJoinDetail.Sinkoui.CreateId equals userMst.UserId
                                  select new
                                  {
                                      CreateId = sinKouiCountJoinDetail.Sinkoui.CreateId,
                                      CreateDate = sinKouiCountJoinDetail.Sinkoui.CreateDate,
                                      HokenPid = sinKouiCountJoinDetail.Sinkoui.HokenPid,
                                      SinDate = sinKouiCountJoinDetail.SinKouiCount.SinDate,
                                      RaiinNo = sinKouiCountJoinDetail.SinKouiCount.RaiinNo,
                                      ItemCd = sinKouiCountJoinDetail.SinKouiDetail.ItemCd,
                                      UserName = userMst.Name,
                                      TenMst = tempTenMstList.FirstOrDefault(p => p.StartDate <= sinKouiCountJoinDetail.SinKouiCount.SinDate && sinKouiCountJoinDetail.SinKouiCount.SinDate <= p.EndDate)
                                  };

            var joinTenMstList = joinTenMstQuery.Where(p => raiinNoList.Contains(p.RaiinNo)
                                                            && sinDateList.Contains(p.SinDate)
                                                            && mainPidList.Contains(p.HokenPid)
                                                            && p.TenMst.IsNodspKarte == 0)
                                                .ToList();

            var result = joinTenMstList.Select(item => new SinKouiListModel(
                                                       item.CreateId,
                                                       item.UserName,
                                                       item.CreateDate,
                                                       item.HokenPid,
                                                       item.SinDate,
                                                       item.RaiinNo,
                                                       item.ItemCd,
                                                       item.TenMst.SinKouiKbn,
                                                       item.TenMst.ItemCd,
                                                       item.TenMst.Name ?? string.Empty,
                                                       item.TenMst.KohatuKbn,
                                                       item.TenMst.YohoKbn,
                                                       item.TenMst.IpnNameCd ?? string.Empty,
                                                       item.TenMst.DrugKbn,
                                                       item.TenMst.IsNodspKarte
                                        )).ToList();
            return result;
        }

        #region private method
        private long SearchKarte(int hpId, long ptId, int isDeleted, List<long> raiinNoList, string keyWord, bool isNext)
        {
            var karteInfEntities = NoTrackingDataContext.KarteInfs
                .Where(k => k.PtId == ptId &&
                            k.HpId == hpId &&
                            k.KarteKbn == KarteConst.KarteKbn &&
                            k.Text != null &&
                            k.Text.Contains(keyWord) &&
                            raiinNoList.Contains(k.RaiinNo) &&
                            (
                                k.IsDeleted == DeleteTypes.None ||
                                isDeleted == 1 ||
                                (k.IsDeleted != DeleteTypes.Confirm && isDeleted == 2)
                            )
                       ).AsEnumerable();

            if (isNext)
            {
                karteInfEntities = karteInfEntities.OrderByDescending(k => k.SinDate).OrderByDescending(k => k.RaiinNo);
            }
            else
            {
                karteInfEntities = karteInfEntities.OrderBy(k => k.SinDate).ThenBy(k => k.RaiinNo);
            }

            KarteInf? karteInf = karteInfEntities.FirstOrDefault();

            return karteInf == null ? 0 : karteInf.RaiinNo;
        }

        private long SearchOrder(int hpId, long ptId, int isDeleted, List<long> raiinNoList, string keyWord, bool isNext)
        {
            List<OdrInf> allOdrInfList = NoTrackingDataContext.OdrInfs
                .Where(o => o.HpId == hpId &&
                            o.PtId == ptId &&
                            o.OdrKouiKbn != 10 &&
                            raiinNoList.Contains(o.RaiinNo) &&
                            (
                                o.IsDeleted == DeleteTypes.None ||
                                isDeleted == 1 ||
                                (o.IsDeleted != DeleteTypes.Confirm && isDeleted == 2)
                            )
                      )
                .ToList();
            List<long> raiinNoListByOrder = allOdrInfList.Select(o => o.RaiinNo).Distinct().ToList();
            List<long> rpNoListByOrder = allOdrInfList.Select(o => o.RpNo).Distinct().ToList();
            List<long> rpEdaNoListByOrder = allOdrInfList.Select(o => o.RpEdaNo).Distinct().ToList();
            var allOdrDetailInfList = NoTrackingDataContext.OdrInfDetails
                .Where(o => o.HpId == hpId &&
                            o.PtId == ptId &&
                            raiinNoListByOrder.Contains(o.RaiinNo) &&
                            rpNoListByOrder.Contains(o.RpNo) &&
                            rpEdaNoListByOrder.Contains(o.RpEdaNo) &&
                            o.ItemName != null &&
                            o.ItemName.Contains(keyWord));
            if (isNext)
            {
                allOdrDetailInfList = allOdrDetailInfList.OrderByDescending(o => o.SinDate).ThenByDescending(o => o.RaiinNo);
            }
            else
            {
                allOdrDetailInfList = allOdrDetailInfList.OrderBy(o => o.SinDate).ThenBy(o => o.RaiinNo);
            }

            OdrInfDetail? odrInfDetail = allOdrDetailInfList.FirstOrDefault();

            return odrInfDetail == null ? 0 : odrInfDetail.RaiinNo;
        }

        private List<KarteInfModel> GetKarteInfList(int hpId, long ptId, int isDeleted, List<long> raiinNoList, string? keyWord = null)
        {
            var karteInfEntities = _tenantProvider1.CreateNewNoTrackingDataContext().KarteInfs.Where(k =>
                k.PtId == ptId &&
                k.HpId == hpId &&
                raiinNoList.Contains(k.RaiinNo) &&
                k.KarteKbn == 1 &&
                (string.IsNullOrEmpty(keyWord) || (k.Text != null && k.Text.Contains(keyWord)))
            ).AsEnumerable();

            if (isDeleted == 0)
            {
                karteInfEntities = karteInfEntities.Where(r => r.IsDeleted == DeleteTypes.None);
            }
            else if (isDeleted == 1)
            {
                karteInfEntities = karteInfEntities.Where(r => r.IsDeleted == DeleteTypes.None || r.IsDeleted == DeleteTypes.Deleted || r.IsDeleted == DeleteTypes.Confirm);
            }
            else if (isDeleted == 2)
            {
                karteInfEntities = karteInfEntities.Where(r => r.IsDeleted == DeleteTypes.None || r.IsDeleted == DeleteTypes.Deleted);
            }

            if (karteInfEntities == null)
            {
                return new List<KarteInfModel>();
            }

            var karteInfs = from karte in karteInfEntities
                            join user in _tenantProvider1.CreateNewNoTrackingDataContext().UserMsts.Where(u => u.HpId == hpId)
                          on karte.CreateId equals user.UserId into odrUsers
                            from odrUser in odrUsers.DefaultIfEmpty()
                            select Karte.FromKarte(karte, odrUser?.Sname ?? string.Empty);

            return karteInfs.ToList();
        }

        private Dictionary<long, List<OrdInfModel>> GetOrderInfList(int hpId, long ptId, int isDeleted, List<long> raiinNoList, int type, [Optional] List<OdrInf>? memoryOdrInfs, string? keyWord = null, List<int>? odrKouiKbns = null)
        {
            List<OdrInf> allOdrInfList;
            List<Task> tasks = new();
            Task<List<OdrInf>>? taskOdrInfList = null;

            if (memoryOdrInfs != null)
            {
                allOdrInfList = memoryOdrInfs;
            }
            else
            {
                if (type == 0)
                {
                    taskOdrInfList = Task.Run(() =>
                     {
                         return _tenantProvider6.CreateNewNoTrackingDataContext().OdrInfs
                                 .Where(o => o.HpId == hpId &&
                                             o.PtId == ptId &&
                                             (
                                                 (type == 0 && o.OdrKouiKbn != 10) ||
                                                 (type == 1 && o.OdrKouiKbn == 10)
                                             ) &&
                                             raiinNoList.Contains(o.RaiinNo) &&
                                             (
                                                 o.IsDeleted == DeleteTypes.None ||
                                                 isDeleted == 1 ||
                                                 (o.IsDeleted != DeleteTypes.Confirm && isDeleted == 2)
                                             )
                                       //   (string.IsNullOrEmpty(keyWord) || (o.RpName != null && o.RpName.Contains(keyWord)))
                                       )
                                 .ToList();
                     });
                    tasks.Add(taskOdrInfList);
                }
                else if (type == 1)
                {
                    taskOdrInfList = Task.Run(() =>
                    {
                        return _tenantProvider6.CreateNewNoTrackingDataContext().OdrInfs
                    .Where(o => o.HpId == hpId &&
                                o.PtId == ptId &&
                                (
                                    (type == 0 && o.OdrKouiKbn != 10 && (odrKouiKbns == null || !odrKouiKbns.Any() || odrKouiKbns.Contains(o.OdrKouiKbn))) || type == 1
                                ) &&
                                raiinNoList.Contains(o.RaiinNo) &&
                                (
                                    o.IsDeleted == DeleteTypes.None ||
                                    isDeleted == 1 ||
                                    (o.IsDeleted != DeleteTypes.Confirm && isDeleted == 2)
                                )
                          //   (string.IsNullOrEmpty(keyWord) || (o.RpName != null && o.RpName.Contains(keyWord)))
                          )
                    .ToList();
                    });
                    tasks.Add(taskOdrInfList);
                }
                else
                {
                    allOdrInfList = new();
                }

            }

            var taskOdrInfDetailList = Task.Run(() =>
            {
                return _tenantProvider7.CreateNewNoTrackingDataContext().OdrInfDetails
                .Where(o =>
                    o.HpId == hpId &&
                    o.PtId == ptId &&
                    raiinNoList.Contains(o.RaiinNo) 
                    //&&(string.IsNullOrEmpty(keyWord) || (o.ItemName != null && o.ItemName.Contains(keyWord))
                )
                .ToList();
            });
            tasks.Add(taskOdrInfDetailList);
            Task.WaitAll(tasks.ToArray());
            allOdrInfList = taskOdrInfList?.Result ?? new();
            var allOdrDetailInfList = taskOdrInfDetailList.Result;
            if (!allOdrInfList.Any())
            {
                return new Dictionary<long, List<OrdInfModel>>();
            }

            if (!allOdrDetailInfList.Any())
            {
                return new Dictionary<long, List<OrdInfModel>>();
            }
            tasks.Clear();

            // Filter by keyword with OR condition from 2 tables OdrInfs and OdrInfDetails
            // var filteredOdrInfListByKeyword = NoTrackingDataContext.OdrInfs
            //     .Where(o => string.IsNullOrEmpty(keyWord) || (o.RpName != null && o.RpName.Contains(keyWord)))
            //     .Select(o => o.RaiinNo)
            //     .Distinct()
            //     .ToList();

            // var filteredOdrDetailListByKeyword = NoTrackingDataContext.OdrInfDetails
            //     .Where(o => string.IsNullOrEmpty(keyWord) || (o.ItemName != null && o.ItemName.Contains(keyWord)))
            //     .Select(o => o.RaiinNo)
            //     .Distinct()
            //     .ToList();

            // var combinedFilteredRaiinNos = filteredOdrInfListByKeyword.Union(filteredOdrDetailListByKeyword).Distinct().ToList();

            // allOdrInfList = allOdrInfList
            //     .Where(o => combinedFilteredRaiinNos.Contains(o.RaiinNo))
            //     .ToList();

            // allOdrDetailInfList = allOdrDetailInfList
            //     .Where(o => combinedFilteredRaiinNos.Contains(o.RaiinNo))
            //     .ToList();

            // if (!allOdrInfList.Any())
            // {
            //     return new Dictionary<long, List<OrdInfModel>>();
            // }

            int minSinDate = allOdrDetailInfList.Min(o => o.SinDate);
            int maxSinDate = allOdrDetailInfList.Max(o => o.SinDate);
            //Read config
            var itemCds = allOdrDetailInfList.Select(od => od.ItemCd).Distinct().ToList();
            var ipnCds = allOdrDetailInfList.Select(od => od.IpnCd).Distinct().ToList();
            var taskTenMst = Task.Run(() =>
            {
                return _tenantProvider5.CreateNewNoTrackingDataContext().TenMsts.Where(t => t.HpId == hpId &&
                        ((t.StartDate <= minSinDate && t.EndDate >= minSinDate) || (t.StartDate >= minSinDate && t.EndDate <= maxSinDate) || (t.StartDate <= maxSinDate && t.EndDate >= maxSinDate))
                        && itemCds.Contains(t.ItemCd)).ToList();
            });
            tasks.Add(taskTenMst);
            var taskKensaMsts = Task.Run(() =>
            {
                return _tenantProvider6.CreateNewNoTrackingDataContext().KensaMsts.Where(t => t.HpId == hpId).ToList();
            });
            tasks.Add(taskKensaMsts);
            var taskIpnNameMsts = Task.Run(() =>
            {
                return _tenantProvider7.CreateNewNoTrackingDataContext().IpnNameMsts.Where(ipn => ipnCds.Contains(ipn.IpnNameCd) && ipn.StartDate <= minSinDate && ipn.EndDate >= maxSinDate).ToList();
            });
            tasks.Add(taskIpnNameMsts);
            Task.WaitAll(tasks.ToArray());
            var tenMsts = taskTenMst.Result;
            var kensaMsts = taskKensaMsts.Result;
            var ipnNameMsts = taskIpnNameMsts.Result;
            var checkKensaIrai = _tenantProvider5.CreateNewNoTrackingDataContext().SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2019 && p.GrpEdaNo == 0);
            var kensaIrai = checkKensaIrai?.Val ?? 0;
            var checkKensaIraiCondition = _tenantProvider5.CreateNewNoTrackingDataContext().SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2019 && p.GrpEdaNo == 1);
            var kensaIraiCondition = checkKensaIraiCondition?.Val ?? 0;

            var ipnMinYakkaMstQuery = _tenantProvider5.CreateNewNoTrackingDataContext().IpnMinYakkaMsts.Where(u => ipnCds.Contains(u.IpnNameCd) && u.IsDeleted == DeleteTypes.None && (u.StartDate <= maxSinDate || u.EndDate >= maxSinDate)).ToList();

            var ipnKasanExcludeQuery = _tenantProvider5.CreateNewNoTrackingDataContext().ipnKasanExcludes.Where(u => ipnCds.Contains(u.IpnNameCd) && u.StartDate <= maxSinDate || u.EndDate >= maxSinDate).ToList();

            var ipnKasanExcludeItemQuery = _tenantProvider5.CreateNewNoTrackingDataContext().ipnKasanExcludeItems.Where(u => itemCds.Contains(u.ItemCd) &&  u.StartDate <= maxSinDate || u.EndDate >= maxSinDate).ToList();

            var kensaCenterMsts = _tenantProvider5.CreateNewNoTrackingDataContext().CommonKensaCenterMst.GroupBy(i => i.CenterCd).Select(g => g.First()).ToList();

            ConcurrentDictionary<long, List<OrdInfModel>> result = new ConcurrentDictionary<long, List<OrdInfModel>>();
            Parallel.ForEach(raiinNoList, raiinNo =>
            {
                List<OdrInf> odrInfList = allOdrInfList.Where(o => o.RaiinNo == raiinNo).ToList();
                if (!odrInfList.Any())
                {
                    return;
                }
                ConcurrentBag<OrdInfModel> odrInfModelList = new ConcurrentBag<OrdInfModel>();
                Parallel.ForEach(odrInfList, odrInf =>
                {
                    string createName = _userInfoService.GetNameById(odrInf.CreateId);
                    string updateName = _userInfoService.GetNameById(odrInf.UpdateId);

                    List<OdrInfDetail> odrDetailInfList = allOdrDetailInfList.Where(o => o.RaiinNo == raiinNo && o.RpNo == odrInf.RpNo && o.RpEdaNo == odrInf.RpEdaNo).ToList();
                    if (type == 1)
                    {
                        foreach (var order in odrDetailInfList)
                        {
                            var tenMst = tenMsts.FirstOrDefault(t => t.ItemCd == order.ItemCd);
                            if (tenMst != null && tenMst.IsNodspKarte != 0)
                            {
                                odrDetailInfList.Remove(order);
                            }
                        }
                        if (odrInf.OdrKouiKbn == 10)
                        {
                            odrDetailInfList = odrDetailInfList.Where(detail => detail.ItemCd != ItemCdConst.JikanKihon).ToList();
                        }
                    }

                    OrdInfModel ordInfModel = Order.CreateBy(odrInf, odrDetailInfList, tenMsts, kensaMsts, ipnNameMsts, createName, updateName, odrInf.OdrKouiKbn, (int)kensaIrai, (int)kensaIraiCondition,
                        ipnMinYakkaMstQuery, ipnKasanExcludeQuery, ipnKasanExcludeItemQuery, kensaCenterMsts);
                    odrInfModelList.Add(ordInfModel);
                });

                result.TryAdd(raiinNo, odrInfModelList.ToList());
            });

            return result.ToDictionary(pair => pair.Key, pair => pair.Value);
        }

        private int CountKeyWordMatchedInKarteInfs(string keyWord, int hpId, long ptId, List<long> raiinNoList)
        {
            using (var new_tenantDataContext = _tenantProvider.CreateNewTrackingDataContext())
            {
                using (var connection = new_tenantDataContext.Database.GetDbConnection())
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        string sql = $@"
                            SELECT SUM((SELECT COUNT(*) FROM regexp_matches(text, @keyWord, 'gi')))
                            FROM karte_inf
                            WHERE 
                                hp_id = @hpId
                                AND pt_id = @ptId
                                AND (raiin_no = ANY(@raiinNoList))
                                AND karte_kbn = 1
                                AND text ~* @keyWord
                        ";

                        command.CommandText = sql;
                        command.Parameters.Add(new NpgsqlParameter("@keyWord", keyWord));
                        command.Parameters.Add(new NpgsqlParameter("@hpId", hpId));
                        command.Parameters.Add(new NpgsqlParameter("@ptId", ptId));
                        command.Parameters.Add(new NpgsqlParameter("@raiinNoList", NpgsqlTypes.NpgsqlDbType.Array | NpgsqlTypes.NpgsqlDbType.Bigint)
                        {
                            Value = raiinNoList.ToArray()
                        });

                        var result = command.ExecuteScalar();
                        int count = result != DBNull.Value ? Convert.ToInt32(result) : 0;

                        return count;
                    }
                }
            }
        }

        private int CountKeyWordMatchedInOdrInfs(string keyWord, int hpId, long ptId, int isDeleted, int type, List<long> raiinNoList, List<int>? odrKouiKbns = null)
        {
            using (var new_tenantDataContext = _tenantProvider.CreateNewTrackingDataContext())
            {
                using (var connection = new_tenantDataContext.Database.GetDbConnection())
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        string sql = $@"
                            SELECT SUM((SELECT COUNT(*) FROM regexp_matches(rp_name, @keyWord, 'gi')))
                            FROM odr_inf
                            WHERE 
                                hp_id = @hpId
                                AND pt_id = @ptId
                                AND (
                                    (@type = 0 AND odr_koui_kbn != 10 AND (
                                        @odrKouiKbns IS NULL OR 
                                        array_length(@odrKouiKbns, 1) IS NULL OR 
                                        odr_koui_kbn = ANY(@odrKouiKbns::integer[])
                                    )) 
                                    OR 
                                    (@type = 1 AND odr_koui_kbn = 10)
                                )
                                AND (raiin_no = ANY(@raiinNoList))
                                AND (
                                    is_deleted = 0 OR
                                    @isDeleted = 1 OR
                                    (is_deleted != @DeleteConfirm AND @isDeleted = 2)
                                )
                                AND rp_name ~* @keyWord
                        ";

                        command.CommandText = sql;
                        command.Parameters.Add(new NpgsqlParameter("@keyWord", keyWord));
                        command.Parameters.Add(new NpgsqlParameter("@hpId", hpId));
                        command.Parameters.Add(new NpgsqlParameter("@ptId", ptId));
                        command.Parameters.Add(new NpgsqlParameter("@type", type));
                        command.Parameters.Add(new NpgsqlParameter("@isDeleted", isDeleted));
                        command.Parameters.Add(new NpgsqlParameter("@DeleteConfirm", DeleteTypes.Confirm));
                        command.Parameters.Add(new NpgsqlParameter("@raiinNoList", NpgsqlTypes.NpgsqlDbType.Array | NpgsqlTypes.NpgsqlDbType.Bigint)
                        {
                            Value = raiinNoList.ToArray()
                        });
                        command.Parameters.Add(new NpgsqlParameter("@odrKouiKbns", NpgsqlTypes.NpgsqlDbType.Array | NpgsqlTypes.NpgsqlDbType.Integer)
                        {
                            Value = (odrKouiKbns != null && odrKouiKbns.Any()) ? odrKouiKbns.ToArray() : Array.Empty<int>()
                        });

                        var result = command.ExecuteScalar();
                        int count = result != DBNull.Value ? Convert.ToInt32(result) : 0;

                        return count;
                    }
                }
            }
        }

        private int CountKeyWordMatchedInOdrInfDetails(string keyWord, int hpId, long ptId, int isDeleted, List<long> raiinNoList)
        {
            using (var new_tenantDataContext = _tenantProvider.CreateNewTrackingDataContext())
            {
                using (var connection = new_tenantDataContext.Database.GetDbConnection())
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        string sql = $@"
                            SELECT SUM(
                                (SELECT COUNT(*) FROM regexp_matches(od_detail.item_name, @keyWord, 'gi'))
                                + (SELECT COUNT(*) FROM regexp_matches(od_detail.cmt_opt, @keyWord, 'gi'))
                            ) 
                            FROM odr_inf_detail od_detail
                            JOIN odr_inf odr ON 
                                od_detail.hp_id = odr.hp_id AND
                                od_detail.raiin_no = odr.raiin_no AND
                                od_detail.rp_no = odr.rp_no AND
                                od_detail.rp_eda_no = odr.rp_eda_no AND
                                od_detail.edition = odr.edition
                            WHERE 
                                od_detail.hp_id = @hpId
                                AND od_detail.pt_id = @ptId
                                AND (od_detail.raiin_no = ANY(@raiinNoList))
                                AND (
                                    odr.is_deleted = 0 OR
                                    @isDeleted = 1 OR
                                    (odr.is_deleted != @DeleteConfirm AND @isDeleted = 2)
                                )
                                AND (
                                    od_detail.item_name ~* @keyWord
                                    OR od_detail.cmt_opt ~* @keyWord
                                );
                        ";

                        command.CommandText = sql;
                        command.Parameters.Add(new NpgsqlParameter("@keyWord", keyWord));
                        command.Parameters.Add(new NpgsqlParameter("@hpId", hpId));
                        command.Parameters.Add(new NpgsqlParameter("@ptId", ptId));
                        command.Parameters.Add(new NpgsqlParameter("@raiinNoList", NpgsqlTypes.NpgsqlDbType.Array | NpgsqlTypes.NpgsqlDbType.Bigint)
                        {
                            Value = raiinNoList.ToArray()
                        });
                        command.Parameters.Add(new NpgsqlParameter("@isDeleted", isDeleted));
                        command.Parameters.Add(new NpgsqlParameter("@DeleteConfirm", DeleteTypes.Confirm));

                        var result = command.ExecuteScalar();
                        int count = result != DBNull.Value ? Convert.ToInt32(result) : 0;

                        return count;
                    }
                }
            }
        }

        private int CountKeyWordMatchedInFillingInf(string keyWord, int hpId, long ptId, List<long> raiinNoList)
        {
            using (var new_tenantDataContext = _tenantProvider.CreateNewTrackingDataContext())
            {
                using (var connection = new_tenantDataContext.Database.GetDbConnection())
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        string sql = $@"
                            SELECT SUM((SELECT COUNT(*) FROM regexp_matches(dsp_file_name, @keyWord, 'gi')))
                            FROM filing_inf
                            WHERE 
                                hp_id = @hpId
                                AND pt_id = @ptId
                                AND (raiin_no = ANY(@raiinNoList))
                                AND dsp_file_name ~* @keyWord
                        ";

                        command.CommandText = sql;
                        command.Parameters.Add(new NpgsqlParameter("@keyWord", keyWord));
                        command.Parameters.Add(new NpgsqlParameter("@hpId", hpId));
                        command.Parameters.Add(new NpgsqlParameter("@ptId", ptId));
                        command.Parameters.Add(new NpgsqlParameter("@raiinNoList", NpgsqlTypes.NpgsqlDbType.Array | NpgsqlTypes.NpgsqlDbType.Bigint)
                        {
                            Value = raiinNoList.ToArray()
                        });

                        var result = command.ExecuteScalar();
                        int count = result != DBNull.Value ? Convert.ToInt32(result) : 0;

                        return count;
                    }
                }
            }
        }


        private Dictionary<long, List<DelOrdInfModel>> GetDelOrderInfList(int hpId, long ptId, List<long> raiinNoList, int type, [Optional] List<DelOdrInf>? memoryOdrInfs)
        {
            List<DelOdrInf> allOdrInfList;
            if (memoryOdrInfs != null)
            {
                allOdrInfList = memoryOdrInfs;
            }
            else
            {
                allOdrInfList = NoTrackingDataContext.DelOdrInfs
                    .Where(o => o.HpId == hpId &&
                                o.PtId == ptId &&
                                (type == 1 || o.OdrKouiKbn != 10) &&
                                raiinNoList.Contains(o.RaiinNo)
                          // (string.IsNullOrEmpty(keyWord) || (o.RpName != null && o.RpName.Contains(keyWord)))
                          )
                    .ToList();
            }


            if (!allOdrInfList.Any())
            {
                return new Dictionary<long, List<DelOrdInfModel>>();
            }

            List<long> raiinNoListByOrder = allOdrInfList.Select(o => o.RaiinNo).Distinct().ToList();
            List<long> rpNoListByOrder = allOdrInfList.Select(o => o.RpNo).Distinct().ToList();
            List<long> rpEdaNoListByOrder = allOdrInfList.Select(o => o.RpEdaNo).Distinct().ToList();

            List<DelOdrInfDetail> allOdrDetailInfList = NoTrackingDataContext.DelOdrInfDetails
                .Where(o =>
                    o.HpId == hpId &&
                    o.PtId == ptId &&
                    raiinNoListByOrder.Contains(o.RaiinNo) &&
                    rpNoListByOrder.Contains(o.RpNo) &&
                    rpEdaNoListByOrder.Contains(o.RpEdaNo)
                )
                .ToList();

            if (!allOdrDetailInfList.Any())
            {
                return new Dictionary<long, List<DelOrdInfModel>>();
            }

            int minSinDate = allOdrDetailInfList.Min(o => o.SinDate);
            int maxSinDate = allOdrDetailInfList.Max(o => o.SinDate);
            //Read config
            var itemCds = allOdrDetailInfList.Select(od => od.ItemCd).Distinct().ToList();
            var ipnCds = allOdrDetailInfList.Select(od => od.IpnCd).Distinct().ToList();
            var tenMsts = NoTrackingDataContext.TenMsts.Where(t => t.HpId == hpId &&
            ((t.StartDate <= minSinDate && t.EndDate >= minSinDate) || (t.StartDate >= minSinDate && t.EndDate <= maxSinDate) || (t.StartDate <= maxSinDate && t.EndDate >= maxSinDate))
            && itemCds.Contains(t.ItemCd)).ToList();
            var kensaMsts = NoTrackingDataContext.KensaMsts.Where(t => t.HpId == hpId).ToList();
            var ipnNameMsts = NoTrackingDataContext.IpnNameMsts.Where(ipn => ipnCds.Contains(ipn.IpnNameCd) && ipn.StartDate <= minSinDate && ipn.EndDate >= maxSinDate).ToList();
            var checkKensaIrai = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2019 && p.GrpEdaNo == 0);
            var kensaIrai = checkKensaIrai?.Val ?? 0;
            var checkKensaIraiCondition = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2019 && p.GrpEdaNo == 1);
            var kensaIraiCondition = checkKensaIraiCondition?.Val ?? 0;

            var ipnMinYakkaMstQuery = NoTrackingDataContext.IpnMinYakkaMsts.Where(u => u.IsDeleted == DeleteTypes.None && (u.StartDate <= maxSinDate || u.EndDate >= maxSinDate));

            var ipnKasanExcludeQuery = NoTrackingDataContext.ipnKasanExcludes.Where(u => u.StartDate <= maxSinDate || u.EndDate >= maxSinDate);

            var ipnKasanExcludeItemQuery = NoTrackingDataContext.ipnKasanExcludeItems.Where(u => u.StartDate <= maxSinDate || u.EndDate >= maxSinDate);

            var kensaCenterMsts = NoTrackingDataContext.CommonKensaCenterMst.GroupBy(i => i.CenterCd).Select(g => g.First()).ToList();

            Dictionary<long, List<DelOrdInfModel>> result = new Dictionary<long, List<DelOrdInfModel>>();
            foreach (long raiinNo in raiinNoList)
            {
                List<DelOdrInf> odrInfList = allOdrInfList.Where(o => o.RaiinNo == raiinNo).ToList();
                List<DelOrdInfModel> odrInfModelList = new List<DelOrdInfModel>();
                foreach (DelOdrInf odrInf in odrInfList)
                {
                    string createName = _userInfoService.GetNameById(odrInf.CreateId);
                    string updateName = _userInfoService.GetNameById(odrInf.UpdateId);

                    List<DelOdrInfDetail> odrDetailInfList = allOdrDetailInfList.Where(o => o.RaiinNo == raiinNo && o.RpNo == odrInf.RpNo && o.RpEdaNo == odrInf.RpEdaNo && o.Edition == odrInf.Edition).ToList();
                    if (type == 1)
                    {
                        foreach (var order in odrDetailInfList)
                        {
                            var tenMst = tenMsts.FirstOrDefault(t => t.ItemCd == order.ItemCd);
                            if (tenMst != null && tenMst.IsNodspKarte != 0)
                            {
                                odrDetailInfList.Remove(order);
                            }
                        }
                        if (odrInf.OdrKouiKbn == 10)
                        {
                            odrDetailInfList = odrDetailInfList.Where(detail => detail.ItemCd != ItemCdConst.JikanKihon).ToList();
                        }
                    }

                    DelOrdInfModel ordInfModel = Order.CreateBy(odrInf, odrDetailInfList, tenMsts, kensaMsts, ipnNameMsts, createName, updateName, odrInf.OdrKouiKbn, (int)kensaIrai, (int)kensaIraiCondition,
                        ipnMinYakkaMstQuery, ipnKasanExcludeQuery, ipnKasanExcludeItemQuery, kensaCenterMsts);
                    odrInfModelList.Add(ordInfModel);
                }

                result.Add(raiinNo, odrInfModelList);
            }

            return result;
        }

        private (List<DelOdrInfDetail> details, List<DelOdrInf> odrInfs) GetDelOrderInfJikanAndShinList(int hpId, long ptId, List<long> raiinNoList)
        {
            List<DelOdrInfDetail> details = new List<DelOdrInfDetail>();
            List<DelOdrInf> allOdrInfList;
            allOdrInfList = NoTrackingDataContext.DelOdrInfs
                .Where(o => o.HpId == hpId &&
                            o.PtId == ptId &&
                            (o.OdrKouiKbn == 10) &&
                            raiinNoList.Contains(o.RaiinNo)
                      )
                .ToList();


            if (!allOdrInfList.Any())
            {
                return new();
            }

            List<long> raiinNoListByOrder = allOdrInfList.Select(o => o.RaiinNo).Distinct().ToList();
            List<long> rpNoListByOrder = allOdrInfList.Select(o => o.RpNo).Distinct().ToList();
            List<long> rpEdaNoListByOrder = allOdrInfList.Select(o => o.RpEdaNo).Distinct().ToList();

            details = NoTrackingDataContext.DelOdrInfDetails
                .Where(o =>
                    o.HpId == hpId &&
                    o.PtId == ptId &&
                    raiinNoListByOrder.Contains(o.RaiinNo) &&
                    rpNoListByOrder.Contains(o.RpNo) &&
                    rpEdaNoListByOrder.Contains(o.RpEdaNo)
                )
                .ToList();

            return (details, allOdrInfList);
        }
        private List<int> GetHokenPidListByCondition(int hpId, long ptId, int isDeleted, KarteFilterMstModel karteFilter)
        {
            bool isAllHoken = karteFilter.IsAllHoken;
            bool isHoken = karteFilter.IsHoken;
            bool isJihi = karteFilter.IsJihi;
            bool isRosai = karteFilter.IsRosai;
            bool isJibai = karteFilter.IsJibai;

            return NoTrackingDataContext.PtHokenPatterns
                .Where(p => p.HpId == hpId &&
                            p.PtId == ptId &&
                            (p.IsDeleted == 0 || isDeleted > 0) &&
                            (
                               isAllHoken ||
                               isHoken && (p.HokenKbn == 1 || p.HokenKbn == 2) ||
                               isJihi && p.HokenKbn == 0 ||
                               isRosai && (p.HokenKbn == 11 || p.HokenKbn == 12 || p.HokenKbn == 13) ||
                               isJibai && p.HokenKbn == 14
                            ))
                .Select(p => p.HokenPid)
                .ToList();
        }

        public void ReleaseResource()
        {
            DisposeDataContext();
            _insuranceRepository.ReleaseResource();
            _raiinListTagRepository.ReleaseResource();
            _karteInfRepository.ReleaseResource();
            _kaService.DisposeSource();
            _userInfoService.DisposeSource();
            _tenantProvider1.DisposeDataContext();
            _tenantProvider2.DisposeDataContext();
            _tenantProvider3.DisposeDataContext();
            _tenantProvider4.DisposeDataContext();
            _tenantProvider5.DisposeDataContext();
            _tenantProvider6.DisposeDataContext();
            _tenantProvider7.DisposeDataContext();
        }

        private List<ApproveInfModel> GetApproveInf(int hpId, long ptId, bool isDeleted, List<long> raiinNos)
        {
            var result = _tenantProvider4.CreateNewNoTrackingDataContext().ApprovalInfs.Where(a => a.HpId == hpId && a.PtId == ptId && (isDeleted || a.IsDeleted == 0) && raiinNos.Contains(a.RaiinNo)).OrderByDescending(item => item.UpdateDate).ToList();
            var userIds = result.Select(r => r.UpdateId).Distinct().ToList();
            var userMsts = _tenantProvider4.CreateNewNoTrackingDataContext().UserMsts.Where(u => u.HpId == hpId && userIds.Contains(u.UserId)).ToList();
            return result.AsEnumerable().Select(
                    r => new ApproveInfModel(
                            r.Id,
                            r.HpId,
                            r.PtId,
                            r.SinDate,
                            r.RaiinNo,
                            r.SeqNo,
                            r.IsDeleted,
                            GetDisplayApproveInf(r.UpdateId, r.UpdateDate, userMsts)
                        )
                ).ToList();
        }

        private static string GetDisplayApproveInf(int updateId, DateTime? updateDate, List<UserMst> userMsts)
        {
            string result = string.Empty;
            string info = string.Empty;

            string docName = userMsts.FirstOrDefault(u => u.UserId == updateId)?.Sname ?? string.Empty;
            if (!string.IsNullOrEmpty(docName))
            {
                info += docName;
            }

            string approvalDateTime = string.Empty;
            if (updateDate != null && updateDate.Value != DateTime.MinValue)
            {
                approvalDateTime = " " + updateDate.Value.ToString("yyyy/MM/dd HH:mm");
            }

            info += approvalDateTime;

            if (!string.IsNullOrEmpty(info))
            {
                result += "（承認: " + info + "）";
            }

            return result;
        }

        #endregion
    }
}
