﻿using Domain.Models.Family;
using Entity.Tenant;
using Helper.Common;
using Helper.Constant;
using Helper.Constants;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Infrastructure.Repositories;

public class FamilyRepository : RepositoryBase, IFamilyRepository
{
    public FamilyRepository(ITenantProvider tenantProvider) : base(tenantProvider) { }

    public List<PtFamilyRekiModel> GetFamilyList(int hpId, long ptId, int sinDate)
    {
        var ptFamilyRekis = NoTrackingDataContext.PtFamilyRekis.Where(u => u.HpId == hpId 
                                                                            && u.PtId == ptId
                                                                            && !string.IsNullOrEmpty(u.Byomei)
                                                                            && u.IsDeleted == DeleteTypes.None)
                                                               .ToList();

        return ConvertToPtFamilyRekiModel(ptFamilyRekis)
                        .OrderBy(item => item.SortNo)
                        .ToList();
    }

    public List<PtFamilyRekiModel> GetFamilyListByPtId(int hpId, long ptId, int sinDate)
    {
        var ptFamilyRekis = NoTrackingDataContext.PtFamilyRekis
            .Where(u => u.HpId == hpId && u.PtId == ptId && !string.IsNullOrEmpty(u.Byomei) && u.IsDeleted == DeleteTypes.None)
            .OrderBy(u => u.SortNo)
            .ToList();
        return ConvertToPtFamilyRekiModel(ptFamilyRekis)
                        .OrderBy(item => item.SortNo)
                        .ToList();
    }

    //public List<PtFamilyRekiModel> GetFamilyReverserList(int hpId, long familyPtId, List<long> ptIdInputList)
    //{
    //    var ptIdExistList = NoTrackingDataContext.PtFamilys.Where(item => item.HpId == hpId
    //                                                                && ptIdInputList.Contains(item.PtId)
    //                                                                && item.FamilyPtId == familyPtId
    //                                                                && item.IsDeleted != 1)
    //                                                    .Select(item => item.PtId)
    //                                                    .ToList();


    //    return ptInfList.Select(item => new PtFamilyRekiModel(
    //                                                        item.PtId,
    //                                                        item.PtNum,
    //                                                        item.Name ?? string.Empty,
    //                                                        item.KanaName ?? string.Empty,
    //                                                        item.Sex,
    //                                                        item.Birthday,
    //                                                        item.IsDead,
    //                                                        int.Parse(CIUtil.GetJapanDateTimeNow().ToString("yyyyMMdd"))
    //                                                    )).ToList();
    //}

    //public bool SaveFamilyList(int hpId, int userId, List<PtFamilyRekiModel> familyList)
    //{
    //    var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
    //    return executionStrategy.Execute(
    //        () =>
    //        {
    //            using var transaction = TrackingDataContext.Database.BeginTransaction();
    //            try
    //            {
    //                if (SaveFamilyListAction(hpId, userId, familyList))
    //                {
    //                    transaction.Commit();
    //                    return true;
    //                }
    //                transaction.Rollback();
    //                return false;
    //            }
    //            catch (Exception)
    //            {
    //                transaction.Rollback();
    //                throw;
    //            }
    //        });
    //}

    public List<PtFamilyRekiModel> GetListByPtId(int hpId, long ptId)
    {
        var ptFamilyRekis = NoTrackingDataContext.PtFamilyRekis
            .Where(u => u.HpId == hpId && u.PtId == ptId && u.IsDeleted == DeleteTypes.None)
            .OrderBy(u => u.SortNo)
            .ToList();
        return ConvertToPtFamilyRekiModel(ptFamilyRekis)
                        .OrderBy(item => item.SortNo)
                        .ToList();
    }

    //public bool CheckExistFamilyRekiList(int hpId, List<long> familyRekiIdList)
    //{
    //    familyRekiIdList = familyRekiIdList.Distinct().ToList();
    //    var countFromDB = NoTrackingDataContext.PtFamilyRekis.Count(x => x.HpId == hpId && familyRekiIdList.Contains(x.Id) && x.IsDeleted == DeleteTypes.None);
    //    return familyRekiIdList.Count == countFromDB;
    //}

    public List<RaiinInfModel> GetRaiinInfListByPtId(int hpId, long ptId)
    {
        var listRaiinInf = NoTrackingDataContext.RaiinInfs.Where(item => item.HpId == hpId &&
                                                                         item.PtId == ptId &&
                                                                         item.IsDeleted == DeleteTypes.None);

        var listKarteEdition = NoTrackingDataContext.KarteEditions.Where(item => item.HpId == hpId &&
                                                                                 item.IsDeleted == DeleteTypes.None &&
                                                                                 item.KarteStatus == KarteStatusConst.Official);

        var raiinInfList = (from raiinInf in listRaiinInf
                            join karteEdition in listKarteEdition on
                                raiinInf.RaiinNo equals karteEdition.RaiinNo
                            select raiinInf)
                           .ToList();

        var tantoIdList = raiinInfList.Select(item => item.TantoId).ToList();
        var kaIdList = raiinInfList.Select(item => item.KaId).ToList();
        var hokenPIdList = raiinInfList.Select(item => item.HokenPid).ToList();

        var doctorList = NoTrackingDataContext.UserMsts.Where(item => item.IsDeleted == DeleteTypes.None
                                                                    && item.HpId == hpId
                                                                    && item.JobCd == JobCdConstant.Doctor
                                                                    && tantoIdList.Contains(item.UserId))
                                                      .ToList();

        var kaMstList = NoTrackingDataContext.KaMsts.Where(item => item.IsDeleted == DeleteTypes.None
                                                                    && item.HpId == hpId
                                                                    && kaIdList.Contains(item.KaId))
                                                    .ToList();

        var hokenPatternList = NoTrackingDataContext.PtHokenPatterns.Where(item => item.IsDeleted == DeleteTypes.None
                                                                                    && item.HpId == hpId
                                                                                    && hokenPIdList.Contains(item.HokenPid))
                                                                    .ToList();

        var hokenIdList = hokenPatternList.Select(item => item.HokenId).Distinct().ToList();
        var hokenInfList = NoTrackingDataContext.PtHokenInfs.Where(item => item.IsDeleted == DeleteTypes.None
                                                                            && item.HpId == hpId
                                                                            && hokenIdList.Contains(item.HokenId))
                                                            .ToList();

        return raiinInfList.Select(item => ConvertToRaiinInfModel(item, doctorList, kaMstList, hokenPatternList, hokenInfList))
                           .OrderByDescending(item => item.SinDate)
                           .ToList();
    }

    //public List<PtFamilyRekiModel> GetMaybeFamilyList(int hpId, long ptId, int sinDate)
    //{
    //    var mainPtInf = NoTrackingDataContext.PtInfs.FirstOrDefault(item => item.HpId == hpId && item.PtId == ptId);
    //    if (mainPtInf == null)
    //    {
    //        return new();
    //    }

    //    return ptInfList.Select(item => new PtFamilyRekiModel(
    //                                        item.PtId,
    //                                        item.PtNum,
    //                                        item.Name ?? string.Empty,
    //                                        item.KanaName ?? string.Empty,
    //                                        item.Sex,
    //                                        item.Birthday,
    //                                        item.IsDead,
    //                                        sinDate
    //                     )).ToList();
    //}

    #region private function
    private List<PtFamilyRekiModel> ConvertToPtFamilyRekiModel(List<PtFamilyReki> ptFamilyRekis)
    {
        var ptFamilyRekiFilter = ptFamilyRekis.Select(item => ConvertToPtFamilyRekiModel(item))
                                              .ToList();

        return ptFamilyRekiFilter;
    }

    private PtFamilyRekiModel ConvertToPtFamilyRekiModel(PtFamilyReki ptFamilyReki)
    {
        return new PtFamilyRekiModel(
                                        ptFamilyReki.Id,
                                        ptFamilyReki.ByomeiCd ?? string.Empty,
                                        ptFamilyReki.Byomei ?? string.Empty,
                                        ptFamilyReki.Cmt ?? string.Empty,
                                        ptFamilyReki.SortNo
                                    );
    }

    private RaiinInfModel ConvertToRaiinInfModel(RaiinInf raiinInf, List<UserMst> doctorList, List<KaMst> kaMstList, List<PtHokenPattern> hokenPatternList, List<PtHokenInf> hokenInfList)
    {
        var doctor = doctorList.FirstOrDefault(item => item.UserId == raiinInf.TantoId);
        var kaMst = kaMstList.FirstOrDefault(item => item.KaId == raiinInf.KaId);

        string hokenPatternName = string.Empty;
        var hokenPattern = hokenPatternList.FirstOrDefault(item => item.HokenPid == raiinInf.HokenPid);
        if (hokenPattern != null)
        {
            var hokenInf = hokenInfList.FirstOrDefault(item => item.HokenId == hokenPattern.HokenId);
            hokenPatternName = GetHokenName(hokenPattern.HokenKbn, hokenPattern.HokenSbtCd, hokenInf?.Houbetu ?? string.Empty);
        }

        return new RaiinInfModel(
                raiinInf.PtId,
                raiinInf.SinDate,
                raiinInf.RaiinNo,
                raiinInf.KaId,
                kaMst?.KaName ?? string.Empty,
                raiinInf.TantoId,
                doctor?.Sname ?? string.Empty,
                hokenPattern?.HokenPid ?? CommonConstants.InvalidId,
                hokenPatternName
            );
    }

    private string GetHokenName(int hokenKbn, int hokenSbtCd, string houbetu)
    {
        string result = string.Empty;
        switch (hokenKbn)
        {
            case 0:
                switch (houbetu)
                {
                    case "108":
                        result = "自費";
                        break;
                    case "109":
                        result = "自レ";
                        break;
                }
                break;
            case 11:
            case 12:
            case 13:
                result = "労災";
                break;
            case 14:
                result = "自賠";
                break;
            default:
                if (hokenSbtCd >= 100 && hokenSbtCd <= 199)
                {
                    result = "社保";
                }
                else if (hokenSbtCd >= 200 && hokenSbtCd <= 299)
                {
                    result = "国保";
                }
                else if (hokenSbtCd >= 300 && hokenSbtCd <= 399)
                {
                    result = "後期";
                }
                else if (hokenSbtCd >= 400 && hokenSbtCd <= 499)
                {
                    result = "退職";
                }
                else if (hokenSbtCd >= 500 && hokenSbtCd <= 599)
                {
                    result = "公費";
                }
                break;
        }
        return result;
    }

    //private bool SaveFamilyListAction(int hpId, int userId, List<PtFamilyRekiModel> listFamily)
    //{
    //    var listFamilyPtId = listFamily.Where(item => item.FamilyPtId > 0 && !item.IsDeleted).Select(item => item.FamilyPtId).ToList();
    //    var listFamilyId = listFamily.Select(item => item.FamilyId).ToList();
    //    var listFamilyDB = TrackingDataContext.PtFamilys.Where(item => item.HpId == hpId && listFamilyId.Contains(item.FamilyId) && item.IsDeleted != 1);
    //    var listFamilyRekiDB = TrackingDataContext.PtFamilyRekis.Where(item => item.HpId == hpId && listFamilyId.Contains(item.FamilyId) && item.IsDeleted != 1).ToList();
    //    var listPtInf = TrackingDataContext.PtInfs.Where(item => item.HpId == hpId && item.IsDelete != 1 && listFamilyPtId.Contains(item.PtId)).ToList();

    //    foreach (var PtFamilyRekiModel in listFamily)
    //    {
    //        if (PtFamilyRekiModel.FamilyId <= 0 && !PtFamilyRekiModel.IsDeleted)
    //        {
    //            var ptFamilyEntity = ConvertToNewPtFamily(hpId, userId, PtFamilyRekiModel);
    //            TrackingDataContext.PtFamilys.Add(ptFamilyEntity);
    //            TrackingDataContext.SaveChanges();
    //            UpdatePtInf(listPtInf, PtFamilyRekiModel.FamilyPtId, PtFamilyRekiModel.IsDead);
    //            SaveFamilyRekiList(hpId, userId, ptFamilyEntity.FamilyPtId, ptFamilyEntity.FamilyId, listFamilyRekiDB, PtFamilyRekiModel.ListPtFamilyRekis);
    //        }
    //        else
    //        {
    //            var ptFamilyEntity = listFamilyDB.FirstOrDefault(item => item.FamilyId == PtFamilyRekiModel.FamilyId);
    //            if (ptFamilyEntity == null)
    //            {
    //                continue;
    //            }
    //            ptFamilyEntity.UpdateDate = CIUtil.GetJapanDateTimeNow();
    //            ptFamilyEntity.UpdateId = userId;
    //            if (PtFamilyRekiModel.IsDeleted)
    //            {
    //                ptFamilyEntity.IsDeleted = 1;
    //                continue;
    //            }
    //            ptFamilyEntity.ZokugaraCd = PtFamilyRekiModel.ZokugaraCd;
    //            ptFamilyEntity.SortNo = PtFamilyRekiModel.SortNo;
    //            ptFamilyEntity.FamilyPtId = PtFamilyRekiModel.FamilyPtId;
    //            ptFamilyEntity.KanaName = PtFamilyRekiModel.KanaName;
    //            ptFamilyEntity.Name = PtFamilyRekiModel.Name;
    //            ptFamilyEntity.Sex = PtFamilyRekiModel.Sex;
    //            ptFamilyEntity.Birthday = PtFamilyRekiModel.Birthday;
    //            ptFamilyEntity.IsDead = PtFamilyRekiModel.IsDead;
    //            ptFamilyEntity.IsSeparated = PtFamilyRekiModel.IsSeparated;
    //            ptFamilyEntity.Biko = PtFamilyRekiModel.Biko;
    //            UpdatePtInf(listPtInf, PtFamilyRekiModel.FamilyPtId, PtFamilyRekiModel.IsDead);
    //            SaveFamilyRekiList(hpId, userId, ptFamilyEntity.FamilyPtId, ptFamilyEntity.FamilyId, listFamilyRekiDB, PtFamilyRekiModel.ListPtFamilyRekis);
    //        }
    //    }
    //    TrackingDataContext.SaveChanges();
    //    return true;
    //}

    //private PtFamily ConvertToNewPtFamily(int hpId, int userId, PtFamilyRekiModel model)
    //{
    //    PtFamily ptFamily = new();
    //    ptFamily.FamilyId = 0;
    //    ptFamily.HpId = hpId;
    //    ptFamily.PtId = model.PtId;
    //    ptFamily.ZokugaraCd = model.ZokugaraCd;
    //    ptFamily.SortNo = model.SortNo;
    //    ptFamily.FamilyPtId = model.FamilyPtId;
    //    ptFamily.KanaName = model.KanaName;
    //    ptFamily.Name = model.Name;
    //    ptFamily.Sex = model.Sex;
    //    ptFamily.Birthday = model.Birthday;
    //    ptFamily.IsDead = model.IsDead;
    //    ptFamily.IsSeparated = model.IsSeparated;
    //    ptFamily.Biko = model.Biko;
    //    ptFamily.CreateDate = CIUtil.GetJapanDateTimeNow();
    //    ptFamily.CreateId = userId;
    //    ptFamily.UpdateDate = CIUtil.GetJapanDateTimeNow();
    //    ptFamily.UpdateId = userId;
    //    ptFamily.IsDeleted = 0;
    //    return ptFamily;
    //}

    //private void SaveFamilyRekiList(int hpId, int userId, long familyPtId, long familyId, List<PtFamilyReki> listPtFamilyRekiEntity, List<PtFamilyRekiModel> listPtFamilyRekiModel)
    //{
    //    foreach (var familyRekiModel in listPtFamilyRekiModel)
    //    {
    //        if (familyRekiModel.Id <= 0 && !familyRekiModel.IsDeleted)
    //        {
    //            var familyReki = ConvertToNewPtFamilyReki(hpId, userId, familyPtId, familyId, familyRekiModel);
    //            TrackingDataContext.PtFamilyRekis.Add(familyReki);
    //        }
    //        else
    //        {
    //            var ptFamilyRekiEntity = listPtFamilyRekiEntity.FirstOrDefault(item => item.Id == familyRekiModel.Id);
    //            if (ptFamilyRekiEntity == null)
    //            {
    //                continue;
    //            }
    //            ptFamilyRekiEntity.UpdateDate = CIUtil.GetJapanDateTimeNow();
    //            ptFamilyRekiEntity.UpdateId = userId;
    //            if (familyRekiModel.IsDeleted)
    //            {
    //                ptFamilyRekiEntity.IsDeleted = 1;
    //                continue;
    //            }
    //            ptFamilyRekiEntity.SortNo = familyRekiModel.SortNo;
    //            ptFamilyRekiEntity.ByomeiCd = familyRekiModel.ByomeiCd;
    //            ptFamilyRekiEntity.Byomei = familyRekiModel.Byomei;
    //            ptFamilyRekiEntity.Cmt = familyRekiModel.Cmt;
    //        }
    //    }
    //}

    //private PtFamilyReki ConvertToNewPtFamilyReki(int hpId, int userId, long familyPtId, long familyId, PtFamilyRekiModel model)
    //{
    //    PtFamilyReki ptFamilyRekiEntity = new();
    //    ptFamilyRekiEntity.Id = 0;
    //    ptFamilyRekiEntity.HpId = hpId;
    //    ptFamilyRekiEntity.PtId = familyPtId;
    //    ptFamilyRekiEntity.FamilyId = familyId;
    //    ptFamilyRekiEntity.SortNo = model.SortNo;
    //    ptFamilyRekiEntity.ByomeiCd = model.ByomeiCd;
    //    ptFamilyRekiEntity.Byomei = model.Byomei;
    //    ptFamilyRekiEntity.Cmt = model.Cmt;
    //    ptFamilyRekiEntity.IsDeleted = 0;
    //    ptFamilyRekiEntity.CreateDate = CIUtil.GetJapanDateTimeNow();
    //    ptFamilyRekiEntity.CreateId = userId;
    //    ptFamilyRekiEntity.UpdateDate = CIUtil.GetJapanDateTimeNow();
    //    ptFamilyRekiEntity.UpdateId = userId;
    //    return ptFamilyRekiEntity;
    //}

    //private void UpdatePtInf(List<PtInf> ptInfs, long ptId, int isDead)
    //{
    //    var ptInf = ptInfs.FirstOrDefault(item => item.PtId == ptId);
    //    if (ptInf != null)
    //    {
    //        ptInf.IsDead = isDead;
    //    }
    //}

    //private List<long> GetMaybeFamilyListByAddressOrPhone(int hpId, PtInf mainPtInf)
    //{
    //    string tel1 = string.Empty;
    //    string tel2 = string.Empty;
    //    string homeAddress = string.Empty;
    //    string fullSizeHomeAddress = string.Empty;
    //    string halfSizeHomeAddress = string.Empty;

    //    var systemConfig = NoTrackingDataContext.SystemConfs.FirstOrDefault(item => item.HpId == hpId && item.GrpCd == 1002 && item.GrpEdaNo == 0)?.Val ?? 0;
    //    if (systemConfig == 0)
    //    {
    //        return new();
    //    }
    //    else if (systemConfig == 1)
    //    {
    //        tel1 = mainPtInf.Tel1?.Replace("ー", string.Empty)
    //                                .Replace("ｰ", string.Empty)
    //                                .Replace("-", string.Empty)
    //                                .Replace("　", string.Empty)
    //                                .Replace(" ", string.Empty) ?? string.Empty;

    //        tel2 = mainPtInf.Tel2?.Replace("ー", string.Empty)
    //                                    .Replace("ｰ", string.Empty)
    //                                    .Replace("-", string.Empty)
    //                                    .Replace("　", string.Empty)
    //                                    .Replace(" ", string.Empty) ?? string.Empty;

    //        homeAddress = mainPtInf.HomeAddress1 + mainPtInf.HomeAddress2;
    //        homeAddress = homeAddress?.Replace("　", string.Empty)
    //                                  .Replace(" ", string.Empty) ?? string.Empty;

    //        fullSizeHomeAddress = HenkanJ.ToFullsize(homeAddress);
    //        halfSizeHomeAddress = HenkanJ.ToHalfsize(homeAddress);
    //    }
    //    else if (systemConfig == 2)
    //    {
    //        tel1 = mainPtInf.Tel1?.Replace("ー", string.Empty)
    //                                .Replace("ｰ", string.Empty)
    //                                .Replace("-", string.Empty)
    //                                .Replace("　", string.Empty)
    //                                .Replace(" ", string.Empty) ?? string.Empty;

    //        tel2 = mainPtInf.Tel2?.Replace("ー", string.Empty)
    //                                    .Replace("ｰ", string.Empty)
    //                                    .Replace("-", string.Empty)
    //                                    .Replace("　", string.Empty)
    //                                    .Replace(" ", string.Empty) ?? string.Empty;
    //    }

    //    var ptInfRepos = NoTrackingDataContext.PtInfs.Where(item => item.HpId == hpId &&
    //                                                                    item.IsDelete == 0);

    //    var query = from ptInf in ptInfRepos
    //                select new
    //                {
    //                    PtInf = ptInf,
    //                    ptInf.Tel1,
    //                    ptInf.Tel2,
    //                    HomeAddress = ptInf.HomeAddress1 + ptInf.HomeAddress2
    //                };

    //    query = query.Where(item =>
    //        (!string.IsNullOrEmpty(item.Tel1) &&
    //         tel1 != string.Empty &&
    //         item.Tel1.Replace("ー", string.Empty)
    //                  .Replace("ｰ", string.Empty)
    //                  .Replace("-", string.Empty)
    //                  .Replace("　", string.Empty)
    //                  .Replace(" ", string.Empty) == tel1) ||
    //        (!string.IsNullOrEmpty(item.Tel2) &&
    //         tel1 != string.Empty &&
    //         item.Tel2.Replace("ー", string.Empty)
    //                  .Replace("ｰ", string.Empty)
    //                  .Replace("-", string.Empty)
    //                  .Replace("　", string.Empty)
    //                  .Replace(" ", string.Empty) == tel1) ||
    //        (!string.IsNullOrEmpty(item.Tel1) &&
    //         tel2 != string.Empty &&
    //         item.Tel1.Replace("ー", string.Empty)
    //                  .Replace("ｰ", string.Empty)
    //                  .Replace("-", string.Empty)
    //                  .Replace("　", string.Empty)
    //                  .Replace(" ", string.Empty) == tel2) ||
    //        (!string.IsNullOrEmpty(item.Tel2) &&
    //         tel2 != string.Empty &&
    //         item.Tel2.Replace("ー", string.Empty)
    //                  .Replace("ｰ", string.Empty)
    //                  .Replace("-", string.Empty)
    //                  .Replace("　", string.Empty)
    //                  .Replace(" ", string.Empty) == tel2) ||
    //        (!string.IsNullOrEmpty(item.HomeAddress) &&
    //         item.HomeAddress.Replace("　", string.Empty)
    //                         .Replace(" ", string.Empty) == homeAddress) ||
    //        (!string.IsNullOrEmpty(item.HomeAddress) &&
    //        item.HomeAddress.Replace("　", string.Empty)
    //                        .Replace(" ", string.Empty) == fullSizeHomeAddress) ||
    //        (!string.IsNullOrEmpty(item.HomeAddress) &&
    //        item.HomeAddress.Replace("　", string.Empty)
    //                        .Replace(" ", string.Empty) == halfSizeHomeAddress));

    //    return query.Select(item => item.PtInf.PtId).ToList();
    //}

    //private List<long> GetPatientInfByInsurance(int hpId, long ptId, int sinDate)
    //{
    //    int endDate = (sinDate / 100) * 100 + 1;

    //    var ptHokenInfCollection = NoTrackingDataContext.PtHokenInfs
    //        .Where(item =>
    //            item.HpId == hpId &&
    //            item.PtId == ptId &&
    //            item.IsDeleted == 0 &&
    //            item.StartDate <= sinDate &&
    //            item.EndDate >= sinDate)
    //        .ToList();

    //    var predicate = CreateSameHokNoExpression(ptHokenInfCollection);
    //    if (predicate == null)
    //    {
    //        return new();
    //    }

    //    var ptHokenInfRepos = NoTrackingDataContext.PtHokenInfs
    //        .Where(item =>
    //            item.HpId == hpId &&
    //            item.IsDeleted == 0 &&
    //            item.EndDate >= endDate &&
    //            (item.HokenKbn == 1 || item.HokenKbn == 2));

    //    var listPtHokenInf = ptHokenInfRepos.Where(predicate)
    //        .Select(item => new { item.PtId, item.HokensyaNo, item.Kigo, item.Bango })
    //        .Distinct()
    //        .ToList();

    //    var ptIdCollection = listPtHokenInf
    //        .Select(item => item.PtId)
    //        .Distinct()
    //        .ToList();

    //    var ptIdExpression = CreatePtIdExpression(ptIdCollection);
    //    if (ptIdExpression == null)
    //        return new();

    //    var ptInfCollection =
    //        NoTrackingDataContext.PtInfs.Where(item =>
    //            item.HpId == hpId && item.PtId != ptId && item.IsDelete == 0);

    //    ptInfCollection = ptInfCollection.Where(ptIdExpression);

    //    return ptInfCollection.Select(item => item.PtId).ToList();
    //}

    //private Expression<Func<PtHokenInf, bool>>? CreateSameHokNoExpression(List<PtHokenInf> ptHokenInfCollection)
    //{
    //    if (ptHokenInfCollection == null || ptHokenInfCollection.Count <= 0) return null;

    //    var param = Expression.Parameter(typeof(PtHokenInf));
    //    Expression? expression = null;

    //    foreach (var ptHokenInf in ptHokenInfCollection)
    //    {
    //        if (ptHokenInf == null || !(ptHokenInf.HokenKbn == 1 || ptHokenInf.HokenKbn == 2))
    //            continue;

    //        if (string.IsNullOrEmpty(ptHokenInf.HokensyaNo) || string.IsNullOrEmpty(ptHokenInf.Bango)) continue;

    //        var valHokensyaNo = Expression.Constant(ptHokenInf.HokensyaNo);
    //        var memberHokensyaNo = Expression.Property(param, nameof(PtHokenInf.HokensyaNo));
    //        Expression expressionHokensyaNo = Expression.Equal(valHokensyaNo, memberHokensyaNo);

    //        var valKigo = Expression.Constant(ptHokenInf.Kigo);
    //        var memberKigo = Expression.Property(param, nameof(PtHokenInf.Kigo));
    //        Expression expressionKigo = Expression.Equal(valKigo, memberKigo);

    //        var valBango = Expression.Constant(ptHokenInf.Bango);
    //        var memberBango = Expression.Property(param, nameof(PtHokenInf.Bango));
    //        Expression expressionBango = Expression.Equal(valBango, memberBango);

    //        var expressionAnd = Expression.And(expressionHokensyaNo, expressionKigo);

    //        expressionAnd = Expression.And(expressionAnd, expressionBango);

    //        expression = expression == null ? expressionAnd : Expression.Or(expression, expressionAnd);
    //    }

    //    return expression != null
    //        ? Expression.Lambda<Func<PtHokenInf, bool>>(body: expression, parameters: param)
    //        : null;
    //}

    //private Expression<Func<PtInf, bool>>? CreatePtIdExpression(List<long> ptIdCollection)
    //{
    //    if (ptIdCollection == null || ptIdCollection.Count <= 0) return null;

    //    var param = Expression.Parameter(typeof(PtInf));
    //    Expression? expression = null;

    //    foreach (var ptId in ptIdCollection)
    //    {
    //        var valPtId = Expression.Constant(ptId);
    //        var memberPtId = Expression.Property(param, nameof(PtInf.PtId));
    //        Expression expressionPtId = Expression.Equal(valPtId, memberPtId);

    //        expression = expression == null ? expressionPtId : Expression.Or(expression, expressionPtId);
    //    }

    //    return expression != null
    //        ? Expression.Lambda<Func<PtInf, bool>>(body: expression, parameters: param)
    //        : null;
    //}
    #endregion

    public void ReleaseResource()
    {
        DisposeDataContext();
    }
}
