﻿using Domain.Enum;
using Domain.Models.KarteVSPHYS;
using Domain.Models.SpecialNote.PatientInfo;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Redis;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using System.Globalization;

namespace Infrastructure.Repositories
{
    public class KarteVSPHYSRepository : RepositoryBase, IKarteVSPHYSRepository
    {
        private readonly string key;
        private readonly IDatabase _cache;
        private readonly IConfiguration _configuration;

        public KarteVSPHYSRepository(ITenantProvider tenantProvider, IConfiguration configuration) : base(tenantProvider)
        {
            key = GetDomainKey();
            _configuration = configuration;
            GetRedis();
            _cache = RedisConnectorHelper.Connection.GetDatabase();
        }

        public void GetRedis()
        {
            string connection = string.Concat(_configuration["Redis:RedisHost"], ":", _configuration["Redis:RedisPort"]);
            if (RedisConnectorHelper.RedisHost != connection)
            {
                RedisConnectorHelper.RedisHost = connection;
            }
        }


        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        private KensaInf UpdateKensaInf(int userId, int hpId, long ptId, KensaInfModel kensaInfModel, KensaInf existedKensaInf)
        {
            existedKensaInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
            existedKensaInf.UpdateId = userId;
            existedKensaInf.CreateDate = existedKensaInf.CreateDate.ToUniversalTime();
            if (kensaInfModel.IsDeleted != DeleteTypes.None)
            {
                existedKensaInf.IsDeleted = kensaInfModel.IsDeleted;
            }
            else
            {
                existedKensaInf.IraiDate = kensaInfModel.IraiDate;
                existedKensaInf.RaiinNo = kensaInfModel.RaiinNo;
                existedKensaInf.InoutKbn = kensaInfModel.InoutKbn;
                existedKensaInf.Status = kensaInfModel.Status;
                existedKensaInf.TosekiKbn = kensaInfModel.TosekiKbn;
                existedKensaInf.SikyuKbn = kensaInfModel.SikyuKbn;
                existedKensaInf.ResultCheck = kensaInfModel.ResultCheck;
                existedKensaInf.CenterCd = kensaInfModel.CenterCd;
                existedKensaInf.Nyubi = kensaInfModel.Nyubi;
                existedKensaInf.Yoketu = kensaInfModel.Yoketu;
                existedKensaInf.Bilirubin = kensaInfModel.Bilirubin;
                existedKensaInf.KensaTime = kensaInfModel.KensaTime;
            }
            TrackingDataContext.KensaInfs.Update(existedKensaInf);
            TrackingDataContext.SaveChanges();
            return existedKensaInf;
        }

        private KensaInf UpdateKensaInfConsultationResult(int userId, KensaInf existedKensaInf)
        {
            existedKensaInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
            existedKensaInf.UpdateId = userId;
            existedKensaInf.CreateDate = existedKensaInf.CreateDate.ToUniversalTime();
            TrackingDataContext.KensaInfs.Update(existedKensaInf);
            TrackingDataContext.SaveChanges();
            return existedKensaInf;
        }

        private KensaInf AddKensaInf(int userId, int hpId, long ptId, KensaInfModel kensaInfModel)
        {
            var newKensaInf = new KensaInf()
            {
                HpId = hpId,
                PtId = ptId,
                IraiCd = 0,
                IraiDate = kensaInfModel.IraiDate,
                RaiinNo = kensaInfModel.RaiinNo,
                InoutKbn = kensaInfModel.InoutKbn,
                Status = kensaInfModel.Status,
                TosekiKbn = kensaInfModel.TosekiKbn,
                SikyuKbn = kensaInfModel.SikyuKbn,
                ResultCheck = kensaInfModel.ResultCheck,
                CenterCd = kensaInfModel.CenterCd,
                Nyubi = kensaInfModel.Nyubi,
                Yoketu = kensaInfModel.Yoketu,
                Bilirubin = kensaInfModel.Bilirubin,
                KensaTime = kensaInfModel.KensaTime,
                IsDeleted = DeleteTypes.None,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId,
                CreateMachine = CIUtil.GetComputerName(),
                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateId = userId,
                UpdateMachine = CIUtil.GetComputerName(),
            };

            TrackingDataContext.KensaInfs.Add(newKensaInf);
            TrackingDataContext.SaveChanges();

            kensaInfModel.IraiCd = newKensaInf.IraiCd;
            return newKensaInf;
        }

        private bool SaveKartePhysicalsAction(int userId, int hpId, long ptId, KensaInfModel kensaInfModel, bool saveIfEmpty = true)
        {
            var existedKensaInf = TrackingDataContext.KensaInfs.FirstOrDefault(x => x.HpId == hpId && x.IraiCd == kensaInfModel.IraiCd && x.PtId == ptId && x.IsDeleted == DeleteTypes.None);
            if (existedKensaInf != null)
            {
                if (!saveIfEmpty && kensaInfModel.KensaInfDetailModelList.Count == 0) kensaInfModel.IsDeleted = DeleteTypes.Deleted;
                UpdateKensaInf(userId, hpId, ptId, kensaInfModel, existedKensaInf);
                SaveKensaInfDetails(userId, kensaInfModel);
            }
            else
            {
                if (!saveIfEmpty && kensaInfModel.KensaInfDetailModelList.Count == 0) return true;
                AddKensaInf(userId, hpId, ptId, kensaInfModel);
                SaveKensaInfDetails(userId, kensaInfModel);
            }

            return true;
        }

        public bool SaveKartePhysicals(int userId, int hpId, long ptId, KensaInfModel kensaInfModel, bool saveIfEmpty = true)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(
                () =>
                {
                    using var transaction = TrackingDataContext.Database.BeginTransaction();
                    try
                    {
                        if (SaveKartePhysicalsAction(userId, hpId, ptId, kensaInfModel, saveIfEmpty))
                        {
                            transaction.Commit();
                            return true;
                        }
                        transaction.Rollback();
                        return false;
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                });
        }

        public bool SaveKartePhysicalsConsulationResult(int userId, int hpId, long ptId, KensaInfModel? kensaInfModel)
        {
            if (kensaInfModel == null)
            {
                return false;
            }
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(
                () =>
                {
                    using var transaction = TrackingDataContext.Database.BeginTransaction();
                    try
                    {
                        if (SaveKartePhysicalsActionConsulationResult(userId, hpId, ptId, kensaInfModel))
                        {
                            transaction.Commit();
                            return true;
                        }
                        transaction.Rollback();
                        return false;
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                });
        }

        public bool CheckIsExistedKensaTime(int hpId, int iraiDate, string kensaTime, long raiinNo, long ptId, int inouKbn)
        {
            return NoTrackingDataContext.KensaInfs.Any(x => x.HpId == hpId && x.IraiDate == iraiDate && x.KensaTime == kensaTime && x.PtId == ptId && x.InoutKbn == inouKbn && x.IsDeleted == DeleteTypes.None);
        }

        public bool CheckIsExistedKensaTimeWithIraiCd(int hpId, int iraiDate, string kensaTime, long raiinNo, long ptId, long iraiCd, int inouKbn)
        {
            return NoTrackingDataContext.KensaInfs.Any(x => x.HpId == hpId  && x.IraiDate == iraiDate && x.KensaTime == kensaTime && x.PtId == ptId && x.IraiCd != iraiCd && x.InoutKbn == inouKbn && x.IsDeleted == DeleteTypes.None);
        }

        private void SaveKensaInfDetails(int userId, KensaInfModel kensaInfModel)
        {
            var existingDbRecords = TrackingDataContext.KensaInfDetails
                .Where(k => k.HpId == kensaInfModel.HpId &&
                            k.PtId == kensaInfModel.PtId &&
                            k.IraiCd == kensaInfModel.IraiCd)
                .ToArray();

            var currentTime = CIUtil.GetJapanDateTimeNow();
            var itemsToAdd = new List<KensaInfDetail>();
            var itemsToRemove = new List<KensaInfDetail>();
            var itemsToUpdate = new List<KensaInfDetail>();

            foreach (var model in kensaInfModel.KensaInfDetailModelList)
            {
                var existingRecord = existingDbRecords
                    .FirstOrDefault(k => k.KensaItemCd == model.KensaItemCd);

                if (existingRecord == null)
                {
                    if (model.IsDeleted == DeleteTypes.None)
                    {
                        itemsToAdd.Add(new KensaInfDetail
                        {
                            AbnormalKbn = model.AbnormalKbn,
                            CmtCd1 = model.CmtCd1,
                            CmtCd2 = model.CmtCd2,
                            CreateDate = currentTime,
                            CreateId = userId,
                            HpId = kensaInfModel.HpId,
                            IraiCd = kensaInfModel.IraiCd,
                            IraiDate = model.IraiDate,
                            KensaItemCd = model.KensaItemCd,
                            PtId = kensaInfModel.PtId,
                            RaiinNo = model.RaiinNo,
                            ResultType = model.ResultType,
                            ResultVal = model.ResultVal,
                            UpdateDate = CIUtil.GetJapanDateTimeNow(),
                            UpdateId = userId,
                            UpdateMachine = CIUtil.GetComputerName(),
                        });
                    }
                }
                else
                {
                    if (string.IsNullOrEmpty(model.ResultVal) || model.IsDeleted != DeleteTypes.None)
                    {
                        itemsToRemove.Add(existingRecord);
                    }
                    else
                    {
                        existingRecord.CreateDate = existingRecord.CreateDate.ToUniversalTime();
                        existingRecord.UpdateDate = currentTime;
                        existingRecord.UpdateId = userId;
                        existingRecord.IraiDate = model.IraiDate;
                        existingRecord.ResultVal = model.ResultVal;
                        existingRecord.ResultType = model.ResultType;
                        existingRecord.AbnormalKbn = model.AbnormalKbn;
                        existingRecord.CmtCd1 = model.CmtCd1;
                        existingRecord.CmtCd2 = model.CmtCd2;

                        itemsToUpdate.Add(existingRecord);
                    }
                }
            }

            if (itemsToAdd.Any())
                TrackingDataContext.KensaInfDetails.AddRange(itemsToAdd);

            if (itemsToUpdate.Any())
                TrackingDataContext.KensaInfDetails.UpdateRange(itemsToUpdate);

            if (TrackingDataContext.ChangeTracker.HasChanges())
                TrackingDataContext.SaveChanges();
        }

        public KarteVSPHYModel GetKarteVSPHYSList(int hpId, long ptId, bool isSearch, long? startDate, long? endDate, int raiinNo)
        {
            List<PhysicalInfoModel> physicals = new();

            if (!isSearch)
            {
                endDate = 99999999;
            }
            else if (startDate == null && endDate != null)
            {
                DateTime date2 = DateTime.ParseExact(endDate.ToString(), "yyyyMMdd", null);
                DateTime date1 = date2.AddYears(-1);
                startDate = int.Parse(date1.ToString("yyyyMMdd"));
            }
            else if (startDate != null && endDate == null)
            {
                DateTime date1 = DateTime.ParseExact(startDate.ToString(), "yyyyMMdd", null);
                DateTime date2 = date1.AddYears(+1);
                endDate = int.Parse(date2.ToString("yyyyMMdd"));
            }

            var lastSinDate = (NoTrackingDataContext.RaiinInfs.FirstOrDefault(x => x.HpId == hpId && x.RaiinNo == raiinNo) ?? new()).SinDate;
            var kensaInfs = NoTrackingDataContext.KensaInfs.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == 0 && x.IraiDate >= startDate && x.IraiDate <= endDate && x.InoutKbn == 2).ToList();
            var allKensaInfDetails = NoTrackingDataContext.KensaInfDetails.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == 0 && (x.KensaItemCd != null && x.KensaItemCd.StartsWith("V"))).ToList();

            var querys = (from kensaInfList in kensaInfs
                          join allKensaInfDetailList in allKensaInfDetails on
                              new { kensaInfList.HpId, kensaInfList.PtId, kensaInfList.IraiCd } equals
                              new { allKensaInfDetailList.HpId, allKensaInfDetailList.PtId, allKensaInfDetailList.IraiCd }
                          select new
                          {
                              KensaInfList = kensaInfList,
                              KensaTime = kensaInfList.KensaTime,
                              IraiDate = kensaInfList.IraiDate,
                              IraiCd = kensaInfList.IraiCd,
                              AllKensaInfDetailList = allKensaInfDetailList,
                          }).GroupBy(x => (x.IraiDate * 10000 + x.KensaTime)).ToList();

            var kensaMsts = NoTrackingDataContext.KensaMsts.Where(x => x.HpId == hpId && x.IsDelete == 0 && x.KensaItemCd.StartsWith("V")).OrderBy(mst => mst.SortNo);

            foreach (var query in querys)
            {
                List<KensaInfDetailModel> kensaInfDetailModels = new();
                foreach (var item in query)
                {
                    var formula = kensaMsts.FirstOrDefault(x => x.KensaItemCd == item.AllKensaInfDetailList.KensaItemCd)?.Formula ?? string.Empty;
                    kensaInfDetailModels.Add(new KensaInfDetailModel(item.AllKensaInfDetailList?.HpId ?? 0,
                                                                     item.AllKensaInfDetailList?.PtId ?? 0,
                                                                     item.AllKensaInfDetailList?.IraiCd ?? 0,
                                                                     item.AllKensaInfDetailList?.SeqNo ?? 0,
                                                                     item.AllKensaInfDetailList?.IraiDate ?? 0,
                                                                     item.AllKensaInfDetailList?.RaiinNo ?? 0,
                                                                     item.AllKensaInfDetailList?.KensaItemCd ?? string.Empty,
                                                                     item.AllKensaInfDetailList?.ResultVal ?? string.Empty,
                                                                     item.AllKensaInfDetailList?.ResultType ?? string.Empty,
                                                                     item.AllKensaInfDetailList?.AbnormalKbn ?? string.Empty,
                                                                     item.AllKensaInfDetailList?.IsDeleted ?? 0,
                                                                     item.AllKensaInfDetailList?.CmtCd1 ?? string.Empty,
                                                                     item.AllKensaInfDetailList?.CmtCd2 ?? string.Empty,
                                                                     item.AllKensaInfDetailList?.UpdateDate ?? DateTime.MinValue,
                                                                     string.Empty,
                                                                     string.Empty,
                                                                     0,
                                                                     formula));
                }

                var physical = new PhysicalInfoModel(query.FirstOrDefault()?.KensaTime ?? string.Empty, kensaInfDetailModels, query.FirstOrDefault()?.IraiDate ?? 0, query.FirstOrDefault()?.IraiCd ?? 0);
                physicals.Add(physical);
            }
            var iraiCds = physicals.Select(x => x.IraiCd).ToList();

            foreach (var kensaInf in kensaInfs)
            {
                if (iraiCds.Contains(kensaInf.IraiCd))
                {
                    continue;
                }
                else
                {
                    physicals.Add(new PhysicalInfoModel(kensaInf.KensaTime, new(), kensaInf.IraiDate, kensaInf.IraiCd));
                }
            }

            if (!isSearch)
            {
                var averageDay = GetAverageDay(physicals);
                return new KarteVSPHYModel(physicals.OrderByDescending(x => long.Parse(((long)(x.IraiDate)).ToString() + x.KensaTime)).ToList(), averageDay, new(), lastSinDate);
            }
            else
            {
                var averageDay = GetAverageDay(physicals);
                var averageMonths = GetAverageMonths(physicals, startDate, endDate);

                return new KarteVSPHYModel(physicals.OrderByDescending(x => long.Parse(((long)(x.IraiDate)).ToString() + x.KensaTime)).ToList(), averageDay, averageMonths, lastSinDate);
            }
        }

        private Dictionary<long, Dictionary<string, double>> GetAverageDay(List<PhysicalInfoModel> physicals)
        {
            Dictionary<long, Dictionary<string, double>> result = new();
            List<KensaInfDetailModel> kensaInfDetailModels = new();

            foreach (var physical in physicals)
            {
                kensaInfDetailModels.AddRange(physical.KensaInfDetailModels);
            }

            kensaInfDetailModels = kensaInfDetailModels.OrderBy(x => x.IraiDate).ToList();

            var averageDays = kensaInfDetailModels.Where(k => double.TryParse(k.ResultVal, out _))
                                                  .GroupBy(x => new { x.KensaItemCd, x.IraiDate })
                                                  .Select(g => new
                                                  {
                                                      Day = g.Key.IraiDate,
                                                      KensaItemCd = g.Key.KensaItemCd,
                                                      AverageResultVal = g.Average(k => double.Parse(k.ResultVal))
                                                  })
                                                  .GroupBy(x => x.Day);

            foreach (var averages in averageDays)
            {
                Dictionary<string, double> averageDay = new();
                foreach (var item in averages)
                {
                    if (item.KensaItemCd == "V0007" || item.KensaItemCd == "V0001" || item.KensaItemCd == "V0002")
                    {
                        averageDay.Add(item.KensaItemCd, Math.Round(item.AverageResultVal, 1, MidpointRounding.AwayFromZero));
                    }

                    if (item.KensaItemCd == "V0008" || item.KensaItemCd == "V0009" || item.KensaItemCd == "V0014" || item.KensaItemCd == "V0011" || item.KensaItemCd == "V0012" || item.KensaItemCd == "V0010")
                    {
                        averageDay.Add(item.KensaItemCd, Math.Round(item.AverageResultVal, 0, MidpointRounding.AwayFromZero));
                    }
                }
                result.Add(averages.Key, averageDay);
            }

            return result;
        }

        private Dictionary<string, Dictionary<string, double>> GetAverageMonths(List<PhysicalInfoModel> physicals, long? startDate, long? endDate)
        {
            Dictionary<string, Dictionary<string, double>> result = new();
            List<KensaInfDetailModel> kensaInfDetailModels = new();
            List<PhysicalInfoModel> physicalList = new();

            foreach (var item in physicals)
            {
                if (item.IraiDate >= startDate && item.IraiDate <= endDate)
                {
                    physicalList.Add(item);
                }
            }

            foreach (var item in physicalList)
            {
                kensaInfDetailModels.AddRange(item.KensaInfDetailModels);
            }

            var monthlyAverages = kensaInfDetailModels.Where(k => double.TryParse(k.ResultVal, out _))
                                                       .GroupBy(k =>
                                                       {
                                                           var date = DateTime.ParseExact(k.IraiDate.ToString(), "yyyyMMdd", CultureInfo.InvariantCulture);
                                                           return new { Month = date.ToString("yyyyMM"), k.KensaItemCd };
                                                       })
                                                       .Select(g => new
                                                       {
                                                           Month = g.Key,
                                                           AverageResultVal = g.Average(k => double.Parse(k.ResultVal))
                                                       })
                                                       .GroupBy(x => x.Month.Month)
                                                       .OrderBy(x => x.Key);

            foreach (var monthlyAverage in monthlyAverages)
            {
                Dictionary<string, double> averageMonth = new();
                foreach (var item in monthlyAverage)
                {
                    if (item.Month.KensaItemCd == "V0007" || item.Month.KensaItemCd == "V0001" || item.Month.KensaItemCd == "V0002")
                    {
                        averageMonth.Add(item.Month.KensaItemCd, Math.Round(item.AverageResultVal, 1, MidpointRounding.AwayFromZero));
                    }

                    if (item.Month.KensaItemCd == "V0008" || item.Month.KensaItemCd == "V0009" || item.Month.KensaItemCd == "V0014" || item.Month.KensaItemCd == "V0011" || item.Month.KensaItemCd == "V0012" || item.Month.KensaItemCd == "V0010")
                    {
                        averageMonth.Add(item.Month.KensaItemCd, Math.Round(item.AverageResultVal, 0, MidpointRounding.AwayFromZero));
                    }
                }
                result.Add(monthlyAverage.Key, averageMonth);
            }

            return result;
        }

        public Dictionary<long, long> SaveRequestExams(int userId, int hpId, List<long> orderInfIds, string centerCd)
        {
            Dictionary<long, long> iraiCds = new Dictionary<long, long>();
            var systemTime = CIUtil.GetJapanDateTimeNow();

            var orderInfs = TrackingDataContext.OdrInfs.Where(x => x.HpId == hpId &&
                                                     orderInfIds.Contains(x.Id) &&
                                                     x.IsDeleted == DeleteTypes.None &&
                                                     x.OdrKouiKbn >= ReportOdrKouiKbnConst.KensaMin &&
                                                     x.OdrKouiKbn <= ReportOdrKouiKbnConst.KensaMax)
                                                     .Select(x => new
                                                     {
                                                         x.Id,
                                                         x.PtId,
                                                         x.RaiinNo,
                                                         x.TosekiKbn,
                                                         x.SikyuKbn,
                                                         x.HpId,
                                                         x.RpNo,
                                                         x.RpEdaNo
                                                     })
                                                     .ToList();

            var uniqueOrders = orderInfs.GroupBy(x => new { x.PtId, x.RaiinNo, x.TosekiKbn })
                                       .Select(g => g.Any(x => x.SikyuKbn == (sbyte)SikyuKbnEnums.Urgent)
                                                  ? g.First(x => x.SikyuKbn == (sbyte)SikyuKbnEnums.Urgent)
                                                  : g.First())
                                       .ToList();

            var filteredOdrInfDetails = TrackingDataContext.OdrInfDetails
                .Where(d => d.HpId == hpId &&
                            !string.IsNullOrEmpty(d.ItemCd) &&
                            d.JissiKbn == 0)
                .ToList();

            var filteredTenMsts = TrackingDataContext.TenMsts
                .Where(t => t.HpId == hpId &&
                            t.CenterCd == centerCd &&
                            t.MasterSbt != "C")
                .Select(t => new
                {
                    t.HpId,
                    t.ItemCd,
                    t.KensaItemCd,
                    t.MasterSbt,
                    t.CenterCd
                })
                .ToList();

            var uniqueOrdersDict = uniqueOrders.ToDictionary(
                u => new { u.PtId, u.RaiinNo, u.TosekiKbn },
                u => u
            );

            var joinedData = (from odrInf in orderInfs
                              join detail in filteredOdrInfDetails
                              on new { odrInf.HpId, odrInf.PtId, odrInf.RaiinNo, odrInf.RpNo, odrInf.RpEdaNo }
                              equals new { detail.HpId, detail.PtId, detail.RaiinNo, detail.RpNo, detail.RpEdaNo }
                              join tenMst in filteredTenMsts
                              on new { detail.HpId, detail.ItemCd }
                              equals new { tenMst.HpId, tenMst.ItemCd }
                              select new { odrInf, detail, tenMst })
                             .ToList();

            var result = joinedData
                .GroupBy(x => new
                {
                    x.odrInf.HpId,
                    x.odrInf.PtId,
                    x.odrInf.RaiinNo,
                    x.odrInf.TosekiKbn
                })
                .Select(g => new
                {
                    MainOrder = uniqueOrdersDict[new { g.Key.PtId, g.Key.RaiinNo, g.Key.TosekiKbn }],
                    OrderDetails = g.Select(x => new
                    {
                        SubDetail = x.detail,
                        TenMst = new
                        {
                            x.tenMst.CenterCd,
                            x.tenMst.KensaItemCd,
                            x.tenMst.ItemCd
                        }
                    }).ToList(),
                    GroupIds = g.Select(x => x.odrInf.Id).ToList()
                })
                .ToList();

            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(
               () =>
               {
                   using var transaction = TrackingDataContext.Database.BeginTransaction();
                   try
                   {
                       foreach (var item in result)
                       {
                           var kensaInfDetailModels = item.OrderDetails
                               .DistinctBy(x => new { x.TenMst.KensaItemCd, x.TenMst.ItemCd })
                               .Where(x => !string.IsNullOrEmpty(x.TenMst.KensaItemCd) && x.TenMst.CenterCd == centerCd)
                               .Select((orderInfDetail, index) => new KensaInfDetailModel(
                                   item.MainOrder.HpId,
                                   item.MainOrder.PtId,
                                   0,
                                   index,
                                   CIUtil.DateTimeToInt(systemTime.ToLocalTime()),
                                   item.MainOrder.RaiinNo,
                                   orderInfDetail.TenMst.KensaItemCd,
                                   string.Empty,
                                   string.Empty,
                                   string.Empty,
                                   DeleteTypes.None,
                                   string.Empty,
                                   string.Empty,
                                   DateTime.MinValue,
                                   string.Empty,
                                   string.Empty,
                                   0,
                                   string.Empty
                               ))
                               .ToList();

                           var kensaModel = new KensaInfModel(
                               item.MainOrder.HpId,
                               item.MainOrder.PtId,
                               CIUtil.DateTimeToInt(systemTime.ToLocalTime()),
                               item.MainOrder.RaiinNo,
                               0,
                               (sbyte)InouKbnEnums.OutHospital,
                               (sbyte)KensaInfStatusEnums.Request,
                               item.MainOrder.TosekiKbn,
                               item.MainOrder.SikyuKbn,
                               (sbyte)ResultCheckEnums.Unconfirmed,
                               item.OrderDetails.FirstOrDefault()?.TenMst.CenterCd ?? string.Empty,
                               string.Empty,
                               string.Empty,
                               string.Empty,
                               DeleteTypes.None,
                               kensaInfDetailModels,
                               systemTime.ToLocalTime().ToString("HHmm")
                           );

                           if (!SaveKartePhysicalsAction(userId, hpId, item.MainOrder.PtId, kensaModel) ||
                               !UpdateOrderInfDetail(userId, item.OrderDetails.Select(x => x.SubDetail), kensaModel.IraiCd))
                           {
                               transaction.Rollback();
                               return iraiCds;
                           }
                           foreach (var groupId in item.GroupIds)
                           {
                               if (!iraiCds.ContainsKey(groupId))
                                   iraiCds.Add(groupId, kensaModel.IraiCd);
                           }
                       }
                       transaction.Commit();
                       return iraiCds;
                   }
                   catch (Exception)
                   {
                       transaction.Rollback();
                       throw;
                   }
               });
        }

        public bool DeleteRequestExams(int userId, int hpId, List<long> iraiCds)
        {
            var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();
            return executionStrategy.Execute(
                () =>
                {
                    using var transaction = TrackingDataContext.Database.BeginTransaction();
                    try
                    {
                        if (DeleteKensaInfs(userId, hpId, iraiCds) && UpdateOrderInfDetailByIraiCds(userId, hpId, iraiCds))
                        {
                            transaction.Commit();
                            return true;
                        }
                        transaction.Rollback();
                        return false;
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                });
        }

        private bool UpdateOrderInfDetail(int userId, IEnumerable<OdrInfDetail> enumerable, long iraiCd)
        {
            foreach (var item in enumerable)
            {
                item.JissiKbn = 1;
                item.JissiDate = CIUtil.GetJapanDateTimeNow();
                item.JissiId = userId;
                item.JissiMachine = CIUtil.GetComputerName();
                item.ReqCd = iraiCd.ToString();
            }

            TrackingDataContext.OdrInfDetails.UpdateRange(enumerable);
            return TrackingDataContext.SaveChanges() > 0;
        }

        private bool DeleteKensaInfs(int userId, int hpId, List<long> iraiCds)
        {
            var kensaInfs = TrackingDataContext.KensaInfs.Where(x => x.HpId == hpId && iraiCds.Contains(x.IraiCd) && x.IsDeleted == DeleteTypes.None).AsEnumerable();

            foreach (var item in kensaInfs)
            {
                item.IsDeleted = DeleteTypes.Deleted;
                item.UpdateId = userId;
                item.UpdateDate = CIUtil.GetJapanDateTimeNow();
            }
            TrackingDataContext.SaveChanges();
            return true;
        }

        private bool UpdateOrderInfDetailByIraiCds(int userId, int hpId, List<long> iraiCds)
        {
            foreach (var iraiCd in iraiCds)
            {
                var orderInfDetails = TrackingDataContext.OdrInfDetails.Where(x => x.HpId == hpId && x.ReqCd == iraiCd.ToString()).AsEnumerable();
                foreach (var item in orderInfDetails)
                {
                    item.JissiKbn = 0;
                    item.JissiDate = null;
                    item.JissiId = 0;
                    item.ReqCd = null;
                }
                TrackingDataContext.OdrInfDetails.UpdateRange(orderInfDetails);
            }
            TrackingDataContext.SaveChanges();
            return true;
        }

        private bool SaveKartePhysicalsActionConsulationResult(int userId, int hpId, long ptId, KensaInfModel kensaInfModel)
        {
            var existedKensaInf = TrackingDataContext.KensaInfs.FirstOrDefault(x => x.HpId == hpId && x.IraiCd == kensaInfModel.IraiCd && x.PtId == ptId && x.IsDeleted == DeleteTypes.None);
            if (existedKensaInf != null)
            {
                UpdateKensaInfConsultationResult(userId, existedKensaInf);
                SaveKensaInfDetails(userId, kensaInfModel);
            }
            return true;
        }
    }
}