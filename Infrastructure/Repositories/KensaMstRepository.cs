﻿using System.Linq.Dynamic.Core;
using Domain.Models.KensaMst;
using Entity.Tenant;
using Helper.Common;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;
using Helper.Constants;

namespace Infrastructure.Repositories;

public class KensaMstRepository : RepositoryBase, IKensaMstRepository
{
    public KensaMstRepository(ITenantProvider tenantProvider) : base(tenantProvider)
    {
    }

    public void ReleaseResource()
    {
        DisposeDataContext();
    }

    public List<KensaMstModel> GetKensaMst(int hpId, int isDelete = 0)
    {
        // 院内検索として登録できる項目の一覧を取得する
        // クリニックに登録されている場合はその情報も取得する
        var kensaMstQuery = from kensaMst in TrackingDataContext.KensaMsts
                              where kensaMst.HpId == hpId && kensaMst.IsDelete == isDelete
                              select kensaMst;
        var kensaMstDictionary = kensaMstQuery.ToDictionary(k => k.KensaItemCd);

        var masters = from commonKensaMst in TrackingDataContext.CommonKensaMsts
                      join analyte in TrackingDataContext.CommonJlac10Analyte on commonKensaMst.Analyte equals analyte.Analyte into analyteJoin
                      from analyte in analyteJoin.DefaultIfEmpty()
                      join identification in TrackingDataContext.CommonJlac10Identification on commonKensaMst.Identification equals identification.Identification into identificationJoin
                      from identification in identificationJoin.DefaultIfEmpty()
                      join specimen in TrackingDataContext.CommonJlac10Specimen on commonKensaMst.Specimen equals specimen.Specimen into specimenJoin
                      from specimen in specimenJoin.DefaultIfEmpty()
                      join methodology in TrackingDataContext.CommonJlac10Methodology on commonKensaMst.Methodology equals methodology.Methodology into methodologyJoin
                      from methodology in methodologyJoin.DefaultIfEmpty()
                      join resultIdCommon in TrackingDataContext.CommonJlac10ResultIdCommon on commonKensaMst.ResultIdCommon equals resultIdCommon.ResultIdCommon into resultIdCommonJoin
                      from resultIdCommon in resultIdCommonJoin.DefaultIfEmpty()
                      join resultIdUnique in TrackingDataContext.CommonJlac10ResultIdUnique on commonKensaMst.ResultIdUnique equals resultIdUnique.ResultIdUnique into resultIdUniqueJoin
                      from resultIdUnique in resultIdUniqueJoin.DefaultIfEmpty()
                      select new
                      {
                          commonKensaMst,
                          analyte,
                          identification,
                          specimen,
                          methodology,
                          resultIdCommon,
                          resultIdUnique
                      };

        // 各種マスターデータの結合
        // common_kensa_mst が絶対であり、common_jlac10_* がない場合は空欄とする
        var kensaMstModels = new List<KensaMstModel>();
        foreach (var data in masters)
        {
            var kensaMstModel = new KensaMstModel(
                analyte: data.commonKensaMst.Analyte ?? string.Empty,
                analyteName: data.analyte?.Name ?? string.Empty,
                displayName: data.commonKensaMst.KensaName ?? string.Empty,  // 一旦KensaNameをDisplayNameとして使う
                femaleStd: string.Empty,
                femaleStdLow: string.Empty,
                femaleStdHigh: string.Empty,
                hpId: null,
                identification: data.identification?.Identification ?? string.Empty,
                identificationName: data.identification?.Name ?? string.Empty,
                jlac10: data.commonKensaMst.Jlac10 ?? string.Empty,
                kensaItemCd: data.commonKensaMst.KensaItemCd ?? string.Empty,
                kensaKana: data.commonKensaMst.KensaKana ?? string.Empty,
                kensaName: data.commonKensaMst.KensaName ?? string.Empty,
                maleStd: string.Empty,
                maleStdLow: string.Empty,
                maleStdHigh: string.Empty,
                methodology: data.methodology?.Methodology ?? string.Empty,
                methodologyName: data.methodology?.Name ?? string.Empty,
                resultIdCommon: data.resultIdCommon?.ResultIdCommon ?? string.Empty,
                resultIdCommonName: data.resultIdCommon?.Name ?? string.Empty,
                resultIdUnique: data.resultIdUnique?.ResultIdUnique ?? string.Empty,
                resultIdUniqueName: data.resultIdUnique?.Name ?? string.Empty,
                santeiItemCd: data.commonKensaMst.SanteiItemCd ?? string.Empty,
                specimen: data.specimen?.Specimen ?? string.Empty,
                specimenName: data.specimen?.Name ?? string.Empty,
                unit: string.Empty,
                isDelete: DeleteTypes.None
            );

            // クリニックの登録内容を埋める
            if (kensaMstDictionary.ContainsKey(data.commonKensaMst.KensaItemCd!))
            {
                var clinicRegister = kensaMstDictionary[data.commonKensaMst.KensaItemCd!];
                // DisplayNameが登録されてない場合はKensaNameを使う
                kensaMstModel.DisplayName = clinicRegister.KensaName ?? kensaMstModel.KensaName;
                kensaMstModel.FemaleStdLow = clinicRegister.FemaleStdLow;
                kensaMstModel.FemaleStdHigh = clinicRegister.FemaleStdHigh;
                kensaMstModel.FemaleStd = clinicRegister.FemaleStd;
                kensaMstModel.HpId = clinicRegister.HpId;
                kensaMstModel.MaleStdLow = clinicRegister.MaleStdLow;
                kensaMstModel.MaleStdHigh = clinicRegister.MaleStdHigh;
                kensaMstModel.MaleStd = clinicRegister.MaleStd;
                kensaMstModel.Unit = clinicRegister.Unit;
                kensaMstModel.IsDelete = clinicRegister.IsDelete;
            }

            kensaMstModels.Add(kensaMstModel);
        }

        return kensaMstModels;
    }

    public List<KensaMstModel> GetInHospitalKensaMst(int hpId, int isDelete = 0, bool isExceptVital = false)
    {
        var kensaMstQuery = (from kensaMst in TrackingDataContext.KensaMsts
                            where kensaMst.HpId == hpId && kensaMst.IsDelete == isDelete && (isExceptVital == false || !kensaMst.KensaItemCd.StartsWith("V"))
                            orderby kensaMst.KensaName
                            select new KensaMstModel(kensaMst.KensaName ?? string.Empty, kensaMst.MaleStd, kensaMst.FemaleStd, kensaMst.Unit, kensaMst.KensaItemCd))
                            .ToList();
        return kensaMstQuery;
    }

    public KensaMstModel CreateKensaMst(
        int hpId,
        string kensaItemCd,
        string displayName,
        string maleStdLow,
        string maleStdHigh,
        string maleStd,
        string femaleStdLow,
        string femaleStdHigh,
        string femaleStd,
        string unit
    )
    {
        var recordQuery = from kensaMst in TrackingDataContext.KensaMsts
                            where kensaMst.HpId == hpId && kensaMst.KensaItemCd == kensaItemCd && kensaMst.IsDelete == DeleteTypes.None
                            select kensaMst;
        var recordNum = recordQuery.Count();
        if (recordNum > 0)
        {
            throw new KensaMstSpecialException();
        }

        var allSeqs = TrackingDataContext.Database.SqlQueryRaw<int>(
            "SELECT NEXTVAL('\"kensa_mst_kensa_item_seq_no\"')"
        ).ToList();
        var nextSeq = allSeqs?.FirstOrDefault() ?? 1;
        Console.WriteLine($"nextSeq: {nextSeq}");

        var newKensaMst = new KensaMst
        {
            HpId = hpId,
            KensaItemCd = kensaItemCd,
            KensaItemSeqNo = nextSeq,
            KensaName = displayName,
            MaleStdLow = maleStdLow,
            MaleStdHigh = maleStdHigh,
            MaleStd = maleStd,
            FemaleStdLow = femaleStdLow,
            FemaleStdHigh = femaleStdHigh,
            FemaleStd = femaleStd,
            Unit = unit,
            CreateDate = CIUtil.GetJapanDateTimeNow(),
            IsDelete = DeleteTypes.None
        };

        TrackingDataContext.KensaMsts.Add(newKensaMst);
        TrackingDataContext.SaveChanges();

        var kensaMsts = GetKensaMst(hpId);
        var filteredKensaMst = kensaMsts.FirstOrDefault(km => km.KensaItemCd == kensaItemCd);
        if (filteredKensaMst == null)
        {
            throw new Exception("指定された院内検査は作成されませんでした。");
        }
        return filteredKensaMst;
    }

    public KensaMstModel UpdateKensaMst(
        int hpId,
        string kensaItemCd,
        string displayName,
        string maleStdLow,
        string maleStdHigh,
        string maleStd,
        string femaleStdLow,
        string femaleStdHigh,
        string femaleStd,
        string unit
    )
    {
        var query = from kensaMst in TrackingDataContext.KensaMsts
                    where kensaMst.HpId == hpId && kensaMst.KensaItemCd == kensaItemCd && kensaMst.IsDelete == DeleteTypes.None
                    select kensaMst;
        var record = query.FirstOrDefault();

        if (record == null)
        {
            throw new KensaMstSpecialException();
        }

        record.KensaName = displayName;
        record.MaleStdLow = maleStdLow;
        record.MaleStdHigh = maleStdHigh;
        record.MaleStd = maleStd;
        record.FemaleStdLow = femaleStdLow;
        record.FemaleStdHigh = femaleStdHigh;
        record.FemaleStd = femaleStd;
        record.Unit = unit;

        TrackingDataContext.SaveChanges();

        var kensaMsts = GetKensaMst(hpId);
        var filteredKensaMst = kensaMsts.FirstOrDefault(km => km.KensaItemCd == kensaItemCd);
        if (filteredKensaMst == null)
        {
            throw new Exception("指定されたkensaItemCdのデータが見つかりません。");
        }
        return filteredKensaMst;
    }

    // 論理削除
    public bool DeleteKensaMst(
        int hpId,
        string kensaItemCd
    )
    {
        var records = TrackingDataContext.KensaMsts.Where(
            k => k.HpId == hpId && k.KensaItemCd == kensaItemCd && k.IsDelete == DeleteTypes.None).ToList();
        if (records.Count == 0)
        {
            throw new KensaMstSpecialException();
        }
        if (records.Count > 1)
        {
            // 意図しないデータを消さないようにするため、2つ以上のレコードが見つかった場合は例外を投げる
            throw new Exception("削除対象が複数見つかったため、削除処理を中断しました。");
        }

        var record = records.FirstOrDefault();
        if (record != null)
        {
            record.IsDelete = DeleteTypes.Deleted;
            TrackingDataContext.SaveChanges();
            return true;
        }
        return false;
    }
}

public class KensaMstSpecialException : Exception
{
    public KensaMstSpecialException()
        : base("cannot process KensaMst.") { }
}
