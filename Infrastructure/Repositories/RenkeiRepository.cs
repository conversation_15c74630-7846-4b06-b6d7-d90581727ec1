﻿using Domain.Constant;
using Domain.Models.EventProcessor;
using Domain.Models.Renkei;
using Domain.Models.SystemConf;
using Entity.Tenant;
using Helper.Constants;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using System.Linq.Dynamic.Core;

namespace Infrastructure.Repositories
{
    public class RenkeiRepository : RepositoryBase, IRenkeiRepository
    {
        private readonly ISystemConfRepository _systemConfRepository;

        public RenkeiRepository(ITenantProvider tenantProvider, ISystemConfRepository systemConfRepository) : base(tenantProvider)
        {
            _systemConfRepository = systemConfRepository;
        }

        public List<Renkei130DataModel> GetRenkei130Data(int hpId, ArgumentModel arg)
        {
            PtInfModel ptInf = null;
            List<PtGrpInfModel> ptGrpInfs = new List<PtGrpInfModel>();
            PtMemoModel ptMemo = null;
            RaiinInfModel raiinInf = null;

            List<RaiinKbnInfModel> raiinKbnInfs = new List<RaiinKbnInfModel>();
            List<KaikeiInfModel> kaikeiInfs = new List<KaikeiInfModel>();
            UserMstModel userMst = GetUserMst(hpId, arg.UserId);

            if (arg.PtId > 0)
            {
                // PT_INF取得
                ptInf = GetPtInf(arg.PtId, hpId);
                // PT_GRP_INF取得
                ptGrpInfs = GetPtGrpInf(arg.PtId, hpId);
                // PT_MEMO取得
                ptMemo = GetPtMemo(arg.PtId, hpId);
            }

            // KAIKEI_INF取得
            if (arg.Misyu != null && arg.Nyukin != null)
            {
                // 診療日に計算中のデータがないかチェック 最大15秒待機
                bool isCalculate = true;
                int count = 0;
                while (isCalculate == true && count < 150)
                {
                    isCalculate = ExistCalculateRequest(hpId, arg.PtId, arg.SinDate, arg.SinDate);
                    Thread.Sleep(100);
                    count++;
                }

                if (arg.EventCd == EventCode.Madoguchi_Seisan)
                {
                    // 窓口精算の場合、親来院番号で取得
                    kaikeiInfs = GetKaikeiInf(hpId, arg.PtId, arg.SinDate, 0, arg.RaiinNo);
                }
                else
                {
                    // 収納一覧からの場合、来院番号で取得
                    kaikeiInfs = GetKaikeiInf(hpId, arg.PtId, arg.SinDate, arg.RaiinNo, 0);
                }
            }
            //List<KaikeiInfModel> kaikeiInfs = GetKaikeiInf(ptId, sinDate, raiinNo, oyaRaiinNo);

            List<Renkei130DataModel> results = new List<Renkei130DataModel>();

            int totalNyukin = arg.Nyukin ?? 0;
            int misyu = arg.Misyu ?? 0;

            int seikyuRenkeiMadoguchi = (int)_systemConfRepository.GetSettingValue(100031, 0, hpId);
            int seikyuRenkeiSyuno = (int)_systemConfRepository.GetSettingValue(100031, 1, hpId);
            foreach (KaikeiInfModel kaikeiInf in kaikeiInfs)
            {
                raiinInf = GetRaiinInf(hpId, kaikeiInf.PtId, kaikeiInf.SinDate, kaikeiInf.RaiinNo);
                raiinKbnInfs = GetRaiinKbnInf(hpId, kaikeiInf.PtId, kaikeiInf.SinDate, kaikeiInf.RaiinNo);

                CommonDataModel common =
                    new CommonDataModel(
                        kaikeiInf.PtId, kaikeiInf.SinDate, kaikeiInf.RaiinNo,
                        arg.Misyu, arg.NyukinDate, arg.Nyukin,
                        ptInf, ptGrpInfs, ptMemo, raiinInf, raiinKbnInfs, new List<KaikeiInfModel> { kaikeiInf }, userMst);

                common.SeikyuGokei = kaikeiInf.PtFutan + kaikeiInf.AdjustRound + misyu;
                //common.SeikyuDate = kaikeiInf.SinDate;
                common.Nyukin = 0;
                common.NyukinDate = 0;
                common.Seikyu = 0;
                common.SeikyuJihi = 0;
                common.NyukinSortNo = arg.NyukinSortNo;

                if (arg.EventCd == EventCode.Madoguchi_Seisan)
                {
                    // 窓口精算
                    common.NyukinDate = kaikeiInf.SinDate;
                    common.Seikyu = common.SumPtFutan;
                    common.SeikyuJihi = common.SumJihiFutanTotal;
                    common.Misyu = misyu;
                    common.SeikyuGokei = common.SumPtFutan + common.SumJihiFutanTotal + common.Misyu;

                    common.Nyukin = totalNyukin;
                    if ((common.SeikyuGokei > 0 && common.Nyukin > (common.SeikyuGokei)) ||
                        (common.SeikyuGokei < 0 && common.Nyukin < (common.SeikyuGokei)))
                    {
                        // 請求合計が正のとき、入金額 > 請求合計の場合、入金額を請求合計に入れ替える
                        // 請求合計が負のとき、入金額 < 請求合計の場合、入金額を請求合計に入れ替える
                        common.Nyukin = common.SeikyuGokei;
                    }

                    totalNyukin -= common.Nyukin ?? 0;
                }
                else
                {
                    // 収納一覧
                    common.Nyukin = totalNyukin;
                    common.NyukinDate = arg.NyukinDate;
                    common.Misyu = totalNyukin + misyu;
                    common.SeikyuGokei = totalNyukin + misyu;

                    //if(totalNyukin + misyu == common.SumPtFutan + common.SumJihiFutanTotal)
                    if (GetNyukinKbn(hpId, kaikeiInf.PtId, kaikeiInf.SinDate, kaikeiInf.RaiinNo) == 0)
                    {
                        common.Seikyu = common.SumPtFutan;
                        common.SeikyuJihi = common.SumJihiFutanTotal;
                        common.Misyu = 0;
                    }
                }

                if (common.Nyukin != 0 ||
                    (arg.EventCd == EventCode.Madoguchi_Seisan && seikyuRenkeiMadoguchi == 1) ||
                    (arg.EventCd == EventCode.Syuno_Seisan && seikyuRenkeiSyuno == 1))
                {
                    // 入金額 > 0
                    // または、窓口精算で、入金0円連携する設定の場合
                    // または、収納一覧精算で、入金0円連携する設定の場合
                    results.Add(new Renkei130DataModel(common));
                }

                misyu = 0;
            }
            return results;
        }

        public List<RenkeiModel> GetRenkeiModels(int hpId, string machineName, string eventCd)
        {
            var result = new List<RenkeiModel>();
            var renkeiMsts = NoTrackingDataContext.RenkeiMsts
                .Where(p => p.HpId == hpId
                && p.RenkeiSbt == 0
                && p.IsInvalid == 0)
                .ToList();

            var renkeiConfs = NoTrackingDataContext.RenkeiConfs
                .Where(p => p.HpId == hpId && p.IsInvalid == 0)
                .ToList();

            machineName = machineName.ToUpper();
            var renkeiPathConfs = NoTrackingDataContext.RenkeiPathConfs
                .Where(p => p.HpId == hpId
                && (string.IsNullOrEmpty(p.Machine) || p.Machine.ToUpper() == machineName)
                && p.IsInvalid == 0)
                .ToList();

            var renkeiTimingConfs = NoTrackingDataContext.RenkeiTimingConfs
                .Where(p => p.HpId == hpId
                && p.EventCd == eventCd
                && p.IsInvalid == 0)
                .ToList();

            var renkeiTemplateMsts = NoTrackingDataContext.RenkeiTemplateMsts
                .Where(p => p.HpId == hpId)
                .ToList();

            var intermediateResults = (
                from renkeiMst in renkeiMsts
                join renkeiConf in renkeiConfs on
                    new { renkeiMst.HpId, renkeiMst.RenkeiId } equals
                    new { renkeiConf.HpId, renkeiConf.RenkeiId }
                join renkeiPathConf in renkeiPathConfs on
                    new { renkeiConf.HpId, renkeiConf.RenkeiId, renkeiConf.SeqNo } equals
                    new { renkeiPathConf.HpId, renkeiPathConf.RenkeiId, renkeiPathConf.SeqNo }
                join renkeiTimingConf in renkeiTimingConfs on
                    new { renkeiConf.HpId, renkeiConf.RenkeiId, renkeiConf.SeqNo } equals
                    new { renkeiTimingConf.HpId, renkeiTimingConf.RenkeiId, renkeiTimingConf.SeqNo }
                join renkeiTemplateMst in renkeiTemplateMsts on
                    new { renkeiConf.HpId, renkeiConf.TemplateId } equals
                    new { renkeiTemplateMst.HpId, renkeiTemplateMst.TemplateId } into renkeiTemplateMstJoins
                from renkeiTemplateMstJoin in renkeiTemplateMstJoins.DefaultIfEmpty()
                orderby
                    renkeiConf.SortNo, renkeiConf.RenkeiId, renkeiConf.SeqNo, renkeiPathConf.EdaNo
                select new
                {
                    renkeiMst,
                    renkeiConf,
                    renkeiPathConf,
                    renkeiTimingConf,
                    renkeiTemplateMstJoin
                }
            ).ToList();

            result = intermediateResults
            .Select(item => new RenkeiModel(
                item.renkeiMst,
                item.renkeiConf,
                item.renkeiPathConf,
                item.renkeiTimingConf,
                item.renkeiTemplateMstJoin
            ))
            .ToList();

            return result;
        }

        public PtInfModel GetPtInf(long ptId, int hpId)
        {
            var ptInfs = NoTrackingDataContext.PtInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.IsDelete == DeleteStatus.None
            ).ToList();

            PtInfModel result = null;

            if (ptInfs != null && ptInfs.Any())
            {
                result = new PtInfModel(ptInfs.First());
            }

            return result;
        }

        private bool ExistCalculateRequest(int hpId, long ptId, int startDate, int endDate)
        {
            bool ret = false;

            var calcStatuies = NoTrackingDataContext.CalcStatus.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate >= startDate &&
                p.SinDate <= endDate &&
                p.Status == 1
            );

            if (calcStatuies != null && calcStatuies.Any())
            {
                ret = true;
            }

            return ret;
        }

        public List<PtGrpInfModel> GetPtGrpInf(long ptId, int hpId)
        {
            var ptGrpInfs = NoTrackingDataContext.PtGrpInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.IsDeleted == DeleteStatus.None
            ).ToList();

            List<PtGrpInfModel> results = new List<PtGrpInfModel>();

            ptGrpInfs?.ForEach(entity =>
            {
                results.Add(new PtGrpInfModel(entity));
            }
            );

            return results;
        }

        public PtMemoModel GetPtMemo(long ptId, int hpId)
        {
            var ptMemos = NoTrackingDataContext.PtMemos.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.IsDeleted == DeleteStatus.None
            ).ToList();

            PtMemoModel result = null;

            if (ptMemos != null && ptMemos.Any())
            {
                result = new PtMemoModel(ptMemos.First());
            }

            return result;
        }

        public List<KaikeiInfModel> GetKaikeiInf(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo)
        {
            List<KaikeiInfModel> ret = new List<KaikeiInfModel>();

            var kaikeiInfs = NoTrackingDataContext.KaikeiInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate
            );
            var ptInfs = NoTrackingDataContext.PtInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.IsDelete == DeleteStatus.None
            );
            var ptHokenInfs = NoTrackingDataContext.PtHokenInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                //p.StartDate <= sinDate &&
                //(p.EndDate == 0 ? 99999999 : p.EndDate) >= sinDate &&
                p.IsDeleted == DeleteStatus.None
            );

            var hokenMsts = NoTrackingDataContext.HokenMsts;
            //診療日基準で保険番号マスタのキー情報を取得
            var hokenMstKeys = NoTrackingDataContext.HokenMsts.Where(
                h => h.StartDate <= sinDate && h.PrefNo == 0 && new int[] { 0, 1, 3, 4, 8, 9 }.Contains(h.HokenSbtKbn)
            ).GroupBy(
                x => new { x.HpId, x.PrefNo, x.HokenNo, x.HokenEdaNo }
            ).Select(
                x => new
                {
                    x.Key.HpId,
                    x.Key.PrefNo,
                    x.Key.HokenNo,
                    x.Key.HokenEdaNo,
                    StartDate = x.Max(d => d.StartDate)
                }
            );

            int oyaStatus = 0;
            if (oyaRaiinNo > 0)
            {
                // 自分の来院のステータスをチェック
                if (GetRaiinStatus(hpId, oyaRaiinNo) == 9)
                {
                    oyaStatus = 1;
                }
                // 親来院番号を取得しなおす（引数のoyaRaiinNoはRaiinNoが設定されている為）
                oyaRaiinNo = GetOyaRaiinNo(hpId, oyaRaiinNo);
            }

            var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                (raiinNo > 0 ? p.RaiinNo == raiinNo : true) &&
                (oyaRaiinNo > 0 ? p.OyaRaiinNo == oyaRaiinNo : true) &&
                (oyaRaiinNo > 0 ? (oyaStatus == 0 ? p.Status < 9 : p.Status == 9) : true)
            );
            //保険番号マスタの取得
            var houbetuMsts = (
                from hokenMst in hokenMsts
                join hokenKey in hokenMstKeys on
                    new { hokenMst.HpId, hokenMst.HokenNo, hokenMst.HokenEdaNo, hokenMst.PrefNo, hokenMst.StartDate } equals
                    new { hokenKey.HpId, hokenKey.HokenNo, hokenKey.HokenEdaNo, hokenKey.PrefNo, hokenKey.StartDate }
                select new
                {
                    hokenMst
                }
            );
            var hokensyaMsts = NoTrackingDataContext.HokensyaMsts.Where(p =>
                    p.HpId == hpId &&
                    p.IsDelete == 0
                );

            var join = (
                from kaikeiInf in kaikeiInfs
                join ptInf in ptInfs on
                    new { kaikeiInf.HpId, kaikeiInf.PtId } equals
                    new { ptInf.HpId, ptInf.PtId }
                join raiinInf in raiinInfs on
                    new { kaikeiInf.HpId, kaikeiInf.PtId, kaikeiInf.SinDate, kaikeiInf.RaiinNo } equals
                    new { raiinInf.HpId, raiinInf.PtId, raiinInf.SinDate, raiinInf.RaiinNo }
                join ptHokenInf in ptHokenInfs on
                    new { kaikeiInf.HpId, kaikeiInf.PtId, kaikeiInf.HokenId } equals
                    new { ptHokenInf.HpId, ptHokenInf.PtId, ptHokenInf.HokenId } into ptHokenInfJoins
                from ptHokenInfJoin in ptHokenInfJoins.DefaultIfEmpty()
                    //join hokenMst in hokenMsts on
                join hokenMst in houbetuMsts on
                    new { ptHokenInfJoin.HpId, ptHokenInfJoin.HokenNo, ptHokenInfJoin.HokenEdaNo } equals
                    new { hokenMst.hokenMst.HpId, hokenMst.hokenMst.HokenNo, hokenMst.hokenMst.HokenEdaNo } into hokenMstJoins
                from hokenMstJoin in hokenMstJoins.DefaultIfEmpty()
                join hokensyaMst in hokensyaMsts on
                    new { ptHokenInfJoin.HpId, ptHokenInfJoin.HokensyaNo } equals
                    new { hokensyaMst.HpId, hokensyaMst.HokensyaNo } into hokensyaMstJoins
                from hokensyaMstJoin in hokensyaMstJoins.DefaultIfEmpty()
                select new
                {
                    kaikeiInf,
                    ptInf,
                    ptHokenInf = ptHokenInfJoin,
                    hokenMst = hokenMstJoin,
                    hokensyaMst = hokensyaMstJoin
                }
                ).ToList();

            var entities = join.AsEnumerable().Select(
                data =>
                    new KaikeiInfModel(
                        data.kaikeiInf,
                        data.ptInf,
                        data.ptHokenInf,
                        data.hokenMst?.hokenMst ?? null,
                        data.hokensyaMst,
                        FindPtKohi(
                            hpId, ptId, data.kaikeiInf.SinDate,
                            new HashSet<int> { data.kaikeiInf.Kohi1Id, data.kaikeiInf.Kohi2Id, data.kaikeiInf.Kohi3Id, data.kaikeiInf.Kohi4Id })
                    )
                )
                .ToList();
            List<KaikeiInfModel> results = new List<KaikeiInfModel>();

            entities?.ForEach(entity =>
            {

                results.Add(
                    new KaikeiInfModel(
                        entity.KaikeiInf,
                        entity.PtInf,
                        entity.PtHokenInf,
                        entity.HokenMst,
                        entity.HokensyaMst,
                        entity.PtKohis
                    ));
            }
            );

            return results;
        }

        /// <summary>
        /// ユーザーマスタを取得する
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns></returns>
        public UserMstModel GetUserMst(int hpId, int userId)
        {
            var userMsts = NoTrackingDataContext.UserMsts.Where(p =>
                p.HpId == hpId &&
                p.UserId == userId &&
                p.IsDeleted == DeleteStatus.None
            ).ToList();

            UserMstModel result = null;

            if (userMsts != null && userMsts.Any())
            {
                result = new UserMstModel(userMsts.First());
            }

            return result;
        }

        /// <summary>
        /// 公費保険情報を取得する
        /// </summary>
        /// <param name="ptId">患者ID</param>
        /// <param name="sinDate">診療日</param>
        /// <param name="kohiIds">公費ID</param>
        /// <returns></returns>
        private List<PtKohiModel> FindPtKohi(int hpid, long ptId, int sinDate, HashSet<int> kohiIds)
        {
            var hokenMsts = NoTrackingDataContext.HokenMsts;
            //診療日基準で保険番号マスタのキー情報を取得
            var hokenMstKeys = NoTrackingDataContext.HokenMsts.Where(
                h => h.StartDate <= sinDate
            ).GroupBy(
                x => new { x.HpId, x.PrefNo, x.HokenNo, x.HokenEdaNo }
            ).Select(
                x => new
                {
                    x.Key.HpId,
                    x.Key.PrefNo,
                    x.Key.HokenNo,
                    x.Key.HokenEdaNo,
                    StartDate = x.Max(d => d.StartDate)
                }
            );

            var kohiPriorities = NoTrackingDataContext.KohiPriorities;
            var ptKohis = NoTrackingDataContext.PtKohis.Where(p =>
                p.HpId == hpid &&
                p.PtId == ptId &&
                kohiIds.Contains(p.HokenId)
            );
            //保険番号マスタの取得
            var houbetuMsts = (
                from hokenMst in hokenMsts
                join hokenKey in hokenMstKeys on
                    new { hokenMst.HpId, hokenMst.HokenNo, hokenMst.HokenEdaNo, hokenMst.PrefNo, hokenMst.StartDate } equals
                    new { hokenKey.HpId, hokenKey.HokenNo, hokenKey.HokenEdaNo, hokenKey.PrefNo, hokenKey.StartDate }
                select new
                {
                    hokenMst
                }
            );

            //公費の優先順位を取得
            var ptKohiQuery = (
                from ptKohi in ptKohis
                join houbetuMst in houbetuMsts on
                    new { ptKohi.HpId, ptKohi.HokenNo, ptKohi.HokenEdaNo, ptKohi.PrefNo } equals
                    new { houbetuMst.hokenMst.HpId, houbetuMst.hokenMst.HokenNo, houbetuMst.hokenMst.HokenEdaNo, houbetuMst.hokenMst.PrefNo }
                join kPriority in kohiPriorities on
                    new { houbetuMst.hokenMst.PrefNo, houbetuMst.hokenMst.Houbetu } equals
                    new { kPriority.PrefNo, kPriority.Houbetu } into kohiPriorityJoin
                from kohiPriority in kohiPriorityJoin.DefaultIfEmpty()
                where
                    ptKohi.HpId == hpid &&
                    ptKohi.PtId == ptId &&
                    ptKohi.IsDeleted == DeleteStatus.None
                select new
                {
                    ptKohi,
                    hokenMst = houbetuMst.hokenMst,
                    kohiPriority
                }
            ).ToList();

            var entities = ptKohiQuery.AsEnumerable().Select(
                data =>
                    new PtKohiModel(
                        data.ptKohi,
                        data.hokenMst,
                        data.kohiPriority
                    )
                )
                .ToList();
            List<PtKohiModel> results = new List<PtKohiModel>();

            entities?.ForEach(entity =>
            {

                results.Add(
                    new PtKohiModel(
                        entity.PtKohi,
                        entity.HokenMst,
                        entity.KohiPriority
                    ));

            }
            );

            return results;
        }


        /// <summary>
        /// 指定の来院番号の来院のOYA_RAIIN_NOを返す
        /// </summary>
        /// <param name="raiinNo"></param>
        /// <returns></returns>
        private long GetOyaRaiinNo(int hpId, long raiinNo)
        {
            long ret = raiinNo;

            var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(p =>
                p.HpId == hpId &&
                p.RaiinNo == raiinNo
            );

            if (raiinInfs != null)
            {
                ret = raiinInfs.First().OyaRaiinNo;
            }

            return ret;
        }

        /// <summary>
        /// 指定の来院番号の来院のSTATUSを返す
        /// </summary>
        /// <param name="raiinNo">来院番号</param>
        /// <returns></returns>
        private int GetRaiinStatus(int hpId, long raiinNo)
        {
            int ret = 0;

            var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(p =>
                p.HpId == hpId &&
                p.RaiinNo == raiinNo
            );

            if (raiinInfs != null)
            {
                ret = raiinInfs.First().Status;
            }

            return ret;
        }

        public RaiinInfModel GetRaiinInf(int hpId, long ptId, int sinDate, long raiinNo)
        {
            var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate &&
                p.RaiinNo == raiinNo &&
                p.IsDeleted == DeleteTypes.None
            );

            var kaMsts = NoTrackingDataContext.KaMsts.Where(p =>
                p.HpId == hpId &&
                p.IsDeleted == DeleteTypes.None
            );

            var rsvInfs = NoTrackingDataContext.RsvInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate &&
                p.RaiinNo == raiinNo
            );

            var rsvFrameMsts = NoTrackingDataContext.RsvFrameMsts.Where(p =>
                p.HpId == hpId &&
                p.IsDeleted == DeleteTypes.None
            );

            var raiinCmtInfs = NoTrackingDataContext.RaiinCmtInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate &&
                p.RaiinNo == raiinNo &&
                p.CmtKbn == 1 &&
                p.IsDelete == DeleteTypes.None
            );

            var rsvRenkeiInfs = NoTrackingDataContext.RsvRenkeiInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.RaiinNo == raiinNo
            );

            var ptLastVisitDates = NoTrackingDataContext.PtLastVisitDates.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId
            );

            var join = (
                    from raiinInf in raiinInfs
                    join kaMst in kaMsts on
                        new { raiinInf.HpId, raiinInf.KaId } equals
                        new { kaMst.HpId, kaMst.KaId } into joinKaMsts
                    from joinKaMst in joinKaMsts.DefaultIfEmpty()
                    join rsvInf in rsvInfs on
                        new { raiinInf.HpId, raiinInf.RaiinNo } equals
                        new { rsvInf.HpId, rsvInf.RaiinNo } into joinRsvInfs
                    from joinRsvInf in joinRsvInfs.DefaultIfEmpty()
                    join rsvFrameMst in rsvFrameMsts on
                        new { joinRsvInf.HpId, joinRsvInf.RsvFrameId } equals
                        new { rsvFrameMst.HpId, rsvFrameMst.RsvFrameId } into joinRsvFrameMsts
                    from joinRsvFrameMst in joinRsvFrameMsts.DefaultIfEmpty()
                    join raiinCmtInf in raiinCmtInfs on
                        new { raiinInf.HpId, raiinInf.PtId, raiinInf.SinDate, raiinInf.RaiinNo } equals
                        new { raiinCmtInf.HpId, raiinCmtInf.PtId, raiinCmtInf.SinDate, raiinCmtInf.RaiinNo } into joinRaiinCmtInfs
                    from joinRaiinCmtInf in joinRaiinCmtInfs.DefaultIfEmpty()
                    join rsvRenkeiInf in rsvRenkeiInfs on
                        new { raiinInf.HpId, raiinInf.PtId, raiinInf.RaiinNo } equals
                        new { rsvRenkeiInf.HpId, rsvRenkeiInf.PtId, rsvRenkeiInf.RaiinNo } into joinRsvRenkeiInfs
                    from joinRsvRenkeiInf in joinRsvRenkeiInfs.DefaultIfEmpty()
                    join ptLastVisitDate in ptLastVisitDates on
                        new { raiinInf.HpId, raiinInf.PtId } equals
                        new { ptLastVisitDate.HpId, ptLastVisitDate.PtId } into joinPtLastVisitDates
                    from joinPtLastVisitDate in joinPtLastVisitDates.DefaultIfEmpty()
                    select new
                    {
                        raiinInf,
                        joinKaMst,
                        joinRsvInf,
                        joinRsvFrameMst,
                        joinRaiinCmtInf,
                        joinRsvRenkeiInf,
                        joinPtLastVisitDate
                    }
                    ).ToList();


            RaiinInfModel result = null;


            if (join != null && join.Any())
            {
                result =
                    new RaiinInfModel(
                        join.First().raiinInf,
                        join.First().joinKaMst,
                        join.First().joinRsvInf,
                        join.First().joinRsvFrameMst,
                        join.First().joinRaiinCmtInf,
                        join.First().joinRsvRenkeiInf,
                        join.First().joinPtLastVisitDate
                        );
            }

            return result;
        }

        public List<RaiinKbnInfModel> GetRaiinKbnInf(int hpId, long ptId, int sinDate, long raiinNo)
        {
            var raiinKbnInfs = NoTrackingDataContext.RaiinKbnInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate &&
                p.RaiinNo == raiinNo &&
                p.IsDelete == DeleteStatus.None
            );

            var raiinKbnDtls = NoTrackingDataContext.RaiinKbnDetails.Where(p =>
                p.HpId == hpId &&
                p.IsDeleted == DeleteStatus.None
            );

            var join = (
                    from raiinKbnInf in raiinKbnInfs
                    join raiinKbnDtl in raiinKbnDtls on
                        new { raiinKbnInf.HpId, raiinKbnInf.GrpId, raiinKbnInf.KbnCd } equals
                        new { raiinKbnDtl.HpId, GrpId = raiinKbnDtl.GrpCd, raiinKbnDtl.KbnCd } into joinRaiinKbnDtls
                    from joinRaiinKbnDtl in joinRaiinKbnDtls.DefaultIfEmpty()
                    select new
                    {
                        raiinKbnInf,
                        joinRaiinKbnDtl
                    }
                    ).ToList();

            List<RaiinKbnInfModel> results = new List<RaiinKbnInfModel>();

            join?.ForEach(entity =>
            {
                results.Add(new RaiinKbnInfModel(entity.raiinKbnInf, entity.joinRaiinKbnDtl));
            }
                );

            return results;

        }

        private int GetNyukinKbn(int hpId, long ptId, int sinDate, long raiinNo)
        {
            int ret = 0;

            var syunoSeikyus = NoTrackingDataContext.SyunoSeikyus.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.RaiinNo == raiinNo
            );

            if (syunoSeikyus != null && syunoSeikyus.Any() == true)
            {
                ret = syunoSeikyus.First().NyukinKbn;
            }

            return ret;
        }

        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        public List<Renkei260OdrInfModel> GetRenkei260OdrInf(int hpId, long ptId, int sinDate)
        {
            var ptInfs = NoTrackingDataContext.PtInfs.Where(p =>
               p.HpId == hpId &&
               p.PtId == ptId &&
               p.IsDelete == DeleteTypes.None
           );
            var odrInfs = NoTrackingDataContext.OdrInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate &&
                p.IsDeleted == DeleteTypes.None
            );

            var odrDtls = NoTrackingDataContext.OdrInfDetails.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate
            );

            var raiinInfs = NoTrackingDataContext.RaiinInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate &&
                p.IsDeleted == DeleteTypes.None
            );

            var tenMsts = NoTrackingDataContext.TenMsts.Where(p =>
                p.HpId == hpId &&
                p.StartDate <= sinDate &&
                p.EndDate >= sinDate
            );

            var kensaMsts = NoTrackingDataContext.KensaMsts.Where(p =>
                p.HpId == hpId
            );

            var join = (
                    from odrInf in odrInfs
                    join ptInf in ptInfs on
                        new { odrInf.HpId, odrInf.PtId } equals
                        new { ptInf.HpId, ptInf.PtId }
                    join odrDtl in odrDtls on
                        new { odrInf.HpId, odrInf.PtId, odrInf.RaiinNo, odrInf.RpNo, odrInf.RpEdaNo } equals
                        new { odrDtl.HpId, odrDtl.PtId, odrDtl.RaiinNo, odrDtl.RpNo, odrDtl.RpEdaNo }
                    join raiinInf in raiinInfs on
                        new { odrInf.HpId, odrInf.PtId, odrInf.RaiinNo } equals
                        new { raiinInf.HpId, raiinInf.PtId, raiinInf.RaiinNo }
                    join tenMst in tenMsts on
                        new { odrDtl.HpId, odrDtl.ItemCd } equals
                        new { tenMst.HpId, tenMst.ItemCd } into tenMstJoins
                    from tenMstJoin in tenMstJoins.DefaultIfEmpty()
                    join kensaMst in kensaMsts on
                        new { tenMstJoin.HpId, tenMstJoin.KensaItemCd, tenMstJoin.KensaItemSeqNo } equals
                        new { kensaMst.HpId, kensaMst.KensaItemCd, kensaMst.KensaItemSeqNo } into kensaMstJoins
                    from kensaMstJoin in kensaMstJoins.DefaultIfEmpty()
                    orderby odrInf.SinDate, odrInf.RaiinNo, odrInf.OdrKouiKbn, odrInf.SortNo, odrInf.RpNo, odrInf.RpEdaNo, odrDtl.RowNo
                    select new
                    {
                        ptInf,
                        odrInf,
                        odrDtl,
                        raiinInf,
                        tenMstJoin,
                        kensaMstJoin
                    }
                ).ToList();

            var raiinInfSeqs = NoTrackingDataContext.RaiinInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate
            ).OrderBy(p => p.RaiinNo)
            .ToList();

            List<(long raiinNo, int seq)> raiinSeqs = new List<(long raiinNo, int seq)>();

            int seq = 0;
            foreach (var raiinInfSeq in raiinInfSeqs)
            {
                seq++;
                raiinSeqs.Add((raiinInfSeq.RaiinNo, seq));
            }

            var odrInfSeqs = NoTrackingDataContext.OdrInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate
            ).OrderBy(p => p.RaiinNo)
            .ThenBy(p => p.Id)
            .ToList();

            List<(long raiinNo, long rpno, long rpedano, int seq)> odrSeqs = new List<(long raiinNo, long rpno, long rpedano, int seq)>();

            seq = 0;
            foreach (var odrInfSeq in odrInfSeqs)
            {
                seq++;
                odrSeqs.Add((odrInfSeq.RaiinNo, odrInfSeq.RpNo, odrInfSeq.RpEdaNo, seq));
            }

            string height = "0";
            KensaInfDetail heightInfo = GetBodyInfo(hpId, ptId, sinDate, "V0001");
            if (heightInfo != null)
            {
                height = heightInfo.ResultVal;
            }

            string weight = "0";
            KensaInfDetail weightInfo = GetBodyInfo(hpId, ptId, sinDate, "V0002");
            if (weightInfo != null)
            {
                weight = weightInfo.ResultVal;
            }

            List<Renkei260OdrInfModel> results = new List<Renkei260OdrInfModel>();

            join?.ForEach(entity =>
            {
                int seqNo = 0;
                if (raiinSeqs.Any(p => p.raiinNo == entity.raiinInf.RaiinNo))
                {
                    seqNo = raiinSeqs.Find(p => p.raiinNo == entity.raiinInf.RaiinNo).seq;
                }

                int uchiNo = 0;
                if (odrSeqs.Any(p =>
                    p.raiinNo == entity.raiinInf.RaiinNo &&
                    p.rpno == entity.odrInf.RpNo &&
                    p.rpedano == entity.odrInf.RpEdaNo))
                {
                    uchiNo = odrSeqs.Find(p =>
                                p.raiinNo == entity.raiinInf.RaiinNo &&
                                p.rpno == entity.odrInf.RpNo &&
                                p.rpedano == entity.odrInf.RpEdaNo).seq;
                }
                results.Add(new Renkei260OdrInfModel(entity.ptInf, entity.odrInf, entity.odrDtl, entity.raiinInf, entity.tenMstJoin, entity.kensaMstJoin, seqNo, uchiNo, height, weight));
            }

            );

            return results;
        }

        public KensaInfDetail GetBodyInfo(int hpId, long ptId, int sinday, string kensaItemCode)
        {
            return NoTrackingDataContext.KensaInfDetails
                .Where(k => k.HpId == hpId && k.PtId == ptId && k.IraiDate <= sinday && k.KensaItemCd == kensaItemCode && !string.IsNullOrEmpty(k.ResultVal))
                .OrderByDescending(k => k.IraiDate).FirstOrDefault();
        }

        public PtHokenPidModel GetHokenPid(int hpId, long ptId, int sinDate, int hokenPid)
        {
            int HokenPid = hokenPid;

            if (HokenPid == 0)
            {
                // なかった場合、主保険設定から取得
                HokenPid = GetHokenPidByMainHokenPid(hpId, ptId);
            }

            if (HokenPid == 0)
            {
                // なかった場合、健保の有効保険を取得
                HokenPid = GetHokenPidByYukoKenpo(hpId, ptId, sinDate);
            }

            if (HokenPid == 0)
            {
                // なかった場合、健保以外の有効保険を取得
                HokenPid = GetHokenPidByYukoNoKenpo(hpId, ptId, sinDate);
            }

            if (HokenPid == 0)
            {
                // なかった場合、保険を取得
                HokenPid = GetHokenPidByHokenPattern(hpId, ptId);
            }

            var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.HokenPid == HokenPid &&
                p.IsDeleted == DeleteStatus.None
            );

            var ptHokenInfs = NoTrackingDataContext.PtHokenInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.IsDeleted == DeleteStatus.None
            );

            var hokensyaMsts = NoTrackingDataContext.HokensyaMsts.Where(p =>
                p.HpId == hpId &&
                p.IsDelete == DeleteStatus.None
            );

            var ptHokenPatternQuery = (
                from ptHokenPattern in ptHokenPatterns
                join ptHokenInf in ptHokenInfs on
                    new { ptHokenPattern.HpId, ptHokenPattern.PtId, ptHokenPattern.HokenId } equals
                    new { ptHokenInf.HpId, ptHokenInf.PtId, ptHokenInf.HokenId } into joinPtHokenInfs
                from joinPtHokenInf in joinPtHokenInfs.DefaultIfEmpty()
                join hokensyaMst in hokensyaMsts on
                    new { joinPtHokenInf.HpId, joinPtHokenInf.HokensyaNo } equals
                    new { hokensyaMst.HpId, hokensyaMst.HokensyaNo } into joinHokensyaMsts
                from joinHokensyaMst in joinHokensyaMsts.DefaultIfEmpty()
                where
                    joinPtHokenInf.HpId == hpId &&
                    joinPtHokenInf.PtId == ptId &&
                    joinPtHokenInf.IsDeleted == DeleteStatus.None
                select new
                {
                    ptHokenInf = joinPtHokenInf,
                    hokensyaMst = joinHokensyaMst
                }
            ).ToList();

            var entities = ptHokenPatternQuery.AsEnumerable().Select(
                data =>
                    new PtHokenPidModel(
                        data.ptHokenInf,
                        data.hokensyaMst
                    )
                )
                .ToList();

            return entities?.FirstOrDefault();
        }

        private int GetHokenPidByMainHokenPid(int hpId, long ptId)
        {
            // なかった場合、主保険設定から取得
            int ret = 0;

            var ptInf = NoTrackingDataContext.PtInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.IsDelete == DeleteStatus.None
            ).ToList();

            if (ptInf != null && ptInf.Any())
            {
                ret = ptInf.First().MainHokenPid;
            }

            return ret;
        }

        private int GetHokenPidByYukoKenpo(int hpId, long ptId, int sinDate)
        {
            // なかった場合、健保の有効保険を取得
            int ret = 0;

            var hokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.StartDate <= sinDate &&
                p.EndDate >= sinDate &&
                new int[] { 1, 2 }.Contains(p.HokenKbn) &&
                p.IsDeleted == DeleteStatus.None
            );

            var hokenPatternSelect = (
                    from hokenPattern in hokenPatterns
                    select new
                    {
                        hokenPattern,
                        Kumiawase = (hokenPattern.Kohi4Id > 0 ? 4 : (hokenPattern.Kohi3Id > 0 ? 3 : (hokenPattern.Kohi2Id > 0 ? 2 : (hokenPattern.Kohi1Id > 0 ? 1 : 0))))
                    }
                ).ToList();

            if (hokenPatternSelect != null && hokenPatternSelect.Any())
            {
                hokenPatternSelect =
                    hokenPatternSelect
                        .OrderByDescending(p => p.Kumiawase)
                        .OrderByDescending(p => p.hokenPattern.HokenPid)
                        .ToList();
                ret = hokenPatterns.First().HokenPid;
            }
            return ret;
        }

        private int GetHokenPidByYukoNoKenpo(int hpId, long ptId, int sinDate)
        {
            // なかった場合、健保以外の有効保険を取得
            int ret = 0;

            var hokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.StartDate <= sinDate &&
                p.EndDate >= sinDate &&
                !new int[] { 1, 2 }.Contains(p.HokenKbn) &&
                p.IsDeleted == DeleteStatus.None
            );

            var hokenPatternSelect = (
                    from hokenPattern in hokenPatterns
                    select new
                    {
                        hokenPattern,
                        Kumiawase = (hokenPattern.Kohi4Id > 0 ? 4 : (hokenPattern.Kohi3Id > 0 ? 3 : (hokenPattern.Kohi2Id > 0 ? 2 : (hokenPattern.Kohi1Id > 0 ? 1 : 0))))
                    }
                ).ToList();

            if (hokenPatternSelect != null && hokenPatternSelect.Any())
            {
                hokenPatternSelect =
                    hokenPatternSelect
                        .OrderByDescending(p => p.Kumiawase)
                        .OrderByDescending(p => p.hokenPattern.HokenPid)
                        .ToList();
                ret = hokenPatterns.First().HokenPid;
            }
            return ret;
        }

        private int GetHokenPidByHokenPattern(int hpId, long ptId)
        {
            // なかった場合、保険を取得
            int ret = 0;

            var hokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.IsDeleted == DeleteStatus.None
            );

            var hokenPatternSelect = (
                    from hokenPattern in hokenPatterns
                    select new
                    {
                        hokenPattern,
                        Kumiawase = (hokenPattern.Kohi4Id > 0 ? 4 : (hokenPattern.Kohi3Id > 0 ? 3 : (hokenPattern.Kohi2Id > 0 ? 2 : (hokenPattern.Kohi1Id > 0 ? 1 : 0))))
                    }
                ).ToList();

            if (hokenPatternSelect != null && hokenPatternSelect.Any())
            {
                hokenPatternSelect =
                    hokenPatternSelect
                        .OrderByDescending(p => p.Kumiawase)
                        .OrderByDescending(p => p.hokenPattern.HokenPid)
                        .ToList();
                ret = hokenPatterns.First().HokenPid;
            }
            return ret;
        }

    }
}
