using System.Text;
using Domain.Models.ChartApproval;
using Domain.Models.Diseases;
using Domain.Models.Insurance;
using Domain.Models.KarteInfs;
using Domain.Models.MstItem;
using Domain.Models.NextOrder;
using Domain.Models.OrdInfDetails;
using Domain.Models.OrdInfs;
using Domain.Models.RaiinKubunMst;
using Domain.Models.Receipt.Recalculation;
using Domain.Models.SystemConf;
using Domain.Models.TodayOdr;
using Entity.Tenant;
using Helper.Common;
using Helper.Constant;
using Helper.Constants;
using Helper.Enum;
using Helper.Extension;
using Helper.Mapping;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repositories;

public class TodayOdrRepository : RepositoryBase, ITodayOdrRepository
{
    private readonly int headerOdrKouiKbn = 10;
    private readonly string jikanItemCd = "@JIKAN";
    private readonly string shinItemCd = "@SHIN";
    private readonly string shinItemName = "診察料基本点数算定用";
    private readonly string jikanItemName = "時間外算定用";
    private readonly int jikanRow = 2;
    private readonly int shinRow = 1;
    private readonly int rpEdaNoDefault = 1;
    private readonly int rpNoDefault = 1;
    private readonly int daysCntDefalt = 1;

    private const string SUSPECT_FLAG = "の疑い";
    private readonly ISystemConfRepository? _systemConf;
    private readonly IApprovalInfRepository? _approvalInfRepository;
    private readonly IInsuranceRepository? _insuranceInforRepository;
    public TodayOdrRepository(ITenantProvider tenantProvider, ISystemConfRepository systemConf, IApprovalInfRepository approvalInfRepository) : base(tenantProvider)
    {
        _systemConf = systemConf;
        _approvalInfRepository = approvalInfRepository;
    }

    public TodayOdrRepository(ITenantProvider tenantProvider, ISystemConfRepository systemConf, IApprovalInfRepository approvalInfRepository, IInsuranceRepository insuranceRepository) : base(tenantProvider)
    {
        _systemConf = systemConf;
        _approvalInfRepository = approvalInfRepository;
        _insuranceInforRepository = insuranceRepository;
    }

    public TodayOdrRepository(ITenantProvider tenantProvider) : base(tenantProvider)
    {
    }

    public bool Upsert(int hpId, long ptId, long raiinNo, int sinDate, int karteStatus, int syosaiKbn, int jikanKbn, int hokenPid, int santeiKbn, string uketukeTime, string sinStartTime, string sinEndTime, List<OrdInfModel> odrInfs, KarteInfModel karteInfModel, int userId, byte modeSaveData, List<SchemaItemModel>? schemaItems = null, byte modeSave = 0, bool isHasChange = false)
    {
        bool isUpdateApproveInf = false;
        var user = NoTrackingDataContext.UserMsts.FirstOrDefault(item => item.HpId == hpId
                                                                         && item.UserId == userId
                                                                         && item.StartDate <= sinDate
                                                                         && item.EndDate >= sinDate
                                                                         && item.IsDeleted == DeleteTypes.None);
        //isDococtor
        if (user != null) isUpdateApproveInf = user.JobCd == 1;

        var executionStrategy = TrackingDataContext.Database.CreateExecutionStrategy();

        return executionStrategy.Execute(
            () =>
            {
                using var transaction = TrackingDataContext.Database.BeginTransaction();
                try
                {
                    if (modeSave == 1)
                    {
                        bool isChangeFile = IsChangeKarteFile(hpId, ptId, raiinNo);
                        if (isChangeFile)
                        {
                            modeSave = 0;
                        }
                    }
                    if (modeSave == 0)
                    {

                        var timeUpdate = CIUtil.GetJapanDateTimeNow();
                        int karteEdition = SaveKarteEdition(hpId, ptId, sinDate, raiinNo, karteStatus, userId);
                        if (karteEdition == 0)
                        {
                            transaction.Rollback();
                            return false;
                        }

                        if (!UpsertOdrInfs(hpId, ptId, raiinNo, sinDate, karteEdition, odrInfs, userId, modeSaveData, isHasChange))
                        {
                            transaction.Rollback();
                            return false;
                        }

                        if (karteInfModel.PtId > 0 && karteInfModel.HpId > 0 && karteInfModel.RaiinNo > 0 && karteInfModel.SinDate > 0)
                        {
                            // karte change
                            if (!UpsertKarteInfs(karteInfModel, userId, karteEdition, modeSaveData))
                            {
                                transaction.Rollback();
                                return false;
                            }
                        }

                        if (!SaveRaiinListInf(odrInfs, userId))
                        {
                            transaction.Rollback();
                            return false;
                        }

                        if (!SaveHeaderInf(hpId, ptId, raiinNo, sinDate, syosaiKbn, jikanKbn, hokenPid, santeiKbn, userId, karteEdition, modeSaveData))
                        {
                            transaction.Rollback();
                            return false;
                        }

                        if (!UpdateSchemaFiles(hpId, ptId, userId, raiinNo, sinDate, schemaItems ?? new(), timeUpdate))
                        {
                            transaction.Rollback();
                            return false;
                        }
                        // update adopted in tenitem
                        if (odrInfs.Count > 0)
                        {
                            List<string> itemCds = odrInfs.SelectMany(o => o.OrdInfDetails).Select(o => o.ItemCd).ToList();
                            if (!UpdateAdoptedInTenItem(hpId, itemCds, sinDate))
                            {
                                transaction.Rollback();
                                return false;
                            }
                        }
                    }
                    else
                    {
                        if (isUpdateApproveInf && karteStatus == KarteStatusConst.Official)
                        {
                            if (!UpdateApproveInfoWhenNotCreateEdition(hpId, ptId, sinDate, raiinNo, userId))
                            {
                                transaction.Rollback();
                                return false;
                            }
                        }
                    }

                    if (!SaveRaiinInf(hpId, ptId, raiinNo, sinDate, syosaiKbn, jikanKbn, hokenPid, santeiKbn, uketukeTime, sinStartTime, sinEndTime, userId, modeSaveData, isHasChange))
                    {
                        transaction.Rollback();
                        return false;
                    }

                    transaction.Commit();
                    return true;
                }
                catch (Exception)
                {
                    transaction.Rollback();
                    return false;
                }
            });
    }

    private bool IsChangeKarteFile(int hpId, long ptId, long raiinNo)
    {
        var listFileKarte = NoTrackingDataContext.FilingInf.Where(item => item.HpId == hpId
                                                                                 && item.PtId == ptId
                                                                                 && raiinNo == item.RaiinNo);
        var queryListFile = from file in listFileKarte
                            join edition in NoTrackingDataContext.KarteEditions on file.RaiinNo equals edition.RaiinNo
                            where edition.HpId == hpId && edition.PtId == ptId && edition.IsDeleted == DeleteTypes.None
                            && file.UpdateDate > edition.UpdateDate
                            select file;
        if (queryListFile.Any())
        {
            return true;
        }
        return false;
    }

    public List<(string, string, List<CheckedDiseaseModel>)> GetCheckDiseases(int hpId, int sinDate, List<PtDiseaseModel> todayByomeis, List<OrdInfModel> todayOdrs)
    {
        var ptByomeis = new List<PtDiseaseModel>();
        var ptId = todayOdrs.FirstOrDefault()?.PtId ?? 0;
        if (todayByomeis.Any())
        {
            ptByomeis = todayByomeis.Where(p => p.IsDeleted == DeleteTypes.None && p.IsInMonth).ToList();
        }
        else
        {
            ptByomeis = NoTrackingDataContext.PtByomeis.Where(p => p.HpId == hpId && p.PtId == ptId && p.IsDeleted == DeleteTypes.None).Select(p => new PtDiseaseModel(
                    p.HpId,
                    p.PtId,
                    p.SeqNo,
                    p.ByomeiCd ?? string.Empty,
                    p.SortNo,
                    new(),
                    p.Byomei ?? string.Empty,
                    p.StartDate,
                    p.TenkiKbn,
                    p.TenkiDate,
                    p.SyubyoKbn,
                    p.SikkanKbn,
                    p.NanByoCd,
                    p.IsNodspRece,
                    p.IsNodspKarte,
                    p.IsDeleted,
                    p.Id,
                    p.IsImportant,
                    sinDate,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    p.HokenPid,
                    p.HosokuCmt ?? string.Empty,
                    0,
                    0
                )).AsEnumerable().Where(p => p.IsInMonth).ToList();
        }

        List<OrdInfDetailModel> drugOrders = new List<OrdInfDetailModel>();
        int odrCount = 0;
        var checkedDiseases = new List<(string, string, List<CheckedDiseaseModel>)>();

        foreach (var order in todayOdrs)
        {
            foreach (var odrDetail in order.OrdInfDetails)
            {
                string itemCd = odrDetail.ItemCd;
                if (string.IsNullOrEmpty(itemCd) ||
                    itemCd == ItemCdConst.Con_TouyakuOrSiBunkatu ||
                    itemCd == ItemCdConst.Con_Refill) continue;

                string santeiItemCd = GetSanteiItemCd(hpId, itemCd, sinDate);

                var byomeisByOdr = GetTekiouByomeiByOrder(hpId, new List<string>() { itemCd, santeiItemCd });
                if (byomeisByOdr.Count == 0) continue;
                // No.6510 future byomei check
                List<string> byomeiCds = byomeisByOdr.Select(p => p.ByomeiMst.ByomeiCd).ToList();
                var check = byomeiCds.Contains("8842157");

                if (!drugOrders.Exists(p => p.ItemCd == odrDetail.ItemCd)
                    && !ptByomeis.Where(p => (p.HokenPid == 0 || p.HokenPid == order.HokenPid)
                    && p.StartDate <= sinDate && (!p.IsTenki || p.TenkiDate >= sinDate)
                    && (!odrDetail.IsDrug || !p.Byomei.AsString().Contains(SUSPECT_FLAG)))
                    .Any(p => byomeiCds.Contains(p.ByomeiCd)))
                {
                    //set item name for grid mode
                    odrCount++;
                    List<CheckedDiseaseModel> byomeis = new();
                    foreach (var byomei in byomeisByOdr)
                    {
                        var byomeiModify = new CheckedDiseaseModel(byomei.SikkanCd, byomei.NanByoCd, byomei.Byomei, odrCount, byomei.PtDiseaseModel, byomei.ByomeiMst);
                        byomeis.Add(byomeiModify);
                    }
                    drugOrders.Add(odrDetail);
                    checkedDiseases.Add((odrDetail.ItemCd, odrDetail.ItemName, byomeis));
                }
            }
        }

        return checkedDiseases;
    }



    private bool SaveRaiinInf(int hpId, long ptId, long raiinNo, int sinDate, int syosaiKbn, int jikanKbn, int hokenPid, int santeiKbn, string uketukeTime, string sinStartTime, string sinEndTime, int userId, byte modeSaveData, bool isHasChange)
    {
        var raiinInf = TrackingDataContext.RaiinInfs.FirstOrDefault(r => r.HpId == hpId && r.PtId == ptId && r.RaiinNo == raiinNo && r.SinDate == sinDate);

        if (raiinInf != null)
        {
            //var preProcess = GetModeSaveDate(modeSaveData, raiinInf.Status, sinEndTime, sinStartTime, uketukeTime);
            //var preProcessStatus = preProcess.status != 0 ? preProcess.status : raiinInf.Status;
            raiinInf.Status = modeSaveData;
            //(raiinInf.Status <= RaiinState.TempSave && modeSaveData == 0) ? RaiinState.TempSave : preProcessStatus;
            //modeSaveData != 0 ? 7 : RaiinState.TempSave; // temperaror with status 7
            raiinInf.SyosaisinKbn = syosaiKbn;
            raiinInf.JikanKbn = jikanKbn;
            raiinInf.HokenPid = hokenPid;
            raiinInf.SanteiKbn = santeiKbn;
            //raiinInf.UketukeTime = string.IsNullOrEmpty(preProcess.uketukeTime) ? raiinInf.UketukeTime : preProcess.uketukeTime;
            if (string.IsNullOrEmpty(raiinInf.SinEndTime) && modeSaveData != 0)
                raiinInf.SinEndTime = sinEndTime;
            if (string.IsNullOrEmpty(raiinInf.SinStartTime))
                raiinInf.SinStartTime = sinStartTime;
            raiinInf.UpdateId = userId;
            if (isHasChange)
            {
                raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
            }
            raiinInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
            TrackingDataContext.SaveChanges();
        }
        return true;
    }

    public (int status, string sinStartTime, string sinEndTime, string uketukeTime) GetModeSaveDate(byte modeSaveData, int status, string sinEndTime, string sinStartTime, string uketukeTime)
    {
        string sinStartTimeReCalculate = string.Empty, sinEndTimeReCalculate = string.Empty, uketukeTimeReCalculate = string.Empty;
        int statusRecalculate = 0;

        if (modeSaveData == (byte)ModeSaveData.KeisanSave)
        {
            if (status != RaiinState.AmountConfirmed && status != RaiinState.Paid && status != RaiinState.FcoWaiting)
            {
                // Update mode
                if (status >= RaiinState.ConsultationCompleted)
                {
                    //診察終了時間がなければ
                    if (string.IsNullOrEmpty(sinEndTime) || sinEndTime == "0")
                    {
                        sinEndTimeReCalculate = CIUtil.DateTimeToTime(CIUtil.GetJapanDateTimeNow());
                    }
                }
                // Add new mode
                else
                {
                    sinStartTimeReCalculate = sinStartTime;
                    sinEndTimeReCalculate = CIUtil.DateTimeToTime(CIUtil.GetJapanDateTimeNow());
                    // 来院時間がないときは更新する
                    if (string.IsNullOrEmpty(uketukeTime) || uketukeTime == "0")
                    {
                        uketukeTimeReCalculate = sinStartTime;
                    }
                }
                statusRecalculate = RaiinState.ConsultationCompleted;
            }
        }
        // 保存
        else if (modeSaveData == (byte)ModeSaveData.KaikeiSave)
        {
            // Update mode
            if (status >= RaiinState.ConsultationCompleted)
            {
                if (status <= RaiinState.AmountConfirmed)
                {
                    statusRecalculate = RaiinState.AmountConfirmed;
                }
                if ((status == RaiinState.ConsultationCompleted || status == RaiinState.AmountConfirmed) && (string.IsNullOrEmpty(sinEndTime) || sinEndTime == "0"))
                {
                    sinEndTimeReCalculate = CIUtil.DateTimeToTime(CIUtil.GetJapanDateTimeNow());
                }
            }
            // Add new mode
            else
            {
                sinStartTimeReCalculate = sinStartTime;
                sinEndTimeReCalculate = CIUtil.DateTimeToTime(CIUtil.GetJapanDateTimeNow());
                // 来院時間がないときは更新する
                if (string.IsNullOrEmpty(uketukeTime) || uketukeTime == "0")
                {
                    uketukeTimeReCalculate = sinStartTime;
                }
                statusRecalculate = RaiinState.AmountConfirmed;
            }
        }

        return new(statusRecalculate, sinStartTimeReCalculate, sinEndTimeReCalculate, uketukeTimeReCalculate);
    }

    private bool SaveHeaderInf(int hpId, long ptId, long raiinNo, int sinDate, int syosaiKbn, int jikanKbn, int hokenPid, int santeiKbn, int userId, int karteEdition, int status)
    {

        var oldHeaderInfModel = TrackingDataContext.OdrInfs.Where(o => o.HpId == hpId && o.PtId == ptId && o.RaiinNo == raiinNo && o.SinDate == sinDate && o.OdrKouiKbn == 10).OrderByDescending(o => o.RpEdaNo).FirstOrDefault();
        var oldoldSyosaiKihon = TrackingDataContext.OdrInfDetails.FirstOrDefault(odr => odr.HpId == hpId && odr.PtId == ptId && odr.RaiinNo == raiinNo && odr.SinDate == sinDate && odr.ItemCd == ItemCdConst.SyosaiKihon && (oldHeaderInfModel != null && odr.RpEdaNo == oldHeaderInfModel.RpEdaNo) && (oldHeaderInfModel != null && odr.RpNo == oldHeaderInfModel.RpNo));
        var oldJikanKihon = TrackingDataContext.OdrInfDetails.FirstOrDefault(odr => odr.HpId == hpId && odr.PtId == ptId && odr.RaiinNo == raiinNo && odr.SinDate == sinDate && odr.ItemCd == ItemCdConst.JikanKihon && (oldHeaderInfModel != null && odr.RpEdaNo == oldHeaderInfModel.RpEdaNo) && (oldHeaderInfModel != null && odr.RpNo == oldHeaderInfModel.RpNo));

        if (oldHeaderInfModel != null)
        {
            if (jikanKbn == -1 && oldJikanKihon != null)
            {
                int.TryParse(oldJikanKihon?.Suryo.ToString(), out int oldJikan);
                jikanKbn = oldJikan;
            }

            var newHeaderInf = new OdrInf
            {
                HpId = hpId,
                RaiinNo = raiinNo,
                RpNo = oldHeaderInfModel.RpNo,
                RpEdaNo = oldHeaderInfModel.RpEdaNo + 1,
                PtId = ptId,
                SinDate = sinDate,
                HokenPid = hokenPid,
                OdrKouiKbn = headerOdrKouiKbn,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateId = userId,
                CreateId = userId,
                DaysCnt = daysCntDefalt,
                Edition = karteEdition
            };
            TrackingDataContext.OdrInfs.Add(newHeaderInf);

            var odrSyosaiKionDetail = new OdrInfDetail
            {
                HpId = hpId,
                RaiinNo = raiinNo,
                RpNo = newHeaderInf.RpNo,
                RpEdaNo = newHeaderInf.RpEdaNo,
                RowNo = shinRow,
                PtId = ptId,
                SinDate = sinDate,
                SinKouiKbn = headerOdrKouiKbn,
                ItemCd = shinItemCd,
                ItemName = shinItemName,
                Suryo = syosaiKbn,
                Edition = karteEdition
            };
            TrackingDataContext.OdrInfDetails.Add(odrSyosaiKionDetail);

            if (jikanKbn >= 0)
            {
                var odrJikanDetail = new OdrInfDetail
                {
                    HpId = hpId,
                    RaiinNo = raiinNo,
                    RpNo = newHeaderInf.RpNo,
                    RpEdaNo = newHeaderInf.RpEdaNo,
                    RowNo = jikanRow,
                    PtId = ptId,
                    SinDate = sinDate,
                    SinKouiKbn = headerOdrKouiKbn,
                    ItemCd = jikanItemCd,
                    ItemName = jikanItemName,
                    Suryo = jikanKbn,
                    Edition = karteEdition
                };
                TrackingDataContext.OdrInfDetails.Add(odrJikanDetail);
            }

            var odrInfDetails = new List<OdrInfDetail>();

            TrackingDataContext.OdrInfs.Remove(oldHeaderInfModel);
            if (oldoldSyosaiKihon != null)
            {
                odrInfDetails.Add(oldoldSyosaiKihon);
                TrackingDataContext.OdrInfDetails.Remove(oldoldSyosaiKihon);
            }
            if (oldJikanKihon != null)
            {
                odrInfDetails.Add(oldJikanKihon);
                TrackingDataContext.OdrInfDetails.Remove(oldJikanKihon);
            }
            TrackingDataContext.OdrInfs.Remove(oldHeaderInfModel);
            SaveDelOdrInfs(new List<OdrInf> { oldHeaderInfModel }, odrInfDetails);
        }
        else
        {

            var newHeaderInf = new OdrInf
            {
                HpId = hpId,
                RaiinNo = raiinNo,
                RpNo = rpNoDefault,
                RpEdaNo = rpEdaNoDefault,
                PtId = ptId,
                SinDate = sinDate,
                HokenPid = hokenPid,
                OdrKouiKbn = headerOdrKouiKbn,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateId = userId,
                CreateId = userId,
                DaysCnt = daysCntDefalt,
                Edition = karteEdition
            };
            TrackingDataContext.OdrInfs.Add(newHeaderInf);

            var odrSyosaiKionDetail = new OdrInfDetail
            {
                HpId = hpId,
                RaiinNo = raiinNo,
                RpNo = newHeaderInf.RpNo,
                RpEdaNo = rpEdaNoDefault,
                RowNo = shinRow,
                PtId = ptId,
                SinDate = sinDate,
                SinKouiKbn = headerOdrKouiKbn,
                ItemCd = shinItemCd,
                ItemName = shinItemName,
                Suryo = syosaiKbn,
                Edition = karteEdition
            };
            TrackingDataContext.OdrInfDetails.Add(odrSyosaiKionDetail);

            if (jikanKbn >= 0)
            {
                var odrJikanDetail = new OdrInfDetail
                {
                    HpId = hpId,
                    RaiinNo = raiinNo,
                    RpNo = newHeaderInf.RpNo,
                    RpEdaNo = rpEdaNoDefault,
                    RowNo = jikanRow,
                    PtId = ptId,
                    SinDate = sinDate,
                    SinKouiKbn = headerOdrKouiKbn,
                    ItemCd = jikanItemCd,
                    ItemName = jikanItemName,
                    Suryo = jikanKbn,
                    Edition = karteEdition
                };
                TrackingDataContext.OdrInfDetails.Add(odrJikanDetail);
            }

        }

        TrackingDataContext.SaveChanges();
        return true;
    }

    private bool SaveRaiinListInf(List<OrdInfModel> ordInfs, int userId)
    {
        // Check input list todayOdrInfModels
        if (ordInfs.Count == 0)
        {
            return true;
        }

        int hpId = ordInfs[0].HpId;
        long raiinNo = ordInfs[0].RaiinNo;
        long ptId = ordInfs[0].PtId;
        int sinDate = ordInfs[0].SinDate;

        // Get Raiin List Inf
        var raiinListInfs = TrackingDataContext.RaiinListInfs.Where(item => item.HpId == hpId
                                                            && item.RaiinNo == raiinNo
                                                            && item.PtId == ptId
                                                            && item.SinDate == sinDate).ToList();

        // Get KouiKbnMst
        var kouiKbnMst = TrackingDataContext.KouiKbnMsts.ToList();

        // Get Raiin List
        var raiinListMstList = TrackingDataContext.RaiinListMsts.Where(item => item.HpId == hpId && item.IsDeleted == 0).ToList();
        var raiinListDetailList = TrackingDataContext.RaiinListDetails.Where(item => item.HpId == hpId && item.IsDeleted == 0).ToList();
        var raiinListKouiList = TrackingDataContext.RaiinListKouis.Where(item => item.HpId == hpId && item.IsDeleted == 0).ToList();
        var raiinListItemList = TrackingDataContext.RaiinListItems.Where(item => item.HpId == hpId && item.IsDeleted == 0).ToList();

        // Filter GrpId
        // Get all raiin list master contain item and koui
        List<string> itemList = new();
        List<int> kouiList = new();
        foreach (var odr in ordInfs)
        {
            foreach (var odrInfDetail in odr.OrdInfDetails)
            {
                if (string.IsNullOrEmpty(odrInfDetail.ItemCd) || odrInfDetail.SinKouiKbn == 0) continue;
                if (!itemList.Contains(odrInfDetail.ItemCd) && !string.IsNullOrEmpty(odrInfDetail.ItemCd))
                {
                    itemList.Add(odrInfDetail.ItemCd);
                }
                if (!kouiList.Contains(odrInfDetail.SinKouiKbn))
                {
                    kouiList.Add(odrInfDetail.SinKouiKbn);
                }
            }
        }
        var kouiIdList = kouiKbnMst.FindAll(item => kouiList.Contains(item.KouiKbn1) || kouiList.Contains(item.KouiKbn2)).Select(item => item.KouiKbnId);
        var grpIdByItemList = raiinListItemList.FindAll(item => item.ItemCd != null && itemList.Contains(item.ItemCd)).Select(item => item.GrpId).Distinct().ToList();
        var grpIdByKouiKbnList = raiinListKouiList.FindAll(item => kouiIdList.Contains(item.KouiKbnId)).Select(item => item.GrpId).Distinct().ToList();
        grpIdByItemList.AddRange(grpIdByKouiKbnList);
        raiinListMstList = raiinListMstList.FindAll(item => grpIdByItemList.Distinct().Contains(item.GrpId));

        // Define Added RaiinListInf
        List<RaiinListInf> raiinListInfList = new();
        bool IsDeleteExecute = false;
        // Process to raiin list inf
        foreach (var mst in raiinListMstList)
        {
            var detailList = raiinListDetailList.FindAll(item => item.GrpId == mst.GrpId);
            foreach (var detail in detailList)
            {
                HashSet<int> deleteKouiSet = new();
                HashSet<int> currentKouiSet = new();
                HashSet<string> deleteItemCdSet = new();
                HashSet<string> currentItemCdSet = new();
                var raiinListKouis = raiinListKouiList.FindAll(item => item.GrpId == detail.GrpId && item.KbnCd == detail.KbnCd);
                var raiinListItems = raiinListItemList.FindAll(item => item.GrpId == detail.GrpId && item.KbnCd == detail.KbnCd);
                var raiinListItemExclude = raiinListItemList.FindAll(item => item.GrpId == detail.GrpId && item.KbnCd == detail.KbnCd && item.IsExclude == 1).Select(item => item.ItemCd).ToList();
                foreach (var odr in ordInfs)
                {
                    foreach (var odrInfDetail in odr.OrdInfDetails)
                    {
                        if (string.IsNullOrEmpty(odrInfDetail.ItemCd) || odrInfDetail.SinKouiKbn == 0) continue;
                        if (odr.IsDeleted != 0 || raiinListItemExclude.Contains(odrInfDetail.ItemCd))
                        {
                            deleteKouiSet.Add(odrInfDetail.SinKouiKbn);
                            deleteItemCdSet.Add(odrInfDetail.ItemCd);
                            continue;
                        }
                        currentKouiSet.Add(odrInfDetail.SinKouiKbn);
                        currentItemCdSet.Add(odrInfDetail.ItemCd);
                    }
                }

                // Delete with SinKouiKbn
                foreach (int koui in deleteKouiSet.ToArray())
                {
                    if (currentKouiSet.Contains(koui))
                    {
                        continue;
                    }
                    var kouiMst = kouiKbnMst?.Find(item => item.KouiKbn1 == koui || item.KouiKbn2 == koui);
                    if (kouiMst == null) continue;

                    // Get List RaiinListKoui contains koui 
                    List<RaiinListKoui> kouiItemList = raiinListKouis.FindAll(item => item.KouiKbnId == kouiMst.KouiKbnId);
                    foreach (RaiinListKoui kouiItem in kouiItemList)
                    {
                        var raiinListInf = raiinListInfs?.Find(item => item.GrpId == kouiItem.GrpId
                                                                            && item.KbnCd == kouiItem.KbnCd
                                                                            && item.RaiinListKbn == RaiinListKbnConstants.KOUI_KBN);
                        if (raiinListInf != null)
                        {
                            TrackingDataContext.RaiinListInfs.Remove(raiinListInf);
                            raiinListInfs?.Remove(raiinListInf);
                            IsDeleteExecute = true;
                        }
                    }
                }

                // Delete with ItemCd
                foreach (string itemCd in deleteItemCdSet.ToArray())
                {
                    if (currentItemCdSet.Contains(itemCd))
                    {
                        continue;
                    }
                    List<RaiinListItem> itemCdList = raiinListItems.FindAll(item => item.ItemCd == itemCd);
                    foreach (RaiinListItem raiinListItem in itemCdList)
                    {
                        var raiinListInf = raiinListInfs?.Find(item => item.GrpId == raiinListItem.GrpId
                                                                       && item.KbnCd == raiinListItem.KbnCd
                                                                       && item.RaiinListKbn == RaiinListKbnConstants.ITEM_KBN);
                        if (raiinListInf != null)
                        {
                            TrackingDataContext.RaiinListInfs.Remove(raiinListInf);
                            raiinListInfs?.Remove(raiinListInf);
                            IsDeleteExecute = true;
                        }
                    }
                }


                // Add or Update with ItemCd
                foreach (string itemCd in currentItemCdSet.ToArray())
                {
                    List<RaiinListItem> itemCdList = raiinListItems.FindAll(item => item.ItemCd == itemCd);
                    foreach (RaiinListItem raiinListItem in itemCdList)
                    {
                        var raiinListInf = raiinListInfs?.Find(item => item.GrpId == raiinListItem.GrpId && item.RaiinListKbn == RaiinListKbnConstants.ITEM_KBN);
                        if (raiinListInf == null)
                        {
                            // Check contains with grpId
                            if (raiinListInfList.Find(item => item.RaiinNo == raiinNo
                                                             && item.SinDate == sinDate
                                                             && item.GrpId == raiinListItem.GrpId
                                                             && item.RaiinListKbn == RaiinListKbnConstants.ITEM_KBN) == null)
                            {
                                // create new 
                                raiinListInf = new RaiinListInf()
                                {
                                    HpId = hpId,
                                    PtId = ptId,
                                    RaiinNo = raiinNo,
                                    SinDate = sinDate,
                                    GrpId = raiinListItem.GrpId,
                                    KbnCd = raiinListItem.KbnCd,
                                    UpdateDate = CIUtil.GetJapanDateTimeNow(),
                                    UpdateId = userId,
                                    RaiinListKbn = RaiinListKbnConstants.ITEM_KBN
                                };
                                raiinListInfList.Add(raiinListInf);
                            }
                        }
                        else
                        {
                            // update
                            var originSortNo = detailList.Find(item => item.KbnCd == raiinListInf.KbnCd)?.SortNo;
                            var newSortNo = detailList.Find(item => item.KbnCd == raiinListItem.KbnCd)?.SortNo;
                            if (originSortNo == null || originSortNo > newSortNo)
                            {
                                raiinListInf.KbnCd = raiinListItem.KbnCd;
                                raiinListInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                                raiinListInf.UpdateId = userId;
                            }
                        }
                    }
                }

                // Add or Update with SinKouiKbn
                foreach (int koui in currentKouiSet.ToArray())
                {
                    var kouiMst = kouiKbnMst?.Find(item => item.KouiKbn1 == koui || item.KouiKbn2 == koui);
                    if (kouiMst == null) continue;

                    List<RaiinListKoui> kouiItemList = raiinListKouis.FindAll(item => item.KouiKbnId == kouiMst.KouiKbnId);
                    foreach (RaiinListKoui kouiItem in kouiItemList)
                    {
                        var raiinListInf = raiinListInfs?.Find(item => item.GrpId == kouiItem.GrpId && item.RaiinListKbn == RaiinListKbnConstants.KOUI_KBN);
                        if (raiinListInf == null)
                        {
                            // Check contains with grpId
                            if (raiinListInfList.Find(item => item.RaiinNo == raiinNo
                                                             && item.SinDate == sinDate
                                                             && item.GrpId == kouiItem.GrpId
                                                             && item.RaiinListKbn == RaiinListKbnConstants.KOUI_KBN) == null)
                            {
                                // create new 
                                raiinListInf = new RaiinListInf()
                                {
                                    HpId = hpId,
                                    PtId = ptId,
                                    RaiinNo = raiinNo,
                                    SinDate = sinDate,
                                    GrpId = kouiItem.GrpId,
                                    KbnCd = kouiItem.KbnCd,
                                    UpdateDate = CIUtil.GetJapanDateTimeNow(),
                                    UpdateId = userId,
                                    RaiinListKbn = RaiinListKbnConstants.KOUI_KBN
                                };
                                raiinListInfList.Add(raiinListInf);
                            }
                        }
                        else
                        {
                            // update
                            var originSortNo = detailList.Find(item => item.KbnCd == raiinListInf.KbnCd)?.SortNo;
                            var newSortNo = detailList.Find(item => item.KbnCd == kouiItem.KbnCd)?.SortNo;
                            if (originSortNo == null || originSortNo > newSortNo)
                            {
                                raiinListInf.KbnCd = kouiItem.KbnCd;
                                raiinListInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                                raiinListInf.UpdateId = userId;
                            }
                        }
                    }
                }
            }
        }
        if (raiinListInfList.Count > 0 || IsDeleteExecute)
        {
            TrackingDataContext.RaiinListInfs.AddRange(raiinListInfList);
        }
        TrackingDataContext.SaveChanges();
        return true;
    }

    private bool UpdateAdoptedInTenItem(int hpId, List<string> itemCds, int sinDate)
    {
        TrackingDataContext.TenMsts
        .Where(t =>
            t.HpId == hpId
            && itemCds.Contains(t.ItemCd)
            && sinDate >= t.StartDate && sinDate <= t.EndDate
            && t.IsAdopted == 0
            && t.IsDeleted == DeleteTypes.None
        )
        .ExecuteUpdate(setters => setters
            .SetProperty(t => t.IsAdopted, 1)
            .SetProperty(t => t.UpdateDate, CIUtil.GetJapanDateTimeNow())
        );

        TrackingDataContext.SaveChanges();
        return true;
    }

    private int SaveKarteEdition(int hpId, long ptId, int sinDate, long raiinNo, int karteStatus, int userId)
    {
        int karteEditionResult = 0;
        var timeNow = CIUtil.GetJapanDateTimeNow();
        var karteEdition = TrackingDataContext.KarteEditions.FirstOrDefault(
            k => k.HpId == hpId
            && k.PtId == ptId
            && k.RaiinNo == raiinNo
        );

        if (karteEdition == null)
        {
            karteEdition = new KarteEdition
            {
                HpId = hpId,
                PtId = ptId,
                SinDate = sinDate,
                RaiinNo = raiinNo,
                KarteStatus = karteStatus,
                CreateDate = timeNow,
                UpdateDate = timeNow,
                UpdateId = userId,
                CreateId = userId,
                Edition = 1,
            };
            if (karteStatus == KarteStatusConst.Official)
            {
                var approve_id = UpdateApproveInf(hpId, ptId, sinDate, raiinNo, karteStatus, userId, timeNow);
                karteEdition.ApprovalId = approve_id;
                if (approve_id != 0) karteEdition.ApprovalDate = timeNow;
            }
            TrackingDataContext.KarteEditions.Add(karteEdition);
            karteEditionResult = karteEdition.Edition;
        }
        else
        {
            // save old karte edition to DelKarteEdition
            var delKarteEdition = new DelKarteEdition
            {
                HpId = karteEdition.HpId,
                PtId = karteEdition.PtId,
                SinDate = karteEdition.SinDate,
                RaiinNo = karteEdition.RaiinNo,
                Edition = karteEdition.Edition,
                KarteStatus = karteEdition.KarteStatus,
                CreateDate = karteEdition.CreateDate,
                CreateId = karteEdition.CreateId,
                UpdateDate = karteEdition.UpdateDate,
                UpdateId = karteEdition.UpdateId,
                ApprovalDate = karteEdition.ApprovalDate,
                ApprovalId = karteEdition.ApprovalId
            };
            TrackingDataContext.DelKarteEditions.Add(delKarteEdition);

            // hard-delete old karte edition
            TrackingDataContext.KarteEditions.Remove(karteEdition);

            // create new karte edition
            var newKarteEdition = new KarteEdition
            {
                HpId = hpId,
                PtId = ptId,
                SinDate = sinDate,
                RaiinNo = raiinNo,
                KarteStatus = karteStatus,
                CreateDate = karteEdition.CreateDate,
                UpdateDate = timeNow,
                UpdateId = userId,
                CreateId = karteEdition.CreateId,
                Edition = karteEdition.Edition + 1,

            };
            if (karteStatus == KarteStatusConst.Official)
            {
                var approve_id = UpdateApproveInf(hpId, ptId, sinDate, raiinNo, karteStatus, userId, timeNow);
                newKarteEdition.ApprovalId = approve_id;
                if (approve_id != 0) newKarteEdition.ApprovalDate = timeNow;
            }

            TrackingDataContext.KarteEditions.Add(newKarteEdition);
            karteEditionResult = newKarteEdition.Edition;
        }
        TrackingDataContext.SaveChanges();
        return karteEditionResult;
    }

    private int UpdateApproveInf(int hpId, long ptId, int sinDate, long raiinNo, int karteStatus, int userId, DateTime timeNow)
    {
        var userMst = NoTrackingDataContext.UserMsts.FirstOrDefault(u => u.UserId == userId && u.HpId == hpId);
        var approveInf = TrackingDataContext.ApprovalInfs.FirstOrDefault(a => a.HpId == hpId && a.RaiinNo == raiinNo && a.IsDeleted == DeleteTypes.None);
        int approve_id = 0;
        if (approveInf != null)
        {
            approveInf.IsDeleted = DeleteTypes.Deleted;
            TrackingDataContext.ApprovalInfs.Update(approveInf);
        }

        if (userMst?.JobCd == JobCdConstant.Doctor)
        {
            TrackingDataContext.ApprovalInfs.Add(new ApprovalInf()
            {
                HpId = hpId,
                CreateDate = timeNow,
                CreateId = userId,
                IsDeleted = 0,
                PtId = ptId,
                RaiinNo = raiinNo,
                SinDate = sinDate,
                SeqNo = approveInf == null ? 1 : approveInf.SeqNo + 1,
                UpdateId = userId,
            });
            approve_id = userId;
        }
        TrackingDataContext.SaveChanges();
        return approve_id;
    }
    
    private bool UpdateApproveInfoWhenNotCreateEdition(int hpId, long ptId, int sinDate, long raiinNo, int userId)
    {
        var timeNow = CIUtil.GetJapanDateTimeNow();

        var karteEdition = TrackingDataContext.KarteEditions.FirstOrDefault(
            k => k.HpId == hpId
            && k.PtId == ptId
            && k.RaiinNo == raiinNo
        );

        if (karteEdition == null)
            return false;

        if (karteEdition.ApprovalId == 0)
        {
            TrackingDataContext.ApprovalInfs.Add(new ApprovalInf()
            {
                HpId = hpId,
                CreateDate = timeNow,
                CreateId = userId,
                IsDeleted = 0,
                PtId = ptId,
                RaiinNo = raiinNo,
                SinDate = sinDate,
                SeqNo = 1,
                UpdateId = userId,
                UpdateDate = timeNow
            });
            karteEdition.ApprovalId = userId;
            karteEdition.ApprovalDate = timeNow;
            TrackingDataContext.KarteEditions.Update(karteEdition);
            TrackingDataContext.SaveChanges();
        }
        return true;
    }
    
    private void SaveDelOdrInfs(List<OdrInf> ordInfs, List<OdrInfDetail> odrInfDetails)
    {
        var delOdrInfs = ordInfs.Select(o => new DelOdrInf
        {
            HpId = o.HpId,
            PtId = o.PtId,
            SinDate = o.SinDate,
            RaiinNo = o.RaiinNo,
            RpNo = o.RpNo,
            RpEdaNo = o.RpEdaNo,
            Id = o.Id,
            HokenPid = o.HokenPid,
            OdrKouiKbn = o.OdrKouiKbn,
            RpName = o.RpName,
            InoutKbn = o.InoutKbn,
            SikyuKbn = o.SikyuKbn,
            SyohoSbt = o.SyohoSbt,
            SanteiKbn = o.SanteiKbn,
            TosekiKbn = o.TosekiKbn,
            DaysCnt = o.DaysCnt,
            SortNo = o.SortNo,
            IsDeleted = o.IsDeleted,
            CreateDate = o.CreateDate,
            CreateId = o.CreateId,
            UpdateDate = o.UpdateDate,
            UpdateId = o.UpdateId,
            CreateMachine = o.CreateMachine,
            UpdateMachine = o.UpdateMachine,
            Edition = o.Edition
        });

        var delOdrInfDetails = odrInfDetails.Select(od => new DelOdrInfDetail
        {
            HpId = od.HpId,
            PtId = od.PtId,
            SinDate = od.SinDate,
            RaiinNo = od.RaiinNo,
            RpNo = od.RpNo,
            RpEdaNo = od.RpEdaNo,
            RowNo = od.RowNo,
            SinKouiKbn = od.SinKouiKbn,
            ItemCd = od.ItemCd,
            ItemName = od.ItemName,
            Suryo = od.Suryo,
            UnitName = od.UnitName,
            UnitSBT = od.UnitSBT,
            TermVal = od.TermVal,
            KohatuKbn = od.KohatuKbn,
            SyohoKbn = od.SyohoKbn,
            SyohoLimitKbn = od.SyohoLimitKbn,
            DrugKbn = od.DrugKbn,
            YohoKbn = od.YohoKbn,
            Kokuji1 = od.Kokuji1,
            Kokiji2 = od.Kokiji2,
            IsNodspRece = od.IsNodspRece,
            IpnCd = od.IpnCd,
            IpnName = od.IpnName,
            JissiKbn = od.JissiKbn,
            JissiDate = od.JissiDate == DateTime.MinValue ? null : DateTime.SpecifyKind(od.JissiDate ?? DateTime.UtcNow, DateTimeKind.Utc),
            JissiId = od.JissiId,
            JissiMachine = od.JissiMachine,
            ReqCd = od.ReqCd,
            Bunkatu = od.Bunkatu,
            CmtName = od.CmtName,
            CmtOpt = od.CmtOpt,
            FontColor = od.FontColor,
            CommentNewline = od.CommentNewline,
            Edition = od.Edition,
            BikoComment = od.BikoComment,
            RelationItem = od.RelationItem,
        });

        TrackingDataContext.DelOdrInfs.AddRange(delOdrInfs);
        TrackingDataContext.DelOdrInfDetails.AddRange(delOdrInfDetails);
    }

    public PtHokenPatternModel UpsertPtHokenPattern(int hpId, long ptId, PtHokenPatternModel ptHokenPatternModel, int userId)
    {
        var ptHokenPattern = TrackingDataContext.PtHokenPatterns.FirstOrDefault(
            p => p.HpId == hpId
            && p.PtId == ptId
            && p.HokenId == ptHokenPatternModel.HokenId
            && p.Kohi1Id == ptHokenPatternModel.Kohi1Id
            && p.Kohi2Id == ptHokenPatternModel.Kohi2Id
            && p.Kohi3Id == ptHokenPatternModel.Kohi3Id
            && p.Kohi4Id == ptHokenPatternModel.Kohi4Id
        );

        if (ptHokenPattern == null)
        {
            // query max HokenPid from pt_hoken_pattern. if not exist then maxHokenPid = 0
            var hokenPids = TrackingDataContext.PtHokenPatterns
                .Where(p => p.HpId == hpId && p.PtId == ptId)
                .Select(p => p.HokenPid);

            int maxHokenPid = hokenPids.Any() ? hokenPids.Max() : 0;
            int hokenPid = maxHokenPid + 1;

            ptHokenPattern = new PtHokenPattern
            {
                HpId = hpId,
                PtId = ptId,
                HokenPid = hokenPid,
                HokenId = ptHokenPatternModel.HokenId,
                Kohi1Id = ptHokenPatternModel.Kohi1Id,
                Kohi2Id = ptHokenPatternModel.Kohi2Id,
                Kohi3Id = ptHokenPatternModel.Kohi3Id,
                Kohi4Id = ptHokenPatternModel.Kohi4Id,
                HokenKbn = ptHokenPatternModel.HokenKbn,
                HokenSbtCd = ptHokenPatternModel.HokenSbtCd,
                HokenMemo = ptHokenPatternModel.HokenMemo,
                StartDate = ptHokenPatternModel.StartDate,
                EndDate = ptHokenPatternModel.EndDate,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateId = userId,
                CreateId = userId
            };
            TrackingDataContext.PtHokenPatterns.Add(ptHokenPattern);
            TrackingDataContext.SaveChanges();
        }
        return new PtHokenPatternModel(
            ptHokenPattern.PtId,
            ptHokenPattern.HokenPid,
            ptHokenPattern.SeqNo,
            ptHokenPattern.HokenKbn,
            ptHokenPattern.HokenSbtCd,
            ptHokenPattern.HokenId,
            ptHokenPattern.Kohi1Id,
            ptHokenPattern.Kohi2Id,
            ptHokenPattern.Kohi3Id,
            ptHokenPattern.Kohi4Id,
            ptHokenPattern.HokenMemo ?? string.Empty,
            ptHokenPattern.StartDate,
            ptHokenPattern.EndDate

        );
    }

    private bool UpsertOdrInfs(int hpId, long ptId, long raiinNo, int sinDate, int karteEdition, List<OrdInfModel> ordInfs, int userId, int status, bool isHasChange)
    {
        var oldOdrInfs = TrackingDataContext.OdrInfs.Where(
            o => o.HpId == hpId
            && o.PtId == ptId
            && o.RaiinNo == raiinNo
            && o.SinDate == sinDate
            && o.Edition == karteEdition - 1
            && o.OdrKouiKbn != 10
        ).ToList();

        var oldOdrInfDetails = TrackingDataContext.OdrInfDetails.Where(
            od => od.HpId == hpId
            && od.PtId == ptId
            && od.RaiinNo == raiinNo
            && od.SinDate == sinDate
            && od.Edition == karteEdition - 1
            && od.ItemCd != ItemCdConst.SyosaiKihon
            && od.ItemCd != ItemCdConst.JikanKihon
        ).ToList();

        // save history 
        SaveDelOdrInfs(oldOdrInfs, oldOdrInfDetails);

        // insert new ordInf
        var rpNoMax = GetMaxRpNo(hpId, ptId, raiinNo, sinDate);
        rpNoMax = rpNoMax < 2 ? 1 : rpNoMax;
        foreach (var item in ordInfs)
        {
            rpNoMax++;
            var ordInfEntity = new OdrInf
            {
                HpId = item.HpId,
                PtId = item.PtId,
                SinDate = item.SinDate,
                RaiinNo = item.RaiinNo,
                RpNo = rpNoMax,
                RpEdaNo = 1,
                Id = 0,
                HokenPid = item.HokenPid,
                OdrKouiKbn = item.OdrKouiKbn,
                RpName = item.RpName,
                InoutKbn = item.InoutKbn,
                SikyuKbn = item.SikyuKbn,
                SyohoSbt = item.SyohoSbt,
                SanteiKbn = item.SanteiKbn,
                TosekiKbn = item.TosekiKbn,
                DaysCnt = item.DaysCnt,
                SortNo = item.SortNo,
                CreateDate = CIUtil.GetJapanDateTimeNow(),
                CreateId = userId,
                UpdateDate = CIUtil.GetJapanDateTimeNow(),
                UpdateId = userId,
                Edition = karteEdition
            };

            var ordInfDetailEntity = item?.OrdInfDetails.Select(
                od => new OdrInfDetail
                {
                    HpId = od.HpId,
                    PtId = od.PtId,
                    SinDate = od.SinDate,
                    RaiinNo = od.RaiinNo,
                    RpNo = ordInfEntity.RpNo,
                    RpEdaNo = 1,
                    RowNo = od.RowNo,
                    SinKouiKbn = od.SinKouiKbn,
                    ItemCd = od.ItemCd,
                    ItemName = od.ItemName,
                    Suryo = od.Suryo,
                    UnitName = od.UnitName,
                    UnitSBT = od.UnitSbt,
                    TermVal = od.TermVal,
                    KohatuKbn = od.KohatuKbn,
                    SyohoKbn = od.SyohoKbn,
                    SyohoLimitKbn = od.SyohoLimitKbn,
                    DrugKbn = od.DrugKbn,
                    YohoKbn = od.YohoKbn,
                    Kokuji1 = od.Kokuji1,
                    Kokiji2 = od.Kokuji2,
                    IsNodspRece = od.IsNodspRece,
                    IpnCd = od.IpnCd,
                    IpnName = od.IpnName,
                    JissiKbn = od.JissiKbn,
                    JissiDate = od.JissiDate == DateTime.MinValue ? null : DateTime.SpecifyKind(od.JissiDate, DateTimeKind.Utc),
                    JissiId = od.JissiId,
                    JissiMachine = od.JissiMachine,
                    ReqCd = od.ReqCd,
                    Bunkatu = od.Bunkatu,
                    CmtName = od.CmtName,
                    CmtOpt = od.CmtOpt,
                    FontColor = od.FontColor,
                    CommentNewline = od.CommentNewline,
                    Edition = karteEdition,
                    BikoComment = od.BikoComment,
                    RelationItem = od.RelationItem
                }
            ) ?? new List<OdrInfDetail>();

            TrackingDataContext.OdrInfs.Add(ordInfEntity);
            TrackingDataContext.OdrInfDetails.AddRange(ordInfDetailEntity);
        }

        // hard-delete old ordInf and ordInfDetails 
        TrackingDataContext.OdrInfs.RemoveRange(oldOdrInfs);
        TrackingDataContext.OdrInfDetails.RemoveRange(oldOdrInfDetails);

        TrackingDataContext.SaveChanges();
        return true;
    }

    private void SaveDelKarteInf(KarteInf karte)
    {
        var delKarteInf = new DelKarteInf
        {
            HpId = karte.HpId,
            PtId = karte.PtId,
            SinDate = karte.SinDate,
            RaiinNo = karte.RaiinNo,
            KarteKbn = karte.KarteKbn,
            SeqNo = karte.SeqNo,
            Text = karte.Text,
            RichText = karte.RichText,
            IsDeleted = karte.IsDeleted,
            CreateDate = karte.CreateDate,
            CreateId = karte.CreateId,
            UpdateDate = karte.UpdateDate,
            UpdateId = karte.UpdateId,
            Edition = karte.Edition
        };

        TrackingDataContext.DelKarteInfs.Add(delKarteInf);
    }

    private bool UpsertKarteInfs(KarteInfModel karte, int userId, int karteEdition, byte status)
    {
        int hpId = karte.HpId;
        long ptId = karte.PtId;
        long raiinNo = karte.RaiinNo;
        int karteKbn = karte.KarteKbn;

        var seqNo = GetMaxSeqNo(ptId, hpId, raiinNo, karteKbn) + 1;

        if (karte.IsDeleted == DeleteTypes.Deleted || (string.IsNullOrEmpty(karte.Text) && string.IsNullOrEmpty(karte.RichText)))
        {
            var karteMst = TrackingDataContext.KarteInfs.FirstOrDefault(o => o.HpId == karte.HpId && o.PtId == karte.PtId && o.RaiinNo == karte.RaiinNo && o.KarteKbn == 1);
            if (karteMst != null)
            {
                // save history
                SaveDelKarteInf(karteMst);

                karteMst.IsDeleted = DeleteTypes.Deleted;
                karteMst.UpdateDate = CIUtil.GetJapanDateTimeNow();
                karteMst.UpdateId = userId;
                karteMst.Edition = karteEdition;
            }
        }
        else
        {
            var karteMst = TrackingDataContext.KarteInfs.OrderByDescending(k => k.SeqNo).FirstOrDefault(o => o.HpId == karte.HpId && o.PtId == karte.PtId && o.RaiinNo == karte.RaiinNo && o.KarteKbn == 1 && o.IsDeleted == DeleteTypes.None);

            if (karteMst == null)
            {
                if (!string.IsNullOrEmpty(karte.Text) && !string.IsNullOrEmpty(karte.RichText))
                {
                    var karteEntity = new KarteInf
                    {
                        HpId = karte.HpId,
                        PtId = karte.PtId,
                        SinDate = karte.SinDate,
                        RaiinNo = karte.RaiinNo,
                        KarteKbn = 1,
                        SeqNo = seqNo,
                        Text = karte.Text,
                        RichText = Encoding.UTF8.GetBytes(karte.RichText),
                        IsDeleted = karte.IsDeleted,
                        CreateDate = CIUtil.GetJapanDateTimeNow(),
                        CreateId = userId,
                        UpdateDate = CIUtil.GetJapanDateTimeNow(),
                        UpdateId = userId,
                        Edition = karteEdition
                    };

                    TrackingDataContext.KarteInfs.Add(karteEntity);
                }
            }
            else
            {
                // save history
                SaveDelKarteInf(karteMst);

                // karteMst.IsDeleted = status == RaiinState.Reservation ? DeleteTypes.Confirm : DeleteTypes.Deleted;

                // create new karteMst
                var karteEntity = new KarteInf
                {
                    HpId = karte.HpId,
                    PtId = karte.PtId,
                    SinDate = karte.SinDate,
                    RaiinNo = karte.RaiinNo,
                    KarteKbn = 1,
                    SeqNo = seqNo,
                    Text = karte.Text,
                    RichText = Encoding.UTF8.GetBytes(karte.RichText),
                    IsDeleted = karte.IsDeleted,
                    CreateDate = karteMst.CreateDate,
                    CreateId = karteMst.CreateId,
                    UpdateDate = CIUtil.GetJapanDateTimeNow(),
                    UpdateId = userId,
                    Edition = karteEdition
                };
                TrackingDataContext.KarteInfs.Add(karteEntity);

                // hard-delete old karteMst
                TrackingDataContext.KarteInfs.Remove(karteMst);
            }

        }

        TrackingDataContext.SaveChanges();
        return true;
    }

    private long GetMaxSeqNo(long ptId, int hpId, long raiinNo, int karteKbn)
    {
        var karteInf = NoTrackingDataContext.KarteInfs.Where(k => k.HpId == hpId && k.RaiinNo == raiinNo && k.KarteKbn == karteKbn && k.PtId == ptId).OrderByDescending(k => k.SeqNo).FirstOrDefault();

        return karteInf != null ? karteInf.SeqNo : 0;
    }

    public long GetMaxRpNo(int hpId, long ptId, long raiinNo, int sinDate)
    {
        var odrList = NoTrackingDataContext.OdrInfs
        .Where(odr => odr.HpId == hpId && odr.PtId == ptId && odr.RaiinNo == raiinNo && odr.SinDate == sinDate);

        if (odrList.Any())
        {
            return odrList.Max(odr => odr.RpNo);
        }

        return 0;
    }

    ///<summary>
    ///指定の月数後の日付を取得する
    ///休日の場合は、その前の休日以外の日付
    ///</summary>
    ///<param name="baseDate">基準日</param>
    ///<param name="term">月数</param>
    ///<returns>基準日の指定月数後の休日以外の日付</returns>
    public int MonthsAfterExcludeHoliday(int hpId, int baseDate, int term)
    {
        int retDate = CIUtil.MonthsAfter(baseDate, term);

        DateTime? dt;
        DateTime dt1;

        dt = CIUtil.SDateToDateTime(retDate);
        if (dt != null)
        {
            dt1 = (DateTime)dt;
            int i = 1;

            while (IsHoliday(hpId, CIUtil.DateTimeToInt(dt1)))
            {
                // 休日の場合、1日前に移動
                dt1 = dt1.AddDays(-1);
                i++;
                if (i > 31)
                {
                    break;
                }
            }
            retDate = CIUtil.DateTimeToInt(dt1);
        }
        return retDate;
    }

    private bool IsHoliday(int hpId, int datetime)
    {
        var holidayMst = NoTrackingDataContext.HolidayMsts
            .Where(p =>
                p.HpId == hpId &&
                p.SinDate == datetime &&
                p.HolidayKbn > 0 &&
                p.KyusinKbn > 0 &&
                p.IsDeleted != DeleteTypes.Deleted)
            .FirstOrDefault();
        return holidayMst != null;
    }

    /// <summary>
    /// 指定の期間に指定の項目が何回算定されているかカウントする
    /// ※複数項目用
    /// </summary>
    /// <param name="ptId">患者ID</param>
    /// <param name="startDate">カウント開始日</param>
    /// <param name="endDate">カウント終了日</param>
    /// <param name="sinDate">診療日（除外する日）</param>
    /// <param name="itemCds">カウントする項目のリスト</param>
    /// <param name="santeiKbn">算定区分</param>
    /// <returns>算定回数</returns>
    public double SanteiCount(int hpId, long ptId, int startDate, int endDate, int sinDate, long raiinNo, List<string> itemCds, List<int> santeiKbns, List<int> hokenKbns)
    {
        int startYm = startDate / 100;
        int endYm = endDate / 100;

        List<int> checkHokenKbn = new List<int>();

        if (hokenKbns != null)
        {
            checkHokenKbn = hokenKbns;
        }

        List<int> checkSanteiKbn = new List<int>();

        if (santeiKbns != null)
        {
            checkSanteiKbn = santeiKbns;
        }

        var sinRpInfs = NoTrackingDataContext.SinRpInfs.Where(o =>
            o.HpId == hpId &&
            o.PtId == ptId &&
            o.SinYm >= startYm &&
            o.SinYm <= endYm &&
            checkHokenKbn.Contains(o.HokenKbn) &&
            checkSanteiKbn.Contains(o.SanteiKbn)
        ).AsQueryable();
        var sinKouiCounts = NoTrackingDataContext.SinKouiCounts.Where(o =>
            o.HpId == hpId &&
            o.PtId == ptId &&
            o.SinDate >= startDate &&
            o.SinDate <= endDate &&
            o.RaiinNo != raiinNo);
        var sinKouiDetails = NoTrackingDataContext.SinKouiDetails.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptId &&
            p.SinYm >= startYm &&
            p.SinYm <= endYm &&
            p.IsDeleted == 0 &&
            itemCds.Contains(p.ItemCd ?? string.Empty) &&
            p.FmtKbn != 10  // 在がん医総のダミー項目を除く
            );

        if (raiinNo == 0)
        {
            var sinKouis = NoTrackingDataContext.SinKouis.Where(o => o.HpId == hpId &&
                                                             o.PtId == ptId &&
                                                             o.IsDeleted == 0);

            var joinSinkouiWithSinKouiCount = from sinKouiCount in sinKouiCounts
                                              join sinKoui in sinKouis on
                new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.SinYm, sinKouiCount.RpNo, sinKouiCount.SeqNo } equals
                new { sinKoui.HpId, sinKoui.PtId, sinKoui.SinYm, sinKoui.RpNo, sinKoui.SeqNo }
                                              select new
                                              {
                                                  SinKouiCount = sinKouiCount,
                                                  SinKoui = sinKoui
                                              };

            var joinQuery = (
                from sinKouiDetail in sinKouiDetails
                join joinSinkouiCount in joinSinkouiWithSinKouiCount.Where(p => p.SinKoui.IsNodspRece == 0) on
                    new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.SinYm, sinKouiDetail.RpNo, sinKouiDetail.SeqNo } equals
                    new { joinSinkouiCount.SinKouiCount.HpId, joinSinkouiCount.SinKouiCount.PtId, joinSinkouiCount.SinKouiCount.SinYm, joinSinkouiCount.SinKouiCount.RpNo, joinSinkouiCount.SinKouiCount.SeqNo }
                join sinRpInf in sinRpInfs on
                    new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.SinYm, sinKouiDetail.RpNo } equals
                    new { sinRpInf.HpId, sinRpInf.PtId, sinRpInf.SinYm, sinRpInf.RpNo }
                where
                    sinKouiDetail.HpId == hpId &&
                    sinKouiDetail.PtId == ptId &&
                    sinKouiDetail.SinYm >= startYm &&
                    sinKouiDetail.SinYm <= endYm &&
                    itemCds.Contains(sinKouiDetail.ItemCd ?? string.Empty) &&
                    joinSinkouiCount.SinKouiCount.SinDate >= startDate &&
                    joinSinkouiCount.SinKouiCount.SinDate <= endDate
                group new { sinKouiDetail, joinSinkouiCount } by new { joinSinkouiCount.SinKouiCount.HpId } into A
                select new { sum = A.Sum(a => (double)a.joinSinkouiCount.SinKouiCount.Count * (a.sinKouiDetail.Suryo <= 0 || ItemCdConst.ZaitakuTokushu.Contains(a.sinKouiDetail.ItemCd ?? string.Empty) ? 1 : a.sinKouiDetail.Suryo)) }
            );

            var result = joinQuery.ToList();

            if (result.Any())
            {
                return result.FirstOrDefault()?.sum ?? 0;
            }
            else
            {
                return 0;
            }
        }
        else
        {
            var joinQuery = (
                from sinKouiDetail in sinKouiDetails
                join sinKouiCount in sinKouiCounts on
                    new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.SinYm, sinKouiDetail.RpNo, sinKouiDetail.SeqNo } equals
                    new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.SinYm, sinKouiCount.RpNo, sinKouiCount.SeqNo }
                join sinRpInf in sinRpInfs on
                    new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.SinYm, sinKouiDetail.RpNo } equals
                    new { sinRpInf.HpId, sinRpInf.PtId, sinRpInf.SinYm, sinRpInf.RpNo }
                where
                    sinKouiDetail.HpId == hpId &&
                    sinKouiDetail.PtId == ptId &&
                    sinKouiDetail.SinYm >= startYm &&
                    sinKouiDetail.SinYm <= endYm &&
                    itemCds.Contains(sinKouiDetail.ItemCd ?? string.Empty) &&
                    sinKouiCount.SinDate >= startDate &&
                    sinKouiCount.SinDate <= endDate &&
                    sinKouiCount.RaiinNo != raiinNo
                group new { sinKouiDetail, sinKouiCount } by new { sinKouiCount.HpId } into A
                select new { sum = A.Sum(a => (double)a.sinKouiCount.Count * (a.sinKouiDetail.Suryo <= 0 || ItemCdConst.ZaitakuTokushu.Contains(a.sinKouiDetail.ItemCd ?? string.Empty) ? 1 : a.sinKouiDetail.Suryo)) }
            );

            var result = joinQuery.ToList();

            if (result.Any())
            {
                return result.FirstOrDefault()?.sum ?? 0;
            }
            else
            {
                return 0;
            }
        }
    }

    public List<DensiSanteiKaisuModel> FindDensiSanteiKaisuList(int hpId, List<string> itemCds, int minSinDate, int maxSinDate)
    {
        List<int> unitCds = new List<int> { 53, 121, 131, 138, 141, 142, 143, 144, 145, 146, 147, 148, 997, 998, 999 };

        var entities = NoTrackingDataContext.DensiSanteiKaisus.Where((x) =>
                x.HpId == hpId &&
                itemCds.Contains(x.ItemCd) &&
                x.StartDate <= minSinDate &&
                x.EndDate >= maxSinDate &&
                x.IsInvalid == 0 &&
                unitCds.Contains(x.UnitCd)
            ).ToList();

        List<DensiSanteiKaisuModel> results = new List<DensiSanteiKaisuModel>();
        entities?.ForEach(entity =>
        {
            results.Add(new DensiSanteiKaisuModel(entity.Id, entity.HpId, entity.ItemCd, entity.UnitCd, entity.MaxCount, entity.SpJyoken, entity.StartDate, entity.EndDate, entity.SeqNo, entity.UserSetting, entity.TargetKbn, entity.TermCount, entity.TermSbt, entity.IsInvalid, entity.ItemGrpCd));
        });

        return results;
    }

    private string GetSanteiItemCd(int hpId, string itemCd, int sinDate)
    {
        var tenMst = NoTrackingDataContext.TenMsts.FirstOrDefault(p => p.HpId == hpId &&
                                                                              p.ItemCd == itemCd &&
                                                                              p.StartDate <= sinDate &&
                                                                              p.EndDate >= sinDate);
        if (tenMst != null)
        {
            return tenMst.SanteiItemCd ?? string.Empty;
        }
        return string.Empty;
    }

    public List<CheckedDiseaseModel> GetTekiouByomeiByOrder(int hpId, List<string> itemCds)
    {
        var tekiouByomeiMsts = NoTrackingDataContext.TekiouByomeiMsts.Where(p => p.HpId == hpId && itemCds.Contains(p.ItemCd) && p.IsInvalid == 0);
        var byomeiMsts = NoTrackingDataContext.ByomeiMsts.Where(p => p.HpId == hpId);

        var query = from tekiByomei in tekiouByomeiMsts
                    join byomeiMst in byomeiMsts
                    on tekiByomei.ByomeiCd equals byomeiMst.ByomeiCd
                    select new
                    {
                        ByomeiMst = byomeiMst,
                        TekiByomei = tekiByomei
                    };
        List<CheckedDiseaseModel> ptByomeiModels = new();
        foreach (var entity in query.Select(item => item.ByomeiMst).ToList())
        {
            if (!ptByomeiModels.Any(p => p.ByomeiMst.ByomeiCd == entity.ByomeiCd))
            {
                ptByomeiModels.Add(new CheckedDiseaseModel(entity.SikkanCd, entity.NanbyoCd, entity.Byomei ?? string.Empty, 0, new PtDiseaseModel(
                    entity.ByomeiCd, entity.Byomei ?? string.Empty, entity.SikkanCd, entity.Icd101 ?? string.Empty, entity.Icd1012013 ?? string.Empty,
                    entity.Icd1012013 ?? string.Empty, entity.Icd1022013 ?? string.Empty
                    ), new ByomeiMstModel(entity.ByomeiCd, string.Empty, entity.Sbyomei ?? string.Empty, entity.KanaName1 ?? string.Empty, string.Empty, entity.NanbyoCd == NanbyoConst.Gairai ? "難病" : string.Empty, string.Empty, string.Empty, entity.IsAdopted == 1, entity.KanaName2 ?? string.Empty, entity.KanaName3 ?? string.Empty, entity.KanaName4 ?? string.Empty, entity.KanaName5 ?? string.Empty, entity.KanaName6 ?? string.Empty, entity.KanaName7 ?? string.Empty)));
            }
        }
        return ptByomeiModels;
    }

    public List<(int, int, List<Tuple<string, string, long>>)> GetAutoAddOrders(int hpId, long ptId, int sinDate, List<Tuple<int, int, string>> addingOdrList, List<Tuple<int, int, string, double, int>> currentOdrList)
    {
        List<string> itemCds = new();
        List<(int, int, List<Tuple<string, string, long>>)> autoItems = new();

        foreach (var itemCd in addingOdrList.Select(o => o.Item3))
        {
            itemCds.Add(itemCd);
        }

        var allSanteiGrpDetail = NoTrackingDataContext.SanteiGrpDetails
                                .Where(s => s.HpId == hpId && itemCds.Contains(s.ItemCd))
                                .ToList();
        foreach (var addingOrd in addingOdrList)
        {
            if (string.IsNullOrEmpty(addingOrd.Item3))
            {
                continue;
            }

            var santeiGrpDetails = allSanteiGrpDetail.Where(s => s.ItemCd == addingOrd.Item3).ToList();
            var santeiGrpCds = santeiGrpDetails.Select(s => s.SanteiGrpCd);

            if (santeiGrpDetails.Count == 0)
            {
                continue;
            }

            var santeiAutoOrders = NoTrackingDataContext.SanteiAutoOrders.Where(e =>
                                     e.HpId == hpId &&
                                     santeiGrpCds.Contains(e.SanteiGrpCd) &&
                                     e.StartDate <= sinDate &&
                                     e.EndDate >= sinDate).ToList();
            var santeiAutoOrderDetails = NoTrackingDataContext.SanteiAutoOrderDetails.Where(s => s.HpId == hpId && santeiGrpCds.Contains(s.SanteiGrpCd)).ToList();

            foreach (var santeiGrpDetail in santeiGrpDetails)
            {
                var santeiAutoOrder = santeiAutoOrders.FirstOrDefault(s => s.SanteiGrpCd == santeiGrpDetail.SanteiGrpCd && s.HpId == santeiGrpDetail.HpId);
                if (santeiAutoOrder == null)
                {
                    continue;
                }

                if (santeiAutoOrder.TermCnt == 1 && santeiAutoOrder.TermSbt == 4 && (santeiAutoOrder.CntType == 2 || santeiAutoOrder.CntType == 3))
                {
                    var santeiAutoOdrDetailList = santeiAutoOrderDetails.Where(s => s.SanteiGrpCd == santeiAutoOrder.SanteiGrpCd && s.SeqNo == santeiAutoOrder.SeqNo).ToList();
                    List<string> autoOdrDetailItemCdList = santeiAutoOdrDetailList.Select(s => s.ItemCd).Distinct().ToList();

                    if (santeiAutoOdrDetailList.Count == 0)
                    {
                        continue;
                    }

                    double santeiCntInMonth = 0;
                    foreach (var itemCd in autoOdrDetailItemCdList)
                    {
                        santeiCntInMonth += GetOdrCountInMonth(hpId, ptId, sinDate, itemCd);
                    }

                    double countInCurrentOdr = 0;

                    if (santeiAutoOrder.CntType == 2)
                    {
                        foreach (var item in from item in currentOdrList
                                             where autoOdrDetailItemCdList.Contains(item.Item3)
                                             select item)
                        {
                            if (item.Item5 == DeleteTypes.None)
                            {
                                countInCurrentOdr += (item.Item4 <= 0 || ItemCdConst.ZaitakuTokushu.Contains(item.Item3)) ? 1 : item.Item4;
                            }
                            else
                            {
                                countInCurrentOdr -= (item.Item4 <= 0 || ItemCdConst.ZaitakuTokushu.Contains(item.Item3)) ? 1 : item.Item4;
                            }
                        }
                    }
                    else
                    {
                        foreach (var item in from item in currentOdrList
                                             where autoOdrDetailItemCdList.Contains(item.Item3)
                                             select item)
                        {
                            if (item.Item5 == DeleteTypes.None)
                            {
                                countInCurrentOdr++;
                            }
                            else
                            {
                                countInCurrentOdr--;
                            }
                        }
                    }

                    double totalSanteiCount = santeiCntInMonth + countInCurrentOdr;

                    if (totalSanteiCount >= santeiAutoOrder.MaxCnt)
                    {
                        continue;
                    }

                    double countInAutoAdd = autoItems.Count;
                    if (totalSanteiCount + countInAutoAdd >= santeiAutoOrder.MaxCnt)
                    {
                        continue;
                    }

                    var autoAddItem = AutoAddItem(hpId, sinDate, santeiAutoOdrDetailList);
                    autoItems.Add(new(addingOrd.Item1, addingOrd.Item2, autoAddItem));
                }
            }
        }

        return autoItems;
    }

    public List<OrdInfModel> AutoAddOrders(int hpId, int userId, int sinDate, List<Tuple<int, int, string, int, int>> addingOdrList, List<Tuple<int, int, string, long>> autoAddItems)
    {
        List<OrdInfModel> autoAddOdr = new();
        List<string> itemCds = new();

        foreach (var autoAddItem in autoAddItems)
        {
            itemCds.Add(autoAddItem.Item3);
        }
        var kensaMsts = NoTrackingDataContext.KensaMsts.Where(t => t.HpId == hpId).ToList();
        var ipnKasanExcludes = NoTrackingDataContext.ipnKasanExcludes.Where(t => (t.StartDate <= sinDate && t.EndDate >= sinDate)).ToList();
        var ipnKasanExcludeItems = NoTrackingDataContext.ipnKasanExcludeItems.Where(t => (t.StartDate <= sinDate && t.EndDate >= sinDate)).ToList();
        var listYohoSets = NoTrackingDataContext.YohoSetMsts.Where(y => y.HpId == hpId && y.IsDeleted == 0 && y.UserId == userId).ToList();
        var itemCdYohos = listYohoSets?.Select(od => od.ItemCd ?? string.Empty);

        var tenMstYohos = NoTrackingDataContext.TenMsts.Where(t => t.HpId == hpId && t.IsNosearch == 0 && t.StartDate <= sinDate && t.EndDate >= sinDate && (itemCdYohos != null && itemCdYohos.Contains(t.ItemCd))).ToList();

        var checkKensaIrai = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2019 && p.GrpEdaNo == 0);
        var kensaIrai = checkKensaIrai?.Val ?? 0;
        var checkKensaIraiCondition = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2019 && p.GrpEdaNo == 1);
        var kensaIraiCondition = checkKensaIraiCondition?.Val ?? 0;


        var tenMsts = NoTrackingDataContext.TenMsts.Where(t => t.HpId == hpId && t.StartDate <= sinDate && t.EndDate >= sinDate && autoAddItems.Select(i => i.Item3).Contains(t.ItemCd)).ToList();
        var santeiAutoOdrDetailList = NoTrackingDataContext.SanteiAutoOrderDetails.Where(s => s.HpId == hpId && autoAddItems.Select(a => a.Item4).Contains(s.Id)).ToList();
        var kensaCenterMsts = NoTrackingDataContext.CommonKensaCenterMst.GroupBy(i => i.CenterCd).Select(g => g.First()).ToList();

        foreach (var addingOdr in addingOdrList)
        {
            var autoAddItem = autoAddItems.FirstOrDefault(i => i.Item1 == addingOdr.Item1 && i.Item2 == addingOdr.Item2);
            if (autoAddItem == null)
            {
                continue;
            }
            var targetItem = tenMsts.FirstOrDefault(t => t.ItemCd == autoAddItem?.Item3);
            OdrInf odrInf = new();
            odrInf.OdrKouiKbn = targetItem?.SinKouiKbn ?? 0;
            odrInf.SinDate = sinDate;
            odrInf.RpName = addingOdr.Item3;
            odrInf.InoutKbn = addingOdr.Item4;
            odrInf.DaysCnt = 1;

            var santeiAutoOdrDetail = santeiAutoOdrDetailList.FirstOrDefault(s => (autoAddItem != null && s.Id == autoAddItem.Item4));
            OdrInfDetail odrDetail = new OdrInfDetail();
            odrDetail.SinKouiKbn = targetItem?.SinKouiKbn ?? 0;
            odrDetail.SinDate = sinDate;
            odrDetail.ItemCd = autoAddItem?.Item3 ?? string.Empty;
            odrDetail.ItemName = targetItem?.Name ?? string.Empty;

            if (!string.IsNullOrEmpty(targetItem?.OdrUnitName))
            {
                odrDetail.UnitSBT = 1;
                odrDetail.UnitName = targetItem.OdrUnitName;
                odrDetail.TermVal = targetItem.OdrTermVal;
            }
            else if (!string.IsNullOrEmpty(targetItem?.CnvUnitName))
            {
                odrDetail.UnitSBT = 2;
                odrDetail.UnitName = targetItem.CnvUnitName;
                odrDetail.TermVal = targetItem.CnvTermVal;
            }
            else
            {
                odrDetail.UnitSBT = 0;
                odrDetail.UnitName = string.Empty;
                odrDetail.TermVal = 0;
            }

            odrDetail.KohatuKbn = targetItem?.KohatuKbn ?? 0;
            odrDetail.YohoKbn = targetItem?.YohoKbn ?? 0;
            odrDetail.DrugKbn = targetItem?.DrugKbn ?? 0;
            odrDetail.Suryo = string.IsNullOrEmpty(odrDetail.UnitName) ? 0 : santeiAutoOdrDetail?.Suryo ?? 0;

            var tenMst = tenMsts.FirstOrDefault(t => t.ItemCd == odrDetail.ItemCd);
            if (tenMst != null && string.IsNullOrEmpty(odrDetail.IpnCd)) odrDetail.IpnCd = tenMst.IpnNameCd;

            var kensaMst = tenMst == null ? null : kensaMsts.FirstOrDefault(k => k.KensaItemCd == tenMst.KensaItemCd && k.KensaItemSeqNo == tenMst.KensaItemSeqNo);

            var alternationIndex = addingOdr.Item2 % 2;

            var isGetPriceInYakka = IsGetPriceInYakka(tenMst, ipnKasanExcludes, ipnKasanExcludeItems);

            int kensaGaichu = GetKensaGaichu(odrDetail, tenMst, addingOdr.Item4, addingOdr.Item5, kensaMst, (int)kensaIraiCondition, (int)kensaIrai);

            var newOdr = ConvertToModel(odrInf, odrDetail, tenMst ?? new TenMst(), isGetPriceInYakka, alternationIndex, kensaGaichu, addingOdr.Item4, GetListYohoSetMstModelByUserID(listYohoSets ?? new List<YohoSetMst>(), tenMstYohos?.Where(t => t.SinKouiKbn == odrDetail.SinKouiKbn)?.ToList() ?? new List<TenMst>()), kensaMst ?? new(), kensaCenterMsts?.FirstOrDefault(k => k.CenterCd == tenMst?.CenterCd));
            autoAddOdr.Add(newOdr);
        }

        return autoAddOdr;
    }

    private static bool IsGetPriceInYakka(TenMst? tenMst, List<IpnKasanExclude> ipnKasanExcludes, List<IpnKasanExcludeItem> ipnKasanExcludeItems)
    {
        if (tenMst == null) return false;

        var ipnKasanExclude = ipnKasanExcludes.FirstOrDefault(u => u.IpnNameCd == tenMst.IpnNameCd);

        var ipnKasanExcludeItem = ipnKasanExcludeItems.FirstOrDefault(u => u.ItemCd == tenMst.ItemCd);

        return ipnKasanExclude == null && ipnKasanExcludeItem == null;
    }

    private static int GetKensaGaichu(OdrInfDetail? odrInfDetail, TenMst? tenMst, int inOutKbn, int odrKouiKbn, KensaMst? kensaMst, int kensaIraiCondition, int kensaIrai)
    {
        if (string.IsNullOrEmpty(odrInfDetail?.ItemCd) &&
               string.IsNullOrEmpty(odrInfDetail?.ItemName?.Trim()) &&
               odrInfDetail?.SinKouiKbn == 0)
        {
            return KensaGaichuTextConst.NONE;
        }

        if (odrInfDetail?.SinKouiKbn == 61 || odrInfDetail?.SinKouiKbn == 64)
        {
            bool kensaCondition;
            if (kensaIraiCondition == 0)
            {
                kensaCondition = (odrInfDetail.SinKouiKbn == 61 || odrInfDetail.SinKouiKbn == 64) && odrInfDetail.Kokiji2 != "7" && odrInfDetail.Kokiji2 != "9";
            }
            else
            {
                kensaCondition = odrInfDetail.SinKouiKbn == 61 && odrInfDetail.Kokiji2 != "7" && odrInfDetail.Kokiji2 != "9" && (tenMst == null ? 0 : tenMst.HandanGrpKbn) != 6;
            }

            if (kensaCondition && inOutKbn == 1)
            {
                int kensaSetting = kensaIrai;
                if (kensaMst == null || kensaMst.IsDelete == 1)
                {
                    if (kensaSetting > 0)
                    {
                        return KensaGaichuTextConst.GAICHU_NONE;
                    }
                }
                else if (string.IsNullOrEmpty(kensaMst.CenterItemCd1)
                    && string.IsNullOrEmpty(kensaMst.CenterItemCd2) && kensaSetting > 1)
                {
                    return KensaGaichuTextConst.GAICHU_NOT_SET;
                }
            }
        }

        if (!string.IsNullOrEmpty(odrInfDetail?.ItemName) && string.IsNullOrEmpty(odrInfDetail.ItemCd))
        {
            if (inOutKbn == 1 && (odrKouiKbn >= 20 && odrKouiKbn <= 23) || odrKouiKbn == 28)
            {
                if (odrInfDetail.IsNodspRece == 0)
                {
                    return KensaGaichuTextConst.IS_DISPLAY_RECE_ON;
                }
            }
            else
            {
                if (odrInfDetail.IsNodspRece == 1)
                {
                    return KensaGaichuTextConst.IS_DISPLAY_RECE_OFF;
                }
            }
        }
        return KensaGaichuTextConst.NONE;
    }

    private static int GetKensaGaichu(OrdInfDetailModel? odrInfDetail, TenMst? tenMst, int inOutKbn, int odrKouiKbn, KensaMst? kensaMst, int kensaIraiCondition, int kensaIrai)
    {
        if (string.IsNullOrEmpty(odrInfDetail?.ItemCd) &&
               string.IsNullOrEmpty(odrInfDetail?.ItemName?.Trim()) &&
               odrInfDetail?.SinKouiKbn == 0)
        {
            return KensaGaichuTextConst.NONE;
        }

        if (odrInfDetail?.SinKouiKbn == 61 || odrInfDetail?.SinKouiKbn == 64)
        {
            bool kensaCondition;
            if (kensaIraiCondition == 0)
            {
                kensaCondition = (odrInfDetail.SinKouiKbn == 61 || odrInfDetail.SinKouiKbn == 64) && odrInfDetail.Kokuji2 != "7" && odrInfDetail.Kokuji2 != "9";
            }
            else
            {
                kensaCondition = odrInfDetail.SinKouiKbn == 61 && odrInfDetail.Kokuji2 != "7" && odrInfDetail.Kokuji2 != "9" && (tenMst == null ? 0 : tenMst.HandanGrpKbn) != 6;
            }

            if (kensaCondition && inOutKbn == 1)
            {
                int kensaSetting = kensaIrai;
                if (kensaMst == null || kensaMst.IsDelete == 1)
                {
                    if (kensaSetting > 0)
                    {
                        return KensaGaichuTextConst.GAICHU_NONE;
                    }
                }
                else if (string.IsNullOrEmpty(kensaMst.CenterItemCd1)
                    && string.IsNullOrEmpty(kensaMst.CenterItemCd2) && kensaSetting > 1)
                {
                    return KensaGaichuTextConst.GAICHU_NOT_SET;
                }
            }
        }

        if (!string.IsNullOrEmpty(odrInfDetail?.ItemName) && string.IsNullOrEmpty(odrInfDetail.ItemCd))
        {
            if (inOutKbn == 1 && (odrKouiKbn >= 20 && odrKouiKbn <= 23) || odrKouiKbn == 28)
            {
                if (odrInfDetail.IsNodspRece == 0)
                {
                    return KensaGaichuTextConst.IS_DISPLAY_RECE_ON;
                }
            }
            else
            {
                if (odrInfDetail.IsNodspRece == 1)
                {
                    return KensaGaichuTextConst.IS_DISPLAY_RECE_OFF;
                }
            }
        }
        return KensaGaichuTextConst.NONE;
    }

    private static List<YohoSetMstModel> GetListYohoSetMstModelByUserID(List<YohoSetMst> listYohoSetMst, List<TenMst> listTenMst)
    {
        var query = from yoho in listYohoSetMst
                    join ten in listTenMst on yoho.ItemCd?.Trim() equals ten.ItemCd.Trim()
                    select new
                    {
                        Yoho = yoho,
                        ItemName = ten.Name,
                        ten.YohoKbn
                    };

        return query.OrderBy(u => u.Yoho.SortNo).AsEnumerable().Select(u => new YohoSetMstModel(u.ItemName, u.YohoKbn, u.Yoho?.SetId ?? 0, u.Yoho?.UserId ?? 0, u.Yoho?.ItemCd ?? string.Empty)).ToList();
    }

    private double GetOdrCountInMonth(int hpId, long ptId, int sinDate, string itemCd)
    {
        int firstDayOfSinDate = sinDate / 100 * 100 + 1;
        DateTime firstDaySinDateDateTime = CIUtil.IntToDate(firstDayOfSinDate);
        DateTime lastDayOfPrevMonthDateTime = firstDaySinDateDateTime.AddDays(-1);
        int lastDayOfPrevMonth = CIUtil.DateTimeToInt(lastDayOfPrevMonthDateTime);

        var odrInfQuery = NoTrackingDataContext.OdrInfs
          .Where(odr => odr.HpId == hpId && odr.PtId == ptId && odr.SinDate > lastDayOfPrevMonth && odr.SinDate <= sinDate && odr.OdrKouiKbn != 10 && odr.IsDeleted == 0);
        var odrInfDetailQuery = NoTrackingDataContext.OdrInfDetails
          .Where(odrDetail => odrDetail.HpId == hpId && odrDetail.PtId == ptId
          && odrDetail.SinDate > lastDayOfPrevMonth
          && odrDetail.SinDate <= sinDate
          && odrDetail.ItemCd == itemCd);

        var odrJoinDetail = from odrInf in odrInfQuery.AsEnumerable()
                            join odrDetail in odrInfDetailQuery
                            on new { odrInf.PtId, odrInf.RaiinNo, odrInf.RpNo, odrInf.RpEdaNo }
                            equals new { odrDetail.PtId, odrDetail.RaiinNo, odrDetail.RpNo, odrDetail.RpEdaNo }
                            into ListDetail
                            select new
                            {
                                OdrInf = odrInf,
                                OdrDetail = ListDetail
                            };
        var allDetailList = odrJoinDetail.AsEnumerable().Select(d => d.OdrDetail).ToList();
        List<OdrInfDetail> allDetail = new();
        foreach (var detailList in allDetailList)
        {
            allDetail.AddRange(detailList);
        }
        return allDetail.Sum(d => (d.Suryo <= 0 || ItemCdConst.ZaitakuTokushu.Contains(d.ItemCd ?? string.Empty)) ? 1 : d.Suryo);
    }

    private List<Tuple<string, string, long>> AutoAddItem(int hpId, int sinDate, List<SanteiAutoOrderDetail> santeiAutoOrderDetails)
    {
        List<Tuple<string, string, long>> autoItemList = new();
        var autoItems = santeiAutoOrderDetails.Select(s => s.ItemCd);

        var tenMsts = NoTrackingDataContext.TenMsts.Where(p =>
               p.HpId == hpId &&
               p.StartDate <= sinDate &&
               p.EndDate >= sinDate &&
               autoItems.Contains(p.ItemCd)).ToList();

        foreach (var santeiAutoOrderDetail in santeiAutoOrderDetails)
        {
            var tenItem = tenMsts.FirstOrDefault(t => t.ItemCd == santeiAutoOrderDetail.ItemCd);
            autoItemList.Add(new Tuple<string, string, long>(santeiAutoOrderDetail.ItemCd, tenItem?.Name ?? string.Empty, santeiAutoOrderDetail.Id));
        }

        return autoItemList;
    }

    private static OrdInfModel ConvertToModel(OdrInf ordInf, OdrInfDetail odrInfDetail, TenMst tenMst, bool isGetPriceInYakka, int alternationIndex, int kensaGaichu, int inOutKbn, List<YohoSetMstModel> yohoSets, KensaMst kensaMst, CommonKensaCenterMst? commonCenterKensaMst = null)
    {
        var ordDetail = new OrdInfDetailModel(
                            odrInfDetail.HpId,
                            odrInfDetail.RaiinNo,
                            odrInfDetail.RpNo,
                            odrInfDetail.RpEdaNo,
                            odrInfDetail.RowNo,
                            odrInfDetail.PtId,
                            odrInfDetail.SinDate,
                            odrInfDetail.SinKouiKbn,
                            odrInfDetail.ItemCd ?? string.Empty,
                            odrInfDetail.ItemName ?? string.Empty,
                            odrInfDetail.Suryo,
                            odrInfDetail.UnitName ?? string.Empty,
                            odrInfDetail.UnitSBT,
                            odrInfDetail.TermVal,
                            odrInfDetail.KohatuKbn,
                            odrInfDetail.SyohoKbn,
                            odrInfDetail.SyohoLimitKbn,
                            odrInfDetail.DrugKbn,
                            odrInfDetail.YohoKbn,
                            odrInfDetail.Kokuji1 ?? string.Empty,
                            odrInfDetail.Kokiji2 ?? string.Empty,
                            odrInfDetail.IsNodspRece,
                            odrInfDetail.IpnCd ?? string.Empty,
                            odrInfDetail.IpnName ?? string.Empty,
                            odrInfDetail.JissiKbn,
                            odrInfDetail.JissiDate ?? DateTime.MinValue,
                            odrInfDetail.JissiId,
                            odrInfDetail.JissiMachine ?? string.Empty,
                            odrInfDetail.ReqCd ?? string.Empty,
                            odrInfDetail.Bunkatu ?? string.Empty,
                            odrInfDetail.CmtName ?? string.Empty,
                            odrInfDetail.CmtOpt ?? string.Empty,
                            odrInfDetail.FontColor ?? string.Empty,
                            odrInfDetail.CommentNewline,
                            tenMst?.MasterSbt ?? string.Empty,
                            inOutKbn,
                            0,
                            isGetPriceInYakka,
                            0,
                            0,
                            tenMst?.Ten ?? 0,
                            0,
                            alternationIndex,
                            kensaGaichu,
                            tenMst?.OdrTermVal ?? 0,
                            tenMst?.CnvTermVal ?? 0,
                            tenMst?.YjCd ?? string.Empty,
                            yohoSets ?? new List<YohoSetMstModel>(),
                            0,
                            0,
                            tenMst?.CnvUnitName ?? string.Empty,
                            tenMst?.OdrUnitName ?? string.Empty,
                            kensaMst?.CenterItemCd1 ?? string.Empty,
                            kensaMst?.CenterItemCd2 ?? string.Empty,
                            tenMst?.CmtColKeta1 ?? 0,
                            tenMst?.CmtColKeta2 ?? 0,
                            tenMst?.CmtColKeta3 ?? 0,
                            tenMst?.CmtColKeta4 ?? 0,
                            tenMst?.CmtCol2 ?? 0,
                            tenMst?.CmtCol3 ?? 0,
                            tenMst?.CmtCol4 ?? 0,
                            tenMst?.HandanGrpKbn ?? 0,
                            kensaMst == null,
                            null,
                            ordInf.OdrKouiKbn,
                            tenMst?.BuiKbn ?? 0,
                            tenMst?.RousaiKbn ?? 0,
                            tenMst?.CenterCd ?? string.Empty,
                            odrInfDetail.BikoComment,
                            commonCenterKensaMst?.DspCenterName ?? string.Empty,
                            odrInfDetail.RelationItem ?? string.Empty
                );

        return new OrdInfModel(ordInf.HpId,
                    ordInf.RaiinNo,
                    ordInf.RpNo,
                    ordInf.RpEdaNo,
                    ordInf.PtId,
                    ordInf.SinDate,
                    ordInf.HokenPid,
                    ordInf.OdrKouiKbn,
                    ordInf.RpName ?? string.Empty,
                    ordInf.InoutKbn,
                    ordInf.SikyuKbn,
                    ordInf.SyohoSbt,
                    ordInf.SanteiKbn,
                    ordInf.TosekiKbn,
                    ordInf.DaysCnt,
                    ordInf.SortNo,
                    ordInf.IsDeleted,
                    ordInf.Id,
                    new List<OrdInfDetailModel>() { ordDetail },
                    ordInf.CreateDate,
                    ordInf.CreateId,
                    string.Empty,
                    ordInf.UpdateDate,
                    ordInf.UpdateId,
                    string.Empty,
                    ordInf.CreateMachine ?? string.Empty,
                    ordInf.UpdateMachine ?? string.Empty
               );
    }

    public Dictionary<string, string> CheckNameChanged(List<OrdInfModel> odrInfModelList)
    {
        Dictionary<string, string> nameChanged = new Dictionary<string, string>();
        foreach (var odrInfModel in odrInfModelList)
        {
            CheckNameChanged(odrInfModel, ref nameChanged);
        }
        return nameChanged;
    }

    private void CheckNameChanged(OrdInfModel odrInfModel, ref Dictionary<string, string> nameChanged)
    {
        foreach (var detail in odrInfModel.OrdInfDetails.Where(o => !o.ItemCd.StartsWith("CO")))
        {
            if (string.IsNullOrEmpty(detail.ItemCd) || detail.IsDrugUsage || detail.IsNormalComment)
            {
                continue;
            }
            string newName = CheckNameChanged(odrInfModel.HpId, odrInfModel.SinDate, detail);
            if (!string.IsNullOrEmpty(newName))
            {
                string oldName;
                if (detail.HasCmtName && !detail.Is831Cmt)
                {
                    oldName = detail.CmtName.Trim();
                }
                else
                {
                    if (!detail.Is831Cmt)
                    {
                        oldName = detail.ItemName.Trim();
                    }
                    else
                    {
                        var tempOldName = detail.ItemName.Trim().Split("；").FirstOrDefault();
                        oldName = tempOldName != null ? tempOldName + "；" : string.Empty;
                    }
                }
                if (string.IsNullOrEmpty(oldName))
                {
                    continue;
                }
                if (!nameChanged.ContainsKey(oldName))
                {
                    nameChanged.Add(oldName, newName);
                }
            }
        }
    }

    private string CheckNameChanged(int hpId, int sinDate, OrdInfDetailModel detail)
    {
        string result = string.Empty;
        if (string.IsNullOrEmpty(detail.ItemCd) || detail.IsDrugUsage || detail.IsNormalComment)
        {
            return result;
        }

        var itemMst = NoTrackingDataContext.TenMsts.FirstOrDefault(p =>
               p.HpId == hpId &&
               p.StartDate <= sinDate &&
               p.EndDate >= sinDate &&
               p.ItemCd == detail.ItemCd);
        if (itemMst != null)
        {
            string oldName;

            if (detail.HasCmtName && !detail.Is831Cmt)
            {
                oldName = detail.CmtName.Trim() ?? string.Empty;
            }
            else
            {
                if (detail.Is831Cmt)
                {
                    var tempOldName = detail.ItemName.Trim().Split("；").FirstOrDefault();
                    oldName = tempOldName != null ? tempOldName + "；" : string.Empty;
                }
                else
                {
                    oldName = detail.ItemName?.Trim() ?? string.Empty;
                }
            }
            if (string.IsNullOrEmpty(oldName))
            {
                return result;
            }
            if (oldName != itemMst.Name?.Trim())
            {
                return itemMst.Name ?? string.Empty;
            }
        }

        return result;
    }

    public void ReleaseResource()
    {
        DisposeDataContext();
    }

    /// <summary>
    /// 外来リハ初再診チェック
    /// </summary>
    /// Item1: ItemCd, Item2: ItemName
    public List<(int type, string itemName, int lastDaySanteiRiha, string rihaItemName)> GetValidGairaiRiha(int hpId, long ptId, long raiinNo, int sinDate, int syosaiKbn, List<Tuple<string, string>> allOdrInfItems)
    {
        List<(int type, string itemName, int lastDaySanteiRiha, string rihaItemName)> result = new();
        var checkGairaiRiha = NoTrackingDataContext.SystemConfs.FirstOrDefault(p =>
              p.HpId == hpId && p.GrpCd == 2016 && p.GrpEdaNo == 0)?.Val ?? 0;

        if (checkGairaiRiha == 0)
        {
            result.Add(new(0, string.Empty, 0, string.Empty));
        }

        if (syosaiKbn != SyosaiConst.None && syosaiKbn != SyosaiConst.Jihi)
        {
            // 外来リハビリテーション診療料１
            int lastDaySanteiRiha1 = GetLastDaySantei(hpId, ptId, sinDate, raiinNo, ItemCdConst.IgakuGairaiRiha1);
            if (lastDaySanteiRiha1 != 0)
            {
                int tgtDay = CIUtil.SDateInc(lastDaySanteiRiha1, 6);
                if (lastDaySanteiRiha1 <= sinDate && tgtDay >= sinDate)
                {
                    //前回算定日より7日以内の場合
                    string itemName = NoTrackingDataContext.TenMsts.FirstOrDefault(p =>
                                       p.HpId == hpId &&
                                       p.StartDate <= sinDate &&
                                       p.EndDate >= sinDate &&
                                       p.ItemCd == ItemCdConst.IgakuGairaiRiha1)?.Name ?? string.Empty;

                    result.Add(new(1, itemName, lastDaySanteiRiha1, string.Empty));
                }
            }

        }

        if (syosaiKbn != SyosaiConst.None && syosaiKbn != SyosaiConst.Jihi)
        {
            // 外来リハビリテーション診療料２
            int lastDaySanteiRiha2 = GetLastDaySantei(hpId, ptId, sinDate, raiinNo, ItemCdConst.IgakuGairaiRiha2);
            if (lastDaySanteiRiha2 != 0)
            {
                int tgtDay = CIUtil.SDateInc(lastDaySanteiRiha2, 13);
                if (lastDaySanteiRiha2 <= sinDate && tgtDay >= sinDate)
                {
                    //前回算定日より14日以内の場合
                    string itemName = NoTrackingDataContext.TenMsts.FirstOrDefault(p =>
                                       p.HpId == hpId &&
                                       p.StartDate <= sinDate &&
                                       p.EndDate >= sinDate &&
                                       p.ItemCd == ItemCdConst.IgakuGairaiRiha2)?.Name ?? string.Empty;
                    result.Add(new(2, itemName, lastDaySanteiRiha2, string.Empty));
                }
            }
        }

        if (syosaiKbn != SyosaiConst.None && syosaiKbn != SyosaiConst.Jihi)
        {
            //外来リハビリテーション診療料がオーダーされているか？
            string rihaItemName = string.Empty;
            foreach (var allOdrInfItem in allOdrInfItems)
            {
                if (allOdrInfItem.Item1 == ItemCdConst.IgakuGairaiRiha1 || allOdrInfItem.Item1 == ItemCdConst.IgakuGairaiRiha2)
                {
                    rihaItemName = allOdrInfItem.Item2;
                    break;
                }
            }
            if (!string.IsNullOrEmpty(rihaItemName))
            {
                result.Add(new(3, string.Empty, 0, rihaItemName));

            }
        }

        return result;
    }

    /// <summary>
    /// 予防注射再診チェック
    /// </summary>
    /// <returns></returns>
    public (double systemSetting, bool isExistYoboItemOnly) GetValidJihiYobo(int hpId, int syosaiKbn, int sinDate, List<string> itemCds)
    {
        var systemSetting = NoTrackingDataContext.SystemConfs.FirstOrDefault(p =>
              p.HpId == hpId && p.GrpCd == 2016 && p.GrpEdaNo == 2)?.Val ?? 0;

        bool isExistYoboItemOnly = false;

        if (syosaiKbn != SyosaiConst.None && syosaiKbn != SyosaiConst.Jihi)
        {
            itemCds = itemCds.Distinct().ToList();

            var tenMstItems = NoTrackingDataContext.TenMsts.Where(p =>
           p.HpId == hpId &&
           p.StartDate <= sinDate &&
           p.EndDate >= sinDate &&
           itemCds.Contains(p.ItemCd));
            foreach (var itemCd in itemCds)
            {
                isExistYoboItemOnly = true;
                bool hasItemDetail = false;

                if (!string.IsNullOrEmpty(itemCd) && !IsCommentMaster(itemCd))
                {
                    hasItemDetail = true;
                    var tenMstItem = tenMstItems.FirstOrDefault(t => t.ItemCd == itemCd) ?? new();
                    if (tenMstItem != null)
                    {
                        if (tenMstItem.JihiSbt == 0)
                        {
                            isExistYoboItemOnly = false;
                            break;
                        }
                        var jihiSbtItem = NoTrackingDataContext.JihiSbtMsts
                                        .FirstOrDefault(i => i.HpId == hpId
                                        && i.IsDeleted == DeleteTypes.None
                                        && i.JihiSbt == tenMstItem.JihiSbt);
                        if (jihiSbtItem != null)
                        {
                            if (jihiSbtItem.IsYobo != 1)
                            {
                                isExistYoboItemOnly = false;
                                break;
                            }
                        }
                        else
                        {
                            isExistYoboItemOnly = false;
                            break;
                        }
                    }
                }

                if (!hasItemDetail) // Contain comment only
                {
                    isExistYoboItemOnly = false;
                }
                if (hasItemDetail && !isExistYoboItemOnly)
                {
                    break;
                }
            }
        }

        return (systemSetting, isExistYoboItemOnly);
    }

    private bool IsCommentMaster(string itemCd)
    {
        return !string.IsNullOrEmpty(itemCd) && (itemCd.StartsWith(ItemCdConst.Comment820Pattern) ||

            itemCd.StartsWith(ItemCdConst.Comment830Pattern) || itemCd.StartsWith(ItemCdConst.Comment831Pattern)

            || itemCd.StartsWith(ItemCdConst.Comment850Pattern) || itemCd.StartsWith(ItemCdConst.Comment851Pattern) ||

            itemCd.StartsWith(ItemCdConst.Comment852Pattern) || itemCd.StartsWith(ItemCdConst.Comment853Pattern) ||

            (itemCd.StartsWith(ItemCdConst.Comment840Pattern) && itemCd != ItemCdConst.GazoDensibaitaiHozon) ||

            itemCd.StartsWith(ItemCdConst.Comment842Pattern) || itemCd.StartsWith(ItemCdConst.Comment880Pattern));
    }

    public int GetLastDaySantei(int hpId, long ptId, int sinDate, long raiinNo, string itemCd)
    {
        int result = 0;
        var sinKouiCountDiffDayQuery = NoTrackingDataContext.SinKouiCounts.Where(s => s.HpId == hpId && s.PtId == ptId && (s.SinYm * 100 + s.SinDay) < sinDate);
        var sinKouiDetailQuery = NoTrackingDataContext.SinKouiDetails.Where(s => s.HpId == hpId && s.PtId == ptId && s.ItemCd == itemCd);
        var resultDiffDayQuery = from sinKouiCount in sinKouiCountDiffDayQuery
                                 join sinKouiDetail in sinKouiDetailQuery
                                 on new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.RpNo, sinKouiCount.SinYm }
                                 equals new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.RpNo, sinKouiDetail.SinYm }
                                 select new
                                 {
                                     SinKouiCount = sinKouiCount,
                                 };
        var resultCountList = resultDiffDayQuery.ToList();
        if (resultCountList.Count > 0)
        {
            //当日を含めない
            result = resultCountList.Max(d => d.SinKouiCount.SinYm * 100 + d.SinKouiCount.SinDay);
        }
        else
        {
            //当日を含める
            var sinKouiCountSameDayQuery = NoTrackingDataContext.SinKouiCounts
                .Where(s => s.HpId == hpId && s.PtId == ptId && (s.SinYm * 100 + s.SinDay) <= sinDate && s.RaiinNo != raiinNo);
            var resultSameDayQuery = from sinKouiCount in sinKouiCountSameDayQuery
                                     join sinKouiDetail in sinKouiDetailQuery
                                     on new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.RpNo, sinKouiCount.SinYm }
                                     equals new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.RpNo, sinKouiDetail.SinYm }
                                     select new
                                     {
                                         SinKouiCount = sinKouiCount,
                                     };
            resultCountList = resultSameDayQuery.ToList();
            if (resultCountList.Count > 0)
            {
                result = resultCountList.Max(d => d.SinKouiCount.SinYm * 100 + d.SinKouiCount.SinDay);
            }
        }

        return result;
    }

    public List<RaiinKbnModel> InitDefaultByTodayOrder(List<RaiinKbnModel> raiinKbns, List<(int grpId, int kbnCd, int kouiKbn1, int kouiKbn2)> raiinKouiKbns, List<RaiinKbnItemModel> raiinKbnItemCds, List<OrdInfModel> todayOrds)
    {
        foreach (var raiinKbn in raiinKbns)
        {
            int settingRaiinKbnCd = 0;
            foreach (var kbnDetail in raiinKbn.RaiinKbnDetailModels)
            {
                if (!kbnDetail.IsTodayOrderChecked) continue;

                //grid raiinKbnItem
                var kouiKbns = raiinKouiKbns.FindAll(k => k.grpId == kbnDetail.GrpCd && k.kbnCd == kbnDetail.KbnCd);

                //checkbox group raiinKouiKbn
                var kbnItems = raiinKbnItemCds.FindAll(p => p.GrpCd == kbnDetail.GrpCd && p.KbnCd == kbnDetail.KbnCd && !string.IsNullOrEmpty(p.ItemCd));
                var includeItems = kbnItems.FindAll(p => p.IsExclude != 1);
                var excludeItems = kbnItems.FindAll(p => p.IsExclude == 1);

                bool existItem = false;
                foreach (var todayOrd in todayOrds)
                {
                    if (todayOrd.IsDeleted == 0)
                    {
                        continue;
                    }

                    foreach (var itemCd in todayOrd.OrdInfDetails.Select(item => item.ItemCd).ToList())
                    {
                        if (excludeItems.Exists(p => p.ItemCd == itemCd))
                        {
                            continue;
                        }

                        if (kouiKbns.Exists(p => p.kouiKbn1 == todayOrd.OdrKouiKbn || p.kouiKbn2 == todayOrd.OdrKouiKbn) ||
                            includeItems.Exists(p => p.ItemCd == itemCd))
                        {
                            existItem = true;
                            break;
                        }
                    }
                }

                if (existItem && kbnDetail.IsAutoDelete == DeleteTypes.Deleted && raiinKbn.RaiinKbnInfModel.KbnCd == kbnDetail.KbnCd)
                {
                    raiinKbn.RaiinKbnInfModel.ChangeKbnCd(0);
                }

                existItem = false;
                foreach (var todayOrd in todayOrds)
                {
                    if (todayOrd.IsDeleted == 1 || todayOrd.IsDeleted == 2)
                    {
                        continue;
                    }

                    foreach (var itemCd in todayOrd.OrdInfDetails.Select(item => item.ItemCd).ToList())
                    {
                        if (excludeItems.Exists(p => p.ItemCd == itemCd)) continue;

                        if (kouiKbns.Exists(p => p.kouiKbn1 == todayOrd.OdrKouiKbn || p.kouiKbn2 == todayOrd.OdrKouiKbn) ||
                            includeItems.Exists(p => p.ItemCd == itemCd))
                        {
                            existItem = true;
                            break;
                        }
                    }
                }

                if (existItem)
                {
                    settingRaiinKbnCd = kbnDetail.KbnCd;
                }
            }
            if (settingRaiinKbnCd != 0 && raiinKbn.RaiinKbnInfModel.KbnCd == 0)
            {
                raiinKbn.RaiinKbnInfModel.ChangeKbnCd(settingRaiinKbnCd);
            }
        }

        return raiinKbns;
    }

    public Dictionary<string, bool> ConvertInputItemToTodayOdr(int sinDate, Dictionary<string, string> detailInfs)
    {
        var ipnKasanExcludeQuery = NoTrackingDataContext.IpnKasanMsts.Where(u => u.StartDate <= sinDate && u.EndDate >= sinDate);

        var ipnKasanExcludeItemQuery = NoTrackingDataContext.ipnKasanExcludeItems.Where(u => u.StartDate <= sinDate && u.EndDate >= sinDate);

        var query = from detail in detailInfs
                    join ipnkasan in ipnKasanExcludeQuery
                        on detail.Value equals ipnkasan.IpnNameCd into ipnKasanList
                    join ipnKasanItem in ipnKasanExcludeItemQuery
                        on detail.Key equals ipnKasanItem.ItemCd into ipnKasanItemList
                    select new
                    {
                        ItemCd = detail.Key,
                        IsGetYaka = ipnKasanList.FirstOrDefault() == null && ipnKasanItemList.FirstOrDefault() == null
                    };

        return query.AsEnumerable().ToDictionary(u => u.ItemCd, u => u.IsGetYaka);
    }

    public List<OrdInfModel> FromNextOrderToTodayOrder(int hpId, int sinDate, long raiinNo, int userId, List<RsvkrtOrderInfModel> rsvkrtOdrInfModels)
    {
        List<OrdInfModel> ordInfs;
        List<string> itemCds = new();
        List<string> ipNameCds = new();
        List<int> sinKouiKbns = new();
        foreach (var ordInfDetail in rsvkrtOdrInfModels.Select(item => item.OrdInfDetails).ToList())
        {
            itemCds.AddRange(ordInfDetail.Select(od => od.ItemCd));
            ipNameCds.AddRange(ordInfDetail.Select(od => od.IpnCd));
            sinKouiKbns.AddRange(ordInfDetail.Select(od => od.SinKouiKbn));
        }
        itemCds = itemCds.Distinct().ToList();
        ipNameCds = ipNameCds.Distinct().ToList();
        var tenMsts = NoTrackingDataContext.TenMsts.Where(t => t.HpId == hpId && t.StartDate <= sinDate && t.EndDate >= sinDate && itemCds.Contains(t.ItemCd)).ToList();
        var kensaItemCds = tenMsts.Select(t => t.KensaItemCd).ToList();
        var kensaItemSeqNos = tenMsts.Select(t => t.KensaItemSeqNo).ToList();
        var ipns = NoTrackingDataContext.IpnNameMsts.Where(ipn =>
               ipn.StartDate <= sinDate &&
               ipn.EndDate >= sinDate &&
               ipNameCds.Contains(ipn.IpnNameCd)).ToList();
        var kensMsts = NoTrackingDataContext.KensaMsts.Where(e =>
            e.HpId == hpId &&
            kensaItemCds.Contains(e.KensaItemCd) &&
            kensaItemSeqNos.Contains(e.KensaItemSeqNo))
            .ToList();
        var ipnMinYakkas = NoTrackingDataContext.IpnMinYakkaMsts.Where(p =>
               p.StartDate <= sinDate &&
               p.EndDate >= sinDate &&
               ipNameCds.Contains(p.IpnNameCd)).ToList();

        var listYohoSets = NoTrackingDataContext.YohoSetMsts.Where(y => y.HpId == hpId && y.IsDeleted == 0 && y.UserId == userId).ToList();
        var itemCdYohos = listYohoSets?.Select(od => od.ItemCd ?? string.Empty);

        var tenMstYohos = NoTrackingDataContext.TenMsts.Where(t => t.HpId == hpId && t.IsNosearch == 0 && t.StartDate <= sinDate && t.EndDate >= sinDate && (sinKouiKbns != null && sinKouiKbns.Contains(t.SinKouiKbn)) && (itemCdYohos != null && itemCdYohos.Contains(t.ItemCd))).ToList();

        var kensaCenterMsts = NoTrackingDataContext.CommonKensaCenterMst.GroupBy(i => i.CenterCd).Select(g => g.First()).ToList();

        ordInfs = ConvertToDetailModel(hpId, raiinNo, sinDate, userId, rsvkrtOdrInfModels, ipns, tenMsts, kensMsts, ipnMinYakkas, listYohoSets ?? new(), tenMstYohos ?? new(), kensaCenterMsts);

        return ordInfs;
    }

    public List<OrdInfModel> FromHistory(int hpId, int sinDate, long raiinNo, int sainteiKbn, int userId, long ptId, List<OrdInfModel> historyOdrInfModels)
    {
        List<OrdInfModel> ordInfModels = new();
        int autoSetKohatu = (int)_systemConf!.GetSettingValue(2020, 2, hpId);
        int autoSetSenpatu = (int)_systemConf.GetSettingValue(2021, 2, hpId);
        int rowNo = 0;
        List<string> itemCds = new();
        List<string> ipnCds = new();
        List<int> sinKouiKbns = new();
        foreach (var ordInfDetails in historyOdrInfModels.Select(item => item.OrdInfDetails).ToList())
        {
            itemCds.AddRange(ordInfDetails.Select(od => od.ItemCd));
            ipnCds.AddRange(ordInfDetails.Select(od => od.IpnCd));
            sinKouiKbns.AddRange(ordInfDetails.Select(od => od.SinKouiKbn));
        }

        var listYohoSets = NoTrackingDataContext.YohoSetMsts.Where(y => y.HpId == hpId && y.IsDeleted == 0 && y.UserId == userId).ToList();
        var itemCdYohos = listYohoSets?.Select(od => od.ItemCd ?? string.Empty);

        var tenMstYohos = NoTrackingDataContext.TenMsts.Where(t => t.HpId == hpId && t.IsNosearch == 0 && t.StartDate <= sinDate && t.EndDate >= sinDate && (sinKouiKbns != null && sinKouiKbns.Contains(t.SinKouiKbn)) && (itemCdYohos != null && itemCdYohos.Contains(t.ItemCd))).ToList();
        var tenMsts = NoTrackingDataContext.TenMsts.Where(t => t.HpId == hpId && itemCds.Contains(t.ItemCd) && t.StartDate <= sinDate && t.EndDate >= sinDate).ToList();
        var kensaItemCds = tenMsts.Select(k => k.KensaItemCd).Distinct().ToList();
        var KensaSeqNos = tenMsts.Select(k => k.KensaItemSeqNo).Distinct().ToList();
        var kensaMsts = NoTrackingDataContext.KensaMsts.Where(t => t.HpId == hpId && kensaItemCds.Contains(t.KensaItemCd) && KensaSeqNos.Contains(t.KensaItemSeqNo)).ToList();
        var ipnNameCds = tenMsts.Select(k => k.IpnNameCd).Distinct().ToList();
        var ipnMinYakkaMsts = NoTrackingDataContext.IpnMinYakkaMsts.Where(p =>
               p.StartDate <= sinDate &&
               p.EndDate >= sinDate &&
               ipnNameCds.Contains(p.IpnNameCd)).ToList();

        var ipnNameMsts = NoTrackingDataContext.IpnNameMsts.Where(p =>
               p.StartDate <= sinDate &&
               p.EndDate >= sinDate &&
               ipnCds.Contains(p.IpnNameCd));

        var ipnKasanExcludes = NoTrackingDataContext.ipnKasanExcludes.Where(p =>
               p.StartDate <= sinDate &&
               p.EndDate >= sinDate &&
               ipnCds.Contains(p.IpnNameCd)).ToList();

        var ipnKasanExcludeItems = NoTrackingDataContext.ipnKasanExcludeItems.Where(p =>
            p.StartDate <= sinDate &&
            p.EndDate >= sinDate &&
            ipnCds.Contains(p.ItemCd)).ToList();

        var kensaCenterMsts = NoTrackingDataContext.CommonKensaCenterMst.GroupBy(i => i.CenterCd).Select(g => g.First()).ToList();

        int autoSetSyohoKbnKohatuDrug = (int)_systemConf.GetSettingValue(2020, 0, hpId);
        int autoSetSyohoLimitKohatuDrug = (int)_systemConf.GetSettingValue(2020, 1, hpId);
        int autoSetSyohoKbnSenpatuDrug = (int)_systemConf.GetSettingValue(2021, 0, hpId);
        int autoSetSyohoLimitSenpatuDrug = (int)_systemConf.GetSettingValue(2021, 1, hpId);

        foreach (var historyOdrInfModel in historyOdrInfModels)
        {
            List<OrdInfDetailModel> odrInfDetails = new();

            int newSanteiKbn = 0;
            if (_systemConf.GetSettingValue(2008, 1, hpId) == 1 || historyOdrInfModel.SanteiKbn == 1)
            {
                newSanteiKbn = historyOdrInfModel.SanteiKbn;
            }
            else
            {
                newSanteiKbn = sainteiKbn;
            }

            foreach (var detail in historyOdrInfModel.OrdInfDetails)
            {
                string ipnCd = string.Empty;
                string ipnName = string.Empty;
                string kokuji1 = string.Empty;
                string kokuji2 = string.Empty;
                string cmtName = string.Empty;
                string cmtOpt = string.Empty;
                string fontColor = string.Empty;
                int commentNewline = 0;
                int kohatuKbn = 0;
                int sinKouiKbn = detail.SinKouiKbn;
                string itemCd = detail.ItemCd;
                string itemName = detail.ItemName;
                double suryo = detail.Suryo;
                string unitName = detail.UnitName;
                int unitSBT = detail.UnitSbt;
                double termVal = detail.TermVal;
                string bunkatu = detail.Bunkatu;
                string relateItem = detail.RelationItem;
                kohatuKbn = detail.KohatuKbn;
                int drugKbn = detail.DrugKbn;
                int yohoKbn = detail.YohoKbn;
                int isNodspRece = detail.IsNodspRece;
                int bikoComment = detail.BikoComment;
                int odrKouiKbn = historyOdrInfModel.OdrKouiKbn;

                TenMst? tenMst = null;
                if (!string.IsNullOrEmpty(detail.ItemCd))
                {
                    tenMst = tenMsts.FirstOrDefault(t => t.ItemCd == detail.ItemCd);
                }

                string centerName = kensaCenterMsts.FirstOrDefault(k => k.CenterCd == tenMst?.CenterCd)?.DspCenterName ?? string.Empty;

                ipnCd = tenMst == null ? string.Empty : tenMst.IpnNameCd ?? string.Empty;
                if (!string.IsNullOrEmpty(detail.IpnCd))
                {
                    ipnName = ipnNameMsts.FirstOrDefault(ipn => ipn.IpnNameCd == detail.IpnCd)?.IpnName ?? string.Empty;
                }
                else
                {
                    ipnName = string.Empty;
                }

                kokuji1 = tenMst == null ? string.Empty : tenMst.Kokuji1 ?? string.Empty;
                kokuji2 = tenMst == null ? string.Empty : tenMst.Kokuji2 ?? string.Empty;

                cmtName = detail.CmtName;
                cmtOpt = detail.CmtOpt;
                fontColor = detail.FontColor;
                commentNewline = detail.CommentNewline;
                var syohoKbn = detail.SyohoKbn;
                var syohoLimitKbn = detail.SyohoLimitKbn;

                if ((detail.SinKouiKbn == 20 && detail.DrugKbn > 0) || (((detail.SinKouiKbn >= 20 && detail.SinKouiKbn <= 23) || detail.SinKouiKbn == 28) && detail.IsInjection))
                {
                    bool isChangeKouhatu = tenMst != null && detail.KohatuKbn != tenMst.KohatuKbn;
                    if (isChangeKouhatu)
                    {
                        switch (tenMst?.KohatuKbn)
                        {
                            case 0:
                                // 先発品
                                syohoKbn = 0;
                                syohoLimitKbn = 0;
                                break;
                            case 1:
                                // 後発品
                                syohoKbn = autoSetSyohoKbnKohatuDrug + 1;
                                syohoLimitKbn = autoSetSyohoLimitKohatuDrug;
                                break;
                            case 2:
                                // 後発品のある先発品
                                syohoKbn = autoSetSyohoKbnSenpatuDrug + 1;
                                syohoLimitKbn = autoSetSyohoLimitSenpatuDrug;
                                break;
                        }
                        if (syohoKbn == 3 && string.IsNullOrEmpty(detail.IpnName))
                        {
                            // 一般名マスタに登録がない
                            syohoKbn = 2;
                        }
                    }
                }
                kohatuKbn = tenMst == null ? detail.KohatuKbn : tenMst.KohatuKbn;

                if ((detail.SinKouiKbn == 20 && detail.DrugKbn > 0) || (((detail.SinKouiKbn >= 20 && detail.SinKouiKbn <= 23) || detail.SinKouiKbn == 28) && detail.IsInjection))
                {
                    switch (detail.KohatuKbn)
                    {
                        case 0:
                            // 先発品
                            syohoKbn = 0;
                            syohoLimitKbn = 0;
                            break;
                        case 1:
                            // 後発品
                            if (autoSetKohatu == 0)
                            {
                                //マスタ設定に準じる
                                syohoKbn = autoSetSyohoKbnKohatuDrug + 1;
                                syohoLimitKbn = autoSetSyohoLimitKohatuDrug;
                            }
                            if (detail.SyohoKbn == 0 && autoSetSyohoKbnKohatuDrug == 2 && !string.IsNullOrEmpty(detail.IpnName))
                            {
                                syohoKbn = autoSetSyohoKbnKohatuDrug + 1;
                            }
                            break;
                        case 2:
                            // 後発品のある先発品
                            if (autoSetSenpatu == 0)
                            {
                                //マスタ設定に準じる
                                syohoKbn = autoSetSyohoKbnSenpatuDrug + 1;
                                syohoLimitKbn = autoSetSyohoLimitSenpatuDrug;
                            }
                            if (detail.SyohoKbn == 0 && autoSetSyohoKbnSenpatuDrug == 2 && !string.IsNullOrEmpty(detail.IpnName))
                            {
                                syohoKbn = autoSetSyohoKbnSenpatuDrug + 1;
                            }
                            break;
                    }

                    if (tenMst != null && detail.SyohoKbn == 3 && string.IsNullOrEmpty(detail.IpnName))
                    {
                        // 一般名マスタに登録がない
                        syohoKbn = 2;
                    }
                }

                // Correct TermVal
                termVal = CorrectTermVal(detail.UnitSbt, tenMst ?? new(), detail.TermVal);

                ++rowNo;

                var kensaMstModel = tenMst != null ? kensaMsts.FirstOrDefault(k => k.KensaItemCd == tenMst.KensaItemCd && k.KensaItemSeqNo == k.KensaItemSeqNo) : new();
                var ipnMinYakkaMstModel = tenMst != null ? ipnMinYakkaMsts.FirstOrDefault(i => i.IpnNameCd == tenMst.IpnNameCd) : new();
                var isGetYakkaPrice = CheckIsGetYakkaPrice(tenMst ?? new(), sinDate, ipnKasanExcludes, ipnKasanExcludeItems);

                var odrInfDetail = new OrdInfDetailModel(
                        hpId,
                        raiinNo,
                        0,
                        0,
                        rowNo,
                        ptId,
                        sinDate,
                        sinKouiKbn,
                        itemCd,
                        itemName,
                        suryo,
                        unitName,
                        unitSBT,
                        termVal,
                        kohatuKbn,
                        syohoKbn,
                        syohoLimitKbn,
                        drugKbn,
                        yohoKbn,
                        kokuji1,
                        kokuji2,
                        isNodspRece,
                        ipnCd,
                        ipnName,
                        0,
                        DateTime.MinValue,
                        0,
                        string.Empty,
                        string.Empty,
                        bunkatu,
                        cmtName,
                        cmtOpt,
                        fontColor,
                        commentNewline,
                        tenMst?.MasterSbt ?? string.Empty,
                        0,
                        ipnMinYakkaMstModel?.Yakka ?? 0,
                        isGetYakkaPrice,
                        0,
                        tenMst?.CmtCol1 ?? 0,
                        tenMst?.Ten ?? 0,
                        0,
                        0,
                        detail.KensaGaichu,
                        tenMst?.OdrTermVal ?? 0,
                        tenMst?.CnvTermVal ?? 0,
                        tenMst?.YjCd ?? string.Empty,
                        GetListYohoSetMstModelByUserID(listYohoSets ?? new(), tenMstYohos?.Where(t => t.SinKouiKbn == tenMst?.SinKouiKbn).ToList() ?? new()),
                        0,
                        0,
                        tenMst?.CnvUnitName ?? string.Empty,
                        tenMst?.OdrUnitName ?? string.Empty,
                        kensaMstModel?.CenterItemCd1 ?? string.Empty,
                        kensaMstModel?.CenterItemCd2 ?? string.Empty,
                        tenMst?.CmtColKeta1 ?? 0,
                        tenMst?.CmtColKeta2 ?? 0,
                        tenMst?.CmtColKeta3 ?? 0,
                        tenMst?.CmtColKeta4 ?? 0,
                        tenMst?.CmtCol2 ?? 0,
                        tenMst?.CmtCol3 ?? 0,
                        tenMst?.CmtCol4 ?? 0,
                        tenMst?.HandanGrpKbn ?? 0,
                        kensaMstModel == null,
                        null,
                        odrKouiKbn,
                        tenMst?.BuiKbn ?? 0,
                        tenMst?.RousaiKbn ?? 0,
                        tenMst?.CenterCd ?? string.Empty,
                        bikoComment,
                        centerName,
                        relateItem
                    );

                odrInfDetails.Add(odrInfDetail);
            }
            var newTodayOdrInfModel = new OrdInfModel(
               hpId,
               raiinNo,
               0,
               0,
               ptId,
               sinDate,
               historyOdrInfModel.HokenPid,
               historyOdrInfModel.OdrKouiKbn,
               historyOdrInfModel.RpName,
               historyOdrInfModel.InoutKbn,
               historyOdrInfModel.SikyuKbn,
               historyOdrInfModel.SyohoSbt,
               newSanteiKbn,
               historyOdrInfModel.TosekiKbn,
               historyOdrInfModel.DaysCnt,
               historyOdrInfModel.SortNo,
               0,
               0,
               odrInfDetails,
               DateTime.MinValue,
               userId,
               string.Empty,
               DateTime.MinValue,
               userId,
               string.Empty,
               string.Empty,
               string.Empty
           );
            ordInfModels.Add(newTodayOdrInfModel);
        }

        return ordInfModels;
    }

    public List<OrdInfModel> ConvertToDetailModel(int hpId, long raiinNo, int sinDate, int userId, List<RsvkrtOrderInfModel> rsvkrtOdrInfModels, List<IpnNameMst> ipns, List<TenMst> tenMsts, List<KensaMst> kensMsts, List<IpnMinYakkaMst> ipnMinYakkas, List<YohoSetMst> listYohoSets, List<TenMst> tenMstYohos, List<CommonKensaCenterMst>? commonCenterKensaMsts = null)
    {
        int autoSetKohatu = (int)_systemConf!.GetSettingValue(2020, 2, hpId);
        int autoSetSenpatu = (int)_systemConf.GetSettingValue(2021, 2, hpId);
        int autoSetSyohoKbnKohatuDrug = (int)_systemConf.GetSettingValue(2020, 0, hpId);
        int autoSetSyohoLimitKohatuDrug = (int)_systemConf.GetSettingValue(2020, 1, hpId);
        int autoSetSyohoKbnSenpatuDrug = (int)_systemConf.GetSettingValue(2021, 0, hpId);
        int autoSetSyohoLimitSenpatuDrug = (int)_systemConf.GetSettingValue(2021, 1, hpId);

        List<OrdInfModel> ordInfs = new();
        foreach (var rsvkrtOdrInfModel in rsvkrtOdrInfModels)
        {
            List<OrdInfDetailModel> odrInfDetails = new();
            int rowNo = 0;
            foreach (var odrDetail in rsvkrtOdrInfModel.OrdInfDetails)
            {
                int sinKouiKbn = odrDetail.SinKouiKbn;
                string itemCd = odrDetail.ItemCd;
                string itemName = odrDetail.ItemName;
                double suryo = odrDetail.Suryo;
                string unitName = odrDetail.UnitName;
                int unitSBT = odrDetail.UnitSbt;
                double termVal = odrDetail.TermVal;
                string bunkatu = odrDetail.Bunkatu;

                int kohatuKbn = odrDetail.KohatuKbn;
                int drugKbn = odrDetail.DrugKbn;
                int yohoKbn = odrDetail.YohoKbn;
                int isNodspRece = odrDetail.IsNodspRece;
                string ipnName = string.Empty;
                int bikoComment = odrDetail.BikoComment;
                string centerName = commonCenterKensaMsts?.FirstOrDefault(c => c.CenterCd == odrDetail.CenterCd)?.DspCenterName ?? string.Empty;
                string relationItem = odrDetail.RelationItem;
                TenMst? tenMst = new();
                if (!string.IsNullOrEmpty(itemCd))
                {
                    tenMst = tenMsts.FirstOrDefault(od => od.ItemCd == itemCd);
                }
                string ipnCd = tenMst == null ? string.Empty : tenMst.IpnNameCd ?? string.Empty;
                if (!string.IsNullOrEmpty(ipnCd))
                {
                    ipnName = ipns.FirstOrDefault(od => od.IpnNameCd == ipnCd)?.IpnName ?? string.Empty;
                }
                else
                {
                    ipnName = string.Empty;
                }
                string kokuji1 = tenMst == null ? string.Empty : tenMst.Kokuji1 ?? string.Empty;
                string kokuji2 = tenMst == null ? string.Empty : tenMst.Kokuji2 ?? string.Empty;

                string cmtName = odrDetail.CmtName;
                string cmtOpt = odrDetail.CmtOpt;
                string fontColor = odrDetail.FontColor;
                int commentNewline = odrDetail.CommentNewline;
                kohatuKbn = tenMst == null ? kohatuKbn : tenMst.KohatuKbn;
                var syosai = CaculateSyosai(sinKouiKbn, autoSetKohatu, autoSetSyohoKbnKohatuDrug, autoSetSyohoLimitKohatuDrug, autoSetSyohoLimitSenpatuDrug, autoSetSyohoKbnSenpatuDrug, autoSetSenpatu, drugKbn, kohatuKbn, itemName, ipnName, odrDetail, tenMst ?? new());

                // Correct TermVal
                termVal = CorrectTermVal(unitSBT, tenMst ?? new(), termVal);
                var kensMst = tenMst == null ? null : kensMsts.FirstOrDefault(k => k.KensaItemCd == tenMst.KensaItemCd && k.KensaItemSeqNo == tenMst.KensaItemSeqNo);
                var ipnMinYakka = tenMst == null ? null : ipnMinYakkas.FirstOrDefault(k => k.IpnNameCd == tenMst.IpnNameCd);
                var isGetPriceInYakka = CheckIsGetYakkaPrice(tenMst ?? new(), sinDate);
                double ten = tenMst == null ? 0 : tenMst.Ten;
                var masterSbt = tenMst == null ? string.Empty : tenMst.MasterSbt;
                var cmtCol1 = tenMst == null ? 0 : tenMst.CmtCol1;

                int currenRowNo = ++rowNo;
                var odrInfDetail = new OrdInfDetailModel(
                       odrDetail.HpId, raiinNo, 0, 0, currenRowNo, odrDetail.PtId, sinDate, sinKouiKbn, itemCd, itemName, suryo, unitName, unitSBT, termVal, kohatuKbn, syosai.Item1, syosai.Item2, drugKbn, yohoKbn, kokuji1, kokuji2, isNodspRece, ipnCd, ipnName, 0, DateTime.MinValue, 0, string.Empty, string.Empty, bunkatu, cmtName, cmtOpt, fontColor, commentNewline, masterSbt ?? string.Empty, 0, ipnMinYakka?.Yakka ?? 0, isGetPriceInYakka, 0, cmtCol1, ten, 0, 0, odrDetail.KensaGaichu, 0, 0, string.Empty, GetListYohoSetMstModelByUserID(listYohoSets ?? new List<YohoSetMst>(), tenMstYohos.Where(t => t.SinKouiKbn == odrDetail.SinKouiKbn).ToList() ?? new()).ToList() ?? new(), 0, 0, string.Empty, string.Empty, kensMst?.CenterItemCd1 ?? string.Empty, kensMst?.CenterItemCd2 ?? string.Empty, tenMst?.CmtColKeta1 ?? 0, tenMst?.CmtColKeta2 ?? 0, tenMst?.CmtColKeta3 ?? 0, tenMst?.CmtColKeta4 ?? 0, tenMst?.CmtCol2 ?? 0, tenMst?.CmtCol3 ?? 0, tenMst?.CmtCol4 ?? 0, tenMst?.HandanGrpKbn ?? 0, kensMst == null, null, rsvkrtOdrInfModel.OdrKouiKbn, tenMst?.BuiKbn ?? 0, tenMst?.RousaiKbn ?? 0, tenMst?.CenterCd ?? string.Empty, bikoComment, centerName, relationItem
                    );
                odrInfDetails.Add(odrInfDetail);
            }
            OrdInfModel odrInf = new OrdInfModel(hpId, raiinNo, 0, 0, rsvkrtOdrInfModel.PtId, sinDate, rsvkrtOdrInfModel.HokenPid, rsvkrtOdrInfModel.OdrKouiKbn, rsvkrtOdrInfModel.RpName, rsvkrtOdrInfModel.InoutKbn, rsvkrtOdrInfModel.SikyuKbn, rsvkrtOdrInfModel.SyohoSbt, rsvkrtOdrInfModel.SanteiKbn, rsvkrtOdrInfModel.TosekiKbn, rsvkrtOdrInfModel.DaysCnt, rsvkrtOdrInfModel.SortNo, rsvkrtOdrInfModel.IsDeleted, 0, odrInfDetails, DateTime.MinValue, userId, string.Empty, DateTime.MinValue, userId, string.Empty, string.Empty, string.Empty);
            ordInfs.Add(odrInf);
        }
        return ordInfs;
    }

    public (int, int) CaculateSyosai(int sinKouiKbn, int autoSetKohatu, int autoSetSyohoKbnKohatuDrug, int autoSetSyohoLimitKohatuDrug, int autoSetSyohoLimitSenpatuDrug, int autoSetSyohoKbnSenpatuDrug, int autoSetSenpatu, int drugKbn, int kohatuKbn, string itemName, string ipnName, RsvKrtOrderInfDetailModel odrDetail, TenMst tenMst)
    {
        int syohoKbn = 0;
        int syohoLimitKbn = 0;
        if ((odrDetail.SinKouiKbn == 20 && odrDetail.DrugKbn > 0) || (odrDetail.IsInDrugOdr && odrDetail.IsInjection))
        {
            bool isChangeKouhatu = tenMst != null && odrDetail.KohatuKbn != tenMst.KohatuKbn;
            if (isChangeKouhatu && tenMst != null)
            {
                switch (tenMst.KohatuKbn)
                {
                    case 0:
                        // 先発品
                        syohoKbn = 0;
                        syohoLimitKbn = 0;
                        break;
                    case 1:
                        // 後発品
                        syohoKbn = autoSetSyohoKbnKohatuDrug + 1;
                        syohoLimitKbn = autoSetSyohoLimitKohatuDrug;
                        break;
                    case 2:
                        // 後発品のある先発品
                        syohoKbn = autoSetSyohoKbnSenpatuDrug + 1;
                        syohoLimitKbn = autoSetSyohoLimitSenpatuDrug;
                        break;
                }
                if (odrDetail.SyohoKbn == 3 && string.IsNullOrEmpty(ipnName))
                {
                    // 一般名マスタに登録がない
                    syohoKbn = 2;
                }
            }
        }

        if ((sinKouiKbn == 20 && drugKbn > 0) || (odrDetail.IsInDrugOdr && odrDetail.IsInjection))
        {
            switch (kohatuKbn)
            {
                case 0:
                    // 先発品
                    syohoKbn = 0;
                    syohoLimitKbn = 0;
                    break;
                case 1:
                    // 後発品
                    if (autoSetKohatu == 0)
                    {
                        //マスタ設定に準じる
                        syohoKbn = autoSetSyohoKbnKohatuDrug + 1;
                        syohoLimitKbn = autoSetSyohoLimitKohatuDrug;
                    }
                    else
                    {
                        //各セットの設定に準じる
                        syohoKbn = odrDetail.SyohoKbn;
                        syohoLimitKbn = odrDetail.SyohoLimitKbn;
                    }
                    if (syohoKbn == 0 && autoSetSyohoKbnKohatuDrug == 2 && !string.IsNullOrEmpty(ipnName))
                    {
                        syohoKbn = autoSetSyohoKbnKohatuDrug + 1;
                    }
                    break;
                case 2:
                    // 後発品のある先発品
                    if (autoSetSenpatu == 0)
                    {
                        //マスタ設定に準じる
                        syohoKbn = autoSetSyohoKbnSenpatuDrug + 1;
                        syohoLimitKbn = autoSetSyohoLimitSenpatuDrug;
                    }
                    else
                    {
                        //各セットの設定に準じる
                        syohoKbn = odrDetail.SyohoKbn;
                        syohoLimitKbn = odrDetail.SyohoLimitKbn;
                    }
                    if (syohoKbn == 0 && autoSetSyohoKbnSenpatuDrug == 2 && !string.IsNullOrEmpty(ipnName))
                    {
                        syohoKbn = autoSetSyohoKbnSenpatuDrug + 1;
                    }
                    break;
            }

            if (tenMst != null && syohoKbn == 3 && string.IsNullOrEmpty(ipnName))
            {
                // 一般名マスタに登録がない
                syohoKbn = 2;
            }
        }
        return (syohoKbn, syohoLimitKbn);
    }

    public static double CorrectTermVal(int unitSbt, TenMst tenMst, double originTermVal)
    {
        if (tenMst == null || (string.IsNullOrEmpty(tenMst.ItemCd) && tenMst.StartDate == 0 && tenMst.HpId == 0)) return 0;
        double termVal = originTermVal;
        if (unitSbt == UnitSbtConst.BASIC)
        {
            termVal = tenMst.OdrTermVal;
        }
        else if (unitSbt == UnitSbtConst.CONVERT)
        {
            termVal = tenMst.CnvTermVal;
        }
        return termVal;
    }

    public List<(int type, string message, int odrInfPosition, int odrInfDetailPosition, TenItemModel tenItemMst, double suryo)> AutoCheckOrder(int hpId, int sinDate, long ptId, List<OrdInfModel> odrInfs)
    {
        var currentListOrder = odrInfs.Where(o => o.Id >= 0).ToList();
        var addingOdrList = odrInfs.Where(o => o.Id == -1).ToList();
        List<(int type, string message, int positionOdr, int odrInfDetailPosition, TenItemModel temItemMst, double suryo)> result = new();
        int odrInfIndex = 0, odrInfDetailIndex = 0;
        foreach (var checkingOdr in addingOdrList)
        {
            var odrInfDetails = checkingOdr.OrdInfDetails.Where(d => !d.IsEmpty).ToList();
            odrInfDetailIndex = 0;
            foreach (var detail in odrInfDetails)
            {
                if (string.IsNullOrEmpty(detail.ItemCd))
                {
                    odrInfDetailIndex++;
                    continue;
                }

                var santeiGrpDetailList = FindSanteiGrpDetailList(hpId, detail.ItemCd);
                if (santeiGrpDetailList.Count == 0)
                {
                    odrInfDetailIndex++;
                    continue;
                }

                foreach (var santeiGrpDetail in santeiGrpDetailList)
                {
                    var santeiCntCheck = FindSanteiCntCheck(hpId, santeiGrpDetail.SanteiGrpCd, sinDate);
                    if (santeiCntCheck == null)
                    {
                        continue;
                    }
                    // Now, check TermCnt = 1 and TermSbt = 4 and CntType = 2 only. In other case, just ignore
                    if (santeiCntCheck.TermCnt == 1 && santeiCntCheck.TermSbt == 4 && (santeiCntCheck.CntType == 2 || santeiCntCheck.CntType == 3))
                    {
                        double santeiCntInMonth = GetOdrCountInMonth(hpId, ptId, sinDate, detail.ItemCd);
                        double countInCurrentOdr = 0;

                        if (santeiCntCheck.CntType == 2)
                        {
                            foreach (var item in currentListOrder)
                            {
                                foreach (var itemDetail in item.OrdInfDetails)
                                {
                                    if (item.Id != checkingOdr.Id && itemDetail.ItemCd == detail.ItemCd)
                                    {
                                        if (item.IsDeleted == DeleteTypes.None)
                                        {
                                            countInCurrentOdr += (itemDetail.Suryo <= 0 || ItemCdConst.ZaitakuTokushu.Contains(itemDetail.ItemCd)) ? 1 : itemDetail.Suryo;
                                        }
                                        else
                                        {
                                            countInCurrentOdr -= (itemDetail.Suryo <= 0 || ItemCdConst.ZaitakuTokushu.Contains(itemDetail.ItemCd)) ? 1 : itemDetail.Suryo;
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            foreach (var item in currentListOrder)
                            {
                                foreach (var itemDetail in item.OrdInfDetails)
                                {
                                    if (item.Id != checkingOdr.Id && itemDetail.ItemCd == detail.ItemCd)
                                    {
                                        if (item.IsDeleted == DeleteTypes.None)
                                        {
                                            countInCurrentOdr++;
                                        }
                                        else
                                        {
                                            countInCurrentOdr--;
                                        }
                                    }
                                }
                            }
                        }

                        var checkingOrders = addingOdrList.Where(a => a != checkingOdr).ToList();
                        if (santeiCntCheck.CntType == 2)
                        {
                            foreach (var item in checkingOrders)
                            {
                                foreach (var itemDetail in item.OrdInfDetails)
                                {
                                    if (itemDetail.ItemCd == detail.ItemCd)
                                    {
                                        countInCurrentOdr += (itemDetail.Suryo <= 0 || ItemCdConst.ZaitakuTokushu.Contains(itemDetail.ItemCd)) ? 1 : itemDetail.Suryo;
                                    }
                                }
                            }
                        }
                        else
                        {
                            foreach (var item in checkingOrders)
                            {
                                foreach (var itemDetail in item.OrdInfDetails)
                                {
                                    if (itemDetail.ItemCd == detail.ItemCd)
                                    {
                                        countInCurrentOdr++;
                                    }
                                }
                            }
                        }


                        double totalSanteiCount = santeiCntInMonth + countInCurrentOdr;

                        if (totalSanteiCount >= santeiCntCheck.MaxCnt)
                        {
                            var targetItem = FindTenMst(hpId, santeiCntCheck.TargetCd ?? string.Empty, sinDate);
                            if (targetItem == null)
                            {
                                continue;
                            }

                            StringBuilder stringBuilder = new StringBuilder(string.Empty);
                            stringBuilder.Append("'");
                            stringBuilder.Append(detail.DisplayItemName);
                            stringBuilder.Append("'");
                            stringBuilder.Append("が");
                            stringBuilder.Append("1ヶ月 ");
                            stringBuilder.Append(santeiCntCheck.MaxCnt);
                            stringBuilder.Append("単位を超えています。");
                            stringBuilder.Append(Environment.NewLine);
                            stringBuilder.Append("'");
                            stringBuilder.Append(targetItem.Name);
                            stringBuilder.Append("'に置き換えますか？");

                            string msg = stringBuilder.ToString();
                            detail.ChangeItemCd(targetItem.ItemCd);
                            result.Add(new(1, msg, odrInfIndex, odrInfDetailIndex, targetItem, 0));
                        }
                        else if (totalSanteiCount + detail.Suryo > santeiCntCheck.MaxCnt)
                        {
                            double suryo = Convert.ToDouble(santeiCntCheck.MaxCnt) - totalSanteiCount;
                            var totalSanteiCountString = totalSanteiCount.ToString();
                            int digits = 0;
                            if (totalSanteiCountString.Contains("."))
                            {
                                int startIndex = totalSanteiCountString.IndexOf(".");
                                digits = totalSanteiCountString.Substring(startIndex, totalSanteiCountString.Length - startIndex).Length - 1;
                                if (digits < 0)
                                {
                                    digits = 0;
                                }
                            }
                            suryo = Math.Round(suryo, digits);

                            StringBuilder stringBuilder = new StringBuilder(string.Empty);
                            stringBuilder.Append("'");
                            stringBuilder.Append(detail.DisplayItemName);
                            stringBuilder.Append("'");
                            stringBuilder.Append("が");
                            stringBuilder.Append("1ヶ月 ");
                            stringBuilder.Append(santeiCntCheck.MaxCnt);
                            stringBuilder.Append("単位を超えます。");
                            stringBuilder.Append(Environment.NewLine);
                            stringBuilder.Append("数量を'");
                            stringBuilder.Append(suryo);
                            stringBuilder.Append("'に変更しますか？");

                            string msg = stringBuilder.ToString();
                            detail.ChangeSuryo(suryo);
                            result.Add(new(2, msg, odrInfIndex, odrInfDetailIndex, new(), suryo));
                        }
                    }
                }

                odrInfDetailIndex++;
            }
            odrInfIndex++;
        }
        return result;
    }

    public List<(int, OrdInfModel)> ChangeAfterAutoCheckOrder(int hpId, int sinDate, int userId, long raiinNo, long ptId, List<OrdInfModel> odrInfs, List<Tuple<int, string, int, int, TenItemModel, double>> targetItems)
    {
        List<(int, OrdInfModel)> result = new();
        var addingOdrList = odrInfs.Where(o => o.Id == -1 && o.OrdInfDetails.Count > 0).ToList();
        int odrInfIndex = 0, odrInfDetailIndex = 0;
        List<string> ipnNameCds = new List<string>();
        List<string> itemCds = new List<string>();
        foreach (var ordInfDetails in odrInfs.Select(o => o.OrdInfDetails))
        {
            ipnNameCds.AddRange(ordInfDetails.Select(od => od.IpnCd));
            itemCds.AddRange(ordInfDetails.Select(od => od.ItemCd));
        }
        ipnNameCds = ipnNameCds.Distinct().ToList();
        var ipnNameMsts = NoTrackingDataContext.IpnNameMsts.Where(i =>
               i.StartDate <= sinDate &&
               i.EndDate >= sinDate).AsEnumerable().Where(i =>
               ipnNameCds.Contains(i.IpnNameCd)).Select(i => new Tuple<string, string>(i.IpnNameCd, i.IpnName ?? string.Empty)).ToList();
        var autoSetSyohoKbnKohatuDrug = _systemConf!.GetSettingValue(2020, 0, hpId);
        var autoSetSyohoLimitKohatuDrug = _systemConf.GetSettingValue(2020, 1, hpId);
        var autoSetSyohoKbnSenpatuDrug = _systemConf.GetSettingValue(2021, 0, hpId);
        var autoSetSyohoLimitSenpatuDrug = _systemConf.GetSettingValue(2021, 1, hpId);
        var tenMsts = NoTrackingDataContext.TenMsts.Where(t => t.HpId == hpId && (t.StartDate <= sinDate && t.EndDate >= sinDate) && (itemCds != null && itemCds.Contains(t.ItemCd))).ToList();
        var centerCds = tenMsts.Select(t => t.CenterCd).Distinct().ToList();
        var commonCenters = NoTrackingDataContext.CommonKensaCenterMst.GroupBy(i => i.CenterCd).Select(g => g.First()).ToList(); ;

        foreach (var checkingOdr in addingOdrList)
        {
            var index = odrInfs.FindIndex(o => o.Equals(checkingOdr));
            var odrInfDetails = checkingOdr.OrdInfDetails.Where(d => !d.IsEmpty).ToList();
            bool isAdded = false;
            odrInfDetailIndex = 0;
            var kensaMsts = NoTrackingDataContext.KensaMsts.Where(t => t.HpId == hpId).ToList();
            foreach (var detail in odrInfDetails)
            {
                var targetItem = targetItems.FirstOrDefault(t => t.Item3 == odrInfIndex && t.Item4 == odrInfDetailIndex);

                if (targetItem == null)
                {
                    odrInfDetailIndex++;
                    continue;
                }

                if (targetItem.Item1 == 1)
                {
                    var tenItemMst = targetItem.Item5;
                    var grpKouiDetail = CIUtil.GetGroupKoui(detail.SinKouiKbn);
                    var grpKouiTarget = CIUtil.GetGroupKoui(tenItemMst.SinKouiKbn);
                    var itemShugiList = odrInfDetails.Where(d => d.IsShugi).ToList();
                    string itemCd = tenItemMst.ItemCd;
                    string itemName = tenItemMst.Name;
                    int sinKouiKbn = tenItemMst.SinKouiKbn;
                    int kohatuKbn = tenItemMst.KohatuKbn;
                    int drugKbn = tenItemMst.DrugKbn;
                    string unitNameBefore = detail.UnitName;
                    int unitSBT = 0;
                    string unitName = string.Empty;
                    double termVal = 0;
                    double suryo = 0;
                    int yohoKbn = tenItemMst.YohoKbn;
                    string ipnCd = tenItemMst.IpnNameCd;
                    string ipnName = string.Empty;
                    string kokuji1 = tenItemMst.Kokuji1;
                    string kokuji2 = tenItemMst.Kokuji2;
                    int syohoKbn = 0;
                    int syohoLimitKbn = 0;

                    if (grpKouiDetail == grpKouiTarget || itemShugiList.Count == 1)
                    {
                        if (!string.IsNullOrEmpty(tenItemMst.OdrUnitName))
                        {
                            unitSBT = 1;
                            unitName = tenItemMst.OdrUnitName;
                            termVal = tenItemMst.OdrTermVal;
                        }
                        else if (!string.IsNullOrEmpty(tenItemMst.CnvUnitName))
                        {
                            unitSBT = 2;
                            unitName = tenItemMst.CnvUnitName;
                            termVal = tenItemMst.CnvTermVal;
                        }
                        else
                        {
                            unitSBT = 0;
                            unitName = string.Empty;
                            termVal = 0;
                            suryo = 0;
                        }
                        if (!string.IsNullOrEmpty(detail.UnitName) && detail.UnitName != unitNameBefore)
                        {
                            suryo = 1;
                        }

                        if (!string.IsNullOrEmpty(detail.IpnCd))
                        {
                            ipnName = ipnNameMsts.FirstOrDefault(i => i.Item1 == tenItemMst.IpnNameCd)?.Item2 ?? string.Empty;
                        }
                        else
                        {
                            ipnName = string.Empty;
                        }

                        if (detail.SinKouiKbn == 20 && detail.DrugKbn > 0)
                        {
                            switch (detail.KohatuKbn)
                            {
                                case 0:
                                    // 先発品
                                    syohoKbn = 0;
                                    syohoLimitKbn = 0;
                                    break;
                                case 1:
                                    // 後発品
                                    syohoKbn = (int)autoSetSyohoKbnKohatuDrug + 1;
                                    syohoLimitKbn = (int)autoSetSyohoLimitKohatuDrug;
                                    break;
                                case 2:
                                    // 後発品のある先発品
                                    syohoKbn = (int)autoSetSyohoKbnSenpatuDrug + 1;
                                    syohoLimitKbn = (int)autoSetSyohoLimitSenpatuDrug;
                                    break;
                            }
                            if (detail.SyohoKbn == 3 && string.IsNullOrEmpty(detail.IpnName))
                            {
                                // 一般名マスタに登録がない
                                syohoKbn = 2;
                            }
                        }

                        if (itemShugiList.Count == 1)
                        {
                            checkingOdr.ChangeOdrKouiKbn(detail.SinKouiKbn);
                            detail.ChangeOrdInfDetail(itemCd, itemName, sinKouiKbn, kohatuKbn, drugKbn, unitSBT, unitName, termVal, suryo, yohoKbn, ipnCd, ipnName, kokuji1, kokuji2, syohoKbn, syohoLimitKbn);
                            isAdded = true;
                        }
                    }
                    else
                    {
                        // Difference group koui
                        sinKouiKbn = tenItemMst.SinKouiKbn;
                        itemCd = tenItemMst.ItemCd;
                        itemName = tenItemMst.Name;
                        unitNameBefore = detail.UnitName;
                        if (!string.IsNullOrEmpty(tenItemMst.OdrUnitName))
                        {
                            unitSBT = 1;
                            unitName = tenItemMst.OdrUnitName;
                            termVal = tenItemMst.OdrTermVal;
                        }
                        else if (!string.IsNullOrEmpty(tenItemMst.CnvUnitName))
                        {
                            unitSBT = 2;
                            unitName = tenItemMst.CnvUnitName;
                            termVal = tenItemMst.CnvTermVal;
                        }
                        else
                        {
                            unitSBT = 0;
                            unitName = string.Empty;
                            termVal = 0;
                            suryo = 0;
                        }
                        if (!string.IsNullOrEmpty(detail.UnitName) && detail.UnitName != unitNameBefore)
                        {
                            suryo = 1;
                        }

                        kohatuKbn = tenItemMst.KohatuKbn;
                        yohoKbn = tenItemMst.YohoKbn;
                        drugKbn = tenItemMst.DrugKbn;
                        ipnCd = tenItemMst.IpnNameCd;
                        if (!string.IsNullOrEmpty(detail.IpnCd))
                        {
                            ipnName = ipnNameMsts.FirstOrDefault(t => t.Item1 == tenItemMst.IpnNameCd)?.Item2 ?? string.Empty;
                        }
                        else
                        {
                            ipnName = string.Empty;
                        }

                        kokuji1 = tenItemMst.Kokuji1;
                        kokuji2 = tenItemMst.Kokuji2;

                        if (detail.SinKouiKbn == 20 && detail.DrugKbn > 0)
                        {
                            switch (detail.KohatuKbn)
                            {
                                case 0:
                                    // 先発品
                                    syohoKbn = 0;
                                    syohoLimitKbn = 0;
                                    break;
                                case 1:
                                    // 後発品
                                    syohoKbn = (int)autoSetSyohoKbnKohatuDrug + 1;
                                    syohoLimitKbn = (int)autoSetSyohoLimitKohatuDrug;
                                    break;
                                case 2:
                                    // 後発品のある先発品
                                    syohoKbn = (int)autoSetSyohoKbnSenpatuDrug + 1;
                                    syohoLimitKbn = (int)autoSetSyohoLimitSenpatuDrug;
                                    break;
                            }
                            if (detail.SyohoKbn == 3 && string.IsNullOrEmpty(detail.IpnName))
                            {
                                // 一般名マスタに登録がない
                                syohoKbn = 2;
                            }
                        }

                        var tenMst = tenMsts.FirstOrDefault(t => t.ItemCd == targetItem.Item5.ItemCd);
                        var commonCenter = commonCenters.FirstOrDefault(k => k.CenterCd == tenMst?.CenterCd);
                        var kensaMst = targetItem == null ? null : kensaMsts.FirstOrDefault(k => k.KensaItemCd == tenMst?.KensaItemCd && k.KensaItemSeqNo == tenMst.KensaItemSeqNo); List<OrdInfDetailModel> odrInfDetail = new();

                        string yjCd = tenMst?.YjCd ?? string.Empty;

                        var dosageDrug = NoTrackingDataContext.DosageDrugs.FirstOrDefault(d => d.YjCd == yjCd);
                        var dosageDosages = NoTrackingDataContext.DosageDosages.Where(item => dosageDrug != null && dosageDrug.DoeiCd == item.DoeiCd).ToList();

                        var dosagetModel = dosageDrug == null ? new DosageDrugModel() : new DosageDrugModel(
                                                         dosageDrug.YjCd,
                                                         dosageDrug.DoeiCd,
                                                         dosageDrug.DgurKbn ?? string.Empty,
                                                         dosageDrug.KikakiUnit ?? string.Empty,
                                                         dosageDrug.YakkaiUnit ?? string.Empty,
                                                         dosageDrug.RikikaRate,
                                                         dosageDrug.RikikaUnit ?? string.Empty,
                                                         dosageDrug.YoukaiekiCd ?? string.Empty,
                                                         dosageDosages.FirstOrDefault(item => item.DoeiCd == dosageDrug.DoeiCd)?.UsageDosage?.Replace("；", Environment.NewLine) ?? string.Empty
                                                );


                        var odrInfDetailModel = new OrdInfDetailModel(
                            hpId,
                            raiinNo,
                            0,
                            0,
                            1,
                            ptId,
                            sinDate,
                            sinKouiKbn,
                            itemCd,
                            itemName,
                            suryo,
                            unitName,
                            unitSBT,
                            termVal,
                            kohatuKbn,
                            syohoKbn,
                            syohoLimitKbn,
                            drugKbn,
                            yohoKbn,
                            kokuji1,
                            kokuji2,
                            0,
                            ipnCd,
                            ipnName,
                            0,
                            DateTime.MinValue,
                            0,
                            string.Empty,
                            string.Empty,
                            string.Empty,
                            string.Empty,
                            string.Empty,
                            string.Empty,
                            0,
                            string.Empty,
                            0,
                            0,
                            false,
                            0,
                            tenMst?.CmtCol1 ?? 0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            yjCd,
                            new(),
                            0,
                            0,
                            string.Empty,
                            string.Empty,
                            kensaMst?.CenterItemCd1 ?? string.Empty,
                            kensaMst?.CenterItemCd2 ?? string.Empty,
                            tenMst?.CmtColKeta1 ?? 0,
                            tenMst?.CmtColKeta2 ?? 0,
                            tenMst?.CmtColKeta3 ?? 0,
                            tenMst?.CmtColKeta4 ?? 0,
                            tenMst?.CmtCol2 ?? 0,
                            tenMst?.CmtCol3 ?? 0,
                            tenMst?.CmtCol4 ?? 0,
                            tenMst?.HandanGrpKbn ?? 0,
                            kensaMst == null,
                            dosagetModel.RikikaRate,
                            dosagetModel.KikakiUnit,
                            dosagetModel.YakkaiUnit,
                            dosagetModel.RikikaUnit,
                            dosagetModel.YoukaiekiCd,
                            dosagetModel.MemoItem,
                            tenMst?.BuiKbn ?? 0,
                            tenMst?.IsAdopted ?? 0,
                            tenMst?.SenteiRyoyoKbn ?? 0,
                            tenMst?.CenterCd ?? string.Empty,
                            commonCenter?.DspCenterName ?? string.Empty,
                            tenMst?.RousaiKbn ?? 0
                        );
                        odrInfDetail.Add(odrInfDetailModel);

                        OrdInfModel odrInf = new OrdInfModel(
                                hpId,
                                raiinNo,
                                0,
                                0,
                                ptId,
                                sinDate,
                                0,
                                tenItemMst.SinKouiKbn,
                                string.Empty,
                                0,
                                0,
                                0,
                                0,
                                0,
                                1,
                                0,
                                0,
                                0,
                                odrInfDetail,
                                DateTime.MinValue,
                                userId,
                                string.Empty,
                                DateTime.MinValue,
                                userId,
                                string.Empty,
                                string.Empty,
                                string.Empty
                            );

                        result.Add(new(-1, odrInf));
                        checkingOdr.OrdInfDetails.Remove(detail);
                        isAdded = true;
                    }
                }
                else
                {
                    detail.ChangeSuryo(targetItem.Item6);
                    isAdded = true;
                }
                odrInfDetailIndex++;
            }

            if (isAdded)
            {
                result.Add(new(index, new(DeleteTypes.Deleted)));
                result.Add(new(index, checkingOdr));
            }

            odrInfIndex++;
        }

        foreach (var item in result)
        {
            if (item.Item1 >= 0 && item.Item2.OrdInfDetails.Count == 0)
            {
                item.Item2.Delete();
            }
        }

        return result;
    }

    private List<SanteiGrpDetail> FindSanteiGrpDetailList(int hpId, string itemCd)
    {
        var entities = NoTrackingDataContext.SanteiGrpDetails
                                .Where(s => s.HpId == hpId && s.ItemCd == itemCd);
        return entities.ToList();
    }

    private SanteiCntCheck? FindSanteiCntCheck(int hpId, int santeiGrpCd, int sinDate)
    {
        var entity = NoTrackingDataContext.SanteiCntChecks.Where(e =>
             e.HpId == hpId &&
             e.SanteiGrpCd == santeiGrpCd &&
             e.StartDate <= sinDate &&
             e.EndDate >= sinDate)
             .FirstOrDefault();
        return entity;
    }

    public TenItemModel FindTenMst(int hpId, string itemCd, int sinDate)
    {
        var entity = NoTrackingDataContext.TenMsts.FirstOrDefault(p =>
               p.HpId == hpId &&
               p.StartDate <= sinDate &&
               p.EndDate >= sinDate &&
               p.ItemCd == itemCd &&
               p.IsDeleted == DeleteTypes.None);

        return entity != null ? new TenItemModel(
               entity.HpId,
               entity.ItemCd,
               entity.RousaiKbn,
               entity.KanaName1 ?? string.Empty,
               entity.Name ?? string.Empty,
               entity.KohatuKbn,
               entity.MadokuKbn,
               entity.KouseisinKbn,
               entity.OdrUnitName ?? string.Empty,
               entity.EndDate,
               entity.DrugKbn,
               entity.MasterSbt ?? string.Empty,
               entity.BuiKbn,
               entity.IsAdopted,
               entity.Ten,
               entity.TenId,
               string.Empty,
               string.Empty,
               entity.CmtCol1,
               entity.IpnNameCd ?? string.Empty,
               entity.SinKouiKbn,
               entity.YjCd ?? string.Empty,
               entity.CnvUnitName ?? string.Empty,
               entity.StartDate,
               entity.YohoKbn,
               entity.CmtColKeta1,
               entity.CmtColKeta2,
               entity.CmtColKeta3,
               entity.CmtColKeta4,
               entity.CmtCol2,
               entity.CmtCol3,
               entity.CmtCol4,
               entity.IpnNameCd ?? string.Empty,
               entity.MinAge ?? string.Empty,
               entity.MaxAge ?? string.Empty,
               entity.SanteiItemCd ?? string.Empty,
               entity.OdrTermVal,
               entity.CnvTermVal,
               entity.DefaultVal,
               entity.Kokuji1 ?? string.Empty,
               entity.Kokuji2 ?? string.Empty,
               string.Empty,
               0,
               0,
               true
            ) : new();

    }

    private bool CheckIsGetYakkaPrice(TenMst tenMst, int sinDate, List<IpnKasanExclude> ipnKasanExcludes, List<IpnKasanExcludeItem> ipnKasanExcludeItems)
    {
        if (tenMst == null) return false;
        var ipnKasanExclude = ipnKasanExcludes.FirstOrDefault(u => u.IpnNameCd == tenMst.IpnNameCd && u.StartDate <= sinDate && u.EndDate >= sinDate);
        var ipnKasanExcludeItem = ipnKasanExcludeItems.FirstOrDefault(u => u.ItemCd == tenMst.ItemCd && u.StartDate <= sinDate && u.EndDate >= sinDate);

        return ipnKasanExclude == null && ipnKasanExcludeItem == null;
    }

    private bool CheckIsGetYakkaPrice(TenMst? tenMst, int sinDate)
    {
        if (tenMst == null) return false;
        var ipnKasanExclude = NoTrackingDataContext.ipnKasanExcludes.Where(u => u.IpnNameCd == tenMst.IpnNameCd && u.StartDate <= sinDate && u.EndDate >= sinDate).FirstOrDefault();

        var ipnKasanExcludeItem = NoTrackingDataContext.ipnKasanExcludeItems.Where(u => u.ItemCd == tenMst.ItemCd && u.StartDate <= sinDate && u.EndDate >= sinDate).FirstOrDefault();
        return ipnKasanExclude == null && ipnKasanExcludeItem == null;
    }

    public bool IsHolidayForDefaultTime(int hpId, int sinDate)
    {
        var holidayMst = NoTrackingDataContext.HolidayMsts.Where(t => t.HpId == hpId && t.SinDate == sinDate && t.IsDeleted != 1).FirstOrDefault();
        return holidayMst != null && holidayMst.HolidayKbn != 0;
    }

    //Key of Dictionary is ItemCd
    public List<OrdInfModel> ConvertConversionItemToOrderInfModel(int hpId, long raiinNo, long ptId, int sinDate, List<OrdInfModel> odrInfItems, Dictionary<string, TenItemModel> expiredItems)
    {
        List<string> ipnCds = new();
        List<(int, int, OrdInfDetailModel, bool)> track = new();
        foreach (var odrInfItem in odrInfItems)
        {
            ipnCds.AddRange(odrInfItem.OrdInfDetails.Select(od => od.IpnCd));
        }
        var ipnItems = NoTrackingDataContext.IpnNameMsts.Where(i =>
               i.StartDate <= sinDate &&
               i.EndDate >= sinDate).AsEnumerable().
               Where(i => ipnCds.Contains(i.IpnNameCd)).ToList();

        var ipnMinYakkaMsts = NoTrackingDataContext.IpnMinYakkaMsts.Where(i =>
           i.StartDate <= sinDate &&
           i.EndDate >= sinDate).AsEnumerable().Where(i =>
           ipnCds.Contains(i.IpnNameCd)).ToList();

        var itemCds = expiredItems.Values.Select(e => e.ItemCd).Distinct().ToList();
        var tenMstDbs = NoTrackingDataContext.TenMsts.Where(t => t.HpId == hpId && itemCds.Contains(t.ItemCd) && t.StartDate <= sinDate && sinDate <= t.EndDate);
        var kensaItemCds = tenMstDbs.Select(t => t.KensaItemCd).Distinct().ToList();
        var kensaItemSeqNos = tenMstDbs.Select(t => t.KensaItemSeqNo).Distinct().ToList();

        var kensaMsts = NoTrackingDataContext.KensaMsts.Where(e =>
             e.HpId == hpId &&
             kensaItemCds.Contains(e.KensaItemCd) &&
             kensaItemSeqNos.Contains(e.KensaItemSeqNo)).ToList();
        int autoSetKohatu = (int)_systemConf!.GetSettingValue(2020, 2, hpId);
        int autoSetSenpatu = (int)_systemConf.GetSettingValue(2021, 2, hpId);
        int autoSetSyohoKbnKohatuDrug = (int)_systemConf.GetSettingValue(2020, 0, hpId);
        int autoSetSyohoLimitKohatuDrug = (int)_systemConf.GetSettingValue(2020, 1, hpId);
        int autoSetSyohoKbnSenpatuDrug = (int)_systemConf.GetSettingValue(2021, 0, hpId);
        int autoSetSyohoLimitSenpatuDrug = (int)_systemConf.GetSettingValue(2021, 1, hpId);
        var checkKensaIraiCondition = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2019 && p.GrpEdaNo == 1);
        var kensaIraiCondition = checkKensaIraiCondition?.Val ?? 0;
        var checkKensaIrai = NoTrackingDataContext.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 2019 && p.GrpEdaNo == 0);
        var kensaIrai = checkKensaIrai?.Val ?? 0;
        var kensaCenterMsts = NoTrackingDataContext.CommonKensaCenterMst.GroupBy(i => i.CenterCd).Select(g => g.First()).ToList();
        var orderIndex = 0;
        foreach (var order in odrInfItems)
        {
            var orderDetailIndex = 0;
            foreach (var orderDetail in order.OrdInfDetails)
            {
                if (expiredItems.ContainsKey(orderDetail.ItemCd))
                {
                    var tenMst = expiredItems[orderDetail.ItemCd];

                    var tenMstDb = tenMstDbs.FirstOrDefault(t => t.ItemCd == tenMst.ItemCd);
                    track.Add(new(orderIndex, orderDetailIndex, new(), true));
                    var newOrderDetail = ConvertConversionItemToDetailModel(hpId, orderDetail, tenMstDb ?? new(), ipnItems, autoSetKohatu, autoSetSenpatu, autoSetSyohoKbnKohatuDrug, autoSetSyohoLimitKohatuDrug, autoSetSyohoKbnSenpatuDrug, autoSetSyohoLimitSenpatuDrug, kensaMsts, ipnMinYakkaMsts, sinDate, raiinNo, ptId, order.OdrKouiKbn, (int)kensaIraiCondition, (int)kensaIrai, kensaCenterMsts.FirstOrDefault(k => k.CenterCd == tenMst?.CenterCd));
                    track.Add(new(orderIndex, orderDetailIndex, newOrderDetail, false));
                }
                orderDetailIndex++;
            }
            orderIndex++;
        }

        foreach (var item in track)
        {
            if (item.Item4)
            {
                odrInfItems[item.Item1].OrdInfDetails.RemoveAt(item.Item2);
            }
            else
            {
                odrInfItems[item.Item1].OrdInfDetails.Insert(item.Item2, item.Item3);
            }
        }
        return odrInfItems;
    }

    private OrdInfDetailModel ConvertConversionItemToDetailModel(int hpId, OrdInfDetailModel sourceDetail, TenMst tenMst, List<IpnNameMst> ipnNameMsts, int autoSetKohatu, int autoSetSenpatu, int autoSetSyohoKbnKohatuDrug, int autoSetSyohoLimitKohatuDrug, int autoSetSyohoKbnSenpatuDrug, int autoSetSyohoLimitSenpatuDrug, List<KensaMst> kensaMsts, List<IpnMinYakkaMst> ipnMinYakkaMsts, int sinDate, long raiinNo, long ptId, int odrKouiKbn, int kensaIraiCondition, int kensaIrai, CommonKensaCenterMst? commonCenterKensaMst = null)
    {
        string itemCd = tenMst.ItemCd;
        string itemName = tenMst.Name ?? string.Empty;
        string cmtName = sourceDetail.CmtName;
        string cmtOpt = sourceDetail.CmtOpt;
        double suryo = sourceDetail.Suryo;
        string unitName;
        double ten = tenMst.Ten;
        string masterSbt = tenMst.MasterSbt ?? string.Empty;
        int unitSBT = 0;
        double termVal = 0;
        if (!string.IsNullOrEmpty(tenMst.OdrUnitName))
        {
            unitSBT = 1;
            unitName = tenMst.OdrUnitName;
            termVal = tenMst.OdrTermVal;
        }
        else if (!string.IsNullOrEmpty(tenMst.CnvUnitName))
        {
            unitSBT = 2;
            unitName = tenMst.CnvUnitName;
            termVal = tenMst.CnvTermVal;
        }
        else
        {
            unitSBT = 0;
            unitName = string.Empty;
            termVal = 0;
            suryo = 0;
        }
        int kohatuKbn = tenMst.KohatuKbn;
        int yohoKbn = tenMst.YohoKbn;
        string ipnCd = tenMst.IpnNameCd ?? string.Empty;
        string ipnName = string.Empty;
        if (!string.IsNullOrEmpty(sourceDetail.IpnCd))
        {
            ipnName = ipnNameMsts.FirstOrDefault(i => i.IpnNameCd == tenMst.IpnNameCd)?.IpnName ?? string.Empty;
        }
        else
        {
            ipnName = string.Empty;
        }
        int drugKbn = tenMst.DrugKbn;
        int syohoKbn = 0;
        int syohoLimitKbn = 0;
        if (sourceDetail.SinKouiKbn == 20 && sourceDetail.DrugKbn > 0)
        {
            switch (sourceDetail.KohatuKbn)
            {
                case 0:
                    // 先発品
                    break;
                // Incase KokatuKbn = 1 or 2, need to keep old SyohoKbn and SyohoKbnLimit set from previous step
                case 1:
                    // 後発品
                    if (autoSetKohatu == 0)
                    {
                        //マスタ設定に準じる
                        syohoKbn = autoSetSyohoKbnKohatuDrug + 1;
                        syohoLimitKbn = autoSetSyohoLimitKohatuDrug;
                    }
                    else
                    {
                        //各セットの設定に準じる
                        // keep old SyohoKbn and SyohoKbnLimit set from previous step
                    }
                    break;
                case 2:
                    // 後発品のある先発品
                    if (autoSetSenpatu == 0)
                    {
                        //マスタ設定に準じる
                        syohoKbn = autoSetSyohoKbnSenpatuDrug + 1;
                        syohoLimitKbn = autoSetSyohoLimitSenpatuDrug;
                    }
                    else
                    {
                        //各セットの設定に準じる
                        // keep old SyohoKbn and SyohoKbnLimit set from previous step
                    }
                    break;
            }
            if (sourceDetail.SyohoKbn == 3 && string.IsNullOrEmpty(sourceDetail.IpnName))
            {
                // 一般名マスタに登録がない
                syohoKbn = 2;
            }
        }

        int cmtCol1 = tenMst.CmtCol1;
        KensaMst? kensaMstModel = null;
        if ((sourceDetail.SinKouiKbn == 61 || sourceDetail.SinKouiKbn == 64)
            && !string.IsNullOrEmpty(tenMst.KensaItemCd))
        {
            kensaMstModel = kensaMsts.FirstOrDefault(k => k.KensaItemCd == tenMst.KensaItemCd && k.KensaItemSeqNo == tenMst.KensaItemSeqNo);
        }
        else
        {
            kensaMstModel = null;
        }
        var ipnMinYakkaMstModel = ipnMinYakkaMsts.FirstOrDefault(i => i.IpnNameCd == tenMst.IpnNameCd);

        var result = new OrdInfDetailModel(
                hpId,
                raiinNo,
                0,
                0,
                sourceDetail.RowNo,
                ptId,
                sinDate,
                tenMst?.SinKouiKbn ?? 0,
                itemCd,
                itemName,
                suryo,
                unitName,
                unitSBT,
                termVal,
                kohatuKbn,
                syohoKbn,
                syohoLimitKbn,
                drugKbn,
                yohoKbn,
                tenMst?.Kokuji1 ?? string.Empty,
                tenMst?.Kokuji2 ?? string.Empty,
                tenMst?.IsNodspRece ?? 0,
                ipnCd,
                ipnName,
                sourceDetail.JissiKbn,
                sourceDetail.JissiDate,
                sourceDetail.JissiId,
                sourceDetail.JissiMachine,
                sourceDetail.ReqCd,
                sourceDetail.Bunkatu,
                cmtName,
                cmtOpt,
                sourceDetail.FontColor,
               sourceDetail.CommentNewline,
               masterSbt,
               sourceDetail.InOutKbn,
               ipnMinYakkaMstModel?.Yakka ?? 0,
               CheckIsGetYakkaPrice(tenMst, sinDate),
               sourceDetail.RefillSetting,
               cmtCol1,
               ten,
               sourceDetail.BunkatuKoui,
               sourceDetail.AlternationIndex,
               GetKensaGaichu(sourceDetail, tenMst, sourceDetail.InOutKbn, odrKouiKbn, kensaMstModel, kensaIraiCondition, kensaIrai),
               tenMst?.OdrTermVal ?? 0,
               tenMst?.CnvTermVal ?? 0,
               tenMst?.YjCd ?? string.Empty,
               sourceDetail.YohoSets,
               sourceDetail.Kasan1,
               sourceDetail.Kasan2,
               tenMst?.CnvUnitName ?? string.Empty,
               tenMst?.OdrUnitName ?? string.Empty,
               sourceDetail.CenterItemCd1,
               sourceDetail.CenterItemCd2,
               tenMst?.CmtColKeta1 ?? 0,
               tenMst?.CmtColKeta2 ?? 0,
               tenMst?.CmtColKeta3 ?? 0,
               tenMst?.CmtColKeta4 ?? 0,
               tenMst?.CmtCol2 ?? 0,
               tenMst?.CmtCol3 ?? 0,
               tenMst?.CmtCol4 ?? 0,
               tenMst?.HandanGrpKbn ?? 0,
               sourceDetail == null,
               null,
               odrKouiKbn,
               tenMst?.BuiKbn ?? 0,
               tenMst?.RousaiKbn ?? 0,
               tenMst?.CenterCd ?? string.Empty,
               sourceDetail?.BikoComment ?? 0,
               commonCenterKensaMst?.DspCenterName ?? string.Empty,
               sourceDetail?.RelationItem ?? string.Empty
               );

        return result;
    }

    public List<OdrDateInfModel> GetLastDayInfoList(int hpId, long ptId, int sinDate)
    {
        var odrDateInfs = NoTrackingDataContext.OdrDateInfs.Where(item => item.HpId == hpId && item.IsDeleted == 0).ToList();
        var grpIdList = odrDateInfs.Select(item => item.GrpId).Distinct().ToList();
        var odrDateDetails = NoTrackingDataContext.OdrDateDetails.Where(item => item.HpId == hpId
                                                                                && item.IsDeleted == 0
                                                                                && grpIdList.Contains(item.GrpId))
                                                                 .ToList();

        var itemCdList = odrDateDetails.Select(item => item.ItemCd).Distinct().ToList();
        var odrInfs = NoTrackingDataContext.OdrInfs.Where(item => item.HpId == hpId
                                                                  && item.IsDeleted == 0
                                                                  && item.PtId == ptId
                                                                  && item.SinDate < sinDate)
                                                   .OrderByDescending(x => x.SinDate)
                                                   .ToList();
        var tenMstList = NoTrackingDataContext.TenMsts.Where(item => item.HpId == hpId
                                                                     && item.StartDate <= sinDate
                                                                     && item.EndDate >= sinDate
                                                                     && itemCdList.Contains(item.ItemCd)
                                                                     && item.IsDeleted == 0)
                                                       .ToList();
        var raiinNoList = odrInfs.Select(item => item.RaiinNo).Distinct().ToList();
        var odrInfDetails = NoTrackingDataContext.OdrInfDetails.Where(item => item.HpId == hpId
                                                                              && item.PtId == ptId
                                                                              && item.SinDate < sinDate
                                                                              && raiinNoList.Contains(item.RaiinNo)
                                                                              && itemCdList.Contains(item.ItemCd))
                                                               .OrderByDescending(x => x.SinDate)
                                                               .ToList();

        var queryOdrInf = (from odrInf in odrInfs
                           select new
                           {
                               odrInf,
                               details = odrInfDetails.Where(item => odrInf.RaiinNo == item.RaiinNo
                                                                     && odrInf.RpNo == item.RpNo
                                                                     && odrInf.RpEdaNo == item.RpEdaNo)
                                                      .ToList()
                           }).ToList();

        List<OdrDateInfModel> result = new();
        foreach (var odrDate in odrDateInfs)
        {
            var odrDateDetailItemList = (from detailDate in odrDateDetails.Where(item => item.GrpId == odrDate.GrpId)
                                         join tenMst in tenMstList on detailDate.ItemCd equals tenMst.ItemCd
                                         select new OdrDateDetailModel(
                                                    detailDate.GrpId,
                                                    detailDate.SeqNo,
                                                    detailDate.ItemCd ?? string.Empty,
                                                    tenMst.Name ?? string.Empty,
                                                    detailDate.SortNo))
                                        .ToList();
            var itemCdItemList = odrDateDetailItemList.Select(item => item.ItemCd).Distinct().ToList();
            var odrInfDetailList = queryOdrInf.SelectMany(item => item.details)
                                              .Where(item => item.ItemCd != null
                                                             && itemCdItemList.Contains(item.ItemCd))
                                              .ToList();
            var sinDateDisplay = odrInfDetailList.Any() ? CIUtil.SDateToShowSDate(odrInfDetailList.OrderByDescending(x => x.SinDate).First().SinDate) : "なし";

            result.Add(new OdrDateInfModel(
                           odrDate.GrpId,
                           odrDate.SortNo,
                           odrDate.GrpName ?? string.Empty,
                           sinDateDisplay,
                           odrDateDetailItemList));
        }
        return result;
    }

    public bool SaveSettingLastDayInfo(int hpId, int userId, List<OdrDateInfModel> odrDateInfModels)
    {
        var odrDateInfs = new List<OdrDateInf>();
        var odrDateDetails = new List<OdrDateDetail>();
        var grpIdMax = 0;
        var seqNoMax = 0;
        var OdrDateInfList = NoTrackingDataContext.OdrDateInfs.Where(x => x.HpId == hpId).ToList();
        var OdrDateDetailList = NoTrackingDataContext.OdrDateDetails.Where(x => x.HpId == hpId).ToList();

        if (OdrDateInfList.Count != 0)
        {
            grpIdMax = (NoTrackingDataContext.OdrDateInfs.Where(x => x.HpId == hpId).Select(x => x.GrpId).DefaultIfEmpty()?.Max() ?? 0) + 1;
        }
        else
        {
            grpIdMax = 1;
        }

        if (OdrDateDetailList.Count != 0)
        {
            seqNoMax = (NoTrackingDataContext.OdrDateDetails.Where(x => x.HpId == hpId).Select(x => x.SeqNo).DefaultIfEmpty()?.Max() ?? 0) + 1;
        }
        else
        {
            seqNoMax = 1;
        }

        foreach (var item in odrDateInfModels)
        {
            if (item.GrpName == string.Empty && item.GrpId == 0) continue;

            if (item.IsDeleted == 1)
            {
                var odrDateInf = TrackingDataContext.OdrDateInfs.FirstOrDefault(x => x.HpId == hpId && x.GrpId == item.GrpId);
                if (odrDateInf != null)
                {
                    odrDateInf.IsDeleted = 1;
                }
            }
            else
            {
                var odrDateInf = TrackingDataContext.OdrDateInfs.FirstOrDefault(x => x.HpId == hpId && x.GrpId == item.GrpId);
                if (odrDateInf != null)
                {
                    odrDateInf.GrpName = item.GrpName;
                    odrDateInf.SortNo = item.SortNo;
                    odrDateInf.UpdateId = userId;
                    odrDateInf.UpdateDate = CIUtil.GetJapanDateTimeNow();
                }
                else
                {
                    OdrDateInf odrDateInfItem = ConvertOdrDateInfList(hpId, userId, item);
                    odrDateInfs.Add(odrDateInfItem);
                }
            }
        }

        var grpIdInfMax = grpIdMax;

        foreach (var data in odrDateInfs)
        {
            data.GrpId = grpIdInfMax;
            grpIdInfMax++;
        }

        TrackingDataContext.OdrDateInfs.AddRange(odrDateInfs);

        foreach (var item in odrDateInfModels)
        {
            if (item.GrpId != 0)
            {
                continue;
            }
            else
            {
                item.ChangeGrpId(grpIdMax);
                grpIdMax++;
            }
        }

        foreach (var item in odrDateInfModels)
        {
            foreach (var OdrDateDetailItem in item.OdrDateDetailList)
            {
                if (OdrDateDetailItem.ItemCd == "") continue;

                if (OdrDateDetailItem.IsDeleted == 1)
                {
                    var odrDateDetail = TrackingDataContext.OdrDateDetails.FirstOrDefault(x => x.HpId == hpId && x.GrpId == OdrDateDetailItem.GrpId && x.SeqNo == OdrDateDetailItem.SeqNo);
                    if (odrDateDetail != null)
                    {
                        odrDateDetail.IsDeleted = 1;
                    }
                }
                else
                {
                    var odrDateDetail = TrackingDataContext.OdrDateDetails.FirstOrDefault(x => x.HpId == hpId && x.GrpId == OdrDateDetailItem.GrpId && x.SeqNo == OdrDateDetailItem.SeqNo);
                    if (odrDateDetail != null)
                    {
                        odrDateDetail.ItemCd = OdrDateDetailItem.ItemCd;
                        odrDateDetail.SortNo = OdrDateDetailItem.SortNo;
                        odrDateDetail.UpdateId = userId;
                        odrDateDetail.UpdateDate = CIUtil.GetJapanDateTimeNow();
                    }
                    else
                    {
                        OdrDateDetail odrdateDetailitem = ConvertOdrDateDetailList(hpId, userId, OdrDateDetailItem, item.GrpId);
                        odrDateDetails.Add(odrdateDetailitem);
                    }
                }
            }
        }

        foreach (var data in odrDateDetails)
        {
            data.SeqNo = seqNoMax;
            seqNoMax++;
        }

        TrackingDataContext.OdrDateDetails.AddRange(odrDateDetails);

        return TrackingDataContext.SaveChanges() > 0;
    }

    private OdrDateInf ConvertOdrDateInfList(int hpId, int userId, OdrDateInfModel u)
    {
        return new OdrDateInf
        {
            HpId = hpId,
            GrpId = 0,
            SortNo = u.SortNo,
            GrpName = u.GrpName,
            IsDeleted = u.IsDeleted,
            CreateId = userId,
            UpdateId = userId,
            CreateDate = CIUtil.GetJapanDateTimeNow(),
            UpdateDate = CIUtil.GetJapanDateTimeNow()
        };
    }

    private OdrDateDetail ConvertOdrDateDetailList(int hpId, int userId, OdrDateDetailModel u, int grpId)
    {
        return new OdrDateDetail
        {
            HpId = hpId,
            GrpId = grpId,
            SeqNo = 0,
            ItemCd = u.ItemCd,
            IsDeleted = u.IsDeleted,
            SortNo = u.SortNo,
            CreateId = userId,
            UpdateId = userId,
            CreateDate = CIUtil.GetJapanDateTimeNow(),
            UpdateDate = CIUtil.GetJapanDateTimeNow()
        };
    }

    public int GetConfirmationType(int hpId, long ptId, long raiinNo, int sinDate)
    {
        return NoTrackingDataContext.RaiinInfs.FirstOrDefault(item => item.HpId == hpId
                                                                      && item.PtId == ptId
                                                                      && item.RaiinNo == raiinNo
                                                                      && item.SinDate == sinDate
                                                                      && item.IsDeleted == DeleteTypes.None)?.ConfirmationType ?? 0;
    }

    public bool CheckValidPtId(int hpId, long ptId)
    {
        var check = NoTrackingDataContext.PtInfs.Any(p => p.HpId == hpId && p.PtId == ptId && p.IsDelete == DeleteTypes.None);
        return check;
    }

    public bool CheckHokenId(int hpId, long ptId, int hokenId, long seqNo)
    {
        var check = NoTrackingDataContext.PtHokenInfs.Any(h => h.HpId == hpId && h.HokenId == hokenId && h.SeqNo == seqNo && h.IsDeleted == DeleteTypes.None);
        return check;
    }

    public bool CheckPtKohi(int hpId, long ptId, int hokenId, long seqNo)
    {
        var check = NoTrackingDataContext.PtKohis.Any(k => k.HpId == hpId && k.HokenId == hokenId && k.SeqNo == seqNo && k.IsDeleted == DeleteTypes.None);
        return check;
    }
    public List<OrdInfModel> DoFromHistory(int hpId, int sinDate, long raiinNo, int sainteiKbn, int hokenPid, int hokenId, List<int> kohiIds, int userId, long ptId, List<OrdInfModel> historyOdrInfModels)
    {
        var hokenSyosaishin = NoTrackingDataContext.PtHokenInfs
            .FirstOrDefault(p => p.HpId == hpId &&
                                 p.PtId == ptId &&
                                 p.HokenId == hokenId &&
                                 p.IsDeleted == DeleteTypes.None
                            );
        if (hokenSyosaishin == null)
            return FromHistory(hpId, sinDate, raiinNo, sainteiKbn, userId, ptId, historyOdrInfModels);

        if (hokenSyosaishin.HokenKbn != 1 && hokenSyosaishin.HokenKbn != 2)
        {
            historyOdrInfModels.ForEach(p => p.UpdateHokenPid(hokenPid));
            return FromHistory(hpId, sinDate, raiinNo, sainteiKbn, userId, ptId, historyOdrInfModels);
        }

        var lstKohiSyosaishin = kohiIds.Where(p => p > 0).ToList();
        var newOdrInfModels = new List<OrdInfModel>();
        var lstHokenPidHist = historyOdrInfModels.Select(p => p.HokenPid).Distinct().ToList();
        var lockObj = new object();
        foreach (var hokenPidHist in lstHokenPidHist)
        {
            var hokenPInf = NoTrackingDataContext.PtHokenPatterns
            .FirstOrDefault(p => p.HpId == hpId &&
                                 p.PtId == ptId &&
                                 p.HokenPid == hokenPidHist &&
                                 p.IsDeleted == DeleteTypes.None
                            );
            if (hokenPInf == null)
            {
                CopyOrders(historyOdrInfModels, newOdrInfModels, hokenPidHist, hokenPid, lockObj);
                continue;
            }
            var hokenInf = NoTrackingDataContext.PtHokenInfs
            .FirstOrDefault(p => p.HpId == hpId &&
                            p.PtId == ptId &&
                            p.HokenId == hokenPInf.HokenId &&
                            p.IsDeleted == DeleteTypes.None
                       );
            if (hokenInf == null)
            {
                CopyOrders(historyOdrInfModels, newOdrInfModels, hokenPidHist, hokenPid, lockObj);
                continue;
            }
            if (hokenInf.HokenKbn != 1 && hokenInf.HokenKbn != 2)
            {
                CopyOrders(historyOdrInfModels, newOdrInfModels, hokenPidHist, hokenPid, lockObj);
                continue;
            }
            var lstKohiHist = new List<int> { hokenPInf.Kohi1Id, hokenPInf.Kohi2Id, hokenPInf.Kohi3Id, hokenPInf.Kohi4Id }.Where(p => p > 0).ToList();
            var lstKohiNew = GetListKohiValid(lstKohiHist, hpId, ptId, sinDate, lstKohiSyosaishin);

            if (lstKohiNew.Count == 0 && hokenSyosaishin.HokenNo == 100)
            {
                CopyOrders(historyOdrInfModels, newOdrInfModels, hokenPidHist, hokenPid, lockObj);
                continue;
            }
            var (kohi1Id, kohi2Id, kohi3Id, kohi4Id, kohi1SeqNo, kohi2SeqNo, kohi3SeqNo, kohi4SeqNo) = ExtractKohiIds(lstKohiNew);
            var hokenPExist = NoTrackingDataContext.PtHokenPatterns
               .FirstOrDefault(p => p.HpId == hpId && p.PtId == ptId && p.HokenId == hokenId && p.Kohi1Id == kohi1Id && p.Kohi2Id == kohi2Id && p.Kohi3Id == kohi3Id && p.Kohi4Id == kohi4Id);

            if (hokenPExist != null)
            {
                CopyOrders(historyOdrInfModels, newOdrInfModels, hokenPidHist, hokenPExist.HokenPid, lockObj);
                continue;
            }
            int hokenSbtKbn1 = 0, hokenSbtKbn2 = 0, hokenSbtKbn3 = 0, hokenSbtKbn4 = 0;
            string houbetu1 = "", houbetu2 = "", houbetu3 = "", houbetu4 = "";

            _insuranceInforRepository!.UpsertHokenInf(hpId, ptId, userId, hokenId, kohi1Id, kohi2Id, kohi3Id, kohi4Id);

            HokenInfModel? ptHokenInf = _insuranceInforRepository!.GetPtHokenInfByHokenId(
                   hpId: hpId,
                   hokenId: hokenSyosaishin.HokenId,
                   ptId: ptId,
                   seqNo: hokenSyosaishin.SeqNo
               );

            var hokenSbtCd = GetHokenSbtCd(
                            hpId: hpId,
                            ptId: ptId,
                            hokenId: hokenId,
                            kohi1Id: kohi1Id,
                            kohi1SeqNo: kohi1SeqNo,
                            kohi2Id: kohi2Id,
                            kohi2SeqNo: kohi2SeqNo,
                            kohi3Id: kohi3Id,
                            kohi3SeqNo: kohi3SeqNo,
                            kohi4Id: kohi4Id,
                            kohi4SeqNo: kohi4SeqNo,
                            ptHokenInfModel: ptHokenInf!,
                            ref hokenSbtKbn1,
                            ref houbetu1,
                            ref hokenSbtKbn2,
                            ref houbetu2,
                            ref hokenSbtKbn3,
                            ref houbetu3,
                            ref hokenSbtKbn4,
                            ref houbetu4
                        );

            // Pt Hoken pattern
            PtHokenPatternModel ptHokenPatternModel = new PtHokenPatternModel
            (
                ptId,
                hokenInf.SeqNo,
                hokenInf.HokenKbn,
                hokenSbtCd,
                hokenInf.HokenId,
                kohi1Id,
                kohi2Id,
                kohi3Id,
                kohi4Id,
                string.Empty,
                hokenInf.StartDate,
                hokenInf.EndDate
            );
            PtHokenPatternModel ptHokenPattern = UpsertPtHokenPattern(hpId, ptId, ptHokenPatternModel, userId);
            CopyOrders(historyOdrInfModels, newOdrInfModels, hokenPidHist, ptHokenPattern.HokenPid, lockObj);
        }
        ;

        return FromHistory(hpId, sinDate, raiinNo, sainteiKbn, userId, ptId, newOdrInfModels);
    }

    private void CopyOrders(List<OrdInfModel> source, List<OrdInfModel> destination, long oldHokenPid, int newHokenPid, object lockObj)
    {
        var orders = source.Where(p => p.HokenPid == oldHokenPid).ToList();
        orders.ForEach(p => p.UpdateHokenPid(newHokenPid));
        lock (lockObj)
        {
            destination.AddRange(orders);
        }
    }
    private Dictionary<int, long> GetListKohiValid(List<int> kohiHist, int hpId, long ptId, int sinDate, List<int> kohiSyosaishin)
    {
        var lstKohiValid = new List<PtKohi>();
        foreach (var kohi in kohiHist)
        {
            var kohiValid = NoTrackingDataContext.PtKohis
         .FirstOrDefault(p => kohi == p.HokenId &&
                     p.HpId == hpId &&
                     p.PtId == ptId &&
                     p.IsDeleted == DeleteTypes.None);
            if (kohiValid != null)
            {
                lstKohiValid.Add(kohiValid);
            }
        }

        var result = new Dictionary<int, long>();
        foreach (var kohi in lstKohiValid)
        {
            if (kohi.StartDate <= sinDate && kohi.EndDate >= sinDate || kohiSyosaishin.Contains(kohi.HokenId))
                result.Add(kohi.HokenId, kohi.SeqNo);
        }
        return result;
    }
    private (int, int, int, int, long, long, long, long) ExtractKohiIds(Dictionary<int, long> lstKohiNew)
    {
        var ids = lstKohiNew.Keys.ToArray();
        var seqNos = lstKohiNew.Values.ToArray();
        return (
            ids.ElementAtOrDefault(0), ids.ElementAtOrDefault(1), ids.ElementAtOrDefault(2), ids.ElementAtOrDefault(3),
            seqNos.ElementAtOrDefault(0), seqNos.ElementAtOrDefault(1), seqNos.ElementAtOrDefault(2), seqNos.ElementAtOrDefault(3)
        );
    }

    private int GetHokenSbtCd(int hpId, long ptId, int hokenId, int kohi1Id, long kohi1SeqNo, int kohi2Id, long kohi2SeqNo, int kohi3Id, long kohi3SeqNo, int kohi4Id, long kohi4SeqNo, HokenInfModel ptHokenInfModel, ref int hokenSbtKbn1, ref string houbetu1, ref int hokenSbtKbn2, ref string houbetu2, ref int hokenSbtKbn3, ref string houbetu3, ref int hokenSbtKbn4, ref string houbetu4)
    {
        bool IsEmptyHoken = hokenId == 0;

        if (IsEmptyHoken
            && kohi1Id == 0 && kohi2Id == 0 && kohi3Id == 0 && kohi4Id == 0
            && ptHokenInfModel.HokenKbn < 10)
        {
            return -1;
        }

        int firstNum = 0;
        int secondNum = 0;
        int thirNum = 0;
        switch (ptHokenInfModel.HokenKbn)
        {
            case 11:
            case 12:
            case 13:
            case 14:
                return 0;
            case 0:
                return 0;
            case 1:
                firstNum = 1;
                break;
            case 2:
                if (ptHokenInfModel != null)
                {
                    string hokensyaNoTrim = ptHokenInfModel.HokensyaNo.ToString().Trim();
                    if (!string.IsNullOrEmpty(hokensyaNoTrim)
                        && hokensyaNoTrim.Length == 8)
                    {
                        if (hokensyaNoTrim.StartsWith("39"))
                        {
                            firstNum = 3;
                            break;
                        }
                        else if (hokensyaNoTrim.StartsWith("67"))
                        {
                            firstNum = 4;
                            break;
                        }
                    }
                }
                firstNum = 2;
                break;
            case 3:
                firstNum = 3;
                break;
            case 4:
                firstNum = 4;
                break;
        }


        if (!IsEmptyHoken && ptHokenInfModel != null && !ptHokenInfModel.IsNoHoken)
        {
            secondNum++;
        }

        int maruchoCount = 0;

        _insuranceInforRepository!.ProcessKohiId(hpId, ptId, kohi1Id, kohi1SeqNo, ref secondNum, ref maruchoCount, ref hokenSbtKbn1, ref houbetu1);
        _insuranceInforRepository.ProcessKohiId(hpId, ptId, kohi2Id, kohi2SeqNo, ref secondNum, ref maruchoCount, ref hokenSbtKbn2, ref houbetu2);
        _insuranceInforRepository.ProcessKohiId(hpId, ptId, kohi3Id, kohi3SeqNo, ref secondNum, ref maruchoCount, ref hokenSbtKbn3, ref houbetu3);
        _insuranceInforRepository.ProcessKohiId(hpId, ptId, kohi4Id, kohi4SeqNo, ref secondNum, ref maruchoCount, ref hokenSbtKbn4, ref houbetu4);

        thirNum = secondNum - maruchoCount;

        if (IsEmptyHoken || (ptHokenInfModel != null && ptHokenInfModel.IsNoHoken))
        {
            firstNum = 5;
        }

        return firstNum * 100 + secondNum * 10 + thirNum;
    }


    private bool UpdateSchemaFiles(int hpId, long ptId, int userId, long raiinNo, int sinDate, List<SchemaItemModel> schemaItems, DateTime timeUpdate)
    {
        try
        {
            if (!schemaItems.Any())
            {
                return true;
            }
            var listAdd = schemaItems.Where(i => i.IsAddNew == true && i.FileId == 0);
            var listUpdate = schemaItems.Where(i => i.IsAddNew == false && i.FileId != 0);
            int fileNo = 0;
            foreach (var item in listAdd)
            {
                fileNo++;
                var newFiling = new FilingInf
                {
                    HpId = hpId,
                    PtId = item.PtId,
                    CategoryCd = item.CategoryCd,
                    GetDate = item.UploadDate,
                    FileNo = fileNo,
                    DspFileName = item.DspFileName,
                    FileName = item.FileName,
                    S3FileName = item.S3FileName,
                    Memo = item.Memo,
                    SinDate = sinDate,
                    RaiinNo = raiinNo,
                    IsSaveMedical = 1,
                    CreateDate = timeUpdate,
                    CreateId = userId,
                    UpdateDate = timeUpdate,
                    UpdateId = userId,
                };
                TrackingDataContext.FilingInf.Add(newFiling);
            }
            var fileIds = listUpdate.Select(x => x.FileId).ToList();
            var listUpdateEntities = TrackingDataContext.FilingInf
                .Where(f => f.HpId == hpId && f.PtId == ptId && fileIds.Contains(f.FileId))
                .ToList();
            foreach (var item in listUpdate)
            {

                var existFile = listUpdateEntities.FirstOrDefault(f => f.FileId == item.FileId);
                if (existFile == null) continue;

                if (item.IsDeleted == DeleteTypes.None)
                {
                    existFile.DspFileName = item.DspFileName;
                    existFile.GetDate = item.UploadDate;
                    existFile.Memo = item.Memo;
                }
                else
                {
                    existFile.IsDeleted = DeleteTypes.Deleted;
                }
                existFile.UpdateDate = timeUpdate;
                existFile.UpdateId = userId;
            }
            if (listUpdateEntities.Count > 0)
            {
                TrackingDataContext.FilingInf.UpdateRange(listUpdateEntities);
            }

            TrackingDataContext.SaveChanges();
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating schema: {ex.Message}");
            return false;
        }
    }

    public List<OrdInfModel> GetList(int hpId, int inoutKbn, List<int> odrKouiKbnList)
    {
        var todayOrds = TrackingDataContext.OdrInfs.Where(o => o.InoutKbn == inoutKbn && odrKouiKbnList.Contains(o.InoutKbn)).ToList();
        return Mapper.Map<OdrInf, OrdInfModel>(todayOrds);
    }
}
