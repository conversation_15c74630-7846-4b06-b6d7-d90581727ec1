using Domain.Models.ExamResults;
using Domain.Models.SpecialNote.PatientInfo;
using Helper.Common;
using Helper.Redis;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using System.Globalization;
using System.Text.RegularExpressions;
using Domain.Models.ExamResults;
using Helper.Common;
using Helper.Constants;

namespace Infrastructure.Repositories
{
    public class ExamResultsRepository : RepositoryBase, IExamResultsRepository
    {
        private readonly string key;
        private readonly IDatabase _cache;
        private readonly IConfiguration _configuration;

        public ExamResultsRepository(ITenantProvider tenantProvider, IConfiguration configuration) : base(
            tenantProvider)
        {
            key = GetDomainKey();
            _configuration = configuration;
            GetRedis();
            _cache = RedisConnectorHelper.Connection.GetDatabase();
        }

        public void GetRedis()
        {
            string connection =
                string.Concat(_configuration["Redis:RedisHost"], ":", _configuration["Redis:RedisPort"]);
            if (RedisConnectorHelper.RedisHost != connection)
            {
                RedisConnectorHelper.RedisHost = connection;
            }
        }


        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        public ExamResultsDto GetExamResults(int hpId, long ptId, long startDate, long endDate, string? keyword, bool timeSequence, List<string>? kensaItemCds, string centerCd = "")
        {
            List<ExamResultsModel> examResultsModels = new();
            string kensaItemCd = "";
            string kensaName = "";

            if (startDate == 0 && endDate == 0 && !timeSequence)
            {
                endDate = 99999999;
            }
            else if (startDate != 0 && endDate == 0 && !timeSequence)
            {
                endDate = 99999999;
            }

            if (timeSequence)
            {
                if (startDate == 0 && endDate != 0)
                {
                    DateTime date2 = DateTime.ParseExact(endDate.ToString(), "yyyyMMdd", null);
                    DateTime date1 = date2.AddYears(-1);
                    startDate = int.Parse(date1.ToString("yyyyMMdd"));
                }
                else if (startDate != 0 && endDate == 0)
                {
                    DateTime date1 = DateTime.ParseExact(startDate.ToString(), "yyyyMMdd", null);
                    DateTime date2 = date1.AddYears(+1);
                    endDate = int.Parse(date2.ToString("yyyyMMdd"));
                }
            }

            if (Regex.IsMatch(keyword, @"^[0-9]"))
            {
                kensaItemCd = keyword;
            }
            else
            {
                kensaName = keyword;
            }

            string kanaKeyword = CIUtil.ToHalfsize(kensaName);

            if (!WanaKana.IsKana(kanaKeyword) && WanaKana.IsRomaji(kanaKeyword))
            {
                if (WanaKana.IsRomaji(kanaKeyword))
                    kanaKeyword = keyword;
            }

            string sBigKeyword = kanaKeyword.ToUpper()
                .Replace("ｧ", "ｱ")
                .Replace("ｨ", "ｲ")
                .Replace("ｩ", "ｳ")
                .Replace("ｪ", "ｴ")
                .Replace("ｫ", "ｵ")
                .Replace("ｬ", "ﾔ")
                .Replace("ｭ", "ﾕ")
                .Replace("ｮ", "ﾖ")
                .Replace("ｯ", "ﾂ");

            var kensaInfs = NoTrackingDataContext.KensaInfs.Where(x => x.HpId == hpId &&
                                                                       x.PtId == ptId &&
                                                                       x.IsDeleted == DeleteTypes.None &&
                                                                       x.IraiDate >= startDate &&
                                                                       x.IraiDate <= endDate &&
                                                                       (x.InoutKbn == 0 || x.InoutKbn == 1) &&
                                                                       (string.IsNullOrEmpty(centerCd) || x.CenterCd == centerCd))
                                                            .ToList();

            var allKensaInfDetails = NoTrackingDataContext.KensaInfDetails
                .Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None && x.KensaItemCd != null && (!kensaItemCds.Any() || kensaItemCds.Contains(x.KensaItemCd))).ToList();
            var query = (from kensaInf in kensaInfs
                         join kensaInfDetail in allKensaInfDetails on kensaInf.IraiCd equals kensaInfDetail.IraiCd
                         select new
                         {
                             KensaInf = kensaInf,
                             KensaInfDetail = kensaInfDetail,
                         })
                .GroupBy(item => item.KensaInf)
                .Select(group => new
                {
                    KensaInf = group.Key,
                    KensaTime = group.Key.KensaTime,
                    KensaInfDetails = group.Select(item => item.KensaInfDetail).ToList()
                })
                .ToList();

            var allCommonCenterKensaMst = NoTrackingDataContext.CommonCenterKensaMst.ToList();
            var allCommonKensaCenterMst = NoTrackingDataContext.CommonKensaCenterMst.ToList();
            var allCommonCenterStdMst = NoTrackingDataContext.CommonCenterStdMst.ToList();

            var allKensaMst = NoTrackingDataContext.KensaMsts.Where(x => x.HpId == hpId && !x.KensaItemCd.StartsWith("V"))
                                                             .OrderByDescending(x => x.CreateDate)
                                                             .ThenBy(mst => mst.SortNo)
                                                             .ToList();

            foreach (var item in query)
            {
                List<KensaInfDetailModel> kensaInfDetailModels = new();
                string dspCenterName = string.Empty;
                string kensName = string.Empty;
                string kensaKana = string.Empty;
                string maleStd = string.Empty;
                string femaleStd = string.Empty;
                string unit = string.Empty;

                foreach (var detail in item.KensaInfDetails)
                {
                    if (item.KensaInf.CenterCd == CommonConstants.InHospitalCenterCd)
                    {
                        var kensaMst = allKensaMst?.FirstOrDefault(x => x.KensaItemCd == detail.KensaItemCd);
                        if (kensaMst != null)
                        {
                            kensName = kensaMst.KensaName ?? string.Empty;
                            kensaKana = kensaMst.KensaKana ?? string.Empty;
                            maleStd = kensaMst.MaleStd ?? string.Empty;
                            femaleStd = kensaMst.FemaleStd ?? string.Empty;
                            unit = kensaMst.Unit ?? string.Empty;
                            kensaInfDetailModels.Add(new KensaInfDetailModel(
                                detail.HpId,
                                detail.PtId,
                                detail.IraiCd,
                                detail.SeqNo,
                                detail.IraiDate,
                                detail.RaiinNo,
                                detail.KensaItemCd ?? string.Empty,
                                detail.ResultVal ?? string.Empty,
                                detail.ResultType ?? string.Empty,
                                detail.AbnormalKbn ?? string.Empty,
                                detail.IsDeleted,
                                detail.CmtCd1 ?? string.Empty,
                                detail.CmtCd2 ?? string.Empty,
                                detail.UpdateDate,
                                unit,
                                kensName,
                                0,
                                kensaKana,
                                maleStd,
                                femaleStd));
                        }

                        dspCenterName = timeSequence ? "院内" : "院内検査";
                    }
                    else
                    {
                        var commonCenterKensaMst = allCommonCenterKensaMst.FirstOrDefault(x => x.CenterCd == item.KensaInf.CenterCd && x.KensaItemCd == detail.KensaItemCd);
                        if (commonCenterKensaMst != null)
                        {
                            kensName = commonCenterKensaMst.KensaName ?? string.Empty;
                            kensaKana = commonCenterKensaMst.KensaKana ?? string.Empty;
                            unit = commonCenterKensaMst.Unit ?? string.Empty;
                        }

                        var commonCenterStdMst = allCommonCenterStdMst.FirstOrDefault(x => x.CenterCd == item.KensaInf.CenterCd && x.KensaItemCd == detail.KensaItemCd);
                        if (commonCenterStdMst != null)
                        {
                            maleStd = commonCenterStdMst.MealStd ?? string.Empty;
                            femaleStd = commonCenterStdMst.FemelStd ?? string.Empty;
                        }

                        var commonKensaCenterMst =
                            allCommonKensaCenterMst.FirstOrDefault(x => x.CenterCd == item.KensaInf.CenterCd);
                        if (commonKensaCenterMst != null)
                        {
                            dspCenterName = commonKensaCenterMst.DspCenterName ?? string.Empty;
                        }
                        kensaInfDetailModels.Add(new KensaInfDetailModel(
                            detail.HpId,
                            detail.PtId,
                            detail.IraiCd,
                            detail.SeqNo,
                            detail.IraiDate,
                            detail.RaiinNo,
                            detail.KensaItemCd ?? string.Empty,
                            detail.ResultVal ?? string.Empty,
                            detail.ResultType ?? string.Empty,
                            detail.AbnormalKbn ?? string.Empty,
                            detail.IsDeleted,
                            detail.CmtCd1 ?? string.Empty,
                            detail.CmtCd2 ?? string.Empty,
                            detail.UpdateDate,
                            unit,
                            kensName,
                            0,
                            kensaKana,
                            maleStd,
                            femaleStd));
                    }
                    
                }

                if (!string.IsNullOrEmpty(kensaName))
                {
                    kensaInfDetailModels = kensaInfDetailModels.Where(x =>
                        (!string.IsNullOrEmpty(x.KensaKana) && x.KensaKana.ToUpper()
                             .Replace("ｧ", "ｱ")
                             .Replace("ｨ", "ｲ")
                             .Replace("ｩ", "ｳ")
                             .Replace("ｪ", "ｴ")
                             .Replace("ｫ", "ｵ")
                             .Replace("ｬ", "ﾔ")
                             .Replace("ｭ", "ﾕ")
                             .Replace("ｮ", "ﾖ")
                             .Replace("ｯ", "ﾂ").StartsWith(sBigKeyword)) ||
                         (!string.IsNullOrEmpty(x.KensaName) && x.KensaName.Contains(kensaName))
                    ).ToList();
                }

                if (!string.IsNullOrEmpty(kensaItemCd))
                {
                    kensaInfDetailModels = kensaInfDetailModels.Where(x => x.KensaItemCd.StartsWith(kensaItemCd)).ToList();
                }

                if (kensaInfDetailModels.Any())
                {
                    var examResultsModel = new ExamResultsModel(dspCenterName, item.KensaInf.KensaTime, kensaInfDetailModels.OrderBy(x => x.KensaName).ToList(), item.KensaInf.IraiDate, item.KensaInf.InoutKbn, item.KensaInf.SikyuKbn, item.KensaInf.TosekiKbn, item.KensaInf.CenterCd ?? string.Empty);
                    examResultsModels.Add(examResultsModel);
                }
            }

            var averageDays = GetAverageDays(examResultsModels);
            var averageMonths = GetAverageMonths(examResultsModels, startDate, endDate);

            if (!timeSequence)
            {
                examResultsModels = examResultsModels.OrderByDescending(item => long.Parse(item.IraiDate.ToString() + item.KensaTime)).ToList();
            }
            else
            {
                examResultsModels = examResultsModels.OrderBy(item => long.Parse(item.IraiDate.ToString() + item.KensaTime)).ToList();
            }

            return new ExamResultsDto(examResultsModels, averageDays, averageMonths);
        }

        private Dictionary<long, Dictionary<string, double>> GetAverageDays(List<ExamResultsModel> examResultsModels)
        {
            var result = new Dictionary<long, Dictionary<string, double>>();

            var kensaWithCenter = examResultsModels
                .SelectMany(exam => exam.KensaInfDetailModels.Select(kensa => new
                {
                    exam.CenterCd,
                    Kensa = kensa
                }))
                .Where(x => double.TryParse(x.Kensa.ResultVal, out _))
                .OrderBy(x => x.Kensa.IraiDate)
                .ToList();

            var averageDays = kensaWithCenter
                .GroupBy(x => new { x.Kensa.KensaItemCd, x.Kensa.IraiDate, x.CenterCd })
                .Select(g => new
                {
                    Day = g.Key.IraiDate,
                    KensaItemCdWithCenter = $"{g.Key.KensaItemCd}-{g.Key.CenterCd}",
                    AverageResultVal = g.Average(k => double.Parse(k.Kensa.ResultVal))
                })
                .GroupBy(x => x.Day);

            foreach (var averages in averageDays)
            {
                var averageDay = new Dictionary<string, double>();
                foreach (var item in averages)
                {
                    averageDay.Add(item.KensaItemCdWithCenter, Math.Round(item.AverageResultVal, 2, MidpointRounding.AwayFromZero));
                }
                result.Add(averages.Key, averageDay);
            }

            return result;
        }

        private Dictionary<string, Dictionary<string, double>> GetAverageMonths(List<ExamResultsModel> examResultsModels, long? startDate, long? endDate)
        {
            var result = new Dictionary<string, Dictionary<string, double>>();

            var filteredExams = examResultsModels
                .Where(item => item.IraiDate >= startDate && item.IraiDate <= endDate)
                .ToList();

            var kensaWithCenter = filteredExams
                .SelectMany(exam => exam.KensaInfDetailModels.Select(kensa => new
                {
                    exam.CenterCd,
                    Kensa = kensa
                }))
                .Where(x => double.TryParse(x.Kensa.ResultVal, out _))
                .ToList();

            var monthlyAverages = kensaWithCenter
                .GroupBy(x =>
                {
                    var date = DateTime.ParseExact(x.Kensa.IraiDate.ToString(), "yyyyMMdd", CultureInfo.InvariantCulture);
                    var month = date.ToString("yyyyMM");
                    return new { Month = month, x.Kensa.KensaItemCd, x.CenterCd };
                })
                .Select(g => new
                {
                    Month = g.Key.Month,
                    KensaItemCdWithCenter = $"{g.Key.KensaItemCd}-{g.Key.CenterCd}",
                    AverageResultVal = g.Average(x => double.Parse(x.Kensa.ResultVal))
                })
                .GroupBy(x => x.Month)
                .OrderBy(x => x.Key);

            foreach (var monthlyAverage in monthlyAverages)
            {
                var averageMonth = new Dictionary<string, double>();
                foreach (var item in monthlyAverage)
                {
                    averageMonth.Add(item.KensaItemCdWithCenter, Math.Round(item.AverageResultVal, 2, MidpointRounding.AwayFromZero));
                }
                result.Add(monthlyAverage.Key, averageMonth);
            }

            return result;
        }

    }
}