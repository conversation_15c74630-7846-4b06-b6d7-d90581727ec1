﻿namespace Helper.Constants
{
    public class XmlConstant
    {
        public const string NameKana = "NameKana";
        public const string Name = "Name";
        public const string Birthdate = "Birthdate";
        public const string Sex1 = "Sex1";
        public const string Sex2 = "Sex2";
        public const string Address = "Address";
        public const string PostNumber = "PostNumber";
        public const string InsurerName = "InsurerName";
        public const string InsuredName = "InsuredName";
        public const string SegmentOfResult = "SegmentOfResult";
        public const string ProcessingResultStatus = "ProcessingResultStatus";
        public const string ProcessingResultMessage = "ProcessingResultMessage";
        public const string ProcessingResultCode = "ProcessingResultCode";
        public const string ErrorCode = "ErrorCode";
        public const string ErrorMessage = "ErrorMessage";
        public const string ReferenceNumber = "ReferenceNumber";
        public const string QualificationConfirmationDate = "QualificationConfirmationDate";
        public const string ProcessExecutionTime = "ProcessExecutionTime";
        public const string PrescriptionIssueSelect = "PrescriptionIssueSelect";
        public const string QualificationConsTime = "QualificationConsTime";
        public const string QualificationAvailableTime = "QualificationAvailableTime";
        public const string PharmacistsInfoConsFlg = "PharmacistsInfoConsFlg";
        public const string SpecificHealthCheckupsInfoConsFlg = "SpecificHealthCheckupsInfoConsFlg";
        public const string DiagnosisInfoConsFlg = "DiagnosisInfoConsFlg";
        public const string OperationInfoConsFlg = "OperationInfoConsFlg";
        public const string NameOfOtherKana = "NameOfOtherKana";

        public const int UnexpectedEnding = 9;
        public const int Error = 2;

        public const int IsDeleted = 9;

        public class SegmentOfResultStatus
        {
            public const string NormalCompletion = "1";
            public const string Processing = "2";
            public const string AbnormalCompletion = "9";
        }
    }
}