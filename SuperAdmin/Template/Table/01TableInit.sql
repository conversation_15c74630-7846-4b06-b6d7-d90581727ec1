CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);

START TRANSACTION;

CREATE TABLE "ACCOUNTING_FORM_MST" (
    "HP_ID" integer NOT NULL,
    "FORM_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "FORM_NAME" character varying(100) NULL,
    "FORM_TYPE" integer NOT NULL,
    "PRINT_SORT" integer NOT NULL,
    "MISEISAN_KBN" integer NOT NULL,
    "SAI_KBN" integer NOT NULL,
    "MISYU_KBN" integer NOT NULL,
    "SEIKYU_KBN" integer NOT NULL,
    "HOKEN_KBN" integer NOT NULL,
    "FORM" character varying(100) NULL,
    "BASE" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_ACCOUNTING_FORM_MST" PRIMARY KEY ("HP_ID", "FORM_NO")
);

CREATE TABLE "APPROVAL_INF" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_APPROVAL_INF" PRIMARY KEY ("ID", "HP_ID", "RAIIN_NO")
);

CREATE TABLE "AUDIT_TRAIL_LOG" (
    "LOG_ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "LOG_DATE" timestamp with time zone NOT NULL,
    "HP_ID" integer NOT NULL,
    "USER_ID" integer NOT NULL,
    "EVENT_CD" character varying(11) NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DAY" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_AUDIT_TRAIL_LOG" PRIMARY KEY ("LOG_ID")
);

CREATE TABLE "AUDIT_TRAIL_LOG_DETAIL" (
    "LOG_ID" bigint NOT NULL,
    "HOSOKU" text NULL,
    CONSTRAINT "PK_AUDIT_TRAIL_LOG_DETAIL" PRIMARY KEY ("LOG_ID")
);

CREATE TABLE "AUTO_SANTEI_MST" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_AUTO_SANTEI_MST" PRIMARY KEY ("ID", "HP_ID", "ITEM_CD")
);

CREATE TABLE "BACKUP_REQ" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "OUTPUT_TYPE" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "FROM_DATE" integer NOT NULL,
    "TO_DATE" integer NOT NULL,
    "STATUS" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_BACKUP_REQ" PRIMARY KEY ("ID")
);

CREATE TABLE "BUI_ODR_BYOMEI_MST" (
    "HP_ID" integer NOT NULL,
    "BUI_ID" integer NOT NULL,
    "BYOMEI_BUI" character varying(100) NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_BUI_ODR_BYOMEI_MST" PRIMARY KEY ("HP_ID", "BUI_ID", "BYOMEI_BUI")
);

CREATE TABLE "BUI_ODR_ITEM_BYOMEI_MST" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "BYOMEI_BUI" character varying(100) NOT NULL,
    "LR_KBN" integer NOT NULL,
    "BOTH_KBN" integer NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_BUI_ODR_ITEM_BYOMEI_MST" PRIMARY KEY ("HP_ID", "ITEM_CD", "BYOMEI_BUI")
);

CREATE TABLE "BUI_ODR_ITEM_MST" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_BUI_ODR_ITEM_MST" PRIMARY KEY ("HP_ID", "ITEM_CD")
);

CREATE TABLE "BUI_ODR_MST" (
    "HP_ID" integer NOT NULL,
    "BUI_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "ODR_BUI" character varying(100) NULL,
    "LR_KBN" integer NOT NULL,
    "MUST_LR_KBN" integer NOT NULL,
    "BOTH_KBN" integer NOT NULL,
    "KOUI_30" integer NOT NULL,
    "KOUI_40" integer NOT NULL,
    "KOUI_50" integer NOT NULL,
    "KOUI_60" integer NOT NULL,
    "KOUI_70" integer NOT NULL,
    "KOUI_80" integer NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_BUI_ODR_MST" PRIMARY KEY ("HP_ID", "BUI_ID")
);

CREATE TABLE "BYOMEI_MST" (
    "HP_ID" integer NOT NULL,
    "BYOMEI_CD" character varying(7) NOT NULL,
    "BYOMEI" character varying(200) NULL,
    "SBYOMEI" character varying(200) NULL,
    "KANA_NAME1" character varying(200) NULL,
    "KANA_NAME2" character varying(200) NULL,
    "KANA_NAME3" character varying(200) NULL,
    "KANA_NAME4" character varying(200) NULL,
    "KANA_NAME5" character varying(200) NULL,
    "KANA_NAME6" character varying(200) NULL,
    "KANA_NAME7" character varying(200) NULL,
    "IKO_CD" character varying(7) NULL,
    "SIKKAN_CD" integer NOT NULL,
    "TANDOKU_KINSI" integer NOT NULL,
    "HOKEN_GAI" integer NOT NULL,
    "BYOMEI_KANRI" character varying(8) NULL,
    "SAITAKU_KBN" character varying(1) NULL,
    "KOUKAN_CD" character varying(4) NULL,
    "SYUSAI_DATE" integer NOT NULL,
    "UPD_DATE" integer NOT NULL,
    "DEL_DATE" integer NOT NULL,
    "NANBYO_CD" integer NOT NULL,
    "ICD10_1" character varying(5) NULL,
    "ICD10_2" character varying(5) NULL,
    "ICD10_1_2013" character varying(5) NULL,
    "ICD10_2_2013" character varying(5) NULL,
    "IS_ADOPTED" integer NOT NULL,
    "SYUSYOKU_KBN" character varying(8) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_BYOMEI_MST" PRIMARY KEY ("HP_ID", "BYOMEI_CD")
);

CREATE TABLE "BYOMEI_MST_AFTERCARE" (
    "BYOMEI_CD" character varying(2) NOT NULL,
    "BYOMEI" character varying(200) NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_BYOMEI_MST_AFTERCARE" PRIMARY KEY ("BYOMEI_CD", "BYOMEI", "START_DATE")
);

CREATE TABLE "BYOMEI_SET_GENERATION_MST" (
    "HP_ID" integer NOT NULL,
    "GENERATION_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "START_DATE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_BYOMEI_SET_GENERATION_MST" PRIMARY KEY ("HP_ID", "GENERATION_ID")
);

CREATE TABLE "BYOMEI_SET_MST" (
    "HP_ID" integer NOT NULL,
    "GENERATION_ID" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "LEVEL1" integer NOT NULL,
    "LEVEL2" integer NOT NULL,
    "LEVEL3" integer NOT NULL,
    "LEVEL4" integer NOT NULL,
    "LEVEL5" integer NOT NULL,
    "BYOMEI_CD" character varying(7) NULL,
    "SET_NAME" character varying(60) NULL,
    "IS_TITLE" integer NOT NULL,
    "SELECT_TYPE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_BYOMEI_SET_MST" PRIMARY KEY ("HP_ID", "GENERATION_ID", "SEQ_NO")
);

CREATE TABLE "CALC_LOG" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "LOG_SBT" integer NOT NULL,
    "TEXT" character varying(1000) NULL,
    "HOKEN_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "DEL_ITEM_CD" character varying(10) NULL,
    "DEL_SBT" integer NOT NULL,
    "IS_WARNING" integer NOT NULL,
    "TERM_CNT" integer NOT NULL,
    "TERM_SBT" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_CALC_LOG" PRIMARY KEY ("HP_ID", "PT_ID", "RAIIN_NO", "SEQ_NO")
);

CREATE TABLE "CALC_STATUS" (
    "CALC_ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "SEIKYU_UP" integer NOT NULL,
    "CALC_MODE" integer NOT NULL,
    "CLEAR_RECE_CHK" integer NOT NULL,
    "STATUS" integer NOT NULL,
    "BIKO" character varying(200) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_CALC_STATUS" PRIMARY KEY ("CALC_ID")
);

CREATE TABLE "CMT_CHECK_MST" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "SORT_NO" integer NOT NULL,
    "KARTE_KBN" integer NOT NULL,
    "CMT" character varying(250) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_CMT_CHECK_MST" PRIMARY KEY ("HP_ID", "ITEM_CD", "SEQ_NO")
);

CREATE TABLE "CMT_KBN_MST" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "CMT_KBN" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_CMT_KBN_MST" PRIMARY KEY ("ID")
);

CREATE TABLE "CONTAINER_MST" (
    "HP_ID" integer NOT NULL,
    "CONTAINER_CD" bigint NOT NULL,
    "CONTAINER_NAME" character varying(120) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_CONTAINER_MST" PRIMARY KEY ("HP_ID", "CONTAINER_CD")
);

CREATE TABLE "CONVERSION_ITEM_INF" (
    "HP_ID" integer NOT NULL,
    "SOURCE_ITEM_CD" character varying(10) NOT NULL,
    "DEST_ITEM_CD" character varying(10) NOT NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_CONVERSION_ITEM_INF" PRIMARY KEY ("HP_ID", "SOURCE_ITEM_CD", "DEST_ITEM_CD")
);

CREATE TABLE "DEF_HOKEN_NO" (
    "HP_ID" integer NOT NULL,
    "DIGIT_1" character varying(1) NOT NULL,
    "DIGIT_2" character varying(1) NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "DIGIT_3" character varying(1) NULL,
    "DIGIT_4" character varying(1) NULL,
    "DIGIT_5" character varying(1) NULL,
    "DIGIT_6" character varying(1) NULL,
    "DIGIT_7" character varying(1) NULL,
    "DIGIT_8" character varying(1) NULL,
    "HOKEN_NO" integer NOT NULL,
    "HOKEN_EDA_NO" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DEF_HOKEN_NO" PRIMARY KEY ("HP_ID", "DIGIT_1", "DIGIT_2", "SEQ_NO")
);

CREATE TABLE "DENSI_HAIHAN_CUSTOM" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "ITEM_CD1" character varying(10) NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "USER_SETTING" integer NOT NULL,
    "ITEM_CD2" character varying(10) NULL,
    "HAIHAN_KBN" integer NOT NULL,
    "SP_JYOKEN" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "TERM_CNT" integer NOT NULL,
    "TERM_SBT" integer NOT NULL,
    "TARGET_KBN" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DENSI_HAIHAN_CUSTOM" PRIMARY KEY ("ID", "HP_ID", "ITEM_CD1", "SEQ_NO", "USER_SETTING")
);

CREATE TABLE "DENSI_HAIHAN_DAY" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "ITEM_CD1" character varying(10) NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "USER_SETTING" integer NOT NULL,
    "ITEM_CD2" character varying(10) NULL,
    "HAIHAN_KBN" integer NOT NULL,
    "SP_JYOKEN" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "TARGET_KBN" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DENSI_HAIHAN_DAY" PRIMARY KEY ("ID", "HP_ID", "ITEM_CD1", "SEQ_NO", "USER_SETTING")
);

CREATE TABLE "DENSI_HAIHAN_KARTE" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "ITEM_CD1" character varying(10) NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "USER_SETTING" integer NOT NULL,
    "ITEM_CD2" character varying(10) NULL,
    "HAIHAN_KBN" integer NOT NULL,
    "SP_JYOKEN" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "TARGET_KBN" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DENSI_HAIHAN_KARTE" PRIMARY KEY ("ID", "HP_ID", "ITEM_CD1", "SEQ_NO", "USER_SETTING")
);

CREATE TABLE "DENSI_HAIHAN_MONTH" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "ITEM_CD1" character varying(10) NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "USER_SETTING" integer NOT NULL,
    "ITEM_CD2" character varying(10) NULL,
    "HAIHAN_KBN" integer NOT NULL,
    "SP_JYOKEN" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "INC_AFTER" integer NOT NULL,
    "TARGET_KBN" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DENSI_HAIHAN_MONTH" PRIMARY KEY ("ID", "HP_ID", "ITEM_CD1", "SEQ_NO", "USER_SETTING")
);

CREATE TABLE "DENSI_HAIHAN_WEEK" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "ITEM_CD1" character varying(10) NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "USER_SETTING" integer NOT NULL,
    "ITEM_CD2" character varying(10) NULL,
    "HAIHAN_KBN" integer NOT NULL,
    "SP_JYOKEN" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "INC_AFTER" integer NOT NULL,
    "TARGET_KBN" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DENSI_HAIHAN_WEEK" PRIMARY KEY ("ID", "HP_ID", "ITEM_CD1", "SEQ_NO", "USER_SETTING")
);

CREATE TABLE "DENSI_HOJYO" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "HOUKATU_TERM1" integer NOT NULL,
    "HOUKATU_GRP_NO1" character varying(7) NULL,
    "HOUKATU_TERM2" integer NOT NULL,
    "HOUKATU_GRP_NO2" character varying(7) NULL,
    "HOUKATU_TERM3" integer NOT NULL,
    "HOUKATU_GRP_NO3" character varying(7) NULL,
    "HAIHAN_DAY" integer NOT NULL,
    "HAIHAN_MONTH" integer NOT NULL,
    "HAIHAN_KARTE" integer NOT NULL,
    "HAIHAN_WEEK" integer NOT NULL,
    "NYUIN_ID" integer NOT NULL,
    "SANTEI_KAISU" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DENSI_HOJYO" PRIMARY KEY ("HP_ID", "ITEM_CD", "START_DATE")
);

CREATE TABLE "DENSI_HOUKATU" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "USER_SETTING" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "TARGET_KBN" integer NOT NULL,
    "HOUKATU_TERM" integer NOT NULL,
    "HOUKATU_GRP_NO" character varying(7) NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DENSI_HOUKATU" PRIMARY KEY ("START_DATE", "HP_ID", "ITEM_CD", "SEQ_NO", "USER_SETTING")
);

CREATE TABLE "DENSI_HOUKATU_GRP" (
    "HP_ID" integer NOT NULL,
    "HOUKATU_GRP_NO" character varying(7) NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "USER_SETTING" integer NOT NULL,
    "SP_JYOKEN" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "TARGET_KBN" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DENSI_HOUKATU_GRP" PRIMARY KEY ("HP_ID", "HOUKATU_GRP_NO", "ITEM_CD", "SEQ_NO", "USER_SETTING", "START_DATE")
);

CREATE TABLE "DENSI_SANTEI_KAISU" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "USER_SETTING" integer NOT NULL,
    "UNIT_CD" integer NOT NULL,
    "MAX_COUNT" integer NOT NULL,
    "SP_JYOKEN" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "TARGET_KBN" integer NOT NULL,
    "TERM_COUNT" integer NOT NULL,
    "TERM_SBT" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "ITEM_GRP_CD" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DENSI_SANTEI_KAISU" PRIMARY KEY ("HP_ID", "ID", "ITEM_CD", "SEQ_NO", "USER_SETTING")
);

CREATE TABLE "DOC_CATEGORY_MST" (
    "HP_ID" integer NOT NULL,
    "CATEGORY_CD" integer GENERATED BY DEFAULT AS IDENTITY,
    "CATEGORY_NAME" character varying(120) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DOC_CATEGORY_MST" PRIMARY KEY ("HP_ID", "CATEGORY_CD")
);

CREATE TABLE "DOC_COMMENT" (
    "CATEGORY_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "CATEGORY_NAME" character varying(1000) NULL,
    "SORT_NO" integer NOT NULL,
    "REPLACE_WORD" text NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DOC_COMMENT" PRIMARY KEY ("CATEGORY_ID")
);

CREATE TABLE "DOC_COMMENT_DETAIL" (
    "CATEGORY_ID" integer NOT NULL,
    "EDA_NO" integer NOT NULL,
    "COMMENT" character varying(1000) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DOC_COMMENT_DETAIL" PRIMARY KEY ("CATEGORY_ID", "EDA_NO")
);

CREATE TABLE "DOC_INF" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL DEFAULT 0,
	"RAIIN_NO" int8 NOT NULL DEFAULT 0,
	"SEQ_NO" int4 NOT NULL DEFAULT 1,
	"CATEGORY_CD" int4 NOT NULL DEFAULT 0,
	"FILE_NAME" varchar(300) NULL,
	"DSP_FILE_NAME" varchar(300) NULL,
	"IS_LOCKED" int4 NOT NULL DEFAULT 0,
	"LOCK_DATE" timestamptz NULL,
	"LOCK_ID" int4 NOT NULL,
	"LOCK_MACHINE" varchar(60) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_DOC_INF" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_DATE", "RAIIN_NO", "SEQ_NO")
);

CREATE TABLE "DOSAGE_MST" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "ONCE_MIN" double precision NOT NULL,
    "ONCE_MAX" double precision NOT NULL,
    "ONCE_LIMIT" double precision NOT NULL,
    "ONCE_UNIT" integer NOT NULL,
    "DAY_MIN" double precision NOT NULL,
    "DAY_MAX" double precision NOT NULL,
    "DAY_LIMIT" double precision NOT NULL,
    "DAY_UNIT" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DOSAGE_MST" PRIMARY KEY ("ID", "HP_ID", "ITEM_CD", "SEQ_NO")
);

CREATE TABLE "DRUG_DAY_LIMIT" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "LIMIT_DAY" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DRUG_DAY_LIMIT" PRIMARY KEY ("ID", "HP_ID", "ITEM_CD", "SEQ_NO")
);

CREATE TABLE "DRUG_INF" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "INF_KBN" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "DRUG_INF" character varying(2000) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DRUG_INF" PRIMARY KEY ("INF_KBN", "HP_ID", "ITEM_CD", "SEQ_NO")
);

CREATE TABLE "DRUG_UNIT_CONV" (
    "ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "CNV_VAL" double precision NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_DRUG_UNIT_CONV" PRIMARY KEY ("ITEM_CD", "START_DATE")
);

CREATE TABLE "EVENT_MST" (
    "EVENT_CD" character varying(11) NOT NULL,
    "EVENT_NAME" character varying(100) NULL,
    "AUDIT_TRAILING" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_EVENT_MST" PRIMARY KEY ("EVENT_CD")
);

CREATE TABLE "EXCEPT_HOKENSYA" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "PREF_NO" integer NOT NULL,
    "HOKEN_NO" integer NOT NULL,
    "HOKEN_EDA_NO" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "HOKENSYA_NO" character varying(8) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_EXCEPT_HOKENSYA" PRIMARY KEY ("ID", "HP_ID", "PREF_NO", "HOKEN_NO", "HOKEN_EDA_NO", "START_DATE")
);

CREATE TABLE "FILING_AUTO_IMP" (
    "HP_ID" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "CATEGORY_CD" integer NOT NULL,
    "MACHINE" character varying(60) NULL,
    "IMP_PATH" character varying(300) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_FILING_AUTO_IMP" PRIMARY KEY ("SEQ_NO", "HP_ID")
);

CREATE TABLE "FILING_CATEGORY_MST" (
    "HP_ID" integer NOT NULL,
    "CATEGORY_CD" integer GENERATED BY DEFAULT AS IDENTITY,
    "CATEGORY_NAME" character varying(120) NULL,
    "SORT_NO" integer NOT NULL,
    "DSP_KANZOK" integer NOT NULL,
    "IS_FILE_DELETED" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_FILING_CATEGORY_MST" PRIMARY KEY ("CATEGORY_CD", "HP_ID")
);

CREATE TABLE "FILING_INF" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"GET_DATE" int4 NOT NULL DEFAULT 0,
	"CATEGORY_CD" int4 NOT NULL DEFAULT 0,
	"FILE_NO" int4 NOT NULL DEFAULT 1,
	"FILE_NAME" varchar(300) NULL,
	"DSP_FILE_NAME" varchar(1024) NULL,
	"IS_DELETED" int4 NOT NULL,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"FILE_ID" serial4 NOT NULL,
    CONSTRAINT "PK_FILING_INF" PRIMARY KEY ("HP_ID", "FILE_ID", "PT_ID", "GET_DATE", "FILE_NO")
);

CREATE TABLE "FUNCTION_MST" (
    "FUNCTION_CD" character varying(8) NOT NULL,
    "FUNCTION_NAME" character varying(100) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_FUNCTION_MST" PRIMARY KEY ("FUNCTION_CD")
);

CREATE TABLE "GC_STD_MST" (
    "HP_ID" integer NOT NULL,
    "STD_KBN" integer NOT NULL,
    "SEX" integer NOT NULL,
    "POINT" double precision NOT NULL,
    "SD_M25" double precision NOT NULL,
    "SD_M20" double precision NOT NULL,
    "SD_M10" double precision NOT NULL,
    "SD_AVG" double precision NOT NULL,
    "SD_P10" double precision NOT NULL,
    "SD_P20" double precision NOT NULL,
    "SD_P25" double precision NOT NULL,
    "PER_03" double precision NOT NULL,
    "PER_10" double precision NOT NULL,
    "PER_25" double precision NOT NULL,
    "PER_50" double precision NOT NULL,
    "PER_75" double precision NOT NULL,
    "PER_90" double precision NOT NULL,
    "PER_97" double precision NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_GC_STD_MST" PRIMARY KEY ("HP_ID", "STD_KBN", "SEX", "POINT")
);

CREATE TABLE "HOKEN_MST" (
    "HP_ID" integer NOT NULL,
    "PREF_NO" integer NOT NULL,
    "HOKEN_NO" integer NOT NULL,
    "HOKEN_EDA_NO" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "HOKEN_SBT_KBN" integer NOT NULL,
    "HOKEN_KOHI_KBN" integer NOT NULL,
    "HOUBETU" character varying(3) NULL,
    "HOKEN_NAME" character varying(100) NULL,
    "HOKEN_SNAME" character varying(20) NULL,
    "HOKEN_NAME_CD" character varying(5) NULL,
    "CHECK_DIGIT" integer NOT NULL,
    "JYUKYU_CHECK_DIGIT" integer NOT NULL,
    "IS_FUTANSYA_NO_CHECK" integer NOT NULL,
    "IS_JYUKYUSYA_NO_CHECK" integer NOT NULL,
    "IS_TOKUSYU_NO_CHECK" integer NOT NULL,
    "IS_LIMIT_LIST" integer NOT NULL,
    "IS_LIMIT_LIST_SUM" integer NOT NULL,
    "IS_OTHER_PREF_VALID" integer NOT NULL,
    "AGE_START" integer NOT NULL,
    "AGE_END" integer NOT NULL,
    "EN_TEN" integer NOT NULL,
    "SEIKYU_YM" integer NOT NULL,
    "RECE_SP_KBN" integer NOT NULL,
    "RECE_SEIKYU_KBN" integer NOT NULL,
    "RECE_FUTAN_ROUND" integer NOT NULL,
    "RECE_KISAI" integer NOT NULL,
    "RECE_KISAI2" integer NOT NULL,
    "RECE_ZERO_KISAI" integer NOT NULL,
    "RECE_FUTAN_HIDE" integer NOT NULL,
    "RECE_FUTAN_KBN" integer NOT NULL,
    "RECE_TEN_KISAI" integer NOT NULL,
    "KOGAKU_TOTAL_KBN" integer NOT NULL,
    "KOGAKU_TOTAL_ALL" integer NOT NULL,
    "CALC_SP_KBN" integer NOT NULL,
    "KOGAKU_TOTAL_EXC_FUTAN" integer NOT NULL,
    "KOGAKU_TEKIYO" integer NOT NULL,
    "FUTAN_YUSEN" integer NOT NULL,
    "LIMIT_KBN" integer NOT NULL,
    "COUNT_KBN" integer NOT NULL,
    "FUTAN_KBN" integer NOT NULL,
    "FUTAN_RATE" integer NOT NULL,
    "KAI_FUTANGAKU" integer NOT NULL,
    "KAI_LIMIT_FUTAN" integer NOT NULL,
    "DAY_LIMIT_FUTAN" integer NOT NULL,
    "DAY_LIMIT_COUNT" integer NOT NULL,
    "MONTH_LIMIT_FUTAN" integer NOT NULL,
    "MONTH_SP_LIMIT" integer NOT NULL,
    "MONTH_LIMIT_COUNT" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    "RECE_KISAI_KOKHO" integer NOT NULL,
    "KOGAKU_HAIRYO_KBN" integer NOT NULL,
    CONSTRAINT "PK_HOKEN_MST" PRIMARY KEY ("HP_ID", "PREF_NO", "HOKEN_NO", "HOKEN_EDA_NO", "START_DATE")
);

CREATE TABLE "HOKENSYA_MST" (
    "HP_ID" integer NOT NULL,
    "HOKENSYA_NO" character varying(8) NOT NULL,
    "NAME" character varying(100) NULL,
    "KANA_NAME" character varying(100) NULL,
    "HOUBETU_KBN" character varying(2) NULL,
    "HOUBETU" character varying(3) NULL,
    "HOKEN_KBN" integer NOT NULL,
    "PREF_NO" integer NOT NULL,
    "KIGO" character varying(80) NULL,
    "BANGO" character varying(80) NULL,
    "IS_KIGO_NA" integer NOT NULL,
    "RATE_HONNIN" integer NOT NULL,
    "RATE_KAZOKU" integer NOT NULL,
    "POST_CODE" character varying(7) NULL,
    "ADDRESS1" character varying(200) NULL,
    "ADDRESS2" character varying(200) NULL,
    "TEL1" character varying(15) NULL,
    "DELETE_DATE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_HOKENSYA_MST" PRIMARY KEY ("HP_ID", "HOKENSYA_NO")
);

CREATE TABLE "HOLIDAY_MST" (
    "HP_ID" integer NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "HOLIDAY_KBN" integer NOT NULL,
    "KYUSIN_KBN" integer NOT NULL,
    "HOLIDAY_NAME" character varying(20) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_HOLIDAY_MST" PRIMARY KEY ("HP_ID", "SIN_DATE", "SEQ_NO")
);

CREATE TABLE "HP_INF" (
    "HP_ID" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "HP_CD" character varying(7) NULL,
    "ROUSAI_HP_CD" character varying(7) NULL,
    "HP_NAME" character varying(80) NULL,
    "RECE_HP_NAME" character varying(80) NULL,
    "KAISETU_NAME" character varying(40) NULL,
    "POST_CD" character varying(7) NULL,
    "PREF_NO" integer NOT NULL,
    "ADDRESS1" character varying(100) NULL,
    "ADDRESS2" character varying(100) NULL,
    "TEL" character varying(15) NULL,
    "FAX_NO" character varying(15) NULL,
    "OTHER_CONTACTS" character varying(100) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_HP_INF" PRIMARY KEY ("HP_ID", "START_DATE")
);

CREATE TABLE "IPN_KASAN_EXCLUDE" (
    "HP_ID" integer NOT NULL,
    "IPN_NAME_CD" character varying(12) NOT NULL,
    "START_DATE" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_IPN_KASAN_EXCLUDE" PRIMARY KEY ("HP_ID", "START_DATE", "IPN_NAME_CD", "SEQ_NO")
);

CREATE TABLE "IPN_KASAN_EXCLUDE_ITEM" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_IPN_KASAN_EXCLUDE_ITEM" PRIMARY KEY ("HP_ID", "START_DATE", "ITEM_CD")
);

CREATE TABLE "IPN_KASAN_MST" (
    "HP_ID" integer NOT NULL,
    "IPN_NAME_CD" character varying(12) NOT NULL,
    "START_DATE" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "KASAN1" integer NOT NULL,
    "KASAN2" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_IPN_KASAN_MST" PRIMARY KEY ("HP_ID", "START_DATE", "IPN_NAME_CD", "SEQ_NO")
);

CREATE TABLE "IPN_MIN_YAKKA_MST" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "IPN_NAME_CD" character varying(12) NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "YAKKA" double precision NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_IPN_MIN_YAKKA_MST" PRIMARY KEY ("HP_ID", "ID", "IPN_NAME_CD", "SEQ_NO")
);

CREATE TABLE "IPN_NAME_MST" (
    "HP_ID" integer NOT NULL,
    "IPN_NAME_CD" character varying(12) NOT NULL,
    "START_DATE" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "IPN_NAME" character varying(100) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_IPN_NAME_MST" PRIMARY KEY ("HP_ID", "IPN_NAME_CD", "START_DATE", "SEQ_NO")
);

CREATE TABLE "ITEM_GRP_MST" (
    "HP_ID" integer NOT NULL,
    "GRP_SBT" bigint NOT NULL,
    "ITEM_GRP_CD" bigint NOT NULL,
    "START_DATE" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_ITEM_GRP_MST" PRIMARY KEY ("HP_ID", "GRP_SBT", "ITEM_GRP_CD", "SEQ_NO", "START_DATE")
);

CREATE TABLE "JIHI_SBT_MST" (
    "HP_ID" integer NOT NULL,
    "JIHI_SBT" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "NAME" character varying(100) NULL,
    "IS_DELETED" integer NOT NULL,
    "IS_YOBO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_JIHI_SBT_MST" PRIMARY KEY ("HP_ID", "JIHI_SBT")
);

CREATE TABLE "JOB_MST" (
    "HP_ID" integer NOT NULL,
    "JOB_CD" integer GENERATED BY DEFAULT AS IDENTITY,
    "JOB_NAME" character varying(10) NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_JOB_MST" PRIMARY KEY ("JOB_CD", "HP_ID")
);

CREATE TABLE "KA_MST" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "KA_ID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "RECE_KA_CD" character varying(2) NULL,
    "KA_SNAME" character varying(20) NULL,
    "KA_NAME" character varying(40) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KA_MST" PRIMARY KEY ("ID", "HP_ID")
);

CREATE TABLE "KACODE_MST" (
    "RECE_KA_CD" character varying(2) NOT NULL,
    "SORT_NO" integer NOT NULL,
    "KA_NAME" character varying(40) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KACODE_MST" PRIMARY KEY ("RECE_KA_CD")
);

CREATE TABLE "KAIKEI_DETAIL" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "HOKEN_PID" integer NOT NULL,
    "ADJUST_PID" integer NOT NULL,
    "OYA_RAIIN_NO" bigint NOT NULL,
    "ADJUST_KID" integer NOT NULL,
    "HOKEN_KBN" integer NOT NULL,
    "HOKEN_SBT_CD" integer NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "KOHI1_ID" integer NOT NULL,
    "KOHI2_ID" integer NOT NULL,
    "KOHI3_ID" integer NOT NULL,
    "KOHI4_ID" integer NOT NULL,
    "ROUSAI_ID" integer NOT NULL,
    "HOUBETU" character varying(3) NULL,
    "KOHI1_HOUBETU" character varying(3) NULL,
    "KOHI2_HOUBETU" character varying(3) NULL,
    "KOHI3_HOUBETU" character varying(3) NULL,
    "KOHI4_HOUBETU" character varying(3) NULL,
    "KOHI1_PRIORITY" character varying(8) NULL,
    "KOHI2_PRIORITY" character varying(8) NULL,
    "KOHI3_PRIORITY" character varying(8) NULL,
    "KOHI4_PRIORITY" character varying(8) NULL,
    "HONKE_KBN" integer NOT NULL,
    "KOGAKU_KBN" integer NOT NULL,
    "KOGAKU_TEKIYO_KBN" integer NOT NULL,
    "IS_TOKUREI" integer NOT NULL,
    "IS_TASUKAI" integer NOT NULL,
    "KOGAKU_TOTAL_KBN" integer NOT NULL,
    "IS_CHOKI" integer NOT NULL,
    "KOGAKU_LIMIT" integer NOT NULL,
    "TOTAL_KOGAKU_LIMIT" integer NOT NULL,
    "GENMEN_KBN" integer NOT NULL,
    "EN_TEN" integer NOT NULL,
    "HOKEN_RATE" integer NOT NULL,
    "PT_RATE" integer NOT NULL,
    "KOHI1_LIMIT" integer NOT NULL,
    "KOHI1_OTHER_FUTAN" integer NOT NULL,
    "KOHI2_LIMIT" integer NOT NULL,
    "KOHI2_OTHER_FUTAN" integer NOT NULL,
    "KOHI3_LIMIT" integer NOT NULL,
    "KOHI3_OTHER_FUTAN" integer NOT NULL,
    "KOHI4_LIMIT" integer NOT NULL,
    "KOHI4_OTHER_FUTAN" integer NOT NULL,
    "TENSU" integer NOT NULL,
    "TOTAL_IRYOHI" integer NOT NULL,
    "HOKEN_FUTAN" integer NOT NULL,
    "KOGAKU_FUTAN" integer NOT NULL,
    "KOHI1_FUTAN" integer NOT NULL,
    "KOHI2_FUTAN" integer NOT NULL,
    "KOHI3_FUTAN" integer NOT NULL,
    "KOHI4_FUTAN" integer NOT NULL,
    "ICHIBU_FUTAN" integer NOT NULL,
    "GENMEN_GAKU" integer NOT NULL,
    "HOKEN_FUTAN_10EN" integer NOT NULL,
    "KOGAKU_FUTAN_10EN" integer NOT NULL,
    "KOHI1_FUTAN_10EN" integer NOT NULL,
    "KOHI2_FUTAN_10EN" integer NOT NULL,
    "KOHI3_FUTAN_10EN" integer NOT NULL,
    "KOHI4_FUTAN_10EN" integer NOT NULL,
    "ICHIBU_FUTAN_10EN" integer NOT NULL,
    "GENMEN_GAKU_10EN" integer NOT NULL,
    "ADJUST_ROUND" integer NOT NULL,
    "PT_FUTAN" integer NOT NULL,
    "KOGAKU_OVER_KBN" integer NOT NULL,
    "RECE_SBT" character varying(4) NULL,
    "JITUNISU" integer NOT NULL,
    "ROUSAI_I_FUTAN" integer NOT NULL,
    "ROUSAI_RO_FUTAN" integer NOT NULL,
    "JIBAI_I_TENSU" integer NOT NULL,
    "JIBAI_RO_TENSU" integer NOT NULL,
    "JIBAI_HA_FUTAN" integer NOT NULL,
    "JIBAI_NI_FUTAN" integer NOT NULL,
    "JIBAI_HO_SINDAN" integer NOT NULL,
    "JIBAI_HO_SINDAN_COUNT" integer NOT NULL,
    "JIBAI_HE_MEISAI" integer NOT NULL,
    "JIBAI_HE_MEISAI_COUNT" integer NOT NULL,
    "JIBAI_A_FUTAN" integer NOT NULL,
    "JIBAI_B_FUTAN" integer NOT NULL,
    "JIBAI_C_FUTAN" integer NOT NULL,
    "JIBAI_D_FUTAN" integer NOT NULL,
    "JIBAI_KENPO_TENSU" integer NOT NULL,
    "JIBAI_KENPO_FUTAN" integer NOT NULL,
    "JIHI_FUTAN" integer NOT NULL,
    "JIHI_TAX" integer NOT NULL,
    "JIHI_OUTTAX" integer NOT NULL,
    "JIHI_FUTAN_TAXFREE" integer NOT NULL,
    "JIHI_FUTAN_TAX_NR" integer NOT NULL,
    "JIHI_FUTAN_TAX_GEN" integer NOT NULL,
    "JIHI_FUTAN_OUTTAX_NR" integer NOT NULL,
    "JIHI_FUTAN_OUTTAX_GEN" integer NOT NULL,
    "JIHI_TAX_NR" integer NOT NULL,
    "JIHI_TAX_GEN" integer NOT NULL,
    "JIHI_OUTTAX_NR" integer NOT NULL,
    "JIHI_OUTTAX_GEN" integer NOT NULL,
    "TOTAL_PT_FUTAN" integer NOT NULL,
    "SORT_KEY" character varying(61) NULL,
    "IS_NINPU" integer NOT NULL,
    "IS_ZAIISO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KAIKEI_DETAIL" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_DATE", "RAIIN_NO", "HOKEN_PID", "ADJUST_PID")
);

CREATE TABLE "KAIKEI_INF" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "KOHI1_ID" integer NOT NULL,
    "KOHI2_ID" integer NOT NULL,
    "KOHI3_ID" integer NOT NULL,
    "KOHI4_ID" integer NOT NULL,
    "HOKEN_KBN" integer NOT NULL,
    "HOKEN_SBT_CD" integer NOT NULL,
    "RECE_SBT" character varying(4) NULL,
    "HOUBETU" character varying(3) NULL,
    "KOHI1_HOUBETU" character varying(3) NULL,
    "KOHI2_HOUBETU" character varying(3) NULL,
    "KOHI3_HOUBETU" character varying(3) NULL,
    "KOHI4_HOUBETU" character varying(3) NULL,
    "HONKE_KBN" integer NOT NULL,
    "HOKEN_RATE" integer NOT NULL,
    "PT_RATE" integer NOT NULL,
    "DISP_RATE" integer NOT NULL,
    "TENSU" integer NOT NULL,
    "TOTAL_IRYOHI" integer NOT NULL,
    "PT_FUTAN" integer NOT NULL,
    "JIHI_FUTAN" integer NOT NULL,
    "JIHI_TAX" integer NOT NULL,
    "JIHI_OUTTAX" integer NOT NULL,
    "JIHI_FUTAN_TAXFREE" integer NOT NULL,
    "JIHI_FUTAN_TAX_NR" integer NOT NULL,
    "JIHI_FUTAN_TAX_GEN" integer NOT NULL,
    "JIHI_FUTAN_OUTTAX_NR" integer NOT NULL,
    "JIHI_FUTAN_OUTTAX_GEN" integer NOT NULL,
    "JIHI_TAX_NR" integer NOT NULL,
    "JIHI_TAX_GEN" integer NOT NULL,
    "JIHI_OUTTAX_NR" integer NOT NULL,
    "JIHI_OUTTAX_GEN" integer NOT NULL,
    "ADJUST_FUTAN" integer NOT NULL,
    "ADJUST_ROUND" integer NOT NULL,
    "TOTAL_PT_FUTAN" integer NOT NULL,
    "ADJUST_FUTAN_VAL" integer NOT NULL,
    "ADJUST_FUTAN_RANGE" integer NOT NULL,
    "ADJUST_RATE_VAL" integer NOT NULL,
    "ADJUST_RATE_RANGE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KAIKEI_INF" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_DATE", "RAIIN_NO", "HOKEN_ID")
);

CREATE TABLE "KANTOKU_MST" (
    "ROUDOU_CD" character varying(2) NOT NULL,
    "KANTOKU_CD" character varying(2) NOT NULL,
    "KANTOKU_NAME" character varying(60) NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_KANTOKU_MST" PRIMARY KEY ("ROUDOU_CD", "KANTOKU_CD")
);

CREATE TABLE "KARTE_FILTER_DETAIL" (
    "HP_ID" integer NOT NULL,
    "USER_ID" integer NOT NULL,
    "FILTER_ID" bigint NOT NULL,
    "FILTER_ITEM_CD" integer NOT NULL,
    "FILTER_EDA_NO" integer NOT NULL,
    "VAL" integer NOT NULL,
    "PARAM" character varying(300) NULL,
    CONSTRAINT "PK_KARTE_FILTER_DETAIL" PRIMARY KEY ("HP_ID", "USER_ID", "FILTER_ID", "FILTER_ITEM_CD", "FILTER_EDA_NO")
);

CREATE TABLE "KARTE_FILTER_MST" (
    "HP_ID" integer NOT NULL,
    "USER_ID" integer NOT NULL,
    "FILTER_ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "FILTER_NAME" character varying(20) NULL,
    "SORT_NO" integer NOT NULL,
    "AUTO_APPLY" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KARTE_FILTER_MST" PRIMARY KEY ("HP_ID", "USER_ID", "FILTER_ID")
);

CREATE TABLE "KARTE_IMG_INF" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "KARTE_KBN" integer NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "POSITION" bigint NOT NULL,
    "FILE_NAME" character varying(100) NULL,
    "MESSAGE" character varying(2000) NULL,
    CONSTRAINT "PK_KARTE_IMG_INF" PRIMARY KEY ("ID")
);

CREATE TABLE "KARTE_INF" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "KARTE_KBN" integer NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "TEXT" text NULL,
    "RICH_TEXT" bytea NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KARTE_INF" PRIMARY KEY ("HP_ID", "RAIIN_NO", "SEQ_NO", "KARTE_KBN")
);

CREATE TABLE "KARTE_KBN_MST" (
    "HP_ID" integer NOT NULL,
    "KARTE_KBN" integer NOT NULL,
    "KBN_NAME" character varying(10) NULL,
    "KBN_SHORT_NAME" character varying(1) NULL,
    "CAN_IMG" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KARTE_KBN_MST" PRIMARY KEY ("HP_ID", "KARTE_KBN")
);

CREATE TABLE "KENSA_CENTER_MST" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "CENTER_CD" character varying(10) NULL,
    "CENTER_NAME" character varying(120) NULL,
    "PRIMARY_KBN" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KENSA_CENTER_MST" PRIMARY KEY ("HP_ID", "ID")
);

CREATE TABLE "KENSA_INF" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"IRAI_CD" bigserial NOT NULL,
	"IRAI_DATE" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"INOUT_KBN" int4 NOT NULL DEFAULT 0,
	"STATUS" int4 NOT NULL DEFAULT 0,
	"TOSEKI_KBN" int4 NOT NULL DEFAULT 0,
	"SIKYU_KBN" int4 NOT NULL DEFAULT 0,
	"RESULT_CHECK" int4 NOT NULL DEFAULT 0,
	"CENTER_CD" varchar(10) NULL,
	"NYUBI" varchar(3) NULL,
	"YOKETU" varchar(3) NULL,
	"BILIRUBIN" varchar(3) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_KENSA_INF" PRIMARY KEY ("HP_ID", "PT_ID", "IRAI_CD")
);

CREATE TABLE "KENSA_INF_DETAIL" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"IRAI_CD" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"IRAI_DATE" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"KENSA_ITEM_CD" varchar(10) NULL,
	"RESULT_VAL" varchar(10) NULL,
	"RESULT_TYPE" varchar(1) NULL,
	"ABNORMAL_KBN" varchar(1) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CMT_CD1" varchar(3) NULL,
	"CMT_CD2" varchar(3) NULL,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_KENSA_INF_DETAIL" PRIMARY KEY ("HP_ID", "PT_ID", "IRAI_CD", "SEQ_NO")
);

CREATE TABLE "KENSA_IRAI_LOG" (
    "HP_ID" integer NOT NULL,
    "CENTER_CD" character varying(10) NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "IRAI_DATE" integer NOT NULL,
    "FROM_DATE" integer NOT NULL,
    "TO_DATE" integer NOT NULL,
    "IRAI_FILE" text NULL,
    "IRAI_LIST" bytea NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KENSA_IRAI_LOG" PRIMARY KEY ("HP_ID", "CENTER_CD", "CREATE_DATE")
);

CREATE TABLE "KENSA_MST" (
    "HP_ID" integer NOT NULL,
    "KENSA_ITEM_CD" character varying(10) NOT NULL,
    "KENSA_ITEM_SEQ_NO" integer NOT NULL,
    "CENTER_CD" character varying(10) NULL,
    "KENSA_NAME" character varying(120) NULL,
    "KENSA_KANA" character varying(20) NULL,
    "UNIT" character varying(20) NULL,
    "MATERIAL_CD" integer NOT NULL,
    "CONTAINER_CD" integer NOT NULL,
    "MALE_STD" character varying(60) NULL,
    "MALE_STD_LOW" character varying(60) NULL,
    "MALE_STD_HIGH" character varying(60) NULL,
    "FEMALE_STD" character varying(60) NULL,
    "FEMALE_STD_LOW" character varying(60) NULL,
    "FEMALE_STD_HIGH" character varying(60) NULL,
    "FORMULA" character varying(100) NULL,
    "DIGIT" integer NOT NULL,
    "OYA_ITEM_CD" character varying(10) NULL,
    "OYA_ITEM_SEQ_NO" integer NOT NULL,
    "SORT_NO" bigint NOT NULL,
    "CENTER_ITEM_CD1" character varying(10) NULL,
    "CENTER_ITEM_CD2" character varying(10) NULL,
    "IS_DELETE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KENSA_MST" PRIMARY KEY ("HP_ID", "KENSA_ITEM_CD", "KENSA_ITEM_SEQ_NO")
);

CREATE TABLE "KENSA_STD_MST" (
    "HP_ID" integer NOT NULL,
    "KENSA_ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "MALE_STD" character varying(60) NULL,
    "MALE_STD_LOW" character varying(60) NULL,
    "MALE_STD_HIGH" character varying(60) NULL,
    "FEMALE_STD" character varying(60) NULL,
    "FEMALE_STD_LOW" character varying(60) NULL,
    "FEMALE_STD_HIGH" character varying(60) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KENSA_STD_MST" PRIMARY KEY ("HP_ID", "KENSA_ITEM_CD", "START_DATE")
);

CREATE TABLE "KINKI_MST" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "A_CD" character varying(10) NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "B_CD" character varying(10) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KINKI_MST" PRIMARY KEY ("HP_ID", "ID", "A_CD", "SEQ_NO")
);

CREATE TABLE "KOGAKU_LIMIT" (
    "HP_ID" integer NOT NULL,
    "AGE_KBN" integer NOT NULL,
    "KOGAKU_KBN" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "INCOME_KBN" character varying(20) NULL,
    "END_DATE" integer NOT NULL,
    "BASE_LIMIT" integer NOT NULL,
    "ADJUST_LIMIT" integer NOT NULL,
    "TASU_LIMIT" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KOGAKU_LIMIT" PRIMARY KEY ("HP_ID", "AGE_KBN", "KOGAKU_KBN", "START_DATE")
);

CREATE TABLE "KOHI_PRIORITY" (
    "PREF_NO" integer NOT NULL,
    "HOUBETU" character varying(3) NOT NULL,
    "PRIORITY_NO" character varying(5) NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KOHI_PRIORITY" PRIMARY KEY ("PREF_NO", "HOUBETU", "PRIORITY_NO")
);

CREATE TABLE "KOUI_HOUKATU_MST" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "USER_SETTING" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "TARGET_KBN" integer NOT NULL,
    "HOUKATU_TERM" integer NOT NULL,
    "KOUI_FROM" integer NOT NULL,
    "KOUI_TO" integer NOT NULL,
    "IGNORE_SANTEI_KBN" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KOUI_HOUKATU_MST" PRIMARY KEY ("HP_ID", "ITEM_CD", "START_DATE")
);

CREATE TABLE "KOUI_KBN_MST" (
    "HP_ID" integer NOT NULL,
    "KOUI_KBN_ID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "KOUI_KBN1" integer NOT NULL,
    "KOUI_KBN2" integer NOT NULL,
    "KOUI_GRP_NAME" character varying(20) NULL,
    "KOUI_NAME" character varying(20) NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KOUI_KBN_MST" PRIMARY KEY ("HP_ID", "KOUI_KBN_ID")
);

CREATE TABLE "LIMIT_CNT_LIST_INF" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"KOHI_ID" int4 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SEQ_NO" int4 NOT NULL,
	"HOKEN_PID" int4 NOT NULL,
	"SORT_KEY" varchar(61) NULL,
	"OYA_RAIIN_NO" int8 NOT NULL,
	"BIKO" varchar(200) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_LIMIT_CNT_LIST_INF" PRIMARY KEY ("HP_ID", "PT_ID", "KOHI_ID", "SIN_DATE", "SEQ_NO")
);

CREATE TABLE "LIMIT_LIST_INF" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"KOHI_ID" int4 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"HOKEN_PID" int4 NOT NULL,
	"SORT_KEY" varchar(61) NULL,
	"RAIIN_NO" int8 NOT NULL,
	"FUTAN_GAKU" int4 NOT NULL,
	"TOTAL_GAKU" int4 NOT NULL,
	"BIKO" varchar(200) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"ID" bigserial NOT NULL,
    CONSTRAINT "PK_LIMIT_LIST_INF" PRIMARY KEY ("ID")
);

CREATE TABLE "LIST_SET_GENERATION_MST" (
    "HP_ID" integer NOT NULL,
    "GENERATION_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "START_DATE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_LIST_SET_GENERATION_MST" PRIMARY KEY ("HP_ID", "GENERATION_ID")
);

CREATE TABLE "LIST_SET_MST" (
    "HP_ID" integer NOT NULL,
    "GENERATION_ID" integer NOT NULL,
    "SET_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "SET_KBN" integer NOT NULL,
    "LEVEL1" integer NOT NULL,
    "LEVEL2" integer NOT NULL,
    "LEVEL3" integer NOT NULL,
    "LEVEL4" integer NOT NULL,
    "LEVEL5" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "SET_NAME" character varying(240) NULL,
    "IS_TITLE" integer NOT NULL,
    "SELECT_TYPE" integer NOT NULL,
    "SURYO" double precision NOT NULL,
    "UNIT_SBT" integer NOT NULL,
    "SIKYU_KBN" integer NOT NULL,
    "CMT_NAME" character varying(240) NULL,
    "CMT_OPT" character varying(38) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_LIST_SET_MST" PRIMARY KEY ("HP_ID", "GENERATION_ID", "SET_ID")
);

CREATE TABLE "LOCK_INF" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "FUNCTION_CD" character varying(8) NOT NULL,
    "SIN_DATE" bigint NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "OYA_RAIIN_NO" bigint NOT NULL,
    "MACHINE" text NULL,
    "USER_ID" integer NOT NULL,
    "LOCK_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_LOCK_INF" PRIMARY KEY ("HP_ID", "PT_ID", "FUNCTION_CD", "SIN_DATE", "RAIIN_NO", "OYA_RAIIN_NO")
);

CREATE TABLE "LOCK_MST" (
    "FUNCTION_CD_A" character varying(8) NOT NULL,
    "FUNCTION_CD_B" character varying(8) NOT NULL,
    "LOCK_RANGE" integer NOT NULL,
    "LOCK_LEVEL" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_LOCK_MST" PRIMARY KEY ("FUNCTION_CD_A", "FUNCTION_CD_B")
);

CREATE TABLE "M01_KIJYO_CMT" (
    "CMT_CD" character varying(6) NOT NULL,
    "CMT" character varying(200) NULL,
    CONSTRAINT "PK_M01_KIJYO_CMT" PRIMARY KEY ("CMT_CD")
);

CREATE TABLE "M01_KINKI" (
    "A_CD" character varying(12) NOT NULL,
    "B_CD" character varying(12) NOT NULL,
    "CMT_CD" character varying(6) NOT NULL,
    "SAYOKIJYO_CD" character varying(6) NOT NULL,
    "KYODO_CD" character varying(3) NULL,
    "KYODO" character varying(2) NULL,
    "DATA_KBN" character varying(1) NULL,
    CONSTRAINT "PK_M01_KINKI" PRIMARY KEY ("A_CD", "B_CD", "CMT_CD", "SAYOKIJYO_CD")
);

CREATE TABLE "M01_KINKI_CMT" (
    "CMT_CD" character varying(6) NOT NULL,
    "CMT" character varying(200) NULL,
    CONSTRAINT "PK_M01_KINKI_CMT" PRIMARY KEY ("CMT_CD")
);

CREATE TABLE "M10_DAY_LIMIT" (
    "YJ_CD" character varying(12) NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "LIMIT_DAY" integer NOT NULL,
    "ST_DATE" character varying(8) NULL,
    "ED_DATE" character varying(8) NULL,
    "CMT" character varying(400) NULL,
    CONSTRAINT "PK_M10_DAY_LIMIT" PRIMARY KEY ("YJ_CD", "SEQ_NO")
);

CREATE TABLE "M12_FOOD_ALRGY" (
    "YJ_CD" character varying(12) NOT NULL,
    "FOOD_KBN" character varying(2) NOT NULL,
    "TENPU_LEVEL" character varying(2) NOT NULL,
    "KIKIN_CD" character varying(9) NULL,
    "ATTENTION_CMT" character varying(500) NULL,
    "WORKING_MECHANISM" character varying(1000) NULL,
    CONSTRAINT "PK_M12_FOOD_ALRGY" PRIMARY KEY ("YJ_CD", "FOOD_KBN", "TENPU_LEVEL")
);

CREATE TABLE "M12_FOOD_ALRGY_KBN" (
    "FOOD_KBN" character varying(2) NOT NULL,
    "FOOD_NAME" character varying(60) NOT NULL,
    CONSTRAINT "PK_M12_FOOD_ALRGY_KBN" PRIMARY KEY ("FOOD_KBN")
);

CREATE TABLE "M14_AGE_CHECK" (
    "YJ_CD" character varying(12) NOT NULL,
    "ATTENTION_CMT_CD" character varying(7) NOT NULL,
    "WORKING_MECHANISM" character varying(1000) NULL,
    "TENPU_LEVEL" character varying(2) NULL,
    "AGE_KBN" character varying(1) NULL,
    "WEIGHT_KBN" character varying(1) NULL,
    "SEX_KBN" character varying(1) NULL,
    "AGE_MIN" double precision NOT NULL,
    "AGE_MAX" double precision NOT NULL,
    "WEIGHT_MIN" double precision NOT NULL,
    "WEIGHT_MAX" double precision NOT NULL,
    CONSTRAINT "PK_M14_AGE_CHECK" PRIMARY KEY ("YJ_CD", "ATTENTION_CMT_CD")
);

CREATE TABLE "M14_CMT_CODE" (
    "ATTENTION_CMT_CD" character varying(7) NOT NULL,
    "ATTENTION_CMT" character varying(500) NULL,
    CONSTRAINT "PK_M14_CMT_CODE" PRIMARY KEY ("ATTENTION_CMT_CD")
);

CREATE TABLE "M28_DRUG_MST" (
    "YJ_CD" character varying(12) NOT NULL,
    "KOSEISYO_CD" character varying(12) NULL,
    "KIKIN_CD" character varying(9) NULL,
    "DRUG_NAME" character varying(200) NULL,
    "DRUG_KANA1" character varying(100) NULL,
    "DRUG_KANA2" character varying(100) NULL,
    "IPN_NAME" character varying(400) NULL,
    "IPN_KANA" character varying(100) NULL,
    "YAKKA_VAL" integer NOT NULL,
    "YAKKA_UNIT" character varying(20) NULL,
    "SEIBUN_RIKIKA" double precision NOT NULL,
    "SEIBUN_RIKIKA_UNIT" character varying(30) NULL,
    "YORYO_JYURYO" double precision NOT NULL,
    "YORYO_JYURYO_UNIT" character varying(20) NULL,
    "SEIRIKI_YORYO_RATE" double precision NOT NULL,
    "SEIRIKI_YORYO_UNIT" character varying(40) NULL,
    "MAKER_CD" character varying(4) NULL,
    "MAKER_NAME" character varying(40) NULL,
    "DRUG_KBN_CD" character varying(1) NULL,
    "DRUG_KBN" character varying(10) NULL,
    "FORM_KBN_CD" character varying(3) NULL,
    "FORM_KBN" character varying(100) NULL,
    "DOKUYAKU_FLG" character varying(1) NULL,
    "GEKIYAKU_FLG" character varying(1) NULL,
    "MAYAKU_FLG" character varying(1) NULL,
    "KOSEISINYAKU_FLG" character varying(1) NULL,
    "KAKUSEIZAI_FLG" character varying(1) NULL,
    "KAKUSEIZAI_GENRYO_FLG" character varying(1) NULL,
    "SEIBUTU_FLG" character varying(1) NULL,
    "SP_SEIBUTU_FLG" character varying(1) NULL,
    "KOHATU_FLG" character varying(1) NULL,
    "YAKKA" double precision NOT NULL,
    "KIKAKU_UNIT" character varying(100) NULL,
    "YAKKA_RATE_FORMURA" character varying(30) NULL,
    "YAKKA_RATE_UNIT" character varying(40) NULL,
    "YAKKA_SYUSAI_DATE" character varying(8) NULL,
    "KEIKASOTI_DATE" character varying(8) NULL,
    "MAIN_DRUG_CD" character varying(8) NULL,
    "MAIN_DRUG_NAME" character varying(400) NULL,
    "MAIN_DRUG_KANA" character varying(400) NULL,
    "KEY_SEIBUN" character varying(200) NULL,
    "HAIGO_FLG" character varying(1) NULL,
    "MAIN_DRUG_NAME_FLG" character varying(1) NULL,
    CONSTRAINT "PK_M28_DRUG_MST" PRIMARY KEY ("YJ_CD")
);

CREATE TABLE "M34_AR_CODE" (
    "FUKUSAYO_CD" text NOT NULL,
    "FUKUSAYO_CMT" character varying(200) NULL,
    CONSTRAINT "PK_M34_AR_CODE" PRIMARY KEY ("FUKUSAYO_CD")
);

CREATE TABLE "M34_AR_DISCON" (
    "YJ_CD" text NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "FUKUSAYO_CD" text NULL,
    CONSTRAINT "PK_M34_AR_DISCON" PRIMARY KEY ("YJ_CD", "SEQ_NO")
);

CREATE TABLE "M34_AR_DISCON_CODE" (
    "FUKUSAYO_CD" character varying(6) NOT NULL,
    "FUKUSAYO_CMT" character varying(200) NULL,
    CONSTRAINT "PK_M34_AR_DISCON_CODE" PRIMARY KEY ("FUKUSAYO_CD")
);

CREATE TABLE "M34_DRUG_INFO_MAIN" (
    "YJ_CD" text NOT NULL,
    "FORM_CD" text NULL,
    "COLOR" character varying(20) NULL,
    "MARK" character varying(20) NULL,
    "KONO_CD" text NULL,
    "FUKUSAYO_CD" text NULL,
    "FUKUSAYO_INIT_CD" text NULL,
    CONSTRAINT "PK_M34_DRUG_INFO_MAIN" PRIMARY KEY ("YJ_CD")
);

CREATE TABLE "M34_FORM_CODE" (
    "FORM_CD" character varying(4) NOT NULL,
    "FORM" character varying(80) NULL,
    CONSTRAINT "PK_M34_FORM_CODE" PRIMARY KEY ("FORM_CD")
);

CREATE TABLE "M34_INDICATION_CODE" (
    "KONO_CD" text NOT NULL,
    "KONO_DETAIL_CMT" character varying(200) NULL,
    "KONO_SIMPLE_CMT" character varying(200) NULL,
    CONSTRAINT "PK_M34_INDICATION_CODE" PRIMARY KEY ("KONO_CD")
);

CREATE TABLE "M34_INTERACTION_PAT" (
    "YJ_CD" text NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "INTERACTION_PAT_CD" text NULL,
    CONSTRAINT "PK_M34_INTERACTION_PAT" PRIMARY KEY ("YJ_CD", "SEQ_NO")
);

CREATE TABLE "M34_INTERACTION_PAT_CODE" (
    "INTERACTION_PAT_CD" text NOT NULL,
    "INTERACTION_PAT_CMT" character varying(200) NULL,
    CONSTRAINT "PK_M34_INTERACTION_PAT_CODE" PRIMARY KEY ("INTERACTION_PAT_CD")
);

CREATE TABLE "M34_PRECAUTION_CODE" (
    "PRECAUTION_CD" text NOT NULL,
    "EXTEND_CD" text NOT NULL,
    "PRECAUTION_CMT" character varying(200) NULL,
    "PROPERTY_CD" integer NOT NULL,
    "AGE_MAX" integer NOT NULL,
    "AGE_MIN" integer NOT NULL,
    "SEX_CD" text NULL,
    CONSTRAINT "PK_M34_PRECAUTION_CODE" PRIMARY KEY ("PRECAUTION_CD", "EXTEND_CD")
);

CREATE TABLE "M34_PRECAUTIONS" (
    "YJ_CD" text NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "PRECAUTION_CD" text NULL,
    CONSTRAINT "PK_M34_PRECAUTIONS" PRIMARY KEY ("YJ_CD", "SEQ_NO")
);

CREATE TABLE "M34_PROPERTY_CODE" (
    "PROPERTY_CD" integer GENERATED BY DEFAULT AS IDENTITY,
    "PROPERTY" character varying(100) NULL,
    CONSTRAINT "PK_M34_PROPERTY_CODE" PRIMARY KEY ("PROPERTY_CD")
);

CREATE TABLE "M34_SAR_SYMPTOM_CODE" (
    "FUKUSAYO_INIT_CD" character varying(6) NOT NULL,
    "FUKUSAYO_INIT_CMT" text NULL,
    CONSTRAINT "PK_M34_SAR_SYMPTOM_CODE" PRIMARY KEY ("FUKUSAYO_INIT_CD")
);

CREATE TABLE "M38_CLASS_CODE" (
    "CLASS_CD" text NOT NULL,
    "CLASS_NAME" character varying(100) NULL,
    "MAJOR_DIV_CD" text NULL,
    CONSTRAINT "PK_M38_CLASS_CODE" PRIMARY KEY ("CLASS_CD")
);

CREATE TABLE "M38_ING_CODE" (
    "SEIBUN_CD" character varying(7) NOT NULL,
    "SEIBUN" character varying(200) NULL,
    CONSTRAINT "PK_M38_ING_CODE" PRIMARY KEY ("SEIBUN_CD")
);

CREATE TABLE "M38_INGREDIENTS" (
    "SERIAL_NUM" integer NOT NULL,
    "SEIBUN_CD" character varying(7) NOT NULL,
    "SBT" integer NOT NULL,
    CONSTRAINT "PK_M38_INGREDIENTS" PRIMARY KEY ("SEIBUN_CD", "SERIAL_NUM", "SBT")
);

CREATE TABLE "M38_MAJOR_DIV_CODE" (
    "MAJOR_DIV_CD" text NOT NULL,
    "MAJOR_DIV_NAME" character varying(100) NULL,
    CONSTRAINT "PK_M38_MAJOR_DIV_CODE" PRIMARY KEY ("MAJOR_DIV_CD")
);

CREATE TABLE "M38_OTC_FORM_CODE" (
    "FORM_CD" text NOT NULL,
    "FORM" character varying(80) NULL,
    CONSTRAINT "PK_M38_OTC_FORM_CODE" PRIMARY KEY ("FORM_CD")
);

CREATE TABLE "M38_OTC_MAIN" (
    "SERIAL_NUM" integer GENERATED BY DEFAULT AS IDENTITY,
    "OTC_CD" character varying(12) NULL,
    "TRADE_NAME" character varying(200) NULL,
    "TRADE_KANA" character varying(400) NULL,
    "CLASS_CD" character varying(2) NULL,
    "COMPANY_CD" character varying(4) NULL,
    "TRADE_CD" character varying(3) NULL,
    "DRUG_FORM_CD" character varying(6) NULL,
    "YOHO_CD" character varying(6) NULL,
    CONSTRAINT "PK_M38_OTC_MAIN" PRIMARY KEY ("SERIAL_NUM")
);

CREATE TABLE "M38_OTC_MAKER_CODE" (
    "MAKER_CD" text NOT NULL,
    "MAKER_NAME" character varying(200) NULL,
    "MAKER_KANA" character varying(400) NULL,
    CONSTRAINT "PK_M38_OTC_MAKER_CODE" PRIMARY KEY ("MAKER_CD")
);

CREATE TABLE "M41_SUPPLE_INDEXCODE" (
    "SEIBUN_CD" character varying(7) NOT NULL,
    "INDEX_CD" character varying(7) NOT NULL,
    CONSTRAINT "PK_M41_SUPPLE_INDEXCODE" PRIMARY KEY ("SEIBUN_CD", "INDEX_CD")
);

CREATE TABLE "M41_SUPPLE_INDEXDEF" (
    "SEIBUN_CD" character varying(7) NOT NULL,
    "INDEX_WORD" character varying(200) NULL,
    "TOKUHO_FLG" character varying(1) NULL,
    CONSTRAINT "PK_M41_SUPPLE_INDEXDEF" PRIMARY KEY ("SEIBUN_CD")
);

CREATE TABLE "M41_SUPPLE_INGRE" (
    "SEIBUN_CD" character varying(7) NOT NULL,
    "SEIBUN" character varying(200) NULL,
    CONSTRAINT "PK_M41_SUPPLE_INGRE" PRIMARY KEY ("SEIBUN_CD")
);

CREATE TABLE "M42_CONTRA_CMT" (
    "CMT_CD" character varying(7) NOT NULL,
    "CMT" character varying(400) NULL,
    CONSTRAINT "PK_M42_CONTRA_CMT" PRIMARY KEY ("CMT_CD")
);

CREATE TABLE "M42_CONTRAINDI_DIS_BC" (
    "BYOTAI_CD" character varying(7) NOT NULL,
    "BYOTAI_CLASS_CD" character varying(4) NOT NULL,
    CONSTRAINT "PK_M42_CONTRAINDI_DIS_BC" PRIMARY KEY ("BYOTAI_CD", "BYOTAI_CLASS_CD")
);

CREATE TABLE "M42_CONTRAINDI_DIS_CLASS" (
    "BYOTAI_CLASS_CD" character varying(4) NOT NULL,
    "BYOTAI" character varying(50) NULL,
    CONSTRAINT "PK_M42_CONTRAINDI_DIS_CLASS" PRIMARY KEY ("BYOTAI_CLASS_CD")
);

CREATE TABLE "M42_CONTRAINDI_DIS_CON" (
    "BYOTAI_CD" character varying(7) NOT NULL,
    "STANDARD_BYOTAI" character varying(400) NULL,
    "BYOTAI_KBN" integer NOT NULL,
    "BYOMEI" character varying(400) NULL,
    "ICD10" character varying(5) NULL,
    "RECE_CD" character varying(33) NULL,
    CONSTRAINT "PK_M42_CONTRAINDI_DIS_CON" PRIMARY KEY ("BYOTAI_CD")
);

CREATE TABLE "M42_CONTRAINDI_DRUG_MAIN_EX" (
    "YJ_CD" character varying(12) NOT NULL,
    "TENPU_LEVEL" integer NOT NULL,
    "BYOTAI_CD" character varying(7) NOT NULL,
    "CMT_CD" character varying(7) NOT NULL,
    "STAGE" integer NOT NULL,
    "KIO_CD" character varying(1) NULL,
    "FAMILY_CD" character varying(1) NULL,
    "KIJYO_CD" character varying(7) NULL,
    CONSTRAINT "PK_M42_CONTRAINDI_DRUG_MAIN_EX" PRIMARY KEY ("YJ_CD", "TENPU_LEVEL", "BYOTAI_CD", "CMT_CD")
);

CREATE TABLE "M46_DOSAGE_DOSAGE" (
    "DOEI_CD" text NOT NULL,
    "DOEI_SEQ_NO" integer NOT NULL,
    "KONOKOKA_CD" character varying(7) NULL,
    "KENSA_PCD" character varying(7) NULL,
    "AGE_OVER" double precision NOT NULL,
    "AGE_UNDER" double precision NOT NULL,
    "AGE_CD" character varying(1) NULL,
    "WEIGHT_OVER" double precision NOT NULL,
    "WEIGHT_UNDER" double precision NOT NULL,
    "BODY_OVER" double precision NOT NULL,
    "BODY_UNDER" double precision NOT NULL,
    "DRUG_ROUTE" character varying(40) NULL,
    "USE_FLG" character varying(1) NULL,
    "DRUG_CONDITION" character varying(400) NULL,
    "KONOKOKA" text NULL,
    "USAGE_DOSAGE" text NULL,
    "FILENAME_CD" character varying(7) NULL,
    "DRUG_SYUGI" text NULL,
    "TEKIO_BUI" character varying(300) NULL,
    "YOUKAI_KISYAKU" character varying(1500) NULL,
    "KISYAKUEKI" character varying(500) NULL,
    "YOUKAIEKI" character varying(500) NULL,
    "HAITA_FLG" character varying(1) NULL,
    "NG_KISYAKUEKI" character varying(500) NULL,
    "NG_YOUKAIEKI" character varying(500) NULL,
    "COMBI_DRUG" character varying(200) NULL,
    "DRUG_LINK_CD" integer NOT NULL,
    "DRUG_ORDER" integer NOT NULL,
    "SINGLE_DRUG_FLG" character varying(1) NULL,
    "KYUGEN_CD" character varying(1) NULL,
    "DOSAGE_CHECK_FLG" character varying(1) NULL,
    "ONCE_MIN" double precision NOT NULL,
    "ONCE_MAX" double precision NOT NULL,
    "ONCE_UNIT" character varying(30) NULL,
    "ONCE_LIMIT" double precision NOT NULL,
    "ONCE_LIMIT_UNIT" character varying(30) NULL,
    "DAY_MIN_CNT" double precision NOT NULL,
    "DAY_MAX_CNT" double precision NOT NULL,
    "DAY_MIN" double precision NOT NULL,
    "DAY_MAX" double precision NOT NULL,
    "DAY_UNIT" character varying(30) NULL,
    "DAY_LIMIT" double precision NOT NULL,
    "DAY_LIMIT_UNIT" character varying(30) NULL,
    "RISE" integer NOT NULL,
    "MORNING" integer NOT NULL,
    "DAYTIME" integer NOT NULL,
    "NIGHT" integer NOT NULL,
    "SLEEP" integer NOT NULL,
    "BEFORE_MEAL" integer NOT NULL,
    "JUST_BEFORE_MEAL" integer NOT NULL,
    "AFTER_MEAL" integer NOT NULL,
    "JUST_AFTER_MEAL" integer NOT NULL,
    "BETWEEN_MEAL" integer NOT NULL,
    "ELSE_TIME" integer NOT NULL,
    "DOSAGE_LIMIT_TERM" integer NOT NULL,
    "DOSAGE_LIMIT_UNIT" character varying(1) NULL,
    "UNITTERM_LIMIT" double precision NOT NULL,
    "UNITTERM_UNIT" character varying(30) NULL,
    "DOSAGE_ADD_FLG" character varying(1) NULL,
    "INC_DEC_FLG" character varying(1) NULL,
    "DEC_FLG" character varying(1) NULL,
    "INC_DEC_INTERVAL" integer NOT NULL,
    "INC_DEC_INTERVAL_UNIT" character varying(1) NULL,
    "DEC_LIMIT" double precision NOT NULL,
    "INC_LIMIT" double precision NOT NULL,
    "INC_DEC_LIMIT_UNIT" character varying(30) NULL,
    "TIME_DEPEND" character varying(1000) NULL,
    "JUDGE_TERM" integer NOT NULL,
    "JUDGE_TERM_UNIT" character varying(1) NULL,
    "EXTEND_FLG" character varying(1) NULL,
    "ADD_TERM" integer NOT NULL,
    "ADD_TERM_UNIT" character varying(1) NULL,
    "INTERVAL_WARNING_FLG" character varying(1) NULL,
    CONSTRAINT "PK_M46_DOSAGE_DOSAGE" PRIMARY KEY ("DOEI_CD", "DOEI_SEQ_NO")
);

CREATE TABLE "M46_DOSAGE_DRUG" (
    "YJ_CD" character varying(12) NOT NULL,
    "DOEI_CD" character varying(8) NOT NULL,
    "DRUG_KBN" character varying(1) NULL,
    "KIKAKU_UNIT" character varying(100) NULL,
    "YAKKA_UNIT" character varying(20) NULL,
    "RIKIKA_RATE" numeric NOT NULL,
    "RIKIKA_UNIT" character varying(30) NULL,
    "YOUKAIEKI_CD" character varying(1) NULL,
    CONSTRAINT "PK_M46_DOSAGE_DRUG" PRIMARY KEY ("DOEI_CD", "YJ_CD")
);

CREATE TABLE "M56_ALRGY_DERIVATIVES" (
    "YJ_CD" character varying(12) NOT NULL,
    "DRVALRGY_CD" character varying(8) NOT NULL,
    "SEIBUN_CD" character varying(9) NOT NULL,
    CONSTRAINT "PK_M56_ALRGY_DERIVATIVES" PRIMARY KEY ("SEIBUN_CD", "DRVALRGY_CD", "YJ_CD")
);

CREATE TABLE "M56_ANALOGUE_CD" (
    "ANALOGUE_CD" character varying(9) NOT NULL,
    "ANALOGUE_NAME" character varying(200) NULL,
    CONSTRAINT "PK_M56_ANALOGUE_CD" PRIMARY KEY ("ANALOGUE_CD")
);

CREATE TABLE "M56_DRUG_CLASS" (
    "CLASS_CD" character varying(8) NOT NULL,
    "CLASS_NAME" character varying(200) NULL,
    "CLASS_DUPLICATION" character varying(1) NULL,
    CONSTRAINT "PK_M56_DRUG_CLASS" PRIMARY KEY ("CLASS_CD")
);

CREATE TABLE "M56_DRVALRGY_CODE" (
    "DRVALRGY_CD" character varying(8) NOT NULL,
    "DRVALRGY_NAME" character varying(200) NULL,
    "DRVALRGY_GRP" character varying(4) NULL,
    "RANK_NO" integer NOT NULL,
    CONSTRAINT "PK_M56_DRVALRGY_CODE" PRIMARY KEY ("DRVALRGY_CD")
);

CREATE TABLE "M56_EX_ANALOGUE" (
    "SEIBUN_CD" character varying(9) NOT NULL,
    "SEQ_NO" character varying(2) NOT NULL,
    "ANALOGUE_CD" character varying(9) NULL,
    CONSTRAINT "PK_M56_EX_ANALOGUE" PRIMARY KEY ("SEIBUN_CD", "SEQ_NO")
);

CREATE TABLE "M56_EX_ED_INGREDIENTS" (
    "YJ_CD" character varying(12) NOT NULL,
    "SEQ_NO" character varying(3) NOT NULL,
    "SEIBUN_CD" character varying(9) NULL,
    "SEIBUN_INDEX_CD" character varying(3) NULL,
    "SBT" integer NOT NULL,
    "PRODRUG_CHECK" character varying(1) NULL,
    "ANALOGUE_CHECK" character varying(1) NULL,
    "YOKAIEKI_CHECK" character varying(1) NULL,
    "TENKABUTU_CHECK" character varying(1) NULL,
    CONSTRAINT "PK_M56_EX_ED_INGREDIENTS" PRIMARY KEY ("YJ_CD", "SEQ_NO")
);

CREATE TABLE "M56_EX_ING_CODE" (
    "SEIBUN_CD" character varying(9) NOT NULL,
    "SEIBUN_INDEX_CD" character varying(3) NOT NULL,
    "SEIBUN_NAME" character varying(200) NULL,
    "YOHO_CD" character varying(6) NULL,
    CONSTRAINT "PK_M56_EX_ING_CODE" PRIMARY KEY ("SEIBUN_CD", "SEIBUN_INDEX_CD")
);

CREATE TABLE "M56_EX_INGRDT_MAIN" (
    "YJ_CD" character varying(12) NOT NULL,
    "DRUG_KBN" character varying(2) NULL,
    "YOHO_CD" character varying(6) NULL,
    "HAIGOU_FLG" character varying(1) NULL,
    "YUEKI_FLG" character varying(1) NULL,
    "KANPO_FLG" character varying(1) NULL,
    "ZENSINSAYO_FLG" character varying(1) NULL,
    CONSTRAINT "PK_M56_EX_INGRDT_MAIN" PRIMARY KEY ("YJ_CD")
);

CREATE TABLE "M56_PRODRUG_CD" (
    "SEIBUN_CD" character varying(9) NOT NULL,
    "SEQ_NO" character varying(2) NOT NULL,
    "KASSEITAI_CD" character varying(9) NULL,
    CONSTRAINT "PK_M56_PRODRUG_CD" PRIMARY KEY ("SEQ_NO", "SEIBUN_CD")
);

CREATE TABLE "M56_USAGE_CODE" (
    "YOHO_CD" text NOT NULL,
    "YOHO" character varying(200) NULL,
    CONSTRAINT "PK_M56_USAGE_CODE" PRIMARY KEY ("YOHO_CD")
);

CREATE TABLE "M56_YJ_DRUG_CLASS" (
    "YJ_CD" character varying(12) NOT NULL,
    "CLASS_CD" character varying(8) NOT NULL,
    CONSTRAINT "PK_M56_YJ_DRUG_CLASS" PRIMARY KEY ("YJ_CD", "CLASS_CD")
);

CREATE TABLE "MALL_MESSAGE_INF" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "RECEIVE_NO" integer NOT NULL,
    "SEND_NO" integer NOT NULL,
    "MESSAGE" character varying(400) NULL,
    "SIN_DATE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_MALL_MESSAGE_INF" PRIMARY KEY ("ID")
);

CREATE TABLE "MALL_RENKEI_INF" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "SINSATU_NO" integer NOT NULL,
    "KAIKEI_NO" integer NOT NULL,
    "RECEIVE_NO" integer NOT NULL,
    "SEND_NO" integer NOT NULL,
    "SEND_FLG" integer NOT NULL,
    "CLINIC_CD" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_MALL_RENKEI_INF" PRIMARY KEY ("HP_ID", "RAIIN_NO")
);

CREATE TABLE "MATERIAL_MST" (
    "HP_ID" integer NOT NULL,
    "MATERIAL_CD" bigint NOT NULL,
    "MATERIAL_NAME" character varying(120) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_MATERIAL_MST" PRIMARY KEY ("HP_ID", "MATERIAL_CD")
);

CREATE TABLE "MONSHIN_INF" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"TEXT" text NULL,
	"RTEXT" text NULL,
	"GET_KBN" int4 NOT NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_MONSHIN_INF" PRIMARY KEY ("HP_ID", "PT_ID", "RAIIN_NO", "SEQ_NO")
);

CREATE TABLE "ODR_DATE_DETAIL" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "IS_DELETED" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_ODR_DATE_DETAIL" PRIMARY KEY ("HP_ID", "GRP_ID", "SEQ_NO")
);

CREATE TABLE "ODR_DATE_INF" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "GRP_NAME" character varying(20) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_ODR_DATE_INF" PRIMARY KEY ("HP_ID", "GRP_ID")
);

CREATE TABLE "ODR_INF" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "HOKEN_PID" integer NOT NULL,
    "ODR_KOUI_KBN" integer NOT NULL,
    "RP_NAME" character varying(240) NULL,
    "INOUT_KBN" integer NOT NULL,
    "SIKYU_KBN" integer NOT NULL,
    "SYOHO_SBT" integer NOT NULL,
    "SANTEI_KBN" integer NOT NULL,
    "TOSEKI_KBN" integer NOT NULL,
    "DAYS_CNT" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_ODR_INF" PRIMARY KEY ("HP_ID", "RAIIN_NO", "RP_NO", "RP_EDA_NO", "ID")
);

CREATE TABLE "ODR_INF_CMT" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "ROW_NO" integer NOT NULL,
    "EDA_NO" integer NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SORT_NO" integer NOT NULL,
    "FONT_COLOR" integer NOT NULL,
    "CMT_CD" character varying(10) NULL,
    "CMT_NAME" character varying(32) NULL,
    "CMT_OPT" character varying(38) NULL,
    CONSTRAINT "PK_ODR_INF_CMT" PRIMARY KEY ("HP_ID", "RAIIN_NO", "RP_NO", "RP_EDA_NO", "ROW_NO", "EDA_NO")
);

CREATE TABLE "ODR_INF_DETAIL" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "ROW_NO" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "SIN_KOUI_KBN" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "ITEM_NAME" character varying(240) NULL,
    "SURYO" double precision NOT NULL,
    "UNIT_NAME" character varying(24) NULL,
    "UNIT_SBT" integer NOT NULL,
    "TERM_VAL" double precision NOT NULL,
    "KOHATU_KBN" integer NOT NULL,
    "SYOHO_KBN" integer NOT NULL,
    "SYOHO_LIMIT_KBN" integer NOT NULL,
    "DRUG_KBN" integer NOT NULL,
    "YOHO_KBN" integer NOT NULL,
    "KOKUJI1" text NULL,
    "KOKUJI2" text NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IPN_CD" character varying(12) NULL,
    "IPN_NAME" character varying(120) NULL,
    "JISSI_KBN" integer NOT NULL,
    "JISSI_DATE" timestamp with time zone NULL,
    "JISSI_ID" integer NOT NULL,
    "JISSI_MACHINE" character varying(60) NULL,
    "REQ_CD" character varying(10) NULL,
    "BUNKATU" character varying(10) NULL,
    "CMT_NAME" character varying(240) NULL,
    "CMT_OPT" character varying(38) NULL,
    "FONT_COLOR" character varying(8) NULL,
    "COMMENT_NEWLINE" integer NOT NULL,
    CONSTRAINT "PK_ODR_INF_DETAIL" PRIMARY KEY ("HP_ID", "RAIIN_NO", "RP_NO", "RP_EDA_NO", "ROW_NO")
);

CREATE TABLE "ONLINE_CONFIRMATION" (
    "RECEPTION_NO" text NOT NULL,
    "RECEPTION_DATETIME" timestamp with time zone NOT NULL,
    "YOYAKU_DATE" integer NOT NULL,
    "SEGMENT_OF_RESULT" character varying(1) NULL,
    "ERROR_MESSAGE" character varying(60) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_ONLINE_CONFIRMATION" PRIMARY KEY ("RECEPTION_NO")
);

CREATE TABLE "ONLINE_CONFIRMATION_HISTORY" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "PT_ID" bigint NOT NULL,
    "ONLINE_CONFIRMATION_DATE" timestamp with time zone NOT NULL,
    "CONFIRMATION_TYPE" integer NOT NULL,
    "CONFIRMATION_RESULT" text NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_ONLINE_CONFIRMATION_HISTORY" PRIMARY KEY ("ID")
);

CREATE TABLE "ONLINE_CONSENT" (
    "PT_ID" bigint NOT NULL,
    "CONS_KBN" integer NOT NULL,
    "CONS_DATE" timestamp with time zone NOT NULL,
    "LIMIT_DATE" timestamp with time zone NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_ONLINE_CONSENT" PRIMARY KEY ("PT_ID", "CONS_KBN")
);

CREATE TABLE "PATH_CONF" (
    "HP_ID" integer NOT NULL,
    "GRP_CD" integer NOT NULL,
    "GRP_EDA_NO" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "MACHINE" character varying(60) NULL,
    "PATH" character varying(300) NULL,
    "PARAM" character varying(1000) NULL,
    "BIKO" character varying(200) NULL,
    "CHAR_CD" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_PATH_CONF" PRIMARY KEY ("HP_ID", "GRP_CD", "GRP_EDA_NO", "SEQ_NO")
);

CREATE TABLE "PAYMENT_METHOD_MST" (
    "HP_ID" integer NOT NULL,
    "PAYMENT_METHOD_CD" integer NOT NULL,
    "PAY_NAME" character varying(60) NULL,
    "PAY_SNAME" character varying(1) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_PAYMENT_METHOD_MST" PRIMARY KEY ("HP_ID", "PAYMENT_METHOD_CD")
);

CREATE TABLE "PERMISSION_MST" (
    "FUNCTION_CD" character varying(8) NOT NULL,
    "PERMISSION" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_PERMISSION_MST" PRIMARY KEY ("FUNCTION_CD", "PERMISSION")
);

CREATE TABLE "PHYSICAL_AVERAGE" (
    "JISSI_YEAR" integer NOT NULL,
    "AGE_YEAR" integer NOT NULL,
    "AGE_MONTH" integer NOT NULL,
    "AGE_DAY" integer NOT NULL,
    "MALE_HEIGHT" double precision NOT NULL,
    "MALE_WEIGHT" double precision NOT NULL,
    "MALE_CHEST" double precision NOT NULL,
    "MALE_HEAD" double precision NOT NULL,
    "FEMALE_HEIGHT" double precision NOT NULL,
    "FEMALE_WEIGHT" double precision NOT NULL,
    "FEMALE_CHEST" double precision NOT NULL,
    "FEMALE_HEAD" double precision NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_PHYSICAL_AVERAGE" PRIMARY KEY ("JISSI_YEAR", "AGE_YEAR", "AGE_MONTH", "AGE_DAY")
);

CREATE TABLE "PI_IMAGE" (
    "HP_ID" integer NOT NULL,
    "IMAGE_TYPE" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "FILE_NAME" character varying(30) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_PI_IMAGE" PRIMARY KEY ("HP_ID", "IMAGE_TYPE", "ITEM_CD")
);

CREATE TABLE "PI_INF" (
    "PI_ID" character varying(6) NOT NULL,
    "W_DATE" integer NOT NULL,
    "TITLE" character varying(60) NULL,
    "R_DATE" integer NOT NULL,
    "REVISION" character varying(100) NULL,
    "R_TYPE" character varying(20) NULL,
    "R_REASON" character varying(200) NULL,
    "SCCJNO" character varying(200) NULL,
    "THERAPEUTICCLASSIFICATION" character varying(200) NULL,
    "PREPARATION_NAME" character varying(200) NULL,
    "HIGHLIGHT" character varying(200) NULL,
    "FEATURE" character varying(200) NULL,
    "RELATEDMATTER" character varying(200) NULL,
    "COMMONNAME" character varying(200) NULL,
    "GENERICNAME" text NULL,
    CONSTRAINT "PK_PI_INF" PRIMARY KEY ("PI_ID")
);

CREATE TABLE "PI_INF_DETAIL" (
    "PI_ID" character varying(6) NOT NULL,
    "BRANCH" character varying(3) NOT NULL,
    "JPN" character varying(6) NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "LEVEL" integer NOT NULL,
    "TEXT" text NULL,
    CONSTRAINT "PK_PI_INF_DETAIL" PRIMARY KEY ("PI_ID", "BRANCH", "JPN", "SEQ_NO")
);

CREATE TABLE "PI_PRODUCT_INF" (
    "PI_ID" text NOT NULL,
    "BRANCH" text NOT NULL,
    "JPN" text NOT NULL,
    "PI_ID_FULL" text NOT NULL,
    "PRODUCT_NAME" character varying(120) NULL,
    "UNIT" character varying(100) NULL,
    "MAKER" character varying(256) NULL,
    "VENDER" character varying(256) NULL,
    "MARKETER" character varying(256) NULL,
    "OTHER" character varying(256) NULL,
    "YJ_CD" text NULL,
    "HOT_CD" text NULL,
    "SOSYO_NAME" character varying(80) NULL,
    "GENERIC_NAME" character varying(120) NULL,
    "GENERIC_ENG_NAME" character varying(120) NULL,
    "GENERAL_NO" character varying(50) NULL,
    "VER_DATE" text NULL,
    "YAKKA_REG" text NULL,
    "YAKKA_DEL" text NULL,
    "IS_STOPED" text NULL,
    "STOP_DATE" text NULL,
    "PI_STATE" text NULL,
    "PI_SBT" text NULL,
    "BIKO_PI_UNIT" character varying(512) NULL,
    "BIKO_PI_BRANCH" character varying(256) NULL,
    "UPD_DATE_IMG" timestamp with time zone NULL,
    "UPD_DATE_PI" timestamp with time zone NULL,
    "UPD_DATE_PRODUCT" timestamp with time zone NULL,
    "UPD_DATE_XML" timestamp with time zone NULL,
    CONSTRAINT "PK_PI_PRODUCT_INF" PRIMARY KEY ("PI_ID_FULL", "PI_ID", "BRANCH", "JPN")
);

CREATE TABLE "POST_CODE_MST" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "POST_CD" character varying(7) NULL,
    "PREF_KANA" character varying(60) NULL,
    "CITY_KANA" character varying(60) NULL,
    "POSTAL_TERM_KANA" character varying(150) NULL,
    "PREF_NAME" character varying(40) NULL,
    "CITY_NAME" character varying(40) NULL,
    "BANTI" character varying(100) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_POST_CODE_MST" PRIMARY KEY ("ID")
);

CREATE TABLE "PRIORITY_HAIHAN_MST" (
    "HP_ID" integer NOT NULL,
    "HAIHAN_GRP" bigint NOT NULL,
    "START_DATE" integer NOT NULL,
    "USER_SETTING" integer NOT NULL,
    "COUNT" integer NOT NULL,
    "ITEM_CD1" character varying(10) NULL,
    "ITEM_CD2" character varying(10) NULL,
    "ITEM_CD3" character varying(10) NULL,
    "ITEM_CD4" character varying(10) NULL,
    "ITEM_CD5" character varying(10) NULL,
    "ITEM_CD6" character varying(10) NULL,
    "ITEM_CD7" character varying(10) NULL,
    "ITEM_CD8" character varying(10) NULL,
    "ITEM_CD9" character varying(10) NULL,
    "SP_JYOKEN" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "TERM_CNT" integer NOT NULL,
    "TERM_SBT" integer NOT NULL,
    "TARGET_KBN" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_PRIORITY_HAIHAN_MST" PRIMARY KEY ("HP_ID", "HAIHAN_GRP", "START_DATE", "USER_SETTING")
);

CREATE TABLE "PT_ALRGY_DRUG" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"ITEM_CD" varchar(10) NULL,
	"DRUG_NAME" varchar(100) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_ALRGY_DRUG" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_ALRGY_ELSE" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"ALRGY_NAME" varchar(100) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_ALRGY_ELSE" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_ALRGY_FOOD" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"ALRGY_KBN" text NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_ALRGY_FOOD" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_BYOMEI" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "BYOMEI_CD" character varying(7) NULL,
    "SORT_NO" integer NOT NULL,
    "SYUSYOKU_CD1" character varying(7) NULL,
    "SYUSYOKU_CD2" character varying(7) NULL,
    "SYUSYOKU_CD3" character varying(7) NULL,
    "SYUSYOKU_CD4" character varying(7) NULL,
    "SYUSYOKU_CD5" character varying(7) NULL,
    "SYUSYOKU_CD6" character varying(7) NULL,
    "SYUSYOKU_CD7" character varying(7) NULL,
    "SYUSYOKU_CD8" character varying(7) NULL,
    "SYUSYOKU_CD9" character varying(7) NULL,
    "SYUSYOKU_CD10" character varying(7) NULL,
    "SYUSYOKU_CD11" character varying(7) NULL,
    "SYUSYOKU_CD12" character varying(7) NULL,
    "SYUSYOKU_CD13" character varying(7) NULL,
    "SYUSYOKU_CD14" character varying(7) NULL,
    "SYUSYOKU_CD15" character varying(7) NULL,
    "SYUSYOKU_CD16" character varying(7) NULL,
    "SYUSYOKU_CD17" character varying(7) NULL,
    "SYUSYOKU_CD18" character varying(7) NULL,
    "SYUSYOKU_CD19" character varying(7) NULL,
    "SYUSYOKU_CD20" character varying(7) NULL,
    "SYUSYOKU_CD21" character varying(7) NULL,
    "BYOMEI" character varying(160) NULL,
    "START_DATE" integer NOT NULL,
    "TENKI_KBN" integer NOT NULL,
    "TENKI_DATE" integer NOT NULL,
    "SYUBYO_KBN" integer NOT NULL,
    "SIKKAN_KBN" integer NOT NULL,
    "NANBYO_CD" integer NOT NULL,
    "HOSOKU_CMT" character varying(80) NULL,
    "HOKEN_PID" integer NOT NULL,
    "TOGETU_BYOMEI" integer NOT NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IS_NODSP_KARTE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    "SEQ_NO" bigint NOT NULL,
    "IS_IMPORTANT" integer NOT NULL,
    CONSTRAINT "PK_PT_BYOMEI" PRIMARY KEY ("HP_ID", "PT_ID", "ID")
);

CREATE TABLE "PT_CMT_INF" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"TEXT" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"ID" bigserial NOT NULL,
    CONSTRAINT "PK_PT_CMT_INF" PRIMARY KEY ("ID", "HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_FAMILY" (
    "FAMILY_ID" bigint GENERATED BY DEFAULT AS IDENTITY,
	"HP_ID" int4 NOT NULL,
	"PT_ID" bigint GENERATED BY DEFAULT AS IDENTITY,
	"SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
	"ZOKUGARA_CD" varchar(10) NOT NULL,
	"SORT_NO" int4 NOT NULL DEFAULT 1,
	"PARENT_ID" int4 NOT NULL DEFAULT 0,
	"FAMILY_PT_ID" int8 NOT NULL DEFAULT 0,
	"KANA_NAME" varchar(100) NULL,
	"NAME" varchar(100) NULL,
	"SEX" int4 NOT NULL,
	"BIRTHDAY" int4 NOT NULL,
	"IS_DEAD" int4 NOT NULL DEFAULT 0,
	"IS_SEPARATED" int4 NOT NULL DEFAULT 0,
	"BIKO" varchar(120) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_FAMILY" PRIMARY KEY ("PT_ID")
);

CREATE TABLE "PT_FAMILY_REKI" (
    "ID" bigserial NOT NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"FAMILY_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"BYOMEI_CD" varchar(7) NULL,
	"BYOTAI_CD" varchar(7) NULL,
	"BYOMEI" varchar(400) NULL,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_FAMILY_REKI" PRIMARY KEY ("ID")
);

CREATE TABLE "PT_GRP_INF" (
   "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"GRP_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SORT_NO" int4 NOT NULL DEFAULT 1,
	"GRP_CODE" varchar(4) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_GRP_INF" PRIMARY KEY ("HP_ID", "GRP_ID", "GRP_CODE", "SEQ_NO")
);

CREATE TABLE "PT_GRP_ITEM" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "GRP_CODE" character varying(2) NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "GRP_CODE_NAME" character varying(30) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_PT_GRP_ITEM" PRIMARY KEY ("HP_ID", "GRP_ID", "GRP_CODE", "SEQ_NO")
);

CREATE TABLE "PT_GRP_NAME_MST" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "GRP_NAME" character varying(20) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_PT_GRP_NAME_MST" PRIMARY KEY ("HP_ID", "GRP_ID")
);

CREATE TABLE "PT_HOKEN_CHECK" (
   "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_GRP" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"CHECK_DATE" timestamptz NOT NULL,
	"CHECK_ID" int4 NOT NULL DEFAULT 0,
	"CHECK_MACHINE" varchar(60) NULL,
	"CHECK_CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_HOKEN_CHECK" PRIMARY KEY ("HP_ID", "PT_ID", "HOKEN_GRP", "HOKEN_ID", "SEQ_NO")
);

CREATE TABLE "PT_HOKEN_INF" (
   "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"HOKEN_NO" int4 NOT NULL,
	"HOKEN_EDA_NO" int4 NOT NULL,
	"HOKENSYA_NO" varchar(8) NULL,
	"KIGO" varchar(80) NULL,
	"BANGO" varchar(80) NULL,
	"HONKE_KBN" int4 NOT NULL DEFAULT 0,
	"HOKEN_KBN" int4 NOT NULL DEFAULT 0,
	"HOUBETU" varchar(3) NULL,
	"HOKENSYA_NAME" varchar(100) NULL,
	"HOKENSYA_POST" varchar(7) NULL,
	"HOKENSYA_ADDRESS" varchar(100) NULL,
	"HOKENSYA_TEL" varchar(15) NULL,
	"KEIZOKU_KBN" int4 NOT NULL DEFAULT 0,
	"SIKAKU_DATE" int4 NOT NULL DEFAULT 0,
	"KOFU_DATE" int4 NOT NULL DEFAULT 0,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 0,
	"RATE" int4 NOT NULL DEFAULT 0,
	"GENDOGAKU" int4 NOT NULL DEFAULT 0,
	"KOGAKU_KBN" int4 NOT NULL DEFAULT 0,
	"KOGAKU_TYPE" int4 NOT NULL DEFAULT 0,
	"TOKUREI_YM1" int4 NOT NULL DEFAULT 0,
	"TOKUREI_YM2" int4 NOT NULL DEFAULT 0,
	"TASUKAI_YM" int4 NOT NULL DEFAULT 0,
	"SYOKUMU_KBN" int4 NOT NULL DEFAULT 0,
	"GENMEN_KBN" int4 NOT NULL DEFAULT 0,
	"GENMEN_RATE" int4 NOT NULL DEFAULT 0,
	"GENMEN_GAKU" int4 NOT NULL DEFAULT 0,
	"TOKKI1" varchar(2) NULL,
	"TOKKI2" varchar(2) NULL,
	"TOKKI3" varchar(2) NULL,
	"TOKKI4" varchar(2) NULL,
	"TOKKI5" varchar(2) NULL,
	"ROUSAI_KOFU_NO" varchar(14) NULL,
	"ROUSAI_SAIGAI_KBN" int4 NOT NULL DEFAULT 0,
	"ROUSAI_JIGYOSYO_NAME" varchar(80) NULL,
	"ROUSAI_PREF_NAME" varchar(10) NULL,
	"ROUSAI_CITY_NAME" varchar(20) NULL,
	"ROUSAI_SYOBYO_DATE" int4 NOT NULL DEFAULT 0,
	"ROUSAI_SYOBYO_CD" varchar(2) NULL,
	"ROUSAI_ROUDOU_CD" varchar(2) NULL,
	"ROUSAI_KANTOKU_CD" varchar(2) NULL,
	"ROUSAI_RECE_COUNT" int4 NOT NULL DEFAULT 0,
	"JIBAI_HOKEN_NAME" varchar(100) NULL,
	"JIBAI_HOKEN_TANTO" varchar(40) NULL,
	"JIBAI_HOKEN_TEL" varchar(15) NULL,
	"JIBAI_JYUSYOU_DATE" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"RYOYO_START_DATE" int4 NOT NULL DEFAULT 0,
	"RYOYO_END_DATE" int4 NOT NULL DEFAULT 0,
	"EDA_NO" varchar(2) NULL,
    CONSTRAINT "PK_PT_HOKEN_INF" PRIMARY KEY ("HP_ID", "PT_ID", "HOKEN_ID", "SEQ_NO")
);

CREATE TABLE "PT_HOKEN_PATTERN" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_PID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"HOKEN_KBN" int4 NOT NULL DEFAULT 0,
	"HOKEN_SBT_CD" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"KOHI1_ID" int4 NOT NULL,
	"KOHI2_ID" int4 NOT NULL,
	"KOHI3_ID" int4 NOT NULL,
	"KOHI4_ID" int4 NOT NULL,
	"HOKEN_MEMO" varchar(400) NULL,
	"START_DATE" int4 NOT NULL,
	"END_DATE" int4 NOT NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_HOKEN_PATTERN" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO", "HOKEN_PID")
);

CREATE TABLE "PT_HOKEN_SCAN" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_GRP" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"FILE_NAME" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_HOKEN_SCAN" PRIMARY KEY ("HP_ID", "PT_ID", "HOKEN_GRP", "HOKEN_ID", "SEQ_NO")
);

CREATE TABLE "PT_INF" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" bigserial NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"PT_NUM" int8 NOT NULL,
	"KANA_NAME" varchar(100) NULL,
	"NAME" varchar(100) NULL,
	"SEX" int4 NOT NULL DEFAULT 0,
	"BIRTHDAY" int4 NOT NULL DEFAULT 0,
	"IS_DEAD" int4 NOT NULL DEFAULT 0,
	"DEATH_DATE" int4 NOT NULL DEFAULT 0,
	"HOME_POST" varchar(7) NULL,
	"HOME_ADDRESS1" varchar(100) NULL,
	"HOME_ADDRESS2" varchar(100) NULL,
	"TEL1" varchar(15) NULL,
	"TEL2" varchar(15) NULL,
	"MAIL" varchar(100) NULL,
	"SETAINUSI" varchar(100) NULL,
	"ZOKUGARA" varchar(20) NULL,
	"JOB" varchar(40) NULL,
	"RENRAKU_NAME" varchar(100) NULL,
	"RENRAKU_POST" varchar(7) NULL,
	"RENRAKU_ADDRESS1" varchar(100) NULL,
	"RENRAKU_ADDRESS2" varchar(100) NULL,
	"RENRAKU_TEL" varchar(15) NULL,
	"RENRAKU_MEMO" varchar(100) NULL,
	"OFFICE_NAME" varchar(100) NULL,
	"OFFICE_POST" varchar(7) NULL,
	"OFFICE_ADDRESS1" varchar(100) NULL,
	"OFFICE_ADDRESS2" varchar(100) NULL,
	"OFFICE_TEL" varchar(15) NULL,
	"OFFICE_MEMO" varchar(100) NULL,
	"IS_RYOSYO_DETAIL" int4 NOT NULL DEFAULT 1,
	"PRIMARY_DOCTOR" int4 NOT NULL,
	"IS_TESTER" int4 NOT NULL DEFAULT 0,
	"IS_DELETE" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"MAIN_HOKEN_PID" int4 NOT NULL DEFAULT 0,
	"REFERENCE_NO" bigserial NOT NULL,
	"LIMIT_CONS_FLG" int4 NOT NULL DEFAULT 0,
    CONSTRAINT "PK_PT_INF" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_INFECTION" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"BYOMEI_CD" varchar(7) NULL,
	"BYOTAI_CD" text NULL,
	"BYOMEI" varchar(400) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_INFECTION" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_JIBAI_DOC" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "SIN_YM" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "SINDAN_COST" integer NOT NULL,
    "SINDAN_NUM" integer NOT NULL,
    "MEISAI_COST" integer NOT NULL,
    "MEISAI_NUM" integer NOT NULL,
    "ELSE_COST" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_PT_JIBAI_DOC" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "HOKEN_ID", "SEQ_NO")
);

CREATE TABLE "PT_JIBKAR" (
    "HP_ID" int4 NOT NULL,
	"WEB_ID" varchar(16) NOT NULL,
	"PT_ID" int8 NOT NULL,
	"ODR_KAIJI" int4 NOT NULL DEFAULT 0,
	"ODR_UPDATE_DATE" timestamptz NOT NULL,
	"KARTE_KAIJI" int4 NOT NULL DEFAULT 0,
	"KARTE_UPDATE_DATE" timestamptz NOT NULL,
	"KENSA_KAIJI" int4 NOT NULL DEFAULT 0,
	"KENSA_UPDATE_DATE" timestamptz NOT NULL,
	"BYOMEI_KAIJI" int4 NOT NULL DEFAULT 0,
	"BYOMEI_UPDATE_DATE" timestamptz NOT NULL,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_JIBKAR" PRIMARY KEY ("HP_ID", "WEB_ID")
);

CREATE TABLE "PT_KIO_REKI" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"BYOMEI_CD" varchar(7) NULL,
	"BYOTAI_CD" text NULL,
	"BYOMEI" varchar(400) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_KIO_REKI" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_KOHI" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"PREF_NO" int4 NOT NULL,
	"HOKEN_NO" int4 NOT NULL,
	"HOKEN_EDA_NO" int4 NOT NULL,
	"FUTANSYA_NO" varchar(8) NULL,
	"JYUKYUSYA_NO" varchar(7) NULL,
	"TOKUSYU_NO" varchar(20) NULL,
	"SIKAKU_DATE" int4 NOT NULL DEFAULT 0,
	"KOFU_DATE" int4 NOT NULL DEFAULT 0,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 0,
	"RATE" int4 NOT NULL DEFAULT 0,
	"GENDOGAKU" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"HOKEN_SBT_KBN" int4 NOT NULL DEFAULT 0,
	"HOUBETU" varchar(3) NULL,
    CONSTRAINT "PK_PT_KOHI" PRIMARY KEY ("HP_ID", "PT_ID", "HOKEN_ID", "SEQ_NO")
);

CREATE TABLE "PT_KYUSEI" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"KANA_NAME" varchar(100) NULL,
	"NAME" varchar(100) NOT NULL,
	"END_DATE" int4 NOT NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_KYUSEI" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_LAST_VISIT_DATE" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "LAST_VISIT_DATE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_PT_LAST_VISIT_DATE" PRIMARY KEY ("HP_ID", "PT_ID")
);

CREATE TABLE "PT_MEMO" (
   "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"MEMO" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_MEMO" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_OTC_DRUG" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"SERIAL_NUM" int4 NOT NULL,
	"TRADE_NAME" varchar(200) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_OTC_DRUG" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_OTHER_DRUG" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"ITEM_CD" varchar(10) NULL,
	"DRUG_NAME" varchar(100) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_OTHER_DRUG" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_PREGNANCY" (
    "ID" bigserial NOT NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"PERIOD_DATE" int4 NOT NULL DEFAULT 0,
	"PERIOD_DUE_DATE" int4 NOT NULL DEFAULT 0,
	"OVULATION_DATE" int4 NOT NULL DEFAULT 0,
	"OVULATION_DUE_DATE" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_PREGNANCY" PRIMARY KEY ("ID", "HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_ROUSAI_TENKI" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"END_DATE" int4 NOT NULL DEFAULT 999999,
	"SINKEI" int4 NOT NULL,
	"TENKI" int4 NOT NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_ROUSAI_TENKI" PRIMARY KEY ("HP_ID", "PT_ID", "HOKEN_ID", "SEQ_NO")
);

CREATE TABLE "PT_SANTEI_CONF" (
   "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"KBN_NO" int4 NOT NULL,
	"EDA_NO" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"KBN_VAL" int4 NOT NULL,
	"SORT_NO" int4 NOT NULL DEFAULT 1,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_SANTEI_CONF" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_SUPPLE" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"INDEX_CD" text NULL,
	"INDEX_WORD" varchar(200) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_PT_SUPPLE" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "PT_TAG" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"MEMO" text NULL,
	"MEMO_DATA" bytea NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"IS_DSP_UKETUKE" int4 NOT NULL DEFAULT 1,
	"IS_DSP_KARTE" int4 NOT NULL DEFAULT 1,
	"IS_DSP_KAIKEI" int4 NOT NULL DEFAULT 1,
	"IS_DSP_RECE" int4 NOT NULL DEFAULT 1,
	"BACKGROUND_COLOR" varchar(8) NULL,
	"ALPHABLEND_VAL" int4 NOT NULL DEFAULT 200,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"FONTSIZE" int4 NOT NULL DEFAULT 0,
	"WIDTH" int4 NOT NULL DEFAULT 0,
	"HEIGHT" int4 NOT NULL DEFAULT 0,
	"LEFT" int4 NOT NULL DEFAULT 0,
	"TOP" int4 NOT NULL DEFAULT 0,
	"TAG_GRP_CD" int4 NOT NULL DEFAULT 0,
    CONSTRAINT "PK_PT_TAG" PRIMARY KEY ("HP_ID", "PT_ID", "SEQ_NO")
);

CREATE TABLE "RAIIN_CMT_INF" (
    "HP_ID" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"CMT_KBN" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"TEXT" varchar(200) NULL,
	"IS_DELETE" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_RAIIN_CMT_INF" PRIMARY KEY ("HP_ID", "RAIIN_NO", "CMT_KBN", "SEQ_NO")
);

CREATE TABLE "RAIIN_FILTER_KBN" (
    "HP_ID" integer NOT NULL,
    "FILTER_ID" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "GRP_ID" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_FILTER_KBN" PRIMARY KEY ("HP_ID", "FILTER_ID", "SEQ_NO")
);

CREATE TABLE "RAIIN_FILTER_MST" (
    "FILTER_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "FILTER_NAME" text NULL,
    "SELECT_KBN" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "SHORTCUT" character varying(10) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_FILTER_MST" PRIMARY KEY ("FILTER_ID")
);

CREATE TABLE "RAIIN_FILTER_SORT" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "FILTER_ID" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "PRIORITY" integer NOT NULL,
    "COLUMN_NAME" text NULL,
    "KBN_CD" integer NOT NULL,
    "SORT_KBN" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_FILTER_SORT" PRIMARY KEY ("ID")
);

CREATE TABLE "RAIIN_FILTER_STATE" (
    "HP_ID" integer NOT NULL,
    "FILTER_ID" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "STATUS" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_FILTER_STATE" PRIMARY KEY ("HP_ID", "FILTER_ID", "SEQ_NO")
);

CREATE TABLE "RAIIN_INF" (
    "HP_ID" int4 NOT NULL,
	"RAIIN_NO" bigserial NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"OYA_RAIIN_NO" int8 NOT NULL,
	"STATUS" int4 NOT NULL DEFAULT 0,
	"IS_YOYAKU" int4 NOT NULL DEFAULT 0,
	"YOYAKU_TIME" varchar(6) NULL,
	"YOYAKU_ID" int4 NOT NULL DEFAULT 0,
	"UKETUKE_SBT" int4 NOT NULL DEFAULT 0,
	"UKETUKE_TIME" varchar(6) NULL,
	"UKETUKE_ID" int4 NOT NULL DEFAULT 0,
	"UKETUKE_NO" int4 NOT NULL DEFAULT 0,
	"SIN_START_TIME" varchar(6) NULL,
	"SIN_END_TIME" varchar(6) NULL,
	"KAIKEI_TIME" varchar(6) NULL,
	"KAIKEI_ID" int4 NOT NULL DEFAULT 0,
	"KA_ID" int4 NOT NULL DEFAULT 0,
	"TANTO_ID" int4 NOT NULL DEFAULT 0,
	"HOKEN_PID" int4 NOT NULL DEFAULT 0,
	"SYOSAISIN_KBN" int4 NOT NULL DEFAULT 0,
	"JIKAN_KBN" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"SANTEI_KBN" int4 NOT NULL DEFAULT 0,
	"CONFIRMATION_RESULT" varchar(120) NULL,
	"CONFIRMATION_STATE" int4 NOT NULL DEFAULT 0,
    CONSTRAINT "PK_RAIIN_INF" PRIMARY KEY ("HP_ID", "RAIIN_NO")
);

CREATE TABLE "RAIIN_KBN_DETAIL" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "KBN_NAME" character varying(20) NULL,
    "COLOR_CD" character varying(8) NULL,
    "IS_CONFIRMED" integer NOT NULL,
    "IS_AUTO" integer NOT NULL,
    "IS_AUTO_DELETE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_KBN_DETAIL" PRIMARY KEY ("HP_ID", "GRP_ID", "KBN_CD")
);

CREATE TABLE "RAIIN_KBN_INF" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"GRP_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"KBN_CD" int4 NOT NULL,
	"IS_DELETE" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_RAIIN_KBN_INF" PRIMARY KEY ("HP_ID", "PT_ID", "RAIIN_NO", "GRP_ID", "SEQ_NO")
);

CREATE TABLE "RAIIN_KBN_ITEM" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "ITEM_CD" character varying(10) NULL,
    "IS_EXCLUDE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    "SORT_NO" integer NOT NULL,
    CONSTRAINT "PK_RAIIN_KBN_ITEM" PRIMARY KEY ("HP_ID", "GRP_ID", "KBN_CD", "SEQ_NO")
);

CREATE TABLE "RAIIN_KBN_KOUI" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "KOUI_KBN_ID" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_KBN_KOUI" PRIMARY KEY ("HP_ID", "GRP_ID", "KBN_CD", "SEQ_NO")
);

CREATE TABLE "RAIIN_KBN_MST" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "GRP_NAME" character varying(20) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_KBN_MST" PRIMARY KEY ("HP_ID", "GRP_ID")
);

CREATE TABLE "RAIIN_KBN_YOYAKU" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "YOYAKU_CD" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_KBN_YOYAKU" PRIMARY KEY ("HP_ID", "GRP_ID", "KBN_CD", "SEQ_NO")
);

CREATE TABLE "RAIIN_LIST_CMT" (
    "HP_ID" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"CMT_KBN" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SEQ_NO" int8 NOT NULL,
	"TEXT" varchar(300) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamp NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamp NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_RAIIN_LIST_CMT" PRIMARY KEY ("HP_ID", "RAIIN_NO", "CMT_KBN")
);

CREATE TABLE "RAIIN_LIST_DETAIL" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "KBN_NAME" character varying(20) NULL,
    "COLOR_CD" character varying(8) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_LIST_DETAIL" PRIMARY KEY ("HP_ID", "GRP_ID", "KBN_CD")
);

CREATE TABLE "RAIIN_LIST_DOC" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "CATEGORY_CD" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_LIST_DOC" PRIMARY KEY ("HP_ID", "GRP_ID", "KBN_CD", "SEQ_NO")
);

CREATE TABLE "RAIIN_LIST_FILE" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "CATEGORY_CD" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_LIST_FILE" PRIMARY KEY ("HP_ID", "GRP_ID", "KBN_CD", "SEQ_NO")
);

CREATE TABLE "RAIIN_LIST_INF" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "GRP_ID" integer NOT NULL,
    "RAIIN_LIST_KBN" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_LIST_INF" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_DATE", "RAIIN_NO", "GRP_ID", "RAIIN_LIST_KBN")
);

CREATE TABLE "RAIIN_LIST_ITEM" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "ITEM_CD" character varying(10) NULL,
    "IS_EXCLUDE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_LIST_ITEM" PRIMARY KEY ("HP_ID", "KBN_CD", "SEQ_NO", "GRP_ID")
);

CREATE TABLE "RAIIN_LIST_KOUI" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "KBN_CD" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "KOUI_KBN_ID" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_LIST_KOUI" PRIMARY KEY ("HP_ID", "KBN_CD", "SEQ_NO", "GRP_ID")
);

CREATE TABLE "RAIIN_LIST_MST" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "GRP_NAME" character varying(20) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RAIIN_LIST_MST" PRIMARY KEY ("HP_ID", "GRP_ID")
);

CREATE TABLE "RAIIN_LIST_TAG" (
    "HP_ID" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"SEQ_NO" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"TAG_NO" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamp NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamp NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_RAIIN_LIST_TAG" PRIMARY KEY ("HP_ID", "RAIIN_NO", "SEQ_NO")
);

CREATE TABLE "RECE_CHECK_CMT" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"SEQ_NO" int4 NOT NULL DEFAULT 1,
	"IS_PENDING" int4 NOT NULL DEFAULT 0,
	"CMT" varchar(300) NULL,
	"IS_CHECKED" int4 NOT NULL DEFAULT 0,
	"SORT_NO" int4 NOT NULL DEFAULT 1,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_RECE_CHECK_CMT" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "HOKEN_ID", "SEQ_NO")
);

CREATE TABLE "RECE_CHECK_ERR" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "SIN_YM" integer NOT NULL,
    "ERR_CD" character varying(5) NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "A_CD" character varying(100) NOT NULL,
    "B_CD" character varying(100) NOT NULL,
    "MESSAGE_1" character varying(100) NULL,
    "MESSAGE_2" character varying(100) NULL,
    "IS_CHECKED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RECE_CHECK_ERR" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "HOKEN_ID", "ERR_CD", "SIN_DATE", "A_CD", "B_CD")
);

CREATE TABLE "RECE_CHECK_OPT" (
    "HP_ID" integer NOT NULL,
    "ERR_CD" character varying(5) NOT NULL,
    "CHECK_OPT" integer NOT NULL,
    "BIKO" character varying(100) NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RECE_CHECK_OPT" PRIMARY KEY ("HP_ID", "ERR_CD")
);

CREATE TABLE "RECE_CMT" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"CMT_KBN" int4 NOT NULL DEFAULT 1,
	"CMT_SBT" int4 NOT NULL DEFAULT 0,
	"ID" bigserial NOT NULL,
	"SEQ_NO" int4 NOT NULL DEFAULT 1,
	"ITEM_CD" varchar(10) NULL,
	"CMT" text NULL,
	"CMT_DATA" varchar(38) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_RECE_CMT" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "HOKEN_ID", "CMT_KBN", "CMT_SBT", "ID")
);

CREATE TABLE "RECE_FUTAN_KBN" (
    "HP_ID" integer NOT NULL,
    "SEIKYU_YM" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_YM" integer NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "HOKEN_PID" integer NOT NULL,
    "FUTAN_KBN_CD" character varying(1) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RECE_FUTAN_KBN" PRIMARY KEY ("HP_ID", "SEIKYU_YM", "PT_ID", "SIN_YM", "HOKEN_ID", "HOKEN_PID")
);

CREATE TABLE "RECE_INF" (
    "HP_ID" integer NOT NULL,
    "SEIKYU_YM" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_YM" integer NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "HOKEN_ID2" integer NOT NULL,
    "KOHI1_ID" integer NOT NULL,
    "KOHI2_ID" integer NOT NULL,
    "KOHI3_ID" integer NOT NULL,
    "KOHI4_ID" integer NOT NULL,
    "HOKEN_KBN" integer NOT NULL,
    "HOKEN_SBT_CD" integer NOT NULL,
    "RECE_SBT" character varying(4) NULL,
    "HOKENSYA_NO" character varying(8) NULL,
    "HOUBETU" character varying(3) NULL,
    "KOHI1_HOUBETU" character varying(3) NULL,
    "KOHI2_HOUBETU" character varying(3) NULL,
    "KOHI3_HOUBETU" character varying(3) NULL,
    "KOHI4_HOUBETU" character varying(3) NULL,
    "HONKE_KBN" integer NOT NULL,
    "KOGAKU_KBN" integer NOT NULL,
    "KOGAKU_TEKIYO_KBN" integer NOT NULL,
    "IS_TOKUREI" integer NOT NULL,
    "IS_TASUKAI" integer NOT NULL,
    "KOGAKU_KOHI1_LIMIT" integer NOT NULL,
    "KOGAKU_KOHI2_LIMIT" integer NOT NULL,
    "KOGAKU_KOHI3_LIMIT" integer NOT NULL,
    "KOGAKU_KOHI4_LIMIT" integer NOT NULL,
    "TOTAL_KOGAKU_LIMIT" integer NOT NULL,
    "GENMEN_KBN" integer NOT NULL,
    "HOKEN_RATE" integer NOT NULL,
    "PT_RATE" integer NOT NULL,
    "EN_TEN" integer NOT NULL,
    "KOHI1_LIMIT" integer NOT NULL,
    "KOHI1_OTHER_FUTAN" integer NOT NULL,
    "KOHI2_LIMIT" integer NOT NULL,
    "KOHI2_OTHER_FUTAN" integer NOT NULL,
    "KOHI3_LIMIT" integer NOT NULL,
    "KOHI3_OTHER_FUTAN" integer NOT NULL,
    "KOHI4_LIMIT" integer NOT NULL,
    "KOHI4_OTHER_FUTAN" integer NOT NULL,
    "TENSU" integer NOT NULL,
    "TOTAL_IRYOHI" integer NOT NULL,
    "HOKEN_FUTAN" integer NOT NULL,
    "KOGAKU_FUTAN" integer NOT NULL,
    "KOHI1_FUTAN" integer NOT NULL,
    "KOHI2_FUTAN" integer NOT NULL,
    "KOHI3_FUTAN" integer NOT NULL,
    "KOHI4_FUTAN" integer NOT NULL,
    "ICHIBU_FUTAN" integer NOT NULL,
    "GENMEN_GAKU" integer NOT NULL,
    "HOKEN_FUTAN_10EN" integer NOT NULL,
    "KOGAKU_FUTAN_10EN" integer NOT NULL,
    "KOHI1_FUTAN_10EN" integer NOT NULL,
    "KOHI2_FUTAN_10EN" integer NOT NULL,
    "KOHI3_FUTAN_10EN" integer NOT NULL,
    "KOHI4_FUTAN_10EN" integer NOT NULL,
    "ICHIBU_FUTAN_10EN" integer NOT NULL,
    "GENMEN_GAKU_10EN" integer NOT NULL,
    "PT_FUTAN" integer NOT NULL,
    "KOGAKU_OVER_KBN" integer NOT NULL,
    "HOKEN_TENSU" integer NOT NULL,
    "HOKEN_ICHIBU_FUTAN" integer NOT NULL,
    "HOKEN_ICHIBU_FUTAN_10EN" integer NOT NULL,
    "KOHI1_TENSU" integer NOT NULL,
    "KOHI1_ICHIBU_SOTOGAKU" integer NOT NULL,
    "KOHI1_ICHIBU_SOTOGAKU_10EN" integer NOT NULL,
    "KOHI1_ICHIBU_FUTAN" integer NOT NULL,
    "KOHI2_TENSU" integer NOT NULL,
    "KOHI2_ICHIBU_SOTOGAKU" integer NOT NULL,
    "KOHI2_ICHIBU_SOTOGAKU_10EN" integer NOT NULL,
    "KOHI2_ICHIBU_FUTAN" integer NOT NULL,
    "KOHI3_TENSU" integer NOT NULL,
    "KOHI3_ICHIBU_SOTOGAKU" integer NOT NULL,
    "KOHI3_ICHIBU_SOTOGAKU_10EN" integer NOT NULL,
    "KOHI3_ICHIBU_FUTAN" integer NOT NULL,
    "KOHI4_TENSU" integer NOT NULL,
    "KOHI4_ICHIBU_SOTOGAKU" integer NOT NULL,
    "KOHI4_ICHIBU_SOTOGAKU_10EN" integer NOT NULL,
    "KOHI4_ICHIBU_FUTAN" integer NOT NULL,
    "TOTAL_ICHIBU_FUTAN" integer NOT NULL,
    "TOTAL_ICHIBU_FUTAN_10EN" integer NOT NULL,
    "HOKEN_RECE_TENSU" integer NULL,
    "HOKEN_RECE_FUTAN" integer NULL,
    "KOHI1_RECE_TENSU" integer NULL,
    "KOHI1_RECE_FUTAN" integer NULL,
    "KOHI1_RECE_KYUFU" integer NULL,
    "KOHI2_RECE_TENSU" integer NULL,
    "KOHI2_RECE_FUTAN" integer NULL,
    "KOHI2_RECE_KYUFU" integer NULL,
    "KOHI3_RECE_TENSU" integer NULL,
    "KOHI3_RECE_FUTAN" integer NULL,
    "KOHI3_RECE_KYUFU" integer NULL,
    "KOHI4_RECE_TENSU" integer NULL,
    "KOHI4_RECE_FUTAN" integer NULL,
    "KOHI4_RECE_KYUFU" integer NULL,
    "HOKEN_NISSU" integer NULL,
    "KOHI1_NISSU" integer NULL,
    "KOHI2_NISSU" integer NULL,
    "KOHI3_NISSU" integer NULL,
    "KOHI4_NISSU" integer NULL,
    "KOHI1_RECE_KISAI" integer NOT NULL,
    "KOHI2_RECE_KISAI" integer NOT NULL,
    "KOHI3_RECE_KISAI" integer NOT NULL,
    "KOHI4_RECE_KISAI" integer NOT NULL,
    "KOHI1_NAME_CD" character varying(5) NULL,
    "KOHI2_NAME_CD" character varying(5) NULL,
    "KOHI3_NAME_CD" character varying(5) NULL,
    "KOHI4_NAME_CD" character varying(5) NULL,
    "SEIKYU_KBN" integer NOT NULL,
    "TOKKI" character varying(10) NULL,
    "TOKKI1" character varying(10) NULL,
    "TOKKI2" character varying(10) NULL,
    "TOKKI3" character varying(10) NULL,
    "TOKKI4" character varying(10) NULL,
    "TOKKI5" character varying(10) NULL,
    "PT_STATUS" character varying(60) NULL,
    "ROUSAI_I_FUTAN" integer NOT NULL,
    "ROUSAI_RO_FUTAN" integer NOT NULL,
    "JIBAI_I_TENSU" integer NOT NULL,
    "JIBAI_RO_TENSU" integer NOT NULL,
    "JIBAI_HA_FUTAN" integer NOT NULL,
    "JIBAI_NI_FUTAN" integer NOT NULL,
    "JIBAI_HO_SINDAN" integer NOT NULL,
    "JIBAI_HO_SINDAN_COUNT" integer NOT NULL,
    "JIBAI_HE_MEISAI" integer NOT NULL,
    "JIBAI_HE_MEISAI_COUNT" integer NOT NULL,
    "JIBAI_A_FUTAN" integer NOT NULL,
    "JIBAI_B_FUTAN" integer NOT NULL,
    "JIBAI_C_FUTAN" integer NOT NULL,
    "JIBAI_D_FUTAN" integer NOT NULL,
    "JIBAI_KENPO_TENSU" integer NOT NULL,
    "JIBAI_KENPO_FUTAN" integer NOT NULL,
    "SINKEI" integer NOT NULL,
    "TENKI" integer NOT NULL,
    "KA_ID" integer NOT NULL,
    "TANTO_ID" integer NOT NULL,
    "IS_TESTER" integer NOT NULL,
    "IS_ZAIISO" integer NOT NULL,
    "CHOKI_KBN" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RECE_INF" PRIMARY KEY ("HP_ID", "SEIKYU_YM", "PT_ID", "SIN_YM", "HOKEN_ID")
);

CREATE TABLE "RECE_INF_EDIT" (
    "HP_ID" int4 NOT NULL,
	"SEIKYU_YM" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"RECE_SBT" varchar(4) NULL,
	"HOUBETU" varchar(3) NULL,
	"KOHI1_HOUBETU" varchar(3) NULL,
	"KOHI2_HOUBETU" varchar(3) NULL,
	"KOHI3_HOUBETU" varchar(3) NULL,
	"KOHI4_HOUBETU" varchar(3) NULL,
	"HOKEN_RECE_TENSU" int4 NULL,
	"HOKEN_RECE_FUTAN" int4 NULL,
	"KOHI1_RECE_TENSU" int4 NULL,
	"KOHI1_RECE_FUTAN" int4 NULL,
	"KOHI1_RECE_KYUFU" int4 NULL,
	"KOHI2_RECE_TENSU" int4 NULL,
	"KOHI2_RECE_FUTAN" int4 NULL,
	"KOHI2_RECE_KYUFU" int4 NULL,
	"KOHI3_RECE_TENSU" int4 NULL,
	"KOHI3_RECE_FUTAN" int4 NULL,
	"KOHI3_RECE_KYUFU" int4 NULL,
	"KOHI4_RECE_TENSU" int4 NULL,
	"KOHI4_RECE_FUTAN" int4 NULL,
	"KOHI4_RECE_KYUFU" int4 NULL,
	"HOKEN_NISSU" int4 NULL,
	"KOHI1_NISSU" int4 NULL,
	"KOHI2_NISSU" int4 NULL,
	"KOHI3_NISSU" int4 NULL,
	"KOHI4_NISSU" int4 NULL,
	"TOKKI" varchar(10) NULL,
	"TOKKI1" varchar(10) NULL,
	"TOKKI2" varchar(10) NULL,
	"TOKKI3" varchar(10) NULL,
	"TOKKI4" varchar(10) NULL,
	"TOKKI5" varchar(10) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_RECE_INF_EDIT" PRIMARY KEY ("HP_ID", "SEIKYU_YM", "PT_ID", "SIN_YM", "HOKEN_ID", "SEQ_NO")
);

CREATE TABLE "RECE_INF_JD" (
    "HP_ID" integer NOT NULL,
    "SEIKYU_YM" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_YM" integer NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "KOHI_ID" integer NOT NULL,
    "FUTAN_SBT_CD" integer NOT NULL,
    "NISSU1" integer NOT NULL,
    "NISSU2" integer NOT NULL,
    "NISSU3" integer NOT NULL,
    "NISSU4" integer NOT NULL,
    "NISSU5" integer NOT NULL,
    "NISSU6" integer NOT NULL,
    "NISSU7" integer NOT NULL,
    "NISSU8" integer NOT NULL,
    "NISSU9" integer NOT NULL,
    "NISSU10" integer NOT NULL,
    "NISSU11" integer NOT NULL,
    "NISSU12" integer NOT NULL,
    "NISSU13" integer NOT NULL,
    "NISSU14" integer NOT NULL,
    "NISSU15" integer NOT NULL,
    "NISSU16" integer NOT NULL,
    "NISSU17" integer NOT NULL,
    "NISSU18" integer NOT NULL,
    "NISSU19" integer NOT NULL,
    "NISSU20" integer NOT NULL,
    "NISSU21" integer NOT NULL,
    "NISSU22" integer NOT NULL,
    "NISSU23" integer NOT NULL,
    "NISSU24" integer NOT NULL,
    "NISSU25" integer NOT NULL,
    "NISSU26" integer NOT NULL,
    "NISSU27" integer NOT NULL,
    "NISSU28" integer NOT NULL,
    "NISSU29" integer NOT NULL,
    "NISSU30" integer NOT NULL,
    "NISSU31" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RECE_INF_JD" PRIMARY KEY ("HP_ID", "SEIKYU_YM", "PT_ID", "SIN_YM", "HOKEN_ID", "KOHI_ID")
);

CREATE TABLE "RECE_INF_PRE_EDIT" (
    "HP_ID" integer NOT NULL,
    "SEIKYU_YM" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_YM" integer NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "RECE_SBT" character varying(4) NULL,
    "HOUBETU" character varying(3) NULL,
    "KOHI1_HOUBETU" character varying(3) NULL,
    "KOHI2_HOUBETU" character varying(3) NULL,
    "KOHI3_HOUBETU" character varying(3) NULL,
    "KOHI4_HOUBETU" character varying(3) NULL,
    "HOKEN_RECE_TENSU" integer NULL,
    "HOKEN_RECE_FUTAN" integer NULL,
    "KOHI1_RECE_TENSU" integer NULL,
    "KOHI1_RECE_FUTAN" integer NULL,
    "KOHI1_RECE_KYUFU" integer NULL,
    "KOHI2_RECE_TENSU" integer NULL,
    "KOHI2_RECE_FUTAN" integer NULL,
    "KOHI2_RECE_KYUFU" integer NULL,
    "KOHI3_RECE_TENSU" integer NULL,
    "KOHI3_RECE_FUTAN" integer NULL,
    "KOHI3_RECE_KYUFU" integer NULL,
    "KOHI4_RECE_TENSU" integer NULL,
    "KOHI4_RECE_FUTAN" integer NULL,
    "KOHI4_RECE_KYUFU" integer NULL,
    "HOKEN_NISSU" integer NULL,
    "KOHI1_NISSU" integer NULL,
    "KOHI2_NISSU" integer NULL,
    "KOHI3_NISSU" integer NULL,
    "KOHI4_NISSU" integer NULL,
    "TOKKI" character varying(10) NULL,
    "TOKKI1" character varying(10) NULL,
    "TOKKI2" character varying(10) NULL,
    "TOKKI3" character varying(10) NULL,
    "TOKKI4" character varying(10) NULL,
    "TOKKI5" character varying(10) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RECE_INF_PRE_EDIT" PRIMARY KEY ("HP_ID", "SEIKYU_YM", "PT_ID", "SIN_YM", "HOKEN_ID")
);

CREATE TABLE "RECE_SEIKYU" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SEIKYU_YM" int4 NOT NULL DEFAULT 999999,
	"SEIKYU_KBN" int4 NOT NULL DEFAULT 0,
	"PRE_HOKEN_ID" int4 NOT NULL DEFAULT 0,
	"CMT" varchar(60) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_RECE_SEIKYU" PRIMARY KEY ("HP_ID", "SIN_YM", "SEQ_NO")
);

CREATE TABLE "RECE_STATUS" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEIKYU_YM" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"FUSEN_KBN" int4 NOT NULL DEFAULT 0,
	"IS_PAPER_RECE" int4 NOT NULL DEFAULT 0,
	"OUTPUT" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"STATUS_KBN" int4 NOT NULL DEFAULT 0,
	"IS_PRECHECKED" int4 NOT NULL DEFAULT 0,
    CONSTRAINT "PK_RECE_STATUS" PRIMARY KEY ("HP_ID", "PT_ID", "SEIKYU_YM", "HOKEN_ID", "SIN_YM")
);

CREATE TABLE "RECEDEN_CMT_SELECT" (
    "HP_ID" integer NOT NULL,
    "ITEM_NO" integer NOT NULL,
    "EDA_NO" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "COMMENT_CD" character varying(10) NOT NULL,
    "END_DATE" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    "KBN_NO" character varying(64) NULL,
    "PT_STATUS" integer NOT NULL,
    "COND_KBN" integer NOT NULL,
    "NOT_SANTEI_KBN" integer NOT NULL,
    "NYUGAI_KBN" integer NOT NULL,
    "SANTEI_CNT" integer NOT NULL,
    CONSTRAINT "PK_RECEDEN_CMT_SELECT" PRIMARY KEY ("HP_ID", "ITEM_NO", "EDA_NO", "ITEM_CD", "START_DATE", "COMMENT_CD")
);

CREATE TABLE "RECEDEN_HEN_JIYUU" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "SIN_YM" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "HENREI_JIYUU_CD" character varying(9) NULL,
    "HENREI_JIYUU" text NULL,
    "HOSOKU" text NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RECEDEN_HEN_JIYUU" PRIMARY KEY ("HP_ID", "PT_ID", "HOKEN_ID", "SIN_YM", "SEQ_NO")
);

CREATE TABLE "RECEDEN_RIREKI_INF" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "SIN_YM" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "SEARCH_NO" character varying(30) NULL,
    "RIREKI" text NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RECEDEN_RIREKI_INF" PRIMARY KEY ("HP_ID", "PT_ID", "HOKEN_ID", "SIN_YM", "SEQ_NO")
);

CREATE TABLE "RELEASENOTE_READ" (
    "HP_ID" integer NOT NULL,
    "USER_ID" integer NOT NULL,
    "VERSION" character varying(10) NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RELEASENOTE_READ" PRIMARY KEY ("HP_ID", "USER_ID", "VERSION")
);

CREATE TABLE "RENKEI_CONF" (
    "HP_ID" integer NOT NULL,
    "RENKEI_ID" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "PARAM" character varying(1000) NULL,
    "PT_NUM_LENGTH" integer NOT NULL,
    "TEMPLATE_ID" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "BIKO" character varying(300) NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RENKEI_CONF" PRIMARY KEY ("HP_ID", "ID")
);

CREATE TABLE "RENKEI_MST" (
    "HP_ID" integer NOT NULL,
    "RENKEI_ID" integer NOT NULL,
    "RENKEI_NAME" character varying(255) NULL,
    "RENKEI_SBT" integer NOT NULL,
    "FUNCTION_TYPE" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_RENKEI_MST" PRIMARY KEY ("HP_ID", "RENKEI_ID")
);

CREATE TABLE "RENKEI_PATH_CONF" (
    "HP_ID" integer NOT NULL,
    "RENKEI_ID" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "EDA_NO" integer NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "PATH" character varying(300) NULL,
    "MACHINE" character varying(60) NULL,
    "CHAR_CD" integer NOT NULL,
    "WORK_PATH" character varying(300) NULL,
    "INTERVAL" integer NOT NULL,
    "PARAM" character varying(1000) NULL,
    "USER" character varying(100) NULL,
    "PASSWORD" character varying(100) NULL,
    "IS_INVALID" integer NOT NULL,
    "BIKO" character varying(300) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RENKEI_PATH_CONF" PRIMARY KEY ("HP_ID", "EDA_NO", "ID")
);

CREATE TABLE "RENKEI_REQ" (
    "REQ_ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "REQ_SBT" integer NOT NULL,
    "REQ_TYPE" character varying(2) NULL,
    "STATUS" integer NOT NULL,
    "ERR_MST" character varying(120) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RENKEI_REQ" PRIMARY KEY ("REQ_ID")
);

CREATE TABLE "RENKEI_TEMPLATE_MST" (
    "HP_ID" integer NOT NULL,
    "TEMPLATE_ID" integer NOT NULL,
    "TEMPLATE_NAME" character varying(255) NULL,
    "PARAM" character varying(1000) NULL,
    "FILE" character varying(300) NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_RENKEI_TEMPLATE_MST" PRIMARY KEY ("HP_ID", "TEMPLATE_ID")
);

CREATE TABLE "RENKEI_TIMING_CONF" (
    "HP_ID" integer NOT NULL,
    "RENKEI_ID" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "EVENT_CD" character varying(11) NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RENKEI_TIMING_CONF" PRIMARY KEY ("HP_ID", "EVENT_CD", "ID")
);

CREATE TABLE "RENKEI_TIMING_MST" (
    "HP_ID" integer NOT NULL,
    "RENKEI_ID" integer NOT NULL,
    "EVENT_CD" character varying(11) NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_RENKEI_TIMING_MST" PRIMARY KEY ("HP_ID", "RENKEI_ID", "EVENT_CD")
);

CREATE TABLE "ROUDOU_MST" (
    "ROUDOU_CD" character varying(2) NOT NULL,
    "ROUDOU_NAME" character varying(60) NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_ROUDOU_MST" PRIMARY KEY ("ROUDOU_CD")
);

CREATE TABLE "ROUSAI_GOSEI_MST" (
    "HP_ID" integer NOT NULL,
    "GOSEI_GRP" integer NOT NULL,
    "GOSEI_ITEM_CD" character varying(10) NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "SISI_KBN" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_ROUSAI_GOSEI_MST" PRIMARY KEY ("HP_ID", "GOSEI_GRP", "GOSEI_ITEM_CD", "ITEM_CD", "SISI_KBN", "START_DATE")
);

CREATE TABLE "RSV_DAY_COMMENT" (
    "HP_ID" int4 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SEQ_NO" int4 NOT NULL,
	"COMMENT" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_RSV_DAY_COMMENT" PRIMARY KEY ("HP_ID", "SIN_DATE", "SEQ_NO")
);

CREATE TABLE "RSV_FRAME_DAY_PTN" (
    "HP_ID" integer NOT NULL,
    "RSV_FRAME_ID" integer NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "START_TIME" integer NOT NULL,
    "END_TIME" integer NOT NULL,
    "MINUTES" integer NOT NULL,
    "NUMBER" integer NOT NULL,
    "UKETUKE_SBT" integer NOT NULL,
    "IS_HOLIDAY" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSV_FRAME_DAY_PTN" PRIMARY KEY ("HP_ID", "RSV_FRAME_ID", "SIN_DATE", "SEQ_NO")
);

CREATE TABLE "RSV_FRAME_INF" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "RSV_FRAME_ID" integer NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "START_TIME" integer NOT NULL,
    "END_TIME" integer NOT NULL,
    "FRAME_NO" integer NOT NULL,
    "IS_HOLIDAY" integer NOT NULL,
    "NUMBER" bigint NOT NULL,
    "FRAME_SBT" integer NOT NULL,
    "UKETUKE_SBT" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSV_FRAME_INF" PRIMARY KEY ("HP_ID", "RSV_FRAME_ID", "SIN_DATE", "START_TIME", "ID")
);

CREATE TABLE "RSV_FRAME_MST" (
    "HP_ID" integer NOT NULL,
    "RSV_FRAME_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "RSV_GRP_ID" integer NOT NULL,
    "SORT_KEY" integer NOT NULL,
    "RSV_FRAME_NAME" character varying(60) NULL,
    "TANTO_ID" integer NOT NULL,
    "KA_ID" integer NOT NULL,
    "MAKE_RAIIN" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSV_FRAME_MST" PRIMARY KEY ("HP_ID", "RSV_FRAME_ID")
);

CREATE TABLE "RSV_FRAME_WEEK_PTN" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "RSV_FRAME_ID" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "WEEK" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "END_DATE" integer NOT NULL,
    "START_TIME" integer NOT NULL,
    "END_TIME" integer NOT NULL,
    "MINUTES" integer NOT NULL,
    "NUMBER" integer NOT NULL,
    "UKETUKE_SBT" integer NOT NULL,
    "IS_HOLIDAY" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSV_FRAME_WEEK_PTN" PRIMARY KEY ("ID", "HP_ID", "RSV_FRAME_ID", "WEEK", "SEQ_NO")
);

CREATE TABLE "RSV_FRAME_WITH" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "RSV_FRAME_ID" integer NOT NULL,
    "WITH_FRAME_ID" integer NOT NULL,
    "SORT_KEY" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSV_FRAME_WITH" PRIMARY KEY ("ID", "HP_ID", "RSV_FRAME_ID")
);

CREATE TABLE "RSV_GRP_MST" (
    "HP_ID" integer NOT NULL,
    "RSV_GRP_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "SORT_KEY" integer NOT NULL,
    "RSV_GRP_NAME" character varying(60) NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSV_GRP_MST" PRIMARY KEY ("HP_ID", "RSV_GRP_ID")
);

CREATE TABLE "RSV_INF" (
    "HP_ID" int4 NOT NULL,
	"RSV_FRAME_ID" int4 NOT NULL,
	"SIN_DATE" int4 NOT NULL DEFAULT 0,
	"START_TIME" int4 NOT NULL DEFAULT 0,
	"RAIIN_NO" int8 NOT NULL DEFAULT 0,
	"PT_ID" int8 NOT NULL,
	"RSV_SBT" int4 NOT NULL DEFAULT 0,
	"TANTO_ID" int4 NOT NULL DEFAULT 0,
	"KA_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_RSV_INF" PRIMARY KEY ("HP_ID", "RSV_FRAME_ID", "SIN_DATE", "START_TIME", "RAIIN_NO")
);

CREATE TABLE "RSV_RENKEI_INF" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "PT_ID" bigint NOT NULL,
    "OTHER_SEQ_NO" bigint NOT NULL,
    "OTHER_SEQ_NO2" bigint NOT NULL,
    "OTHER_PT_ID" bigint NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSV_RENKEI_INF" PRIMARY KEY ("HP_ID", "RAIIN_NO")
);

CREATE TABLE "RSV_RENKEI_INF_TK" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "SYSTEM_KBN" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "OTHER_SEQ_NO" bigint NOT NULL,
    "OTHER_SEQ_NO2" bigint NOT NULL,
    "OTHER_PT_ID" bigint NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSV_RENKEI_INF_TK" PRIMARY KEY ("HP_ID", "RAIIN_NO", "SYSTEM_KBN")
);

CREATE TABLE "RSVKRT_BYOMEI" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "RSVKRT_NO" bigint NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "BYOMEI_CD" character varying(7) NULL,
    "SYUSYOKU_CD1" character varying(7) NULL,
    "SYUSYOKU_CD2" character varying(7) NULL,
    "SYUSYOKU_CD3" character varying(7) NULL,
    "SYUSYOKU_CD4" character varying(7) NULL,
    "SYUSYOKU_CD5" character varying(7) NULL,
    "SYUSYOKU_CD6" character varying(7) NULL,
    "SYUSYOKU_CD7" character varying(7) NULL,
    "SYUSYOKU_CD8" character varying(7) NULL,
    "SYUSYOKU_CD9" character varying(7) NULL,
    "SYUSYOKU_CD10" character varying(7) NULL,
    "SYUSYOKU_CD11" character varying(7) NULL,
    "SYUSYOKU_CD12" character varying(7) NULL,
    "SYUSYOKU_CD13" character varying(7) NULL,
    "SYUSYOKU_CD14" character varying(7) NULL,
    "SYUSYOKU_CD15" character varying(7) NULL,
    "SYUSYOKU_CD16" character varying(7) NULL,
    "SYUSYOKU_CD17" character varying(7) NULL,
    "SYUSYOKU_CD18" character varying(7) NULL,
    "SYUSYOKU_CD19" character varying(7) NULL,
    "SYUSYOKU_CD20" character varying(7) NULL,
    "SYUSYOKU_CD21" character varying(7) NULL,
    "BYOMEI" character varying(160) NULL,
    "SYUBYO_KBN" integer NOT NULL,
    "SIKKAN_KBN" integer NOT NULL,
    "NANBYO_CD" integer NOT NULL,
    "HOSOKU_CMT" character varying(80) NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IS_NODSP_KARTE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSVKRT_BYOMEI" PRIMARY KEY ("HP_ID", "PT_ID", "RSVKRT_NO", "SEQ_NO", "ID")
);

CREATE TABLE "RSVKRT_KARTE_IMG_INF" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "RSVKRT_NO" bigint NOT NULL,
    "KARTE_KBN" integer NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "POSITION" bigint NOT NULL,
    "FILE_NAME" character varying(100) NULL,
    "MESSAGE" character varying(2000) NULL,
    CONSTRAINT "PK_RSVKRT_KARTE_IMG_INF" PRIMARY KEY ("ID")
);

CREATE TABLE "RSVKRT_KARTE_INF" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "RSVKRT_NO" bigint NOT NULL,
    "KARTE_KBN" integer NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "RSV_DATE" integer NOT NULL,
    "TEXT" text NULL,
    "RICH_TEXT" bytea NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSVKRT_KARTE_INF" PRIMARY KEY ("HP_ID", "PT_ID", "RSVKRT_NO", "KARTE_KBN", "SEQ_NO")
);

CREATE TABLE "RSVKRT_MST" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "RSVKRT_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "RSVKRT_KBN" integer NOT NULL,
    "RSV_DATE" integer NOT NULL,
    "RSV_NAME" character varying(120) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSVKRT_MST" PRIMARY KEY ("HP_ID", "PT_ID", "RSVKRT_NO")
);

CREATE TABLE "RSVKRT_ODR_INF" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "RSVKRT_NO" bigint NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "RSV_DATE" integer NOT NULL,
    "HOKEN_PID" integer NOT NULL,
    "ODR_KOUI_KBN" integer NOT NULL,
    "RP_NAME" character varying(240) NULL,
    "INOUT_KBN" integer NOT NULL,
    "SIKYU_KBN" integer NOT NULL,
    "SYOHO_SBT" integer NOT NULL,
    "SANTEI_KBN" integer NOT NULL,
    "TOSEKI_KBN" integer NOT NULL,
    "DAYS_CNT" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_RSVKRT_ODR_INF" PRIMARY KEY ("HP_ID", "PT_ID", "RSVKRT_NO", "RP_NO", "RP_EDA_NO", "ID")
);

CREATE TABLE "RSVKRT_ODR_INF_CMT" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "RSVKRT_NO" bigint NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "ROW_NO" integer NOT NULL,
    "EDA_NO" integer NOT NULL,
    "RSV_DATE" integer NOT NULL,
    "FONT_COLOR" integer NOT NULL,
    "CMT_CD" character varying(10) NULL,
    "CMT_NAME" character varying(32) NULL,
    "CMT_OPT" character varying(38) NULL,
    CONSTRAINT "PK_RSVKRT_ODR_INF_CMT" PRIMARY KEY ("HP_ID", "PT_ID", "RSVKRT_NO", "RP_EDA_NO", "RP_NO", "ROW_NO", "EDA_NO")
);

CREATE TABLE "RSVKRT_ODR_INF_DETAIL" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "RSVKRT_NO" bigint NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "ROW_NO" integer NOT NULL,
    "RSV_DATE" integer NOT NULL,
    "SIN_KOUI_KBN" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "ITEM_NAME" character varying(240) NULL,
    "SURYO" double precision NOT NULL,
    "UNIT_NAME" character varying(24) NULL,
    "UNIT_SBT" integer NOT NULL,
    "TERM_VAL" double precision NOT NULL,
    "KOHATU_KBN" integer NOT NULL,
    "SYOHO_KBN" integer NOT NULL,
    "SYOHO_LIMIT_KBN" integer NOT NULL,
    "DRUG_KBN" integer NOT NULL,
    "YOHO_KBN" integer NOT NULL,
    "KOKUJI1" character varying(1) NULL,
    "KOKUJI2" character varying(1) NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IPN_CD" character varying(12) NULL,
    "IPN_NAME" character varying(120) NULL,
    "BUNKATU" character varying(10) NULL,
    "CMT_NAME" character varying(240) NULL,
    "CMT_OPT" character varying(38) NULL,
    "FONT_COLOR" character varying(8) NULL,
    "COMMENT_NEWLINE" integer NOT NULL,
    CONSTRAINT "PK_RSVKRT_ODR_INF_DETAIL" PRIMARY KEY ("HP_ID", "PT_ID", "RSVKRT_NO", "RP_NO", "RP_EDA_NO", "ROW_NO")
);

CREATE TABLE "SANTEI_AUTO_ORDER" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "SANTEI_GRP_CD" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "ADD_TYPE" integer NOT NULL,
    "ADD_TARGET" integer NOT NULL,
    "TERM_CNT" integer NOT NULL,
    "TERM_SBT" integer NOT NULL,
    "CNT_TYPE" integer NOT NULL,
    "MAX_CNT" bigint NOT NULL,
    "SP_CONDITION" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SANTEI_AUTO_ORDER" PRIMARY KEY ("ID", "HP_ID", "SANTEI_GRP_CD", "SEQ_NO")
);

CREATE TABLE "SANTEI_AUTO_ORDER_DETAIL" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "SANTEI_GRP_CD" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "SURYO" double precision NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SANTEI_AUTO_ORDER_DETAIL" PRIMARY KEY ("ID", "HP_ID", "SANTEI_GRP_CD", "SEQ_NO", "ITEM_CD")
);

CREATE TABLE "SANTEI_CNT_CHECK" (
    "HP_ID" integer NOT NULL,
    "SANTEI_GRP_CD" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "TERM_CNT" integer NOT NULL,
    "TERM_SBT" integer NOT NULL,
    "CNT_TYPE" integer NOT NULL,
    "MAX_CNT" bigint NOT NULL,
    "UNIT_NAME" character varying(10) NULL,
    "ERR_KBN" integer NOT NULL,
    "TARGET_CD" character varying(10) NULL,
    "SP_CONDITION" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SANTEI_CNT_CHECK" PRIMARY KEY ("HP_ID", "SANTEI_GRP_CD", "SEQ_NO")
);

CREATE TABLE "SANTEI_GRP_DETAIL" (
    "HP_ID" integer NOT NULL,
    "SANTEI_GRP_CD" integer NOT NULL,
    "ITEM_CD" text NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SANTEI_GRP_DETAIL" PRIMARY KEY ("HP_ID", "SANTEI_GRP_CD", "ITEM_CD")
);

CREATE TABLE "SANTEI_GRP_MST" (
    "HP_ID" integer NOT NULL,
    "SANTEI_GRP_CD" integer NOT NULL,
    "SANTEI_GRP_NAME" character varying(100) NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SANTEI_GRP_MST" PRIMARY KEY ("HP_ID", "SANTEI_GRP_CD")
);

CREATE TABLE "SANTEI_INF" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "SEQ_NO" integer NOT NULL,
    "ALERT_DAYS" integer NOT NULL,
    "ALERT_TERM" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SANTEI_INF" PRIMARY KEY ("ID")
);

CREATE TABLE "SANTEI_INF_DETAIL" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"ITEM_CD" varchar(10) NULL,
	"SEQ_NO" int4 NOT NULL,
	"END_DATE" int4 NOT NULL,
	"KISAN_SBT" int4 NOT NULL DEFAULT 0,
	"KISAN_DATE" int4 NOT NULL DEFAULT 0,
	"BYOMEI" varchar(160) NULL,
	"HOSOKU_COMMENT" varchar(80) NULL,
	"COMMENT" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"ID" bigserial NOT NULL,
    CONSTRAINT "PK_SANTEI_INF_DETAIL" PRIMARY KEY ("ID")
);

CREATE TABLE "SCHEMA_CMT_MST" (
    "HP_ID" integer NOT NULL,
    "COMMENT_CD" integer GENERATED BY DEFAULT AS IDENTITY,
    "COMMENT" character varying(60) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SCHEMA_CMT_MST" PRIMARY KEY ("HP_ID", "COMMENT_CD")
);

CREATE TABLE "SEIKATUREKI_INF" (
    "ID" bigserial NOT NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"TEXT" text NULL,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_SEIKATUREKI_INF" PRIMARY KEY ("ID")
);

CREATE TABLE "SENTENCE_LIST" (
    "HP_ID" integer NOT NULL,
    "SENTENCE_CD" integer GENERATED BY DEFAULT AS IDENTITY,
    "SENTENCE" character varying(400) NOT NULL,
    "SET_KBN" integer NOT NULL,
    "KARTE_KBN" integer NOT NULL,
    "LEVEL1" bigint NOT NULL,
    "LEVEL2" bigint NOT NULL,
    "LEVEL3" bigint NOT NULL,
    "SELECT_TYPE" integer NOT NULL,
    "NEW_LINE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SENTENCE_LIST" PRIMARY KEY ("HP_ID", "SENTENCE")
);

CREATE TABLE "SESSION_INF" (
    "HP_ID" integer NOT NULL,
    "MACHINE" text NOT NULL,
    "USER_ID" integer NOT NULL,
    "LOGIN_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_SESSION_INF" PRIMARY KEY ("HP_ID", "MACHINE")
);

CREATE TABLE "SET_BYOMEI" (
    "HP_ID" integer NOT NULL,
    "SET_CD" integer NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "BYOMEI_CD" character varying(7) NULL,
    "SYUSYOKU_CD1" character varying(7) NULL,
    "SYUSYOKU_CD2" character varying(7) NULL,
    "SYUSYOKU_CD3" character varying(7) NULL,
    "SYUSYOKU_CD4" character varying(7) NULL,
    "SYUSYOKU_CD5" character varying(7) NULL,
    "SYUSYOKU_CD6" character varying(7) NULL,
    "SYUSYOKU_CD7" character varying(7) NULL,
    "SYUSYOKU_CD8" character varying(7) NULL,
    "SYUSYOKU_CD9" character varying(7) NULL,
    "SYUSYOKU_CD10" character varying(7) NULL,
    "SYUSYOKU_CD11" character varying(7) NULL,
    "SYUSYOKU_CD12" character varying(7) NULL,
    "SYUSYOKU_CD13" character varying(7) NULL,
    "SYUSYOKU_CD14" character varying(7) NULL,
    "SYUSYOKU_CD15" character varying(7) NULL,
    "SYUSYOKU_CD16" character varying(7) NULL,
    "SYUSYOKU_CD17" character varying(7) NULL,
    "SYUSYOKU_CD18" character varying(7) NULL,
    "SYUSYOKU_CD19" character varying(7) NULL,
    "SYUSYOKU_CD20" character varying(7) NULL,
    "SYUSYOKU_CD21" character varying(7) NULL,
    "BYOMEI" character varying(160) NULL,
    "SYUBYO_KBN" integer NOT NULL,
    "SIKKAN_KBN" integer NOT NULL,
    "NANBYO_CD" integer NOT NULL,
    "HOSOKU_CMT" character varying(80) NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IS_NODSP_KARTE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SET_BYOMEI" PRIMARY KEY ("ID", "HP_ID", "SET_CD", "SEQ_NO")
);

CREATE TABLE "SET_GENERATION_MST" (
    "HP_ID" integer NOT NULL,
    "GENERATION_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "START_DATE" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SET_GENERATION_MST" PRIMARY KEY ("HP_ID", "GENERATION_ID")
);

CREATE TABLE "SET_KARTE_IMG_INF" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "SET_CD" integer NOT NULL,
    "KARTE_KBN" integer NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "POSITION" bigint NOT NULL,
    "FILE_NAME" character varying(100) NULL,
    CONSTRAINT "PK_SET_KARTE_IMG_INF" PRIMARY KEY ("ID")
);

CREATE TABLE "SET_KARTE_INF" (
    "HP_ID" integer NOT NULL,
    "SET_CD" integer NOT NULL,
    "KARTE_KBN" integer NOT NULL,
    "SEQ_NO" bigint NOT NULL,
    "TEXT" text NULL,
    "RICH_TEXT" bytea NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SET_KARTE_INF" PRIMARY KEY ("HP_ID", "SET_CD", "KARTE_KBN", "SEQ_NO")
);

CREATE TABLE "SET_KBN_MST" (
    "HP_ID" integer NOT NULL,
    "SET_KBN" integer NOT NULL,
    "SET_KBN_EDA_NO" integer NOT NULL,
    "GENERATION_ID" integer NOT NULL,
    "SET_KBN_NAME" character varying(60) NULL,
    "KA_CD" integer NOT NULL,
    "DOC_CD" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SET_KBN_MST" PRIMARY KEY ("HP_ID", "SET_KBN", "SET_KBN_EDA_NO", "GENERATION_ID")
);

CREATE TABLE "SET_MST" (
    "HP_ID" integer NOT NULL,
    "SET_CD" integer GENERATED BY DEFAULT AS IDENTITY,
    "SET_KBN" integer NOT NULL,
    "SET_KBN_EDA_NO" integer NOT NULL,
    "GENERATION_ID" integer NOT NULL,
    "LEVEL1" integer NOT NULL,
    "LEVEL2" integer NOT NULL,
    "LEVEL3" integer NOT NULL,
    "SET_NAME" character varying(60) NULL,
    "WEIGHT_KBN" integer NOT NULL,
    "COLOR" integer NOT NULL,
    "IS_GROUP" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SET_MST" PRIMARY KEY ("HP_ID", "SET_CD")
);

CREATE TABLE "SET_ODR_INF" (
    "HP_ID" integer NOT NULL,
    "SET_CD" integer NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "ODR_KOUI_KBN" integer NOT NULL,
    "RP_NAME" character varying(240) NULL,
    "INOUT_KBN" integer NOT NULL,
    "SIKYU_KBN" integer NOT NULL,
    "SYOHO_SBT" integer NOT NULL,
    "SANTEI_KBN" integer NOT NULL,
    "TOSEKI_KBN" integer NOT NULL,
    "DAYS_CNT" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SET_ODR_INF" PRIMARY KEY ("HP_ID", "SET_CD", "RP_NO", "RP_EDA_NO", "ID")
);

CREATE TABLE "SET_ODR_INF_CMT" (
    "HP_ID" integer NOT NULL,
    "SET_CD" integer NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "ROW_NO" integer NOT NULL,
    "EDA_NO" integer NOT NULL,
    "FONT_COLOR" integer NOT NULL,
    "CMT_CD" character varying(10) NULL,
    "CMT_NAME" character varying(32) NULL,
    "CMT_OPT" character varying(38) NULL,
    CONSTRAINT "PK_SET_ODR_INF_CMT" PRIMARY KEY ("HP_ID", "SET_CD", "RP_NO", "RP_EDA_NO", "ROW_NO", "EDA_NO")
);

CREATE TABLE "SET_ODR_INF_DETAIL" (
    "HP_ID" integer NOT NULL,
    "SET_CD" integer NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "ROW_NO" integer NOT NULL,
    "SIN_KOUI_KBN" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "ITEM_NAME" character varying(240) NULL,
    "SURYO" double precision NOT NULL,
    "UNIT_NAME" character varying(24) NULL,
    "UNIT_SBT" integer NOT NULL,
    "ODR_TERM_VAL" double precision NOT NULL,
    "KOHATU_KBN" integer NOT NULL,
    "SYOHO_KBN" integer NOT NULL,
    "SYOHO_LIMIT_KBN" integer NOT NULL,
    "DRUG_KBN" integer NOT NULL,
    "YOHO_KBN" integer NOT NULL,
    "KOKUJI1" character varying(1) NULL,
    "KOKUJI2" character varying(1) NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IPN_CD" character varying(12) NULL,
    "IPN_NAME" character varying(120) NULL,
    "BUNKATU" character varying(10) NULL,
    "CMT_NAME" character varying(240) NULL,
    "CMT_OPT" character varying(38) NULL,
    "FONT_COLOR" character varying(8) NULL,
    "COMMENT_NEWLINE" integer NOT NULL,
    CONSTRAINT "PK_SET_ODR_INF_DETAIL" PRIMARY KEY ("HP_ID", "SET_CD", "RP_NO", "RP_EDA_NO", "ROW_NO")
);

CREATE TABLE "SIN_KOUI" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_YM" integer NOT NULL,
    "RP_NO" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "HOKEN_PID" integer NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "SYUKEI_SAKI" character varying(4) NULL,
    "HOKATU_KENSA" integer NOT NULL,
    "TOTAL_TEN" double precision NOT NULL,
    "TEN" double precision NOT NULL,
    "ZEI" double precision NOT NULL,
    "COUNT" integer NOT NULL,
    "TEN_COUNT" character varying(20) NULL,
    "TEN_COL_COUNT" integer NOT NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IS_NODSP_PAPER_RECE" integer NOT NULL,
    "INOUT_KBN" integer NOT NULL,
    "ENTEN_KBN" integer NOT NULL,
    "CD_KBN" character varying(2) NULL,
    "REC_ID" character varying(2) NULL,
    "JIHI_SBT" integer NOT NULL,
    "KAZEI_KBN" integer NOT NULL,
    "DETAIL_DATA" text NULL,
    "DAY1" integer NOT NULL,
    "DAY2" integer NOT NULL,
    "DAY3" integer NOT NULL,
    "DAY4" integer NOT NULL,
    "DAY5" integer NOT NULL,
    "DAY6" integer NOT NULL,
    "DAY7" integer NOT NULL,
    "DAY8" integer NOT NULL,
    "DAY9" integer NOT NULL,
    "DAY10" integer NOT NULL,
    "DAY11" integer NOT NULL,
    "DAY12" integer NOT NULL,
    "DAY13" integer NOT NULL,
    "DAY14" integer NOT NULL,
    "DAY15" integer NOT NULL,
    "DAY16" integer NOT NULL,
    "DAY17" integer NOT NULL,
    "DAY18" integer NOT NULL,
    "DAY19" integer NOT NULL,
    "DAY20" integer NOT NULL,
    "DAY21" integer NOT NULL,
    "DAY22" integer NOT NULL,
    "DAY23" integer NOT NULL,
    "DAY24" integer NOT NULL,
    "DAY25" integer NOT NULL,
    "DAY26" integer NOT NULL,
    "DAY27" integer NOT NULL,
    "DAY28" integer NOT NULL,
    "DAY29" integer NOT NULL,
    "DAY30" integer NOT NULL,
    "DAY31" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SIN_KOUI" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "RP_NO", "SEQ_NO")
);

CREATE TABLE "SIN_KOUI_COUNT" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_YM" integer NOT NULL,
    "SIN_DAY" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "RP_NO" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "COUNT" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SIN_KOUI_COUNT" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "SIN_DAY", "RAIIN_NO", "RP_NO", "SEQ_NO")
);

CREATE TABLE "SIN_KOUI_DETAIL" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_YM" integer NOT NULL,
    "RP_NO" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "ROW_NO" integer NOT NULL,
    "REC_ID" character varying(2) NULL,
    "ITEM_SBT" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "ODR_ITEM_CD" character varying(10) NULL,
    "ITEM_NAME" character varying(1000) NULL,
    "SURYO" double precision NOT NULL,
    "SURYO2" double precision NOT NULL,
    "FMT_KBN" integer NOT NULL,
    "UNIT_CD" integer NOT NULL,
    "UNIT_NAME" character varying(20) NULL,
    "TEN" double precision NOT NULL,
    "ZEI" double precision NOT NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IS_NODSP_PAPER_RECE" integer NOT NULL,
    "IS_NODSP_RYOSYU" integer NOT NULL,
    "CMT_OPT" character varying(240) NULL,
    "CMT1" character varying(1000) NULL,
    "CMT_CD1" character varying(10) NULL,
    "CMT_OPT1" character varying(240) NULL,
    "CMT2" character varying(1000) NULL,
    "CMT_CD2" character varying(10) NULL,
    "CMT_OPT2" character varying(240) NULL,
    "CMT3" character varying(1000) NULL,
    "CMT_CD3" character varying(10) NULL,
    "CMT_OPT3" character varying(240) NULL,
    "IS_DELETED" integer NOT NULL,
    CONSTRAINT "PK_SIN_KOUI_DETAIL" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "RP_NO", "SEQ_NO", "ROW_NO")
);

CREATE TABLE "SIN_RP_INF" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_YM" integer NOT NULL,
    "RP_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "FIRST_DAY" integer NOT NULL,
    "HOKEN_KBN" integer NOT NULL,
    "SIN_KOUI_KBN" integer NOT NULL,
    "SIN_ID" integer NOT NULL,
    "CD_NO" character varying(15) NULL,
    "SANTEI_KBN" integer NOT NULL,
    "KOUI_DATA" text NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SIN_RP_INF" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "RP_NO")
);

CREATE TABLE "SIN_RP_NO_INF" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_YM" integer NOT NULL,
    "SIN_DAY" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "RP_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SIN_RP_NO_INF" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "SIN_DAY", "RAIIN_NO", "RP_NO")
);

CREATE TABLE "SINGLE_DOSE_MST" (
    "HP_ID" integer NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "UNIT_NAME" character varying(40) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SINGLE_DOSE_MST" PRIMARY KEY ("HP_ID", "ID")
);

CREATE TABLE "SINREKI_FILTER_MST" (
    "HP_ID" integer NOT NULL,
    "GRP_CD" integer NOT NULL,
    "NAME" character varying(100) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SINREKI_FILTER_MST" PRIMARY KEY ("HP_ID", "GRP_CD")
);

CREATE TABLE "SINREKI_FILTER_MST_DETAIL" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "GRP_CD" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SINREKI_FILTER_MST_DETAIL" PRIMARY KEY ("HP_ID", "GRP_CD", "ID")
);

CREATE TABLE "SOKATU_MST" (
    "HP_ID" integer NOT NULL,
    "PREF_NO" integer NOT NULL,
    "START_YM" integer NOT NULL,
    "REPORT_ID" integer NOT NULL,
    "REPORT_EDA_NO" integer NOT NULL,
    "END_YM" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "REPORT_NAME" character varying(30) NULL,
    "PRINT_TYPE" integer NOT NULL,
    "PRINT_NO_TYPE" integer NOT NULL,
    "DATA_ALL" integer NOT NULL,
    "DATA_DISK" integer NOT NULL,
    "DATA_PAPER" integer NOT NULL,
    "DATA_KBN" integer NOT NULL,
    "DISK_KIND" character varying(10) NULL,
    "DISK_CNT" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    "IS_SORT" integer NOT NULL,
    CONSTRAINT "PK_SOKATU_MST" PRIMARY KEY ("HP_ID", "PREF_NO", "START_YM", "REPORT_EDA_NO", "REPORT_ID")
);

CREATE TABLE "STA_CONF" (
    "HP_ID" integer NOT NULL,
    "MENU_ID" integer NOT NULL,
    "CONF_ID" integer NOT NULL,
    "VAL" character varying(1200) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_STA_CONF" PRIMARY KEY ("HP_ID", "MENU_ID", "CONF_ID")
);

CREATE TABLE "STA_CSV" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "REPORT_ID" integer NOT NULL,
    "ROW_NO" integer NOT NULL,
    "CONF_NAME" character varying(100) NULL,
    "DATA_SBT" integer NOT NULL,
    "COLUMNS" character varying(1000) NULL,
    "SORT_KBN" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_STA_CSV" PRIMARY KEY ("ID")
);

CREATE TABLE "STA_GRP" (
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "REPORT_ID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_STA_GRP" PRIMARY KEY ("HP_ID", "GRP_ID", "REPORT_ID")
);

CREATE TABLE "STA_MENU" (
    "MENU_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "GRP_ID" integer NOT NULL,
    "REPORT_ID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "MENU_NAME" character varying(130) NULL,
    "IS_PRINT" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_STA_MENU" PRIMARY KEY ("MENU_ID")
);

CREATE TABLE "STA_MST" (
    "HP_ID" integer NOT NULL,
    "REPORT_ID" integer NOT NULL,
    "REPORT_NAME" character varying(20) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_STA_MST" PRIMARY KEY ("HP_ID", "REPORT_ID")
);

CREATE TABLE "SUMMARY_INF" (
    "ID" bigserial NOT NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"TEXT" text NULL,
	"RTEXT" bytea NULL,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_SUMMARY_INF" PRIMARY KEY ("ID")
);

CREATE TABLE "SYOBYO_KEIKA" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"SIN_DAY" int4 NOT NULL DEFAULT 0,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"KEIKA" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_SYOBYO_KEIKA" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "SIN_DAY", "HOKEN_ID", "SEQ_NO")
);

CREATE TABLE "SYOUKI_INF" (
    "HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"SYOUKI_KBN" int4 NOT NULL,
	"SYOUKI" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_SYOUKI_INF" PRIMARY KEY ("HP_ID", "PT_ID", "SIN_YM", "HOKEN_ID", "SEQ_NO")
);

CREATE TABLE "SYOUKI_KBN_MST" (
    "SYOUKI_KBN" integer NOT NULL,
    "START_YM" integer NOT NULL,
    "END_YM" integer NOT NULL,
    "NAME" character varying(200) NULL,
    CONSTRAINT "PK_SYOUKI_KBN_MST" PRIMARY KEY ("SYOUKI_KBN", "START_YM")
);

CREATE TABLE "SYSTEM_CHANGE_LOG" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "FILE_NAME" text NULL,
    "VERSION" text NULL,
    "IS_PG" integer NOT NULL,
    "IS_DB" integer NOT NULL,
    "IS_MASTER" integer NOT NULL,
    "IS_RUN" integer NOT NULL,
    "IS_NOTE" integer NOT NULL,
    "IS_DRUG_PHOTO" integer NOT NULL,
    "STATUS" integer NOT NULL,
    "ERR_MESSAGE" text NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    CONSTRAINT "PK_SYSTEM_CHANGE_LOG" PRIMARY KEY ("ID")
);

CREATE TABLE "SYSTEM_CONF" (
    "HP_ID" integer NOT NULL,
    "GRP_CD" integer NOT NULL,
    "GRP_EDA_NO" integer NOT NULL,
    "VAL" double precision NOT NULL,
    "PARAM" character varying(300) NULL,
    "BIKO" character varying(200) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SYSTEM_CONF" PRIMARY KEY ("HP_ID", "GRP_CD", "GRP_EDA_NO")
);

CREATE TABLE "SYSTEM_CONF_ITEM" (
    "HP_ID" integer NOT NULL,
    "MENU_ID" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "ITEM_NAME" character varying(100) NULL,
    "VAL" integer NOT NULL,
    "PARAM_MIN" integer NOT NULL,
    "PARAM_MAX" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SYSTEM_CONF_ITEM" PRIMARY KEY ("HP_ID", "MENU_ID", "SEQ_NO")
);

CREATE TABLE "SYSTEM_CONF_MENU" (
    "HP_ID" integer NOT NULL,
    "MENU_ID" integer NOT NULL,
    "MENU_GRP" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "MENU_NAME" character varying(100) NULL,
    "GRP_CD" integer NOT NULL,
    "GRP_EDA_NO" integer NOT NULL,
    "PATH_GRP_CD" integer NOT NULL,
    "IS_PARAM" integer NOT NULL,
    "PARAM_MASK" integer NOT NULL,
    "PARAM_TYPE" integer NOT NULL,
    "PARAM_HINT" character varying(100) NULL,
    "VAL_MIN" double precision NOT NULL,
    "VAL_MAX" double precision NOT NULL,
    "PARAM_MIN" double precision NOT NULL,
    "PARAM_MAX" double precision NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "PREF_NO" integer NOT NULL,
    "IS_VISIBLE" integer NOT NULL,
    "MANAGER_KBN" integer NOT NULL,
    "IS_VALUE" integer NOT NULL,
    "PARAM_MAX_LENGTH" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SYSTEM_CONF_MENU" PRIMARY KEY ("HP_ID", "MENU_ID")
);

CREATE TABLE "SYSTEM_GENERATION_CONF" (
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "GRP_CD" integer NOT NULL,
    "GRP_EDA_NO" integer NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "VAL" integer NOT NULL,
    "PARAM" character varying(300) NULL,
    "BIKO" character varying(200) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SYSTEM_GENERATION_CONF" PRIMARY KEY ("HP_ID", "GRP_EDA_NO", "GRP_CD", "ID")
);

CREATE TABLE "SYUNO_NYUKIN" (
    "HP_ID" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SORT_NO" int4 NOT NULL DEFAULT 0,
	"ADJUST_FUTAN" int4 NOT NULL DEFAULT 0,
	"NYUKIN_GAKU" int4 NOT NULL DEFAULT 0,
	"PAYMENT_METHOD_CD" int4 NOT NULL DEFAULT 0,
	"UKETUKE_SBT" int4 NOT NULL DEFAULT 0,
	"NYUKIN_CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"SEQ_NO" bigserial NOT NULL,
	"NYUKIN_DATE" int4 NOT NULL DEFAULT 0,
	"NYUKINJI_TENSU" int4 NOT NULL DEFAULT 0,
	"NYUKINJI_SEIKYU" int4 NOT NULL DEFAULT 0,
	"NYUKINJI_DETAIL" text NULL,
    CONSTRAINT "PK_SYUNO_NYUKIN" PRIMARY KEY ("HP_ID", "RAIIN_NO", "SEQ_NO")
);

CREATE TABLE "SYUNO_SEIKYU" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "NYUKIN_KBN" integer NOT NULL,
    "SEIKYU_TENSU" integer NOT NULL,
    "ADJUST_FUTAN" integer NOT NULL,
    "SEIKYU_GAKU" integer NOT NULL,
    "SEIKYU_DETAIL" text NULL,
    "NEW_SEIKYU_TENSU" integer NOT NULL,
    "NEW_ADJUST_FUTAN" integer NOT NULL,
    "NEW_SEIKYU_GAKU" integer NOT NULL,
    "NEW_SEIKYU_DETAIL" text NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SYUNO_SEIKYU" PRIMARY KEY ("HP_ID", "RAIIN_NO", "PT_ID", "SIN_DATE")
);

CREATE TABLE "TAG_GRP_MST" (
    "HP_ID" integer NOT NULL,
    "TAG_GRP_NO" integer NOT NULL,
    "TAG_GRP_NAME" character varying(20) NULL,
    "GRP_COLOR" character varying(8) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TAG_GRP_MST" PRIMARY KEY ("HP_ID", "TAG_GRP_NO")
);

CREATE TABLE "TEKIOU_BYOMEI_MST" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "BYOMEI_CD" character varying(7) NOT NULL,
    "SYSTEM_DATA" integer NOT NULL,
    "START_YM" integer NOT NULL,
    "END_YM" integer NOT NULL,
    "IS_INVALID" integer NOT NULL,
    "IS_INVALID_TOKUSYO" integer NOT NULL,
    "EDIT_KBN" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TEKIOU_BYOMEI_MST" PRIMARY KEY ("HP_ID", "ITEM_CD", "BYOMEI_CD", "SYSTEM_DATA")
);

CREATE TABLE "TEKIOU_BYOMEI_MST_EXCLUDED" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TEKIOU_BYOMEI_MST_EXCLUDED" PRIMARY KEY ("HP_ID", "ITEM_CD", "SEQ_NO")
);

CREATE TABLE "TEMPLATE_DETAIL" (
    "HP_ID" integer NOT NULL,
    "TEMPLATE_CD" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "CONTROL_ID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "OYA_CONTROL_ID" integer NULL,
    "TITLE" character varying(200) NULL,
    "CONTROL_TYPE" integer NOT NULL,
    "MENU_KBN" integer NOT NULL,
    "DEFAULT_VAL" character varying(200) NULL,
    "UNIT" character varying(20) NULL,
    "NEW_LINE" integer NOT NULL,
    "KARTE_KBN" integer NOT NULL,
    "CONTROL_WIDTH" integer NOT NULL,
    "TITLE_WIDTH" integer NOT NULL,
    "UNIT_WIDTH" integer NOT NULL,
    "LEFT_MARGIN" integer NOT NULL,
    "WORDWRAP" integer NOT NULL,
    "VAL" double precision NULL,
    "FORMULA" character varying(200) NULL,
    "DECIMAL" integer NOT NULL,
    "IME" integer NOT NULL,
    "COL_COUNT" integer NOT NULL,
    "RENKEI_CD" character varying(20) NULL,
    "BACKGROUND_COLOR" character varying(8) NULL,
    "FONT_COLOR" character varying(8) NULL,
    "FONT_BOLD" integer NOT NULL,
    "FONT_ITALIC" integer NOT NULL,
    "FONT_UNDER_LINE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TEMPLATE_DETAIL" PRIMARY KEY ("HP_ID", "TEMPLATE_CD", "SEQ_NO", "CONTROL_ID")
);

CREATE TABLE "TEMPLATE_DSP_CONF" (
    "HP_ID" integer NOT NULL,
    "TEMPLATE_CD" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "DSP_KBN" integer NOT NULL,
    "IS_DSP" integer NOT NULL,
    CONSTRAINT "PK_TEMPLATE_DSP_CONF" PRIMARY KEY ("HP_ID", "TEMPLATE_CD", "SEQ_NO", "DSP_KBN")
);

CREATE TABLE "TEMPLATE_MENU_DETAIL" (
    "HP_ID" integer NOT NULL,
    "MENU_KBN" integer NOT NULL,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "ITEM_NAME" character varying(100) NULL,
    "SORT_NO" integer NOT NULL,
    "VAL" double precision NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TEMPLATE_MENU_DETAIL" PRIMARY KEY ("HP_ID", "MENU_KBN", "SEQ_NO")
);

CREATE TABLE "TEMPLATE_MENU_MST" (
    "HP_ID" integer NOT NULL,
    "MENU_KBN" integer GENERATED BY DEFAULT AS IDENTITY,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "KBN_NAME" character varying(30) NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TEMPLATE_MENU_MST" PRIMARY KEY ("HP_ID", "MENU_KBN", "SEQ_NO")
);

CREATE TABLE "TEMPLATE_MST" (
    "HP_ID" integer NOT NULL,
    "TEMPLATE_CD" integer GENERATED BY DEFAULT AS IDENTITY,
    "SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "SORT_NO" integer NOT NULL,
    "INSERTION_DESTINATION" integer NOT NULL,
    "TITLE" character varying(40) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TEMPLATE_MST" PRIMARY KEY ("HP_ID", "TEMPLATE_CD", "SEQ_NO")
);

CREATE TABLE public."TEN_MST" (
	"HP_ID" int4 NOT NULL,
	"ITEM_CD" varchar(10) NOT NULL,
	"START_DATE" int4 NOT NULL,
	"END_DATE" int4 NOT NULL,
	"MASTER_SBT" varchar(1) NULL,
	"SIN_KOUI_KBN" int4 NOT NULL,
	"NAME" varchar(240) NULL,
	"KANA_NAME1" varchar(120) NULL,
	"KANA_NAME2" varchar(120) NULL,
	"KANA_NAME3" varchar(120) NULL,
	"KANA_NAME4" varchar(120) NULL,
	"KANA_NAME5" varchar(120) NULL,
	"KANA_NAME6" varchar(120) NULL,
	"KANA_NAME7" varchar(120) NULL,
	"RYOSYU_NAME" varchar(240) NULL,
	"RECE_NAME" varchar(240) NULL,
	"TEN_ID" int4 NOT NULL,
	"TEN" float8 NOT NULL DEFAULT 0,
	"RECE_UNIT_CD" varchar(3) NULL,
	"RECE_UNIT_NAME" varchar(24) NULL,
	"ODR_UNIT_NAME" varchar(24) NULL,
	"CNV_UNIT_NAME" varchar(24) NULL,
	"ODR_TERM_VAL" float8 NOT NULL DEFAULT 0,
	"CNV_TERM_VAL" float8 NOT NULL DEFAULT 0,
	"DEFAULT_VAL" float8 NOT NULL DEFAULT 0,
	"IS_ADOPTED" int4 NOT NULL DEFAULT 0,
	"KOUKI_KBN" int4 NOT NULL DEFAULT 0,
	"HOKATU_KENSA" int4 NOT NULL DEFAULT 0,
	"BYOMEI_KBN" int4 NOT NULL DEFAULT 0,
	"IGAKUKANRI" int4 NOT NULL DEFAULT 0,
	"JITUDAY_COUNT" int4 NOT NULL DEFAULT 0,
	"JITUDAY" int4 NOT NULL DEFAULT 0,
	"DAY_COUNT" int4 NOT NULL DEFAULT 0,
	"DRUG_KANREN_KBN" int4 NOT NULL DEFAULT 0,
	"KIZAMI_ID" int4 NOT NULL DEFAULT 0,
	"KIZAMI_MIN" int4 NOT NULL DEFAULT 0,
	"KIZAMI_MAX" int4 NOT NULL DEFAULT 0,
	"KIZAMI_VAL" int4 NOT NULL DEFAULT 0,
	"KIZAMI_TEN" float8 NOT NULL DEFAULT 0,
	"KIZAMI_ERR" int4 NOT NULL DEFAULT 0,
	"MAX_COUNT" int4 NOT NULL DEFAULT 0,
	"MAX_COUNT_ERR" int4 NOT NULL DEFAULT 0,
	"TYU_CD" varchar(4) NULL,
	"TYU_SEQ" varchar(1) NULL,
	"TUSOKU_AGE" int4 NOT NULL DEFAULT 0,
	"MIN_AGE" varchar(2) NULL,
	"MAX_AGE" varchar(2) NULL,
	"TIME_KASAN_KBN" int4 NOT NULL DEFAULT 0,
	"FUTEKI_KBN" int4 NOT NULL DEFAULT 0,
	"FUTEKI_SISETU_KBN" int4 NOT NULL DEFAULT 0,
	"SYOTI_NYUYOJI_KBN" int4 NOT NULL DEFAULT 0,
	"LOW_WEIGHT_KBN" int4 NOT NULL DEFAULT 0,
	"HANDAN_KBN" int4 NOT NULL DEFAULT 0,
	"HANDAN_GRP_KBN" int4 NOT NULL DEFAULT 0,
	"TEIGEN_KBN" int4 NOT NULL DEFAULT 0,
	"SEKITUI_KBN" int4 NOT NULL DEFAULT 0,
	"KEIBU_KBN" int4 NOT NULL DEFAULT 0,
	"AUTO_HOUGOU_KBN" int4 NOT NULL DEFAULT 0,
	"GAIRAI_KANRI_KBN" int4 NOT NULL DEFAULT 0,
	"TUSOKU_TARGET_KBN" int4 NOT NULL DEFAULT 0,
	"HOKATU_KBN" int4 NOT NULL DEFAULT 0,
	"TYOONPA_NAISI_KBN" int4 NOT NULL DEFAULT 0,
	"AUTO_FUNGO_KBN" int4 NOT NULL DEFAULT 0,
	"TYOONPA_GYOKO_KBN" int4 NOT NULL DEFAULT 0,
	"GAZO_KASAN" int4 NOT NULL DEFAULT 0,
	"KANSATU_KBN" int4 NOT NULL DEFAULT 0,
	"MASUI_KBN" int4 NOT NULL DEFAULT 0,
	"FUKUBIKU_NAISI_KASAN" int4 NOT NULL DEFAULT 0,
	"FUKUBIKU_KOTUNAN_KASAN" int4 NOT NULL DEFAULT 0,
	"MASUI_KASAN" int4 NOT NULL DEFAULT 0,
	"MONITER_KASAN" int4 NOT NULL DEFAULT 0,
	"TOKETU_KASAN" int4 NOT NULL DEFAULT 0,
	"TEN_KBN_NO" varchar(30) NULL,
	"SHORTSTAY_OPE" int4 NOT NULL DEFAULT 0,
	"BUI_KBN" int4 NOT NULL DEFAULT 0,
	"SISETUCD1" int4 NOT NULL DEFAULT 0,
	"SISETUCD2" int4 NOT NULL DEFAULT 0,
	"SISETUCD3" int4 NOT NULL DEFAULT 0,
	"SISETUCD4" int4 NOT NULL DEFAULT 0,
	"SISETUCD5" int4 NOT NULL DEFAULT 0,
	"SISETUCD6" int4 NOT NULL DEFAULT 0,
	"SISETUCD7" int4 NOT NULL DEFAULT 0,
	"SISETUCD8" int4 NOT NULL DEFAULT 0,
	"SISETUCD9" int4 NOT NULL DEFAULT 0,
	"SISETUCD10" int4 NOT NULL DEFAULT 0,
	"AGEKASAN_MIN1" varchar(2) NULL,
	"AGEKASAN_MAX1" varchar(2) NULL,
	"AGEKASAN_CD1" varchar(10) NULL,
	"AGEKASAN_MIN2" varchar(2) NULL,
	"AGEKASAN_MAX2" varchar(2) NULL,
	"AGEKASAN_CD2" varchar(10) NULL,
	"AGEKASAN_MIN3" varchar(2) NULL,
	"AGEKASAN_MAX3" varchar(2) NULL,
	"AGEKASAN_CD3" varchar(10) NULL,
	"AGEKASAN_MIN4" varchar(2) NULL,
	"AGEKASAN_MAX4" varchar(2) NULL,
	"AGEKASAN_CD4" varchar(10) NULL,
	"KENSA_CMT" int4 NOT NULL DEFAULT 0,
	"MADOKU_KBN" int4 NOT NULL DEFAULT 0,
	"SINKEI_KBN" int4 NOT NULL DEFAULT 0,
	"SEIBUTU_KBN" int4 NOT NULL DEFAULT 0,
	"ZOUEI_KBN" int4 NOT NULL DEFAULT 0,
	"DRUG_KBN" int4 NOT NULL DEFAULT 0,
	"ZAI_KBN" int4 NOT NULL DEFAULT 0,
	"CAPACITY" int4 NOT NULL DEFAULT 0,
	"KOHATU_KBN" int4 NOT NULL DEFAULT 0,
	"TOKUZAI_AGE_KBN" int4 NOT NULL DEFAULT 0,
	"SANSO_KBN" int4 NOT NULL DEFAULT 0,
	"TOKUZAI_SBT" int4 NOT NULL DEFAULT 0,
	"MAX_PRICE" int4 NOT NULL DEFAULT 0,
	"MAX_TEN" int4 NOT NULL DEFAULT 0,
	"SYUKEI_SAKI" varchar(3) NULL,
	"CD_KBN" varchar(1) NULL,
	"CD_SYO" int4 NOT NULL DEFAULT 0,
	"CD_BU" int4 NOT NULL DEFAULT 0,
	"CD_KBNNO" int4 NOT NULL DEFAULT 0,
	"CD_EDANO" int4 NOT NULL DEFAULT 0,
	"CD_KOUNO" int4 NOT NULL DEFAULT 0,
	"KOKUJI_KBN" varchar(1) NULL,
	"KOKUJI_SYO" int4 NOT NULL DEFAULT 0,
	"KOKUJI_BU" int4 NOT NULL DEFAULT 0,
	"KOKUJI_KBN_NO" int4 NOT NULL DEFAULT 0,
	"KOKUJI_EDA_NO" int4 NOT NULL DEFAULT 0,
	"KOKUJI_KOU_NO" int4 NOT NULL DEFAULT 0,
	"KOKUJI1" varchar(1) NULL DEFAULT '0'::character varying,
	"KOKUJI2" varchar(1) NULL DEFAULT '0'::character varying,
	"KOHYO_JUN" int4 NOT NULL DEFAULT 0,
	"YJ_CD" varchar(12) NULL DEFAULT '0'::character varying,
	"YAKKA_CD" varchar(12) NULL DEFAULT '0'::character varying,
	"SYUSAI_SBT" int4 NOT NULL DEFAULT 0,
	"SYOHIN_KANREN" varchar(9) NULL,
	"UPD_DATE" int4 NOT NULL DEFAULT 0,
	"DEL_DATE" int4 NOT NULL DEFAULT 0,
	"KEIKA_DATE" int4 NOT NULL DEFAULT 0,
	"ROUSAI_KBN" int4 NOT NULL DEFAULT 0,
	"SISI_KBN" int4 NOT NULL DEFAULT 0,
	"SHOT_CNT" int4 NOT NULL DEFAULT 0,
	"IS_NOSEARCH" int4 NOT NULL DEFAULT 0,
	"IS_NODSP_PAPER_RECE" int4 NOT NULL DEFAULT 0,
	"IS_NODSP_RECE" int4 NOT NULL DEFAULT 0,
	"IS_NODSP_RYOSYU" int4 NOT NULL DEFAULT 0,
	"IS_NODSP_KARTE" int4 NOT NULL DEFAULT 0,
	"JIHI_SBT" int4 NOT NULL DEFAULT 0,
	"KAZEI_KBN" int4 NOT NULL DEFAULT 0,
	"YOHO_KBN" int4 NOT NULL DEFAULT 0,
	"IPN_NAME_CD" varchar(12) NULL,
	"FUKUYO_RISE" int4 NOT NULL DEFAULT 0,
	"FUKUYO_MORNING" int4 NOT NULL DEFAULT 0,
	"FUKUYO_DAYTIME" int4 NOT NULL DEFAULT 0,
	"FUKUYO_NIGHT" int4 NOT NULL DEFAULT 0,
	"FUKUYO_SLEEP" int4 NOT NULL DEFAULT 0,
	"SURYO_ROUNDUP_KBN" int4 NOT NULL DEFAULT 0,
	"KOUSEISIN_KBN" int4 NOT NULL DEFAULT 0,
	"SANTEI_ITEM_CD" varchar(10) NULL,
	"SANTEIGAI_KBN" int4 NOT NULL,
	"KENSA_ITEM_CD" varchar(20) NULL,
	"KENSA_ITEM_SEQ_NO" int4 NOT NULL,
	"RENKEI_CD1" varchar(20) NULL,
	"RENKEI_CD2" varchar(20) NULL,
	"SAIKETU_KBN" int4 NOT NULL DEFAULT 0,
	"CMT_KBN" int4 NOT NULL DEFAULT 0,
	"CMT_COL1" int4 NOT NULL DEFAULT 0,
	"CMT_COL_KETA1" int4 NOT NULL DEFAULT 0,
	"CMT_COL2" int4 NOT NULL DEFAULT 0,
	"CMT_COL_KETA2" int4 NOT NULL DEFAULT 0,
	"CMT_COL3" int4 NOT NULL DEFAULT 0,
	"CMT_COL_KETA3" int4 NOT NULL DEFAULT 0,
	"CMT_COL4" int4 NOT NULL DEFAULT 0,
	"CMT_COL_KETA4" int4 NOT NULL DEFAULT 0,
	"SELECT_CMT_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamp NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"CHUSYA_DRUG_SBT" int4 NOT NULL DEFAULT 0,
	"KENSA_FUKUSU_SANTEI" int4 NOT NULL DEFAULT 0,
	"AGE_CHECK" int4 NOT NULL DEFAULT 0,
	"KOKUJI_BETUNO" int4 NOT NULL DEFAULT 0,
	"KOKUJI_KBNNO" int4 NOT NULL DEFAULT 0,
	"CMT_SBT" int4 NOT NULL DEFAULT 0,
	"IS_NODSP_YAKUTAI" int4 NOT NULL DEFAULT 0,
	"ZAIKEI_POINT" float8 NOT NULL DEFAULT 0,
	"KENSA_LABEL" int4 NOT NULL DEFAULT 0,
	"GAIRAI_KANSEN" int4 NOT NULL DEFAULT 0,
	"JIBI_AGE_KASAN" int4 NOT NULL DEFAULT 0,
	"JIBI_SYONIKOKIN" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"YOHO_CD" varchar(16) NOT NULL DEFAULT ''::character varying,
	"YOHO_HOSOKU_KBN" int4 NOT NULL DEFAULT 0,
	"YOHO_HOSOKU_REC" int4 NOT NULL DEFAULT 0,
	CONSTRAINT "PK_public.TEN_MST" PRIMARY KEY ("HP_ID", "ITEM_CD", "START_DATE")
);

CREATE TABLE "TEN_MST_MOTHER" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "MASTER_SBT" character varying(1) NULL,
    "SIN_KOUI_KBN" integer NOT NULL,
    "NAME" character varying(240) NULL,
    "KANA_NAME1" character varying(120) NULL,
    "KANA_NAME2" character varying(120) NULL,
    "KANA_NAME3" character varying(120) NULL,
    "KANA_NAME4" character varying(120) NULL,
    "KANA_NAME5" character varying(120) NULL,
    "KANA_NAME6" character varying(120) NULL,
    "KANA_NAME7" character varying(120) NULL,
    "RYOSYU_NAME" character varying(240) NULL,
    "RECE_NAME" character varying(240) NULL,
    "TEN_ID" integer NOT NULL,
    "TEN" double precision NOT NULL,
    "RECE_UNIT_CD" character varying(3) NULL,
    "RECE_UNIT_NAME" character varying(24) NULL,
    "ODR_UNIT_NAME" character varying(24) NULL,
    "CNV_UNIT_NAME" character varying(24) NULL,
    "ODR_TERM_VAL" double precision NOT NULL,
    "CNV_TERM_VAL" double precision NOT NULL,
    "DEFAULT_VAL" double precision NOT NULL,
    "IS_ADOPTED" integer NOT NULL,
    "KOUKI_KBN" integer NOT NULL,
    "HOKATU_KENSA" integer NOT NULL,
    "BYOMEI_KBN" integer NOT NULL,
    "IGAKUKANRI" integer NOT NULL,
    "JITUDAY_COUNT" integer NOT NULL,
    "JITUDAY" integer NOT NULL,
    "DAY_COUNT" integer NOT NULL,
    "DRUG_KANREN_KBN" integer NOT NULL,
    "KIZAMI_ID" integer NOT NULL,
    "KIZAMI_MIN" integer NOT NULL,
    "KIZAMI_MAX" integer NOT NULL,
    "KIZAMI_VAL" integer NOT NULL,
    "KIZAMI_TEN" double precision NOT NULL,
    "KIZAMI_ERR" integer NOT NULL,
    "MAX_COUNT" integer NOT NULL,
    "MAX_COUNT_ERR" integer NOT NULL,
    "TYU_CD" character varying(4) NULL,
    "TYU_SEQ" character varying(1) NULL,
    "TUSOKU_AGE" integer NOT NULL,
    "MIN_AGE" character varying(2) NULL,
    "MAX_AGE" character varying(2) NULL,
    "AGE_CHECK" integer NOT NULL,
    "TIME_KASAN_KBN" integer NOT NULL,
    "FUTEKI_KBN" integer NOT NULL,
    "FUTEKI_SISETU_KBN" integer NOT NULL,
    "SYOTI_NYUYOJI_KBN" integer NOT NULL,
    "LOW_WEIGHT_KBN" integer NOT NULL,
    "HANDAN_KBN" integer NOT NULL,
    "HANDAN_GRP_KBN" integer NOT NULL,
    "TEIGEN_KBN" integer NOT NULL,
    "SEKITUI_KBN" integer NOT NULL,
    "KEIBU_KBN" integer NOT NULL,
    "AUTO_HOUGOU_KBN" integer NOT NULL,
    "GAIRAI_KANRI_KBN" integer NOT NULL,
    "TUSOKU_TARGET_KBN" integer NOT NULL,
    "HOKATU_KBN" integer NOT NULL,
    "TYOONPA_NAISI_KBN" integer NOT NULL,
    "AUTO_FUNGO_KBN" integer NOT NULL,
    "TYOONPA_GYOKO_KBN" integer NOT NULL,
    "GAZO_KASAN" integer NOT NULL,
    "KANSATU_KBN" integer NOT NULL,
    "MASUI_KBN" integer NOT NULL,
    "FUKUBIKU_NAISI_KASAN" integer NOT NULL,
    "FUKUBIKU_KOTUNAN_KASAN" integer NOT NULL,
    "MASUI_KASAN" integer NOT NULL,
    "MONITER_KASAN" integer NOT NULL,
    "TOKETU_KASAN" integer NOT NULL,
    "TEN_KBN_NO" character varying(30) NULL,
    "SHORTSTAY_OPE" integer NOT NULL,
    "BUI_KBN" integer NOT NULL,
    "SISETUCD1" integer NOT NULL,
    "SISETUCD2" integer NOT NULL,
    "SISETUCD3" integer NOT NULL,
    "SISETUCD4" integer NOT NULL,
    "SISETUCD5" integer NOT NULL,
    "SISETUCD6" integer NOT NULL,
    "SISETUCD7" integer NOT NULL,
    "SISETUCD8" integer NOT NULL,
    "SISETUCD9" integer NOT NULL,
    "SISETUCD10" integer NOT NULL,
    "AGEKASAN_MIN1" character varying(2) NULL,
    "AGEKASAN_MAX1" character varying(2) NULL,
    "AGEKASAN_CD1" character varying(10) NULL,
    "AGEKASAN_MIN2" character varying(2) NULL,
    "AGEKASAN_MAX2" character varying(2) NULL,
    "AGEKASAN_CD2" character varying(10) NULL,
    "AGEKASAN_MIN3" character varying(2) NULL,
    "AGEKASAN_MAX3" character varying(2) NULL,
    "AGEKASAN_CD3" character varying(10) NULL,
    "AGEKASAN_MIN4" character varying(2) NULL,
    "AGEKASAN_MAX4" character varying(2) NULL,
    "AGEKASAN_CD4" character varying(10) NULL,
    "KENSA_CMT" integer NOT NULL,
    "MADOKU_KBN" integer NOT NULL,
    "SINKEI_KBN" integer NOT NULL,
    "SEIBUTU_KBN" integer NOT NULL,
    "ZOUEI_KBN" integer NOT NULL,
    "DRUG_KBN" integer NOT NULL,
    "ZAI_KBN" integer NOT NULL,
    "CAPACITY" integer NOT NULL,
    "KOHATU_KBN" integer NOT NULL,
    "TOKUZAI_AGE_KBN" integer NOT NULL,
    "SANSO_KBN" integer NOT NULL,
    "TOKUZAI_SBT" integer NOT NULL,
    "MAX_PRICE" integer NOT NULL,
    "MAX_TEN" integer NOT NULL,
    "SYUKEI_SAKI" character varying(3) NULL,
    "CD_KBN" character varying(1) NULL,
    "CD_SYO" integer NOT NULL,
    "CD_BU" integer NOT NULL,
    "CD_KBNNO" integer NOT NULL,
    "CD_EDANO" integer NOT NULL,
    "CD_KOUNO" integer NOT NULL,
    "KOKUJI_KBN" character varying(1) NULL,
    "KOKUJI_SYO" integer NOT NULL,
    "KOKUJI_BU" integer NOT NULL,
    "KOKUJI_KBN_NO" integer NOT NULL,
    "KOKUJI_EDA_NO" integer NOT NULL,
    "KOKUJI_KOU_NO" integer NOT NULL,
    "KOKUJI1" character varying(1) NULL,
    "KOKUJI2" character varying(1) NULL,
    "KOHYO_JUN" integer NOT NULL,
    "YJ_CD" character varying(12) NULL,
    "YAKKA_CD" character varying(12) NULL,
    "SYUSAI_SBT" integer NOT NULL,
    "SYOHIN_KANREN" character varying(9) NULL,
    "UPD_DATE" integer NOT NULL,
    "DEL_DATE" integer NOT NULL,
    "KEIKA_DATE" integer NOT NULL,
    "ROUSAI_KBN" integer NOT NULL,
    "SISI_KBN" integer NOT NULL,
    "SHOT_CNT" integer NOT NULL,
    "IS_NOSEARCH" integer NOT NULL,
    "IS_NODSP_PAPER_RECE" integer NOT NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IS_NODSP_RYOSYU" integer NOT NULL,
    "IS_NODSP_KARTE" integer NOT NULL,
    "JIHI_SBT" integer NOT NULL,
    "KAZEI_KBN" integer NOT NULL,
    "YOHO_KBN" integer NOT NULL,
    "IPN_NAME_CD" character varying(12) NULL,
    "FUKUYO_RISE" integer NOT NULL,
    "FUKUYO_MORNING" integer NOT NULL,
    "FUKUYO_DAYTIME" integer NOT NULL,
    "FUKUYO_NIGHT" integer NOT NULL,
    "FUKUYO_SLEEP" integer NOT NULL,
    "SURYO_ROUNDUP_KBN" integer NOT NULL,
    "KOUSEISIN_KBN" integer NOT NULL,
    "CHUSYA_DRUG_SBT" integer NOT NULL,
    "KENSA_FUKUSU_SANTEI" integer NOT NULL,
    "SANTEI_ITEM_CD" character varying(10) NULL,
    "SANTEIGAI_KBN" integer NOT NULL,
    "KENSA_ITEM_CD" character varying(20) NULL,
    "KENSA_ITEM_SEQ_NO" integer NOT NULL,
    "RENKEI_CD1" character varying(20) NULL,
    "RENKEI_CD2" character varying(20) NULL,
    "SAIKETU_KBN" integer NOT NULL,
    "CMT_KBN" integer NOT NULL,
    "CMT_COL1" integer NOT NULL,
    "CMT_COL_KETA1" integer NOT NULL,
    "CMT_COL2" integer NOT NULL,
    "CMT_COL_KETA2" integer NOT NULL,
    "CMT_COL3" integer NOT NULL,
    "CMT_COL_KETA3" integer NOT NULL,
    "CMT_COL4" integer NOT NULL,
    "CMT_COL_KETA4" integer NOT NULL,
    "SELECT_CMT_ID" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TEN_MST_MOTHER" PRIMARY KEY ("HP_ID", "ITEM_CD", "START_DATE")
);

CREATE TABLE "TIME_ZONE_CONF" (
    "HP_ID" integer NOT NULL,
    "YOUBI_KBN" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "START_TIME" integer NOT NULL,
    "END_TIME" integer NOT NULL,
    "TIME_KBN" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TIME_ZONE_CONF" PRIMARY KEY ("HP_ID", "YOUBI_KBN", "SEQ_NO")
);

CREATE TABLE "TIME_ZONE_DAY_INF" (
    "HP_ID" integer NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "SIN_DATE" integer NOT NULL,
    "START_TIME" integer NOT NULL,
    "END_TIME" integer NOT NULL,
    "TIME_KBN" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TIME_ZONE_DAY_INF" PRIMARY KEY ("HP_ID", "ID", "SIN_DATE")
);

CREATE TABLE "TODO_GRP_MST" (
    "HP_ID" integer NOT NULL,
    "TODO_GRP_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "TODO_GRP_NAME" character varying(20) NULL,
    "GRP_COLOR" character varying(8) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TODO_GRP_MST" PRIMARY KEY ("HP_ID", "TODO_GRP_NO")
);

CREATE TABLE "TODO_INF" (
    "HP_ID" int4 NOT NULL,
	"TODO_NO" serial4 NOT NULL,
	"TODO_EDA_NO" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"TODO_KBN_NO" int4 NOT NULL,
	"TODO_GRP_NO" int4 NOT NULL,
	"TANTO" int4 NOT NULL DEFAULT 0,
	"TERM" int4 NOT NULL,
	"CMT1" text NULL,
	"CMT2" text NULL,
	"IS_DONE" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_TODO_INF" PRIMARY KEY ("HP_ID", "TODO_NO", "TODO_EDA_NO", "PT_ID")
);

CREATE TABLE "TODO_KBN_MST" (
    "HP_ID" integer NOT NULL,
    "TODO_KBN_NO" integer NOT NULL,
    "TODO_KBN_NAME" character varying(20) NULL,
    "ACT_CD" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TODO_KBN_MST" PRIMARY KEY ("HP_ID", "TODO_KBN_NO")
);

CREATE TABLE "TOKKI_MST" (
    "HP_ID" integer NOT NULL,
    "TOKKI_CD" character varying(2) NOT NULL,
    "TOKKI_NAME" character varying(20) NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_TOKKI_MST" PRIMARY KEY ("HP_ID", "TOKKI_CD")
);

CREATE TABLE "UKETUKE_SBT_DAY_INF" (
    "HP_ID" int4 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SEQ_NO" int4 NOT NULL,
	"UKETUKE_SBT" int4 NOT NULL,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL,
	"CREATE_MACHINE" varchar(60) NULL,
    CONSTRAINT "PK_UKETUKE_SBT_DAY_INF" PRIMARY KEY ("HP_ID", "SIN_DATE", "SEQ_NO")
);

CREATE TABLE "UKETUKE_SBT_MST" (
    "HP_ID" integer NOT NULL,
    "KBN_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "KBN_NAME" character varying(20) NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_UKETUKE_SBT_MST" PRIMARY KEY ("HP_ID", "KBN_ID")
);

CREATE TABLE "UNIT_MST" (
    "UNIT_CD" integer GENERATED BY DEFAULT AS IDENTITY,
    "UNIT_NAME" character varying(40) NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_UNIT_MST" PRIMARY KEY ("UNIT_CD")
);

CREATE TABLE "USER_CONF" (
    "HP_ID" integer NOT NULL,
    "USER_ID" integer NOT NULL,
    "GRP_CD" integer NOT NULL,
    "GRP_ITEM_CD" integer NOT NULL,
    "GRP_ITEM_EDA_NO" integer NOT NULL,
    "VAL" integer NOT NULL,
    "PARAM" character varying(300) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_USER_CONF" PRIMARY KEY ("HP_ID", "USER_ID", "GRP_CD", "GRP_ITEM_CD", "GRP_ITEM_EDA_NO")
);

CREATE TABLE "USER_MST" (
    "HP_ID" integer NOT NULL,
    "ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "USER_ID" integer NOT NULL,
    "JOB_CD" integer NOT NULL,
    "MANAGER_KBN" integer NOT NULL,
    "KA_ID" integer NOT NULL,
    "KANA_NAME" character varying(40) NULL,
    "NAME" character varying(40) NOT NULL,
    "SNAME" character varying(20) NOT NULL,
    "DR_NAME" character varying(40) NULL,
    "LOGIN_ID" character varying(20) NOT NULL,
    "MAYAKU_LICENSE_NO" character varying(20) NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "RENKEI_CD1" character varying(14) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
	"HASH_PASSWORD" bytea NULL,
	"SALT" bytea NULL,
    CONSTRAINT "PK_USER_MST" PRIMARY KEY ("ID", "HP_ID")
);

CREATE TABLE "USER_PERMISSION" (
    "HP_ID" integer NOT NULL,
    "USER_ID" integer NOT NULL,
    "FUNCTION_CD" character varying(8) NOT NULL,
    "PERMISSION" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_USER_PERMISSION" PRIMARY KEY ("HP_ID", "USER_ID", "FUNCTION_CD")
);

CREATE TABLE "WRK_SIN_KOUI" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "HOKEN_KBN" integer NOT NULL,
    "RP_NO" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "HOKEN_PID" integer NOT NULL,
    "HOKEN_ID" integer NOT NULL,
    "SYUKEI_SAKI" character varying(4) NULL,
    "HOKATU_KENSA" integer NOT NULL,
    "COUNT" integer NOT NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IS_NODSP_PAPER_RECE" integer NOT NULL,
    "INOUT_KBN" integer NOT NULL,
    "CD_KBN" character varying(2) NULL,
    "REC_ID" character varying(2) NULL,
    "JIHI_SBT" integer NOT NULL,
    "KAZEI_KBN" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_WRK_SIN_KOUI" PRIMARY KEY ("HP_ID", "RAIIN_NO", "HOKEN_KBN", "RP_NO", "SEQ_NO")
);

CREATE TABLE "WRK_SIN_KOUI_DETAIL" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "HOKEN_KBN" integer NOT NULL,
    "RP_NO" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "ROW_NO" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "REC_ID" character varying(2) NULL,
    "ITEM_SBT" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "ODR_ITEM_CD" character varying(10) NULL,
    "ITEM_NAME" character varying(1000) NULL,
    "SURYO" double precision NOT NULL,
    "SURYO2" double precision NOT NULL,
    "FMT_KBN" integer NOT NULL,
    "UNIT_CD" integer NOT NULL,
    "UNIT_NAME" character varying(20) NULL,
    "TEN_ID" integer NOT NULL,
    "TEN" double precision NOT NULL,
    "CD_KBN" character varying(1) NULL,
    "CD_KBNNO" integer NOT NULL,
    "CD_EDANO" integer NOT NULL,
    "CD_KOUNO" integer NOT NULL,
    "KOKUJI1" character varying(1) NULL,
    "KOKUJI2" character varying(1) NULL,
    "TYU_CD" character varying(4) NULL,
    "TYU_SEQ" character varying(1) NULL,
    "TUSOKU_AGE" integer NOT NULL,
    "ITEM_SEQ_NO" integer NOT NULL,
    "ITEM_EDA_NO" integer NOT NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IS_NODSP_PAPER_RECE" integer NOT NULL,
    "IS_NODSP_RYOSYU" integer NOT NULL,
    "IS_AUTO_ADD" integer NOT NULL,
    "CMT_OPT" character varying(160) NULL,
    "CMT1" character varying(1000) NULL,
    "CMT_CD1" character varying(10) NULL,
    "CMT_OPT1" character varying(160) NULL,
    "CMT2" character varying(1000) NULL,
    "CMT_CD2" character varying(10) NULL,
    "CMT_OPT2" character varying(160) NULL,
    "CMT3" character varying(1000) NULL,
    "CMT_CD3" character varying(10) NULL,
    "CMT_OPT3" character varying(160) NULL,
    "IS_DELETED" integer NOT NULL,
    CONSTRAINT "PK_WRK_SIN_KOUI_DETAIL" PRIMARY KEY ("HP_ID", "RAIIN_NO", "HOKEN_KBN", "RP_NO", "SEQ_NO", "ROW_NO")
);

CREATE TABLE "WRK_SIN_KOUI_DETAIL_DEL" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "HOKEN_KBN" integer NOT NULL,
    "RP_NO" integer NOT NULL,
    "SEQ_NO" integer NOT NULL,
    "ROW_NO" integer NOT NULL,
    "ITEM_SEQ_NO" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "DEL_ITEM_CD" character varying(10) NULL,
    "SANTEI_DATE" integer NOT NULL,
    "DEL_SBT" integer NOT NULL,
    "IS_WARNING" integer NOT NULL,
    "TERM_CNT" integer NOT NULL,
    "TERM_SBT" integer NOT NULL,
    CONSTRAINT "PK_WRK_SIN_KOUI_DETAIL_DEL" PRIMARY KEY ("HP_ID", "RAIIN_NO", "HOKEN_KBN", "RP_NO", "SEQ_NO", "ROW_NO", "ITEM_SEQ_NO")
);

CREATE TABLE "WRK_SIN_RP_INF" (
    "HP_ID" integer NOT NULL,
    "RAIIN_NO" bigint NOT NULL,
    "HOKEN_KBN" integer NOT NULL,
    "RP_NO" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "SIN_DATE" integer NOT NULL,
    "SIN_KOUI_KBN" integer NOT NULL,
    "SIN_ID" integer NOT NULL,
    "CD_NO" character varying(15) NULL,
    "SANTEI_KBN" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_WRK_SIN_RP_INF" PRIMARY KEY ("HP_ID", "RAIIN_NO", "HOKEN_KBN", "RP_NO")
);

CREATE TABLE "YAKKA_SYUSAI_MST" (
    "HP_ID" integer NOT NULL,
    "YAKKA_CD" character varying(12) NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "START_DATE" integer NOT NULL,
    "END_DATE" integer NOT NULL,
    "SEIBUN" character varying(255) NULL,
    "HINMOKU" character varying(255) NULL,
    "KBN" character varying(2) NULL,
    "SYUSAI_DATE" integer NOT NULL,
    "KEIKA" character varying(255) NULL,
    "BIKO" character varying(255) NULL,
    "JUN_SENPATU" integer NOT NULL,
    "UNIT_NAME" character varying(100) NULL,
    "YAKKA" double precision NOT NULL,
    "IS_NOTARGET" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_YAKKA_SYUSAI_MST" PRIMARY KEY ("HP_ID", "YAKKA_CD", "ITEM_CD", "START_DATE")
);

CREATE TABLE "YOHO_INF_MST" (
    "HP_ID" integer NOT NULL,
    "ITEM_CD" character varying(10) NOT NULL,
    "YOHO_SUFFIX" character varying(240) NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_YOHO_INF_MST" PRIMARY KEY ("HP_ID", "ITEM_CD")
);

CREATE TABLE "YOHO_SET_MST" (
    "SET_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "USER_ID" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_YOHO_SET_MST" PRIMARY KEY ("SET_ID")
);

CREATE TABLE "YOYAKU_ODR_INF" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "YOYAKU_KARTE_NO" bigint NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "YOYAKU_DATE" integer NOT NULL,
    "ODR_KOUI_KBN" integer NOT NULL,
    "RP_NAME" character varying(120) NULL,
    "INOUT_KBN" integer NOT NULL,
    "SIKYU_KBN" integer NOT NULL,
    "SYOHO_SBT" integer NOT NULL,
    "SANTEI_KBN" integer NOT NULL,
    "TOSEKI_KBN" integer NOT NULL,
    "DAYS_CNT" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_YOYAKU_ODR_INF" PRIMARY KEY ("HP_ID", "PT_ID", "YOYAKU_KARTE_NO", "RP_NO", "RP_EDA_NO")
);

CREATE TABLE "YOYAKU_ODR_INF_DETAIL" (
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "YOYAKU_KARTE_NO" bigint NOT NULL,
    "RP_NO" bigint NOT NULL,
    "RP_EDA_NO" bigint NOT NULL,
    "ROW_NO" bigint NOT NULL,
    "YOYAKU_DATE" integer NOT NULL,
    "SIN_KOUI_KBN" integer NOT NULL,
    "ITEM_CD" character varying(10) NULL,
    "ITEM_NAME" character varying(120) NULL,
    "SURYO" double precision NOT NULL,
    "UNIT_NAME" character varying(24) NULL,
    "UNIT_SBT" integer NOT NULL,
    "TERM_VAL" double precision NOT NULL,
    "KOHATU_KBN" integer NOT NULL,
    "SYOHO_KBN" integer NOT NULL,
    "SYOHO_LIMIT_KBN" integer NOT NULL,
    "DRUG_KBN" integer NOT NULL,
    "YOHO_KBN" integer NOT NULL,
    "KOKUJI1" integer NOT NULL,
    "IS_NODSP_RECE" integer NOT NULL,
    "IPN_CD" character varying(12) NULL,
    "IPN_NAME" character varying(120) NULL,
    "BUNKATU" character varying(10) NULL,
    "CMT_NAME" character varying(32) NULL,
    "CMT_OPT" character varying(38) NULL,
    "FONT_COLOR" integer NOT NULL,
    CONSTRAINT "PK_YOYAKU_ODR_INF_DETAIL" PRIMARY KEY ("HP_ID", "PT_ID", "YOYAKU_KARTE_NO", "RP_NO", "RP_EDA_NO", "ROW_NO")
);

CREATE TABLE "YOYAKU_SBT_MST" (
    "HP_ID" integer NOT NULL,
    "YOYAKU_SBT" integer GENERATED BY DEFAULT AS IDENTITY,
    "SBT_NAME" character varying(120) NOT NULL,
    "DEFAULT_CMT" character varying(120) NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_YOYAKU_SBT_MST" PRIMARY KEY ("HP_ID", "YOYAKU_SBT")
);

CREATE TABLE "Z_DOC_INF" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL DEFAULT 0,
	"RAIIN_NO" int8 NOT NULL DEFAULT 0,
	"SEQ_NO" int4 NOT NULL DEFAULT 1,
	"CATEGORY_CD" int4 NOT NULL DEFAULT 0,
	"FILE_NAME" varchar(300) NULL,
	"DSP_FILE_NAME" varchar(300) NULL,
	"IS_LOCKED" int4 NOT NULL DEFAULT 0,
	"LOCK_DATE" timestamptz NULL,
	"LOCK_ID" int4 NOT NULL,
	"LOCK_MACHINE" varchar(60) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT z_doc_inf_pk PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_FILING_INF" (
    "OP_ID" bigint GENERATED BY DEFAULT AS IDENTITY,
    "OP_TYPE" character varying(10) NULL,
    "OP_TIME" timestamp with time zone NOT NULL,
    "OP_ADDR" character varying(100) NULL,
    "OP_HOSTNAME" character varying(100) NULL,
    "HP_ID" integer NOT NULL,
    "PT_ID" bigint NOT NULL,
    "GET_DATE" integer NOT NULL,
    "CATEGORY_CD" integer NOT NULL,
    "FILE_NO" integer NOT NULL,
    "FILE_NAME" character varying(300) NULL,
    "DSP_FILE_NAME" character varying(1024) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    "FILE_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    CONSTRAINT "PK_Z_FILING_INF" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_KENSA_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"IRAI_CD" bigserial NOT NULL,
	"IRAI_DATE" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"INOUT_KBN" int4 NOT NULL DEFAULT 0,
	"STATUS" int4 NOT NULL DEFAULT 0,
	"TOSEKI_KBN" int4 NOT NULL DEFAULT 0,
	"SIKYU_KBN" int4 NOT NULL DEFAULT 0,
	"RESULT_CHECK" int4 NOT NULL DEFAULT 0,
	"CENTER_CD" varchar(10) NULL,
	"NYUBI" varchar(3) NULL,
	"YOKETU" varchar(3) NULL,
	"BILIRUBIN" varchar(3) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_kensa_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_KENSA_INF_DETAIL" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"IRAI_CD" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"IRAI_DATE" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"KENSA_ITEM_CD" varchar(10) NULL,
	"RESULT_VAL" varchar(10) NULL,
	"RESULT_TYPE" varchar(1) NULL,
	"ABNORMAL_KBN" varchar(1) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CMT_CD1" varchar(3) NULL,
	"CMT_CD2" varchar(3) NULL,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_kensa_inf_detail" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_LIMIT_CNT_LIST_INF" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"KOHI_ID" int4 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SEQ_NO" int4 NOT NULL,
	"HOKEN_PID" int4 NOT NULL,
	"SORT_KEY" varchar(61) NULL,
	"OYA_RAIIN_NO" int8 NOT NULL,
	"BIKO" varchar(200) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_limit_cnt_list_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_LIMIT_LIST_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"KOHI_ID" int4 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"HOKEN_PID" int4 NOT NULL,
	"SORT_KEY" varchar(61) NULL,
	"RAIIN_NO" int8 NOT NULL,
	"FUTAN_GAKU" int4 NOT NULL,
	"TOTAL_GAKU" int4 NOT NULL,
	"BIKO" varchar(200) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"ID" bigserial NOT NULL,
	CONSTRAINT "pk_public.z_limit_list_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_MONSHIN_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"TEXT" text NULL,
	"RTEXT" text NULL,
	"GET_KBN" int4 NOT NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_monshin_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_ALRGY_DRUG" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"ITEM_CD" varchar(10) NULL,
	"DRUG_NAME" varchar(100) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_alrgy_drug" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_ALRGY_ELSE" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"ALRGY_NAME" varchar(100) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_alrgy_else" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_ALRGY_FOOD" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"ALRGY_KBN" text NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_alrgy_food" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_CMT_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"TEXT" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"ID" bigserial NOT NULL,
	CONSTRAINT "pk_public.z_pt_cmt_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_FAMILY" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"FAMILY_ID" bigserial NOT NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"ZOKUGARA_CD" varchar(10) NOT NULL,
	"SORT_NO" int4 NOT NULL DEFAULT 1,
	"PARENT_ID" int4 NOT NULL DEFAULT 0,
	"FAMILY_PT_ID" int8 NOT NULL DEFAULT 0,
	"KANA_NAME" varchar(100) NULL,
	"NAME" varchar(100) NULL,
	"SEX" int4 NOT NULL,
	"BIRTHDAY" int4 NOT NULL,
	"IS_DEAD" int4 NOT NULL DEFAULT 0,
	"IS_SEPARATED" int4 NOT NULL DEFAULT 0,
	"BIKO" varchar(120) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_family" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_FAMILY_REKI" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"ID" bigserial NOT NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"FAMILY_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"BYOMEI_CD" varchar(7) NULL,
	"BYOTAI_CD" varchar(7) NULL,
	"BYOMEI" varchar(400) NULL,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_family_reki" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_GRP_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"GRP_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SORT_NO" int4 NOT NULL DEFAULT 1,
	"GRP_CODE" varchar(4) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_grp_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_HOKEN_CHECK" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_GRP" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"CHECK_DATE" timestamptz NOT NULL,
	"CHECK_ID" int4 NOT NULL DEFAULT 0,
	"CHECK_MACHINE" varchar(60) NULL,
	"CHECK_CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_hoken_check" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_HOKEN_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"HOKEN_NO" int4 NOT NULL,
	"HOKEN_EDA_NO" int4 NOT NULL,
	"HOKENSYA_NO" varchar(8) NULL,
	"KIGO" varchar(80) NULL,
	"BANGO" varchar(80) NULL,
	"HONKE_KBN" int4 NOT NULL DEFAULT 0,
	"HOKEN_KBN" int4 NOT NULL DEFAULT 0,
	"HOUBETU" varchar(3) NULL,
	"HOKENSYA_NAME" varchar(100) NULL,
	"HOKENSYA_POST" varchar(7) NULL,
	"HOKENSYA_ADDRESS" varchar(100) NULL,
	"HOKENSYA_TEL" varchar(15) NULL,
	"KEIZOKU_KBN" int4 NOT NULL DEFAULT 0,
	"SIKAKU_DATE" int4 NOT NULL DEFAULT 0,
	"KOFU_DATE" int4 NOT NULL DEFAULT 0,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 0,
	"RATE" int4 NOT NULL DEFAULT 0,
	"GENDOGAKU" int4 NOT NULL DEFAULT 0,
	"KOGAKU_KBN" int4 NOT NULL DEFAULT 0,
	"KOGAKU_TYPE" int4 NOT NULL DEFAULT 0,
	"TOKUREI_YM1" int4 NOT NULL DEFAULT 0,
	"TOKUREI_YM2" int4 NOT NULL DEFAULT 0,
	"TASUKAI_YM" int4 NOT NULL DEFAULT 0,
	"SYOKUMU_KBN" int4 NOT NULL DEFAULT 0,
	"GENMEN_KBN" int4 NOT NULL DEFAULT 0,
	"GENMEN_RATE" int4 NOT NULL DEFAULT 0,
	"GENMEN_GAKU" int4 NOT NULL DEFAULT 0,
	"TOKKI1" varchar(2) NULL,
	"TOKKI2" varchar(2) NULL,
	"TOKKI3" varchar(2) NULL,
	"TOKKI4" varchar(2) NULL,
	"TOKKI5" varchar(2) NULL,
	"ROUSAI_KOFU_NO" varchar(14) NULL,
	"ROUSAI_SAIGAI_KBN" int4 NOT NULL DEFAULT 0,
	"ROUSAI_JIGYOSYO_NAME" varchar(80) NULL,
	"ROUSAI_PREF_NAME" varchar(10) NULL,
	"ROUSAI_CITY_NAME" varchar(20) NULL,
	"ROUSAI_SYOBYO_DATE" int4 NOT NULL DEFAULT 0,
	"ROUSAI_SYOBYO_CD" varchar(2) NULL,
	"ROUSAI_ROUDOU_CD" varchar(2) NULL,
	"ROUSAI_KANTOKU_CD" varchar(2) NULL,
	"ROUSAI_RECE_COUNT" int4 NOT NULL DEFAULT 0,
	"JIBAI_HOKEN_NAME" varchar(100) NULL,
	"JIBAI_HOKEN_TANTO" varchar(40) NULL,
	"JIBAI_HOKEN_TEL" varchar(15) NULL,
	"JIBAI_JYUSYOU_DATE" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"RYOYO_START_DATE" int4 NOT NULL DEFAULT 0,
	"RYOYO_END_DATE" int4 NOT NULL DEFAULT 0,
	"EDA_NO" varchar(2) NULL,
	CONSTRAINT "pk_public.z_pt_hoken_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_HOKEN_PATTERN" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_PID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"HOKEN_KBN" int4 NOT NULL DEFAULT 0,
	"HOKEN_SBT_CD" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"KOHI1_ID" int4 NOT NULL,
	"KOHI2_ID" int4 NOT NULL,
	"KOHI3_ID" int4 NOT NULL,
	"KOHI4_ID" int4 NOT NULL,
	"HOKEN_MEMO" varchar(400) NULL,
	"START_DATE" int4 NOT NULL,
	"END_DATE" int4 NOT NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_hoken_pattern" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_HOKEN_SCAN" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_GRP" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"FILE_NAME" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_hoken_scan" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_INF" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" int8 NOT NULL,
	"PT_NUM" int8 NOT NULL,
	"KANA_NAME" varchar(100) NULL,
	"NAME" varchar(100) NULL,
	"SEX" int4 NOT NULL DEFAULT 0,
	"BIRTHDAY" int4 NOT NULL DEFAULT 0,
	"IS_DEAD" int4 NOT NULL DEFAULT 0,
	"DEATH_DATE" int4 NOT NULL DEFAULT 0,
	"HOME_POST" varchar(7) NULL,
	"HOME_ADDRESS1" varchar(100) NULL,
	"HOME_ADDRESS2" varchar(100) NULL,
	"TEL1" varchar(15) NULL,
	"TEL2" varchar(15) NULL,
	"MAIL" varchar(100) NULL,
	"SETAINUSI" varchar(100) NULL,
	"ZOKUGARA" varchar(20) NULL,
	"JOB" varchar(40) NULL,
	"RENRAKU_NAME" varchar(100) NULL,
	"RENRAKU_POST" varchar(7) NULL,
	"RENRAKU_ADDRESS1" varchar(100) NULL,
	"RENRAKU_ADDRESS2" varchar(100) NULL,
	"RENRAKU_TEL" varchar(15) NULL,
	"RENRAKU_MEMO" varchar(100) NULL,
	"OFFICE_NAME" varchar(100) NULL,
	"OFFICE_POST" varchar(7) NULL,
	"OFFICE_ADDRESS1" varchar(100) NULL,
	"OFFICE_ADDRESS2" varchar(100) NULL,
	"OFFICE_TEL" varchar(15) NULL,
	"OFFICE_MEMO" varchar(100) NULL,
	"IS_RYOSYO_DETAIL" int4 NOT NULL DEFAULT 1,
	"PRIMARY_DOCTOR" int4 NOT NULL,
	"IS_TESTER" int4 NOT NULL DEFAULT 0,
	"IS_DELETE" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"MAIN_HOKEN_PID" int4 NOT NULL DEFAULT 0,
	"REFERENCE_NO" int8 NOT NULL DEFAULT 0,
	"LIMIT_CONS_FLG" int4 NOT NULL DEFAULT 0,
	CONSTRAINT "pk_public.z_pt_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_INFECTION" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"BYOMEI_CD" varchar(7) NULL,
	"BYOTAI_CD" text NULL,
	"BYOMEI" varchar(400) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_infection" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_JIBKAR" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"WEB_ID" varchar(16) NULL,
	"PT_ID" int8 NOT NULL,
	"ODR_KAIJI" int4 NOT NULL DEFAULT 0,
	"ODR_UPDATE_DATE" timestamptz NOT NULL,
	"KARTE_KAIJI" int4 NOT NULL DEFAULT 0,
	"KARTE_UPDATE_DATE" timestamptz NOT NULL,
	"KENSA_KAIJI" int4 NOT NULL DEFAULT 0,
	"KENSA_UPDATE_DATE" timestamptz NOT NULL,
	"BYOMEI_KAIJI" int4 NOT NULL DEFAULT 0,
	"BYOMEI_UPDATE_DATE" timestamptz NOT NULL,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.Z_PT_JIBKAR" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_KIO_REKI" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"BYOMEI_CD" varchar(7) NULL,
	"BYOTAI_CD" text NULL,
	"BYOMEI" varchar(400) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_kio_reki" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_KOHI" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"PREF_NO" int4 NOT NULL,
	"HOKEN_NO" int4 NOT NULL,
	"HOKEN_EDA_NO" int4 NOT NULL,
	"FUTANSYA_NO" varchar(8) NULL,
	"JYUKYUSYA_NO" varchar(7) NULL,
	"TOKUSYU_NO" varchar(20) NULL,
	"SIKAKU_DATE" int4 NOT NULL DEFAULT 0,
	"KOFU_DATE" int4 NOT NULL DEFAULT 0,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 0,
	"RATE" int4 NOT NULL DEFAULT 0,
	"GENDOGAKU" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"HOKEN_SBT_KBN" int4 NOT NULL DEFAULT 0,
	"HOUBETU" varchar(3) NULL,
	CONSTRAINT "pk_public.z_pt_kohi" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_KYUSEI" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"KANA_NAME" varchar(100) NULL,
	"NAME" varchar(100) NOT NULL,
	"END_DATE" int4 NOT NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_kyusei" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_MEMO" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"MEMO" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_memo" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_OTC_DRUG" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"SERIAL_NUM" int4 NOT NULL,
	"TRADE_NAME" varchar(200) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_otc_drug" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_OTHER_DRUG" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"ITEM_CD" varchar(10) NULL,
	"DRUG_NAME" varchar(100) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_other_drug" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_PREGNANCY" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"ID" bigserial NOT NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"PERIOD_DATE" int4 NOT NULL DEFAULT 0,
	"PERIOD_DUE_DATE" int4 NOT NULL DEFAULT 0,
	"OVULATION_DATE" int4 NOT NULL DEFAULT 0,
	"OVULATION_DUE_DATE" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_pregnancy" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_ROUSAI_TENKI" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"END_DATE" int4 NOT NULL DEFAULT 999999,
	"SINKEI" int4 NOT NULL,
	"TENKI" int4 NOT NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_rousai_tenki" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_SANTEI_CONF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"KBN_NO" int4 NOT NULL,
	"EDA_NO" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"KBN_VAL" int4 NOT NULL,
	"SORT_NO" int4 NOT NULL DEFAULT 1,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_santei_conf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_SUPPLE" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"INDEX_CD" text NULL,
	"INDEX_WORD" varchar(200) NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_pt_supple" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_PT_TAG" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"MEMO" text NULL,
	"MEMO_DATA" bytea NULL,
	"START_DATE" int4 NOT NULL DEFAULT 0,
	"END_DATE" int4 NOT NULL DEFAULT 99999999,
	"IS_DSP_UKETUKE" int4 NOT NULL DEFAULT 1,
	"IS_DSP_KARTE" int4 NOT NULL DEFAULT 1,
	"IS_DSP_KAIKEI" int4 NOT NULL DEFAULT 1,
	"IS_DSP_RECE" int4 NOT NULL DEFAULT 1,
	"BACKGROUND_COLOR" varchar(8) NULL,
	"ALPHABLEND_VAL" int4 NOT NULL DEFAULT 200,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"FONTSIZE" int4 NOT NULL DEFAULT 0,
	"WIDTH" int4 NOT NULL DEFAULT 0,
	"HEIGHT" int4 NOT NULL DEFAULT 0,
	"LEFT" int4 NOT NULL DEFAULT 0,
	"TOP" int4 NOT NULL DEFAULT 0,
	"TAG_GRP_CD" int4 NOT NULL DEFAULT 0,
	CONSTRAINT "pk_public.z_pt_tag" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RAIIN_CMT_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"CMT_KBN" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"TEXT" varchar(200) NULL,
	"IS_DELETE" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_raiin_cmt_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RAIIN_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"RAIIN_NO" bigserial NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"OYA_RAIIN_NO" int8 NOT NULL,
	"STATUS" int4 NOT NULL DEFAULT 0,
	"IS_YOYAKU" int4 NOT NULL DEFAULT 0,
	"YOYAKU_TIME" varchar(6) NULL DEFAULT '0'::character varying,
	"YOYAKU_ID" int4 NOT NULL DEFAULT 0,
	"UKETUKE_SBT" int4 NOT NULL DEFAULT 0,
	"UKETUKE_TIME" varchar(6) NULL DEFAULT '0'::character varying,
	"UKETUKE_ID" int4 NOT NULL DEFAULT 0,
	"UKETUKE_NO" int4 NOT NULL DEFAULT 0,
	"SIN_START_TIME" varchar(6) NULL DEFAULT '0'::character varying,
	"SIN_END_TIME" varchar(6) NULL DEFAULT '0'::character varying,
	"KAIKEI_TIME" varchar(6) NULL DEFAULT '0'::character varying,
	"KAIKEI_ID" int4 NOT NULL DEFAULT 0,
	"KA_ID" int4 NOT NULL DEFAULT 0,
	"TANTO_ID" int4 NOT NULL DEFAULT 0,
	"HOKEN_PID" int4 NOT NULL DEFAULT 0,
	"SYOSAISIN_KBN" int4 NOT NULL DEFAULT 0,
	"JIKAN_KBN" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"SANTEI_KBN" int4 NOT NULL DEFAULT 0,
	"CONFIRMATION_RESULT" varchar(120) NULL,
	"CONFIRMATION_STATE" int4 NOT NULL DEFAULT 0,
	CONSTRAINT "pk_public.z_raiin_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RAIIN_KBN_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"GRP_ID" int4 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"KBN_CD" int4 NOT NULL,
	"IS_DELETE" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_raiin_kbn_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RAIIN_LIST_CMT" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"CMT_KBN" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SEQ_NO" int8 NOT NULL,
	"TEXT" varchar(200) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_raiin_list_cmt" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RAIIN_LIST_TAG" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"SEQ_NO" int4 NOT NULL,
	"TAG_NO" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_raiin_list_tag" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RECE_CHECK_CMT" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"SEQ_NO" int4 NOT NULL DEFAULT 1,
	"IS_PENDING" int4 NOT NULL DEFAULT 0,
	"CMT" varchar(300) NULL,
	"IS_CHECKED" int4 NOT NULL DEFAULT 0,
	"SORT_NO" int4 NOT NULL DEFAULT 1,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_rece_check_cmt" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RECE_CMT" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"CMT_KBN" int4 NOT NULL DEFAULT 1,
	"CMT_SBT" int4 NOT NULL DEFAULT 0,
	"ID" int8 NOT NULL DEFAULT 0,
	"SEQ_NO" int4 NOT NULL DEFAULT 1,
	"ITEM_CD" varchar(10) NULL,
	"CMT" text NULL,
	"CMT_DATA" varchar(38) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_rece_cmt" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RECE_INF_EDIT" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"SEIKYU_YM" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"RECE_SBT" varchar(4) NULL,
	"HOUBETU" varchar(3) NULL,
	"KOHI1_HOUBETU" varchar(3) NULL,
	"KOHI2_HOUBETU" varchar(3) NULL,
	"KOHI3_HOUBETU" varchar(3) NULL,
	"KOHI4_HOUBETU" varchar(3) NULL,
	"HOKEN_RECE_TENSU" int4 NULL,
	"HOKEN_RECE_FUTAN" int4 NULL,
	"KOHI1_RECE_TENSU" int4 NULL,
	"KOHI1_RECE_FUTAN" int4 NULL,
	"KOHI1_RECE_KYUFU" int4 NULL,
	"KOHI2_RECE_TENSU" int4 NULL,
	"KOHI2_RECE_FUTAN" int4 NULL,
	"KOHI2_RECE_KYUFU" int4 NULL,
	"KOHI3_RECE_TENSU" int4 NULL,
	"KOHI3_RECE_FUTAN" int4 NULL,
	"KOHI3_RECE_KYUFU" int4 NULL,
	"KOHI4_RECE_TENSU" int4 NULL,
	"KOHI4_RECE_FUTAN" int4 NULL,
	"KOHI4_RECE_KYUFU" int4 NULL,
	"HOKEN_NISSU" int4 NULL,
	"KOHI1_NISSU" int4 NULL,
	"KOHI2_NISSU" int4 NULL,
	"KOHI3_NISSU" int4 NULL,
	"KOHI4_NISSU" int4 NULL,
	"TOKKI" varchar(10) NULL,
	"TOKKI1" varchar(10) NULL,
	"TOKKI2" varchar(10) NULL,
	"TOKKI3" varchar(10) NULL,
	"TOKKI4" varchar(10) NULL,
	"TOKKI5" varchar(10) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_rece_inf_edit" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RECE_SEIKYU" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SEIKYU_YM" int4 NOT NULL DEFAULT 999999,
	"SEIKYU_KBN" int4 NOT NULL DEFAULT 0,
	"PRE_HOKEN_ID" int4 NOT NULL DEFAULT 0,
	"CMT" varchar(60) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_rece_seikyu" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RSV_DAY_COMMENT" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SEQ_NO" int4 NOT NULL,
	"COMMENT" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_rsv_day_comment" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_RSV_INF" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"RSV_FRAME_ID" int4 NOT NULL,
	"SIN_DATE" int4 NOT NULL DEFAULT 0,
	"START_TIME" int4 NOT NULL DEFAULT 0,
	"RAIIN_NO" int8 NOT NULL DEFAULT 0,
	"PT_ID" int8 NOT NULL DEFAULT 0,
	"RSV_SBT" int4 NOT NULL DEFAULT 0,
	"TANTO_ID" int4 NOT NULL DEFAULT 0,
	"KA_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_rsv_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_SANTEI_INF_DETAIL" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"ITEM_CD" varchar(10) NULL,
	"SEQ_NO" int4 NOT NULL,
	"END_DATE" int4 NOT NULL,
	"KISAN_SBT" int4 NOT NULL DEFAULT 0,
	"KISAN_DATE" int4 NOT NULL DEFAULT 0,
	"BYOMEI" varchar(160) NULL,
	"HOSOKU_COMMENT" varchar(80) NULL,
	"COMMENT" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"ID" bigserial NOT NULL,
	CONSTRAINT "pk_public.z_santei_inf_detail" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_SEIKATUREKI_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"ID" bigserial NOT NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"TEXT" text NULL,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_seikatureki_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_SUMMARY_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"ID" bigserial NOT NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SEQ_NO" bigserial NOT NULL,
	"TEXT" text NULL,
	"RTEXT" bytea NULL,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_summary_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_SYOBYO_KEIKA" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"SIN_DAY" int4 NOT NULL DEFAULT 0,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"KEIKA" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_syobyo_keika" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_SYOUKI_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_YM" int4 NOT NULL,
	"HOKEN_ID" int4 NOT NULL,
	"SEQ_NO" serial4 NOT NULL,
	"SORT_NO" int4 NOT NULL,
	"SYOUKI_KBN" int4 NOT NULL,
	"SYOUKI" text NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_syouki_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_SYUNO_NYUKIN" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SORT_NO" int4 NOT NULL DEFAULT 0,
	"ADJUST_FUTAN" int4 NOT NULL DEFAULT 0,
	"NYUKIN_GAKU" int4 NOT NULL DEFAULT 0,
	"PAYMENT_METHOD_CD" int4 NOT NULL DEFAULT 0,
	"UKETUKE_SBT" int4 NOT NULL DEFAULT 0,
	"NYUKIN_CMT" varchar(100) NULL,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	"SEQ_NO" bigserial NOT NULL,
	"NYUKIN_DATE" int4 NOT NULL DEFAULT 0,
	"NYUKINJI_TENSU" int4 NOT NULL DEFAULT 0,
	"NYUKINJI_SEIKYU" int4 NOT NULL DEFAULT 0,
	"NYUKINJI_DETAIL" text NULL,
	CONSTRAINT "pk_public.z_syuno_nyukin" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_TODO_INF" (
	"OP_ID" int8 NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"TODO_NO" serial4 NOT NULL,
	"TODO_EDA_NO" int4 NOT NULL,
	"PT_ID" int8 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"RAIIN_NO" int8 NOT NULL,
	"TODO_KBN_NO" int4 NOT NULL,
	"TODO_GRP_NO" int4 NOT NULL,
	"TANTO" int4 NOT NULL DEFAULT 0,
	"TERM" int4 NOT NULL,
	"CMT1" text NULL,
	"CMT2" text NULL,
	"IS_DONE" int4 NOT NULL DEFAULT 0,
	"IS_DELETED" int4 NOT NULL DEFAULT 0,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL DEFAULT 0,
	"CREATE_MACHINE" varchar(60) NULL,
	"UPDATE_DATE" timestamptz NOT NULL,
	"UPDATE_ID" int4 NOT NULL DEFAULT 0,
	"UPDATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_todo_inf" PRIMARY KEY ("OP_ID")
);

CREATE TABLE "Z_UKETUKE_SBT_DAY_INF" (
	"OP_ID" bigserial NOT NULL,
	"OP_TYPE" varchar(10) NULL,
	"OP_TIME" timestamptz NOT NULL,
	"OP_ADDR" varchar(100) NULL,
	"OP_HOSTNAME" varchar(100) NULL,
	"HP_ID" int4 NOT NULL,
	"SIN_DATE" int4 NOT NULL,
	"SEQ_NO" int4 NOT NULL,
	"UKETUKE_SBT" int4 NOT NULL,
	"CREATE_DATE" timestamptz NOT NULL,
	"CREATE_ID" int4 NOT NULL,
	"CREATE_MACHINE" varchar(60) NULL,
	CONSTRAINT "pk_public.z_uketuke_sbt_day_inf" PRIMARY KEY ("OP_ID")
);

CREATE INDEX "CALC_STATUS_IDX01" ON "CALC_STATUS" ("HP_ID", "PT_ID", "SIN_DATE", "STATUS", "CREATE_MACHINE");

CREATE INDEX "CALC_STATUS_IDX02" ON "CALC_STATUS" ("HP_ID", "STATUS", "CREATE_MACHINE");

CREATE INDEX "CMT_KBN_MST_IDX01" ON "CMT_KBN_MST" ("HP_ID", "ITEM_CD", "START_DATE");

CREATE INDEX "CONVERSION_ITEM_INF_IDX01" ON "CONVERSION_ITEM_INF" ("HP_ID", "SOURCE_ITEM_CD");

CREATE INDEX "DEF_HOKEN_NO_IDX01" ON "DEF_HOKEN_NO" ("HP_ID", "DIGIT_1", "DIGIT_2", "DIGIT_3", "DIGIT_4", "DIGIT_5", "DIGIT_6", "DIGIT_7", "DIGIT_8", "IS_DELETED");

CREATE INDEX "DENSI_HAIHAN_CUSTOM_IDX03" ON "DENSI_HAIHAN_CUSTOM" ("HP_ID", "ITEM_CD1", "HAIHAN_KBN", "START_DATE", "TARGET_KBN", "IS_INVALID");

CREATE INDEX "DENSI_HAIHAN_DAY_IDX03" ON "DENSI_HAIHAN_DAY" ("HP_ID", "ITEM_CD1", "HAIHAN_KBN", "START_DATE", "END_DATE", "TARGET_KBN", "IS_INVALID");

CREATE INDEX "DENSI_HAIHAN_KARTE_IDX03" ON "DENSI_HAIHAN_KARTE" ("HP_ID", "ITEM_CD1", "HAIHAN_KBN", "START_DATE", "END_DATE", "TARGET_KBN", "IS_INVALID");

CREATE INDEX "DENSI_HAIHAN_MONTH_IDX03" ON "DENSI_HAIHAN_MONTH" ("HP_ID", "ITEM_CD1", "HAIHAN_KBN", "START_DATE", "END_DATE", "TARGET_KBN", "IS_INVALID");

CREATE INDEX "DENSI_HAIHAN_WEEK_IDX03" ON "DENSI_HAIHAN_WEEK" ("HP_ID", "ITEM_CD1", "HAIHAN_KBN", "START_DATE", "END_DATE", "TARGET_KBN", "IS_INVALID");

CREATE INDEX "DENSI_HOUKATU_GRP_IDX02" ON "DENSI_HOUKATU_GRP" ("HP_ID", "ITEM_CD", "START_DATE", "END_DATE", "TARGET_KBN", "IS_INVALID");

CREATE INDEX "DRUG_INF_UKEY01" ON "DRUG_INF" ("HP_ID", "ITEM_CD", "INF_KBN", "IS_DELETED");

CREATE INDEX "FILING_INF_IDX01" ON "FILING_INF" ("PT_ID", "GET_DATE", "FILE_NO", "CATEGORY_CD");

CREATE INDEX "FUNCTION_MST_PKEY" ON "FUNCTION_MST" ("FUNCTION_CD");

CREATE INDEX "HOLIDAY_MST_UKEY01" ON "HOLIDAY_MST" ("HP_ID", "SIN_DATE", "IS_DELETED");

CREATE INDEX "IPN_MIN_YAKKA_MST_IDX01" ON "IPN_MIN_YAKKA_MST" ("HP_ID", "IPN_NAME_CD", "START_DATE");

CREATE INDEX "PT_KA_MST_IDX01" ON "KA_MST" ("KA_ID");

CREATE INDEX "KARTE_INF_IDX01" ON "KARTE_INF" ("HP_ID", "PT_ID", "KARTE_KBN");

CREATE INDEX "LIMIT_LIST_INF_IDX01" ON "LIMIT_LIST_INF" ("PT_ID", "KOHI_ID", "SIN_DATE", "SEQ_NO");

CREATE INDEX "M12_FOOD_ALRGY_IDX01" ON "M12_FOOD_ALRGY" ("KIKIN_CD", "YJ_CD", "FOOD_KBN", "TENPU_LEVEL");

CREATE INDEX "MALL_MESSAGE_INF_IDX01" ON "MALL_MESSAGE_INF" ("SIN_DATE");

CREATE INDEX "ODR_DATE_DETAIL_IDX01" ON "ODR_DATE_DETAIL" ("HP_ID", "GRP_ID", "ITEM_CD", "IS_DELETED");

CREATE INDEX "ODR_DATE_INF_IDX01" ON "ODR_DATE_INF" ("HP_ID", "GRP_ID", "IS_DELETED");

CREATE INDEX "ODR_INF_IDX01" ON "ODR_INF" ("HP_ID", "PT_ID", "SIN_DATE", "IS_DELETED");

CREATE INDEX "ODR_INF_DETAIL_IDX01" ON "ODR_INF_DETAIL" ("HP_ID", "PT_ID", "RAIIN_NO", "ITEM_CD");

CREATE INDEX "ODR_INF_DETAIL_IDX02" ON "ODR_INF_DETAIL" ("ITEM_CD");

CREATE INDEX "ODR_INF_DETAIL_IDX03" ON "ODR_INF_DETAIL" ("SIN_DATE", "PT_ID", "RAIIN_NO");

CREATE INDEX "ODR_INF_DETAIL_IDX04" ON "ODR_INF_DETAIL" ("PT_ID", "SIN_DATE", "ITEM_CD");

CREATE INDEX "ONLINE_CONFIRMATION_HISTORY_IDX01" ON "ONLINE_CONFIRMATION_HISTORY" ("ID");

CREATE INDEX "PT_PATH_CONF_IDX01" ON "PATH_CONF" ("HP_ID", "GRP_CD", "GRP_EDA_NO", "MACHINE", "IS_INVALID");

CREATE INDEX "PT_PATH_CONF_PKEY" ON "PATH_CONF" ("HP_ID", "GRP_CD", "GRP_EDA_NO", "SEQ_NO");

CREATE INDEX "PERMISSION_MST_PKEY" ON "PERMISSION_MST" ("FUNCTION_CD", "PERMISSION");

CREATE INDEX "PT_POST_CODE_MST_IDX01" ON "POST_CODE_MST" ("POST_CD", "IS_DELETED");

CREATE INDEX "PTCMT_INF_IDX01" ON "PT_CMT_INF" ("ID");

CREATE INDEX "PT_FAMILY_IDX01" ON "PT_FAMILY" ("FAMILY_ID", "PT_ID", "FAMILY_PT_ID");

CREATE INDEX "PT_FAMILY_REKI_IDX01" ON "PT_FAMILY_REKI" ("ID", "PT_ID", "FAMILY_ID");

CREATE INDEX "PT_GRP_INF_IDX01" ON "PT_GRP_INF" ("HP_ID", "PT_ID", "GRP_ID", "IS_DELETED");

CREATE INDEX "PT_GRP_ITEM_IDX01" ON "PT_GRP_ITEM" ("HP_ID", "GRP_ID", "GRP_CODE", "IS_DELETED");

CREATE INDEX "PT_GRP_NAME_IDX01" ON "PT_GRP_NAME_MST" ("HP_ID", "GRP_ID", "IS_DELETED");

CREATE INDEX "PT_HOKEN_PATTERN_IDX01" ON "PT_HOKEN_PATTERN" ("HP_ID", "PT_ID", "START_DATE", "END_DATE", "IS_DELETED");

CREATE INDEX "PT_HOKEN_SCAN_IDX01" ON "PT_HOKEN_SCAN" ("HP_ID", "PT_ID", "HOKEN_GRP", "HOKEN_ID");

CREATE INDEX "PT_HOKEN_SCAN_PKEY" ON "PT_HOKEN_SCAN" ("HP_ID", "PT_ID", "HOKEN_GRP", "HOKEN_ID", "SEQ_NO", "IS_DELETED");

CREATE INDEX "PT_INF_IDX01" ON "PT_INF" ("HP_ID", "PT_NUM");

CREATE INDEX "PT_JIBKAR_IDX01" ON "PT_JIBKAR" ("HP_ID", "WEB_ID", "PT_ID");

CREATE INDEX "PT_KYUSEI_IDX01" ON "PT_KYUSEI" ("HP_ID", "PT_ID", "END_DATE", "IS_DELETED");

CREATE INDEX "PT_MEMO_IDX01" ON "PT_MEMO" ("HP_ID", "PT_ID", "IS_DELETED");

CREATE INDEX "PTPREGNANCY_IDX01" ON "PT_PREGNANCY" ("ID", "HP_ID");

CREATE INDEX "PT_ROUSAI_TENKI_IDX01" ON "PT_ROUSAI_TENKI" ("HP_ID", "PT_ID", "HOKEN_ID", "END_DATE", "IS_DELETED");

CREATE INDEX "PT_CALC_CONF_IDX01" ON "PT_SANTEI_CONF" ("HP_ID", "PT_ID", "KBN_NO", "EDA_NO", "START_DATE", "END_DATE", "IS_DELETED");

CREATE INDEX "PT_CALC_CONF_PKEY" ON "PT_SANTEI_CONF" ("HP_ID", "PT_ID", "KBN_NO", "EDA_NO", "SEQ_NO");

CREATE INDEX "PT_TAG_IDX01" ON "PT_TAG" ("HP_ID", "PT_ID", "START_DATE", "END_DATE", "IS_DSP_UKETUKE", "IS_DSP_KARTE", "IS_DSP_KAIKEI", "IS_DELETED");

CREATE INDEX "RAIIN_CMT_INF_IDX01" ON "RAIIN_CMT_INF" ("HP_ID", "RAIIN_NO", "CMT_KBN", "IS_DELETE");

CREATE INDEX "RAIIN_FILTER_MST_IDX01" ON "RAIIN_FILTER_MST" ("HP_ID", "FILTER_ID", "IS_DELETED");

CREATE INDEX "KARTE_INF_IDX011" ON "RAIIN_FILTER_SORT" ("ID", "HP_ID", "FILTER_ID", "IS_DELETED");

CREATE INDEX "RAIIN_INF_IDX01" ON "RAIIN_INF" ("HP_ID", "PT_ID", "SIN_DATE", "STATUS", "IS_DELETED");

CREATE INDEX "RAIIN_INF_IDX02" ON "RAIIN_INF" ("HP_ID", "PT_ID", "SIN_DATE", "STATUS", "SYOSAISIN_KBN", "IS_DELETED");

CREATE INDEX "RAIIN_KBN_DETAIL_IDX01" ON "RAIIN_KBN_DETAIL" ("HP_ID", "GRP_ID", "KBN_CD", "IS_DELETED");

CREATE INDEX "RAIIN_KBN_INF_IDX01" ON "RAIIN_KBN_INF" ("HP_ID", "PT_ID", "SIN_DATE", "RAIIN_NO", "GRP_ID", "IS_DELETE");

CREATE INDEX "RAIIN_KBN_ITEM_IDX01" ON "RAIIN_KBN_ITEM" ("HP_ID", "GRP_ID", "KBN_CD", "IS_DELETED");

CREATE INDEX "RAIIN_KBN_KOUI_IDX01" ON "RAIIN_KBN_KOUI" ("HP_ID", "GRP_ID", "KBN_CD", "IS_DELETED");

CREATE INDEX "RAIIN_KBN_MST_IDX01" ON "RAIIN_KBN_MST" ("HP_ID", "GRP_ID", "IS_DELETED");

CREATE INDEX "RAIIN_KBN_YOYAKU_IDX01" ON "RAIIN_KBN_YOYAKU" ("HP_ID", "GRP_ID", "KBN_CD", "IS_DELETED");

CREATE INDEX "RAIIN_LIST_CMT_UKEY01" ON "RAIIN_LIST_CMT" ("HP_ID", "RAIIN_NO", "CMT_KBN", "IS_DELETED");

CREATE INDEX "RAIIN_LIST_DETAIL_IDX01" ON "RAIIN_LIST_DETAIL" ("HP_ID", "GRP_ID", "KBN_CD", "IS_DELETED");

CREATE INDEX "RAIIN_LIST_FILE_IDX01" ON "RAIIN_LIST_FILE" ("HP_ID", "GRP_ID", "KBN_CD", "IS_DELETED");

CREATE INDEX "RAIIN_LIST_ITEM_IDX01" ON "RAIIN_LIST_ITEM" ("HP_ID", "GRP_ID", "KBN_CD", "IS_DELETED");

CREATE INDEX "RAIIN_LIST_KOUI_IDX01" ON "RAIIN_LIST_KOUI" ("HP_ID", "GRP_ID", "KBN_CD", "IS_DELETED");

CREATE INDEX "RAIIN_LIST_MST_IDX01" ON "RAIIN_LIST_MST" ("HP_ID", "GRP_ID", "IS_DELETED");

CREATE INDEX "RAIIN_LIST_TAG_UKEY01" ON "RAIIN_LIST_TAG" ("HP_ID", "RAIIN_NO", "IS_DELETED");

CREATE INDEX "RECE_CMT_IDX01" ON "RECE_CMT" ("HP_ID", "PT_ID", "SIN_YM", "HOKEN_ID", "IS_DELETED");

CREATE INDEX "RECEDEN_CMT_SELECT_IDX01" ON "RECEDEN_CMT_SELECT" ("HP_ID", "ITEM_CD", "START_DATE", "COMMENT_CD", "IS_INVALID");

CREATE INDEX "PT_KYUSEI_IDX011" ON "Z_PT_KYUSEI" ("HP_ID", "PT_ID", "END_DATE", "IS_DELETED");

CREATE INDEX "SYUNO_NYUKIN_IDX01" ON "Z_SYUNO_NYUKIN" ("HP_ID", "PT_ID", "SIN_DATE", "RAIIN_NO", "IS_DELETED");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20221214113757_Init', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE TABLE "JSON_SETTING" (
    "USER_ID" integer NOT NULL,
    "KEY" text NOT NULL,
    "VALUE" text NOT NULL,
    CONSTRAINT "PK_JSON_SETTING" PRIMARY KEY ("USER_ID", "KEY")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20221214114031_Create_Json_Setting_Table', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE TABLE "COLUMN_SETTING" (
    "USER_ID" integer NOT NULL,
    "TABLE_NAME" text NOT NULL,
    "COLUMN_NAME" text NOT NULL,
    "DISPLAY_ORDER" integer NOT NULL,
    "IS_PINNED" boolean NOT NULL,
    "IS_HIDDEN" boolean NOT NULL,
    "WIDTH" integer NOT NULL,
    CONSTRAINT "PK_COLUMN_SETTING" PRIMARY KEY ("USER_ID", "TABLE_NAME", "COLUMN_NAME")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20221214114155_Create_Column_Setting_Table', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "PT_GRP_INF" DROP CONSTRAINT "PK_PT_GRP_INF";

ALTER TABLE "PT_FAMILY" DROP CONSTRAINT "PK_PT_FAMILY";

ALTER TABLE "PT_GRP_INF" ALTER COLUMN "GRP_CODE" DROP NOT NULL;

ALTER TABLE "PT_FAMILY" ALTER COLUMN "PT_ID" DROP IDENTITY;

ALTER TABLE "PT_GRP_INF" ADD CONSTRAINT "PK_PT_GRP_INF" PRIMARY KEY ("HP_ID", "GRP_ID", "PT_ID", "SEQ_NO");

ALTER TABLE "PT_FAMILY" ADD CONSTRAINT "PK_PT_FAMILY" PRIMARY KEY ("FAMILY_ID");

CREATE UNIQUE INDEX "IX_SET_MST_HP_ID_SET_KBN_SET_KBN_EDA_NO_GENERATION_ID" ON "SET_MST" ("HP_ID", "SET_CD", "SET_KBN", "SET_KBN_EDA_NO", "GENERATION_ID", "LEVEL1", "LEVEL2", "LEVEL3") WHERE "IS_DELETED" = 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230525054559_Update_Structrure_Family_SetMst', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "COLUMN_SETTING" ADD "ORDER_BY" text NOT NULL DEFAULT '';

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230612034049_Add_SortBy_ColummSetting', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE UNIQUE INDEX "IX_LOCK_INF_HP_ID_PT_ID_USER_ID" ON "LOCK_INF" ("HP_ID", "PT_ID", "USER_ID") WHERE "FUNCTION_CD" = '02000000';

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230616105321_Add_Unique_LockInf', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE TABLE "USER_TOKEN" (
    "USER_ID" integer NOT NULL,
    "REFRESH_TOKEN" text NOT NULL,
    "TOKEN_EXPIRY_TIME" timestamp with time zone NOT NULL,
    "REFRESH_TOKEN_IS_USED" boolean NOT NULL,
    CONSTRAINT "PK_USER_TOKEN" PRIMARY KEY ("USER_ID", "REFRESH_TOKEN")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230621022641_InitTableUserToken', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE INDEX "YAKKA_SYUSAI_MST_IDX01" ON "YAKKA_SYUSAI_MST" ("START_DATE", "END_DATE");

CREATE INDEX "TEN_MST_IDX08" ON "TEN_MST" ("HP_ID", "ITEM_CD", "START_DATE", "END_DATE", "NAME", "KANA_NAME1", "KANA_NAME2", "KANA_NAME3", "KANA_NAME4", "KANA_NAME5", "KANA_NAME6", "KANA_NAME7", "IS_DELETED", "IS_ADOPTED");

CREATE INDEX "RAIIN_INF_IDX03" ON "RAIIN_INF" ("IS_DELETED", "SIN_DATE", "PT_ID");

CREATE INDEX "KENSA_MST_IDX01" ON "KENSA_MST" ("KENSA_ITEM_CD");

CREATE INDEX "IPN_NAME_MST_IDX01" ON "IPN_NAME_MST" ("IPN_NAME_CD");

CREATE INDEX "IPN_MIN_YAKKA_MST_IDX02" ON "IPN_MIN_YAKKA_MST" ("HP_ID", "START_DATE", "END_DATE", "IPN_NAME_CD");

CREATE INDEX "IPN_KASAN_EXCLUDE_ITEM_IDX01" ON "IPN_KASAN_EXCLUDE_ITEM" ("HP_ID", "START_DATE", "END_DATE", "ITEM_CD");

CREATE INDEX "IPN_KASAN_EXCLUDE_IDX01" ON "IPN_KASAN_EXCLUDE" ("HP_ID", "START_DATE", "END_DATE", "IPN_NAME_CD");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230710044725_Add_Indexes', '7.0.1');

COMMIT;

START TRANSACTION;

DROP INDEX "IX_LOCK_INF_HP_ID_PT_ID_USER_ID";

CREATE UNIQUE INDEX "IX_LOCK_INF_HP_ID_PT_ID_USER_ID" ON "LOCK_INF" ("HP_ID", "PT_ID", "USER_ID") WHERE "FUNCTION_CD" IN ('02000000', '03000000');

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230720094723_Update_Unique_Lock_Inf', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE UNIQUE INDEX "IX_RSVKRT_MST_HP_ID_PT_ID_RSV_DATE" ON "RSVKRT_MST" ("HP_ID", "PT_ID", "RSV_DATE") WHERE "RSVKRT_KBN" = 0 AND "IS_DELETED" = 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230726014044_create_key_RsvkrtMs', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "Z_RAIIN_INF" ADD "CONFIRMATION_TYPE" integer NOT NULL DEFAULT 0;

ALTER TABLE "Z_RAIIN_INF" ADD "INFO_CONS_FLG" character varying(10) NULL;

ALTER TABLE "Z_RAIIN_INF" ADD "PRESCRIPTION_ISSUE_TYPE" integer NOT NULL DEFAULT 0;

ALTER TABLE "RAIIN_INF" ADD "CONFIRMATION_TYPE" integer NOT NULL DEFAULT 0;

ALTER TABLE "RAIIN_INF" ADD "INFO_CONS_FLG" character varying(10) NULL;

ALTER TABLE "RAIIN_INF" ADD "PRESCRIPTION_ISSUE_TYPE" integer NOT NULL DEFAULT 0;

ALTER TABLE "ONLINE_CONFIRMATION_HISTORY" ADD "INFO_CONS_FLG" character varying(10) NULL;

ALTER TABLE "ONLINE_CONFIRMATION_HISTORY" ADD "PRESCRIPTION_ISSUE_TYPE" integer NOT NULL DEFAULT 0;

ALTER TABLE "ONLINE_CONFIRMATION_HISTORY" ADD "UKETUKE_STATUS" integer NOT NULL DEFAULT 0;

ALTER TABLE "ONLINE_CONFIRMATION_HISTORY" ADD "UPDATE_DATE" timestamp with time zone NOT NULL DEFAULT TIMESTAMPTZ '-infinity';

ALTER TABLE "ONLINE_CONFIRMATION_HISTORY" ADD "UPDATE_ID" integer NOT NULL DEFAULT 0;

ALTER TABLE "ONLINE_CONFIRMATION_HISTORY" ADD "UPDATE_MACHINE" character varying(60) NULL;

ALTER TABLE "LOCK_INF" ADD "LOGINKEY" text NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230831090458_online', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "KA_MST" ADD "YOUSIKI_KA_CD" character varying(3) NOT NULL DEFAULT '';

CREATE TABLE "KACODE_RECE_YOUSIKI" (
    "RECE_KA_CD" character varying(2) NOT NULL,
    "YOUSIKI_KA_CD" character varying(3) NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NOT NULL,
    CONSTRAINT "PK_KACODE_RECE_YOUSIKI" PRIMARY KEY ("RECE_KA_CD", "YOUSIKI_KA_CD")
);

CREATE TABLE "KACODE_YOUSIKI_MST" (
    "YOUSIKI_KA_CD" character varying(3) NOT NULL,
    "SORT_NO" integer NOT NULL,
    "KA_NAME" character varying(40) NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NOT NULL,
    CONSTRAINT "PK_KACODE_YOUSIKI_MST" PRIMARY KEY ("YOUSIKI_KA_CD")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230831110132_F9_Department', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "KA_MST" ALTER COLUMN "YOUSIKI_KA_CD" DROP NOT NULL;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230908093430_F9_Department_String', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "FILING_INF" DROP CONSTRAINT "PK_FILING_INF";

ALTER TABLE "FILING_INF" ADD CONSTRAINT "PK_FILING_INF" PRIMARY KEY ("HP_ID", "FILE_ID");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230921012418_Update_Key_FillingInf', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE TABLE "KENSA_SET" (
    "HP_ID" integer NOT NULL,
    "SET_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "SET_NAME" character varying(30) NOT NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KENSA_SET" PRIMARY KEY ("HP_ID", "SET_ID")
);

CREATE TABLE "KENSA_SET_DETAIL" (
    "HP_ID" integer NOT NULL,
    "SET_ID" integer NOT NULL,
    "SET_EDA_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "KENSA_ITEM_CD" character varying(10) NOT NULL,
    "KENSA_ITEM_SEQ_NO" integer NOT NULL,
    "SORT_NO" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KENSA_SET_DETAIL" PRIMARY KEY ("HP_ID", "SET_ID", "SET_EDA_NO")
);

CREATE INDEX "KENSA_SET_PKEY" ON "KENSA_SET" ("HP_ID", "SET_ID");

CREATE INDEX "KENSA_SET_DETAIL_PKEY" ON "KENSA_SET_DETAIL" ("HP_ID", "SET_ID", "SET_EDA_NO");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230926102448_addTableKensaSet', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE TABLE "KENSA_CMT_MST" (
    "HP_ID" integer NOT NULL,
    "CMT_CD" character varying(3) NOT NULL,
    "CMT_SEQ_NO" integer GENERATED BY DEFAULT AS IDENTITY,
    "CMT" character varying(100) NULL,
    "CENTER_CD" character varying(10) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KENSA_CMT_MST" PRIMARY KEY ("HP_ID", "CMT_CD", "CMT_SEQ_NO")
);

CREATE INDEX "KENSA_CMT_MST_SKEY1" ON "KENSA_CMT_MST" ("HP_ID", "CMT_CD", "CMT_SEQ_NO", "IS_DELETED");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20230929032548_addTableKensaCmtMst', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE INDEX "RAIIN_LIST_INF_IDX01" ON "RAIIN_LIST_INF" ("GRP_ID", "KBN_CD", "RAIIN_LIST_KBN");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231002022339_Update_Index_RAIIN_LIST_INF_IDX01', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE INDEX "RAIIN_LIST_INF_IDX02" ON "RAIIN_LIST_INF" ("HP_ID", "PT_ID");

CREATE INDEX "RAIIN_LIST_DETAIL_IDX02" ON "RAIIN_LIST_DETAIL" ("HP_ID", "IS_DELETED");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231006104244_Index_Table_Flowsheet', '7.0.1');

COMMIT;

START TRANSACTION;

DROP INDEX "RAIIN_LIST_INF_IDX02";

CREATE INDEX "RAIIN_LIST_INF_IDX02" ON "RAIIN_LIST_INF" ("HP_ID", "PT_ID", "RAIIN_NO");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231006113327_Index_Table_Flowsheet_2', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "RAIIN_LIST_INF" DROP CONSTRAINT "PK_RAIIN_LIST_INF";

DROP INDEX "RAIIN_LIST_INF_IDX02";

ALTER TABLE "RAIIN_LIST_INF" ADD "ID" bigint GENERATED BY DEFAULT AS IDENTITY;

ALTER TABLE "RAIIN_LIST_INF" ADD CONSTRAINT "PK_RAIIN_LIST_INF" PRIMARY KEY ("ID");

CREATE UNIQUE INDEX "IX_RAIIN_LIST_INF_HP_ID_PT_ID_SIN_DATE_RAIIN_NO_GRP_ID_RAIIN_L~" ON "RAIIN_LIST_INF" ("HP_ID", "PT_ID", "SIN_DATE", "RAIIN_NO", "GRP_ID", "RAIIN_LIST_KBN");

CREATE INDEX "RAIIN_LIST_INF_IDX02" ON "RAIIN_LIST_INF" ("HP_ID", "PT_ID");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231011110800_ReStructure_RaiinListInf', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE TABLE "KENSA_RESULT_LOG" (
    "OP_ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "HP_ID" integer NOT NULL,
    "IMP_DATE" integer NOT NULL,
    "KEKA_FILE" text NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_KENSA_RESULT_LOG" PRIMARY KEY ("OP_ID")
);

CREATE INDEX "RAIIN_INF_IDX04" ON "RAIIN_INF" ("HP_ID", "RAIIN_NO", "IS_DELETED", "STATUS");

CREATE INDEX "PT_INF_IDX02" ON "PT_INF" ("HP_ID", "PT_ID", "IS_DELETE");

CREATE INDEX "PT_HOKEN_INF_IDX01" ON "PT_HOKEN_INF" ("HP_ID", "PT_ID", "HOKEN_ID", "HOKEN_KBN", "HOUBETU");

CREATE INDEX "KENSA_RESULT_LOG_IDX01" ON "KENSA_RESULT_LOG" ("HP_ID", "IMP_DATE");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231016033004_Add_Table_KensaResultLog', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "Z_KENSA_INF_DETAIL" ADD "SEQ_PARENT_NO" bigint NOT NULL DEFAULT 0;

ALTER TABLE "KENSA_INF_DETAIL" ADD "SEQ_PARENT_NO" bigint NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231019023847_Add_Column_SEQ_PARENT_NO_KensaInfDetail', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "KENSA_SET_DETAIL" ADD "SEQ_PARENT_NO" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231019024718_Add_Column_SEQ_PARENT_NO_KensaSetDetail', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "KARTE_IMG_INF" ADD "CREATE_DATE" timestamp with time zone NOT NULL DEFAULT TIMESTAMPTZ '-infinity';

ALTER TABLE "KARTE_IMG_INF" ADD "CREATE_ID" integer NOT NULL DEFAULT 0;

ALTER TABLE "KARTE_IMG_INF" ADD "UPDATE_DATE" timestamp with time zone NOT NULL DEFAULT TIMESTAMPTZ '-infinity';

ALTER TABLE "KARTE_IMG_INF" ADD "UPDATE_ID" integer NOT NULL DEFAULT 0;

ALTER TABLE "CONVERSION_ITEM_INF" ADD "IS_DELETED" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231030044926_Karte_File_History', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE TABLE "SMARTKARTE_APP_SIGNALR_PORT" (
    "ID" integer GENERATED BY DEFAULT AS IDENTITY,
    "PORT_NUMBER" integer NOT NULL,
    "MACHINE_NAME" character varying(60) NULL,
    "IP" character varying(60) NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    CONSTRAINT "PK_SMARTKARTE_APP_SIGNALR_PORT" PRIMARY KEY ("ID")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231030081905_addTableSmartKarteAppSignalRPort', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "SINREKI_FILTER_MST_DETAIL" ADD "IS_EXCLUDE" integer NOT NULL DEFAULT 0;

ALTER TABLE "KENSA_SET_DETAIL" ALTER COLUMN "KENSA_ITEM_CD" DROP NOT NULL;

ALTER TABLE "KENSA_SET" ALTER COLUMN "SET_NAME" DROP NOT NULL;

CREATE TABLE "SINREKI_FILTER_MST_KOUI" (
    "HP_ID" integer NOT NULL,
    "GRP_CD" integer NOT NULL,
    "SEQ_NO" bigint GENERATED BY DEFAULT AS IDENTITY,
    "KOUI_KBN_ID" integer NOT NULL,
    "IS_DELETED" integer NOT NULL,
    "CREATE_DATE" timestamp with time zone NOT NULL,
    "CREATE_ID" integer NOT NULL,
    "CREATE_MACHINE" character varying(60) NULL,
    "UPDATE_DATE" timestamp with time zone NOT NULL,
    "UPDATE_ID" integer NOT NULL,
    "UPDATE_MACHINE" character varying(60) NULL,
    CONSTRAINT "PK_SINREKI_FILTER_MST_KOUI" PRIMARY KEY ("HP_ID", "GRP_CD", "SEQ_NO")
);

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231103074911_addTableSinrekiFilterMstKoui', '7.0.1');

COMMIT;

START TRANSACTION;

CREATE UNIQUE INDEX "IX_USER_MST_USER_ID" ON "USER_MST" ("USER_ID") WHERE "IS_DELETED" = 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231103090456_addUniqueinUserMst', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "KOUI_KBN_MST" ADD "EXC_KOUI_KBN" integer NOT NULL DEFAULT 0;

ALTER TABLE "KOUI_KBN_MST" ADD "OYA_KOUI_KBN_ID" integer NOT NULL DEFAULT 0;

UPDATE "KENSA_SET_DETAIL" SET "KENSA_ITEM_CD" = '' WHERE "KENSA_ITEM_CD" IS NULL;
ALTER TABLE "KENSA_SET_DETAIL" ALTER COLUMN "KENSA_ITEM_CD" SET NOT NULL;
ALTER TABLE "KENSA_SET_DETAIL" ALTER COLUMN "KENSA_ITEM_CD" SET DEFAULT '';

UPDATE "KENSA_SET" SET "SET_NAME" = '' WHERE "SET_NAME" IS NULL;
ALTER TABLE "KENSA_SET" ALTER COLUMN "SET_NAME" SET NOT NULL;
ALTER TABLE "KENSA_SET" ALTER COLUMN "SET_NAME" SET DEFAULT '';

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231108040706_AddColumnExcKouiKbnToTableKouiKbnMst', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "KENSA_SET_DETAIL" ALTER COLUMN "KENSA_ITEM_CD" DROP NOT NULL;

ALTER TABLE "KENSA_SET" ALTER COLUMN "SET_NAME" DROP NOT NULL;

ALTER TABLE "KENSA_INF_DETAIL" ADD "SEQ_GROUP_NO" bigint NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231116024736_AddSeqGroupNoToTableKensaInfDetail', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "Z_KENSA_INF_DETAIL" ADD "SEQ_GROUP_NO" bigint NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231116042310_AddSeqGroupNoToTableZKensaInfDetail', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "KENSA_INF_DETAIL" DROP CONSTRAINT "PK_KENSA_INF_DETAIL";

ALTER TABLE "KENSA_INF_DETAIL" ADD CONSTRAINT "PK_KENSA_INF_DETAIL" PRIMARY KEY ("HP_ID", "SEQ_NO");

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231117012805_ChangeKeyTableKensaInfDetail', '7.0.1');

COMMIT;

START TRANSACTION;

ALTER TABLE "USER_MST" ADD "HPKI_ISSUER_DN" character varying(100) NULL;

ALTER TABLE "USER_MST" ADD "HPKI_SN" character varying(100) NULL;

ALTER TABLE "USER_MST" ADD "LOGIN_TYPE" integer NOT NULL DEFAULT 0;

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20231130024656_addColumnTableUserMst', '7.0.1');

COMMIT;

CREATE SEQUENCE public."OP_ID_seq"
	INCREMENT BY 1
	MINVALUE 10000000
	MAXVALUE 9223372036854775807
	START 10000000
	CACHE 1
	NO CYCLE;

