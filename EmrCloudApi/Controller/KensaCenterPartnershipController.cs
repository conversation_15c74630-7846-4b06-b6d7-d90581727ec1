using EmrCloudApi.Constants;
using EmrCloudApi.Presenters.KensaCenterPartnership;
using EmrCloudApi.Requests.KensaCenterPartnership;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.KensaCenterPartnership;
using Helper.Responses;
using UseCase.KensaCenterPartnership;
using Microsoft.AspNetCore.Mvc;
using UseCase.Core.Sync;

namespace EmrCloudApi.Controller
{
    [Route("api/[controller]")]
    public class KensaCenterPartnershipController : BaseParamControllerBase
    {
        private readonly UseCaseBus _bus;

        public KensaCenterPartnershipController(UseCaseBus bus, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _bus = bus;
        }

        [HttpGet(ApiPath.GetKensaCenterPartnership)]
        public ActionResult<Response<KensaCenterPartnershipListResponse>> GetkensaCenterPartnership([FromQuery] GetKensaCenterPartnershipRequest request)
        {
            var input = new GetKensaCenterPartnershipInputData(HpId, request.CenterCd);
            var output = _bus.Handle(input);

            var presenter = new GetKensaCenterPartnershipListPresenter();
            presenter.Complete(output);

            // NOTE: Success/NoData
            var okStatus = new[] { GetKensaCenterPartnershipStatus.Successful, GetKensaCenterPartnershipStatus.NoData };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<KensaCenterPartnershipListResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.RegisterKensaCenterPartnership)]
        public ActionResult<Response<KensaCenterPartnershipResponse>> RegisterKensaCenterPartnership([FromBody] RegisterKensaCenterPartnershipRequest request)
        {
            var input = new RegisterKensaCenterPartnershipInputData(HpId, request.CenterCd, request.StartDate, request.EndDate);
            var output = _bus.Handle(input);

            var presenter = new RegisterKensaCenterPartnershipListPresenter();
            presenter.Complete(output);

            // NOTE: Success/NoData
            var okStatus = new[] { RegisterKensaCenterPartnershipStatus.Successful };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<KensaCenterPartnershipResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.UpdateKensaCenterPartnership)]
        public ActionResult<Response<KensaCenterPartnershipResponse>> UpdateKensaCenterPartnership([FromBody] UpdateKensaCenterPartnershipRequest request)
        {
            var input = new UpdateKensaCenterPartnershipInputData(HpId, request.CenterCd, request.OldStartDate, request.StartDate, request.EndDate);
            var output = _bus.Handle(input);

            var presenter = new UpdateKensaCenterPartnershipPresenter();
            presenter.Complete(output);

            // NOTE: Success/NoData
            var okStatus = new[] { UpdateKensaCenterPartnershipStatus.Successful };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<KensaCenterPartnershipResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.UnregisterKensaCenterPartnership)]
        public ActionResult<Response<KensaCenterPartnershipResponse>> UnregisterKensaCenterPartnership([FromBody] UnregisterKensaCenterPartnershipRequest request)
        {
            var input = new UnregisterKensaCenterPartnershipInputData(HpId, request.CenterCd, request.StartDate);
            var output = _bus.Handle(input);

            var presenter = new UnregisterKensaCenterPartnershipPresenter();
            presenter.Complete(output);

            // NOTE: Success/NoData
            var okStatus = new[] { UnregisterKensaCenterPartnershipStatus.Successful };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<KensaCenterPartnershipResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.UpdateKensaCenterPartnershipMstUpdateDate)]
        public ActionResult<Response<KensaCenterPartnershipResponse>> UpdateKensaCenterPartnershipMstUpdateDate()
        {
            var input = new UpdateKensaCenterPartnershipMstUpdateDateInputData(HpId);
            var output = _bus.Handle(input);

            var presenter = new UpdateKensaCenterPartnershipMstUpdateDatePresenter();
            presenter.Complete(output);

            // NOTE: Success/NoData
            var okStatus = new[] { UpdateKensaCenterPartnershipMstUpdateDateStatus.Successful };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<KensaCenterPartnershipResponse>>(presenter.Result);
        }  

    }
}
