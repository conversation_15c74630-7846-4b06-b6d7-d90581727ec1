using EmrCloudApi.Constants;
using EmrCloudApi.Messages;
using EmrCloudApi.Presenters.MaxMoney;
using EmrCloudApi.Presenters.RaiinKubun;
using EmrCloudApi.Presenters.Reception;
using EmrCloudApi.Presenters.ReceptionInsurance;
using EmrCloudApi.Presenters.ReceptionSameVisit;
using EmrCloudApi.Presenters.Schema;
using EmrCloudApi.Realtime;
using EmrCloudApi.Requests.MaxMoney;
using EmrCloudApi.Requests.RaiinKubun;
using EmrCloudApi.Requests.Reception;
using EmrCloudApi.Requests.ReceptionInsurance;
using EmrCloudApi.Requests.ReceptionSameVisit;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.MaxMoney;
using EmrCloudApi.Responses.RaiinKubun;
using EmrCloudApi.Responses.Reception;
using EmrCloudApi.Responses.ReceptionInsurance;
using EmrCloudApi.Responses.ReceptionSameVisit;
using EmrCloudApi.Responses.Schema;
using EmrCloudApi.Services;
using EmrCloudApi.Tenant.Presenters.Reception;
using EmrCloudApi.Tenant.Requests.Reception;
using EmrCloudApi.Tenant.Responses.Reception;
using Helper.Responses;
using Microsoft.AspNetCore.Mvc;
using UseCase.Core.Sync;
using UseCase.Insurance.ValidPatternExpirated;
using UseCase.MaxMoney.GetMaxMoney;
using UseCase.MaxMoney.SaveMaxMoney;
using UseCase.PatientFile;
using UseCase.RaiinKbn.GetPatientRaiinKubunList;
using UseCase.Reception.CombineBill;
using UseCase.Reception.AddPatientFile;
using UseCase.Reception.Delete;
using UseCase.Reception.DeleteFile;
using UseCase.Reception.Get;
using UseCase.Reception.GetDefaultSelectedTime;
using UseCase.Reception.GetHpInf;
using UseCase.Reception.GetLastKarute;
using UseCase.Reception.GetLastRaiinInfs;
using UseCase.Reception.GetListRaiinInf;
using UseCase.Reception.GetOutDrugOrderList;
using UseCase.Reception.GetRaiinInfBySinDate;
using UseCase.Reception.GetRaiinListWithKanInf;
using UseCase.Reception.GetReceptionDefault;
using UseCase.Reception.GetYoyakuRaiinInf;
using UseCase.Reception.InitDoctorCombo;
using UseCase.Reception.Insert;
using UseCase.Reception.ReceptionComment;
using UseCase.Reception.RevertDeleteNoRecept;
using UseCase.Reception.SaveCombineBill;
using UseCase.Reception.Update;
using UseCase.Reception.UpdateTimeZoneDayInf;
using UseCase.ReceptionInsurance.Get;
using UseCase.ReceptionSameVisit.Get;
using UseCase.Schema.GetListImageTemplates;
using UseCase.Reception.InsertFromCalendarOrPortal;
using EmrCloudApi.Realtime.Subscription;
using EmrCloudApi.Responses.PatientInfor;
using EmrCloudApi.Requests.PatientInfor;
using UseCase.PatientInfor.BookingForMultiplePatient;
using EmrCloudApi.Presenters.PatientInfor;
using UseCase.Reception.UpdatePrescription;
using UseCase.Reception.GetDefaultPrescription;
using UseCase.Reception.CheckLinkCard;
using UseCase.Reception.ValidateReservation;
using UseCase.Reception.UpdateRaiinCmtByBooking;

namespace EmrCloudApi.Controller
{
    [Route("api/[controller]")]
    public class ReceptionController : BaseParamControllerBase
    {
        private readonly UseCaseBus _bus;
        private readonly ISubscriptionService _subscriptionService;

        public ReceptionController(UseCaseBus bus, IHttpContextAccessor httpContextAccessor, ISubscriptionService subscriptionService) : base(httpContextAccessor)
        {
            _bus = bus;
            _subscriptionService = subscriptionService;
        }

        [HttpGet(ApiPath.Get + "ReceptionComment")]
        public ActionResult<Response<GetReceptionCommentResponse>> GetReceptionComment([FromQuery] GetReceptionCommentRequest request)
        {
            var input = new GetReceptionCommentInputData(HpId, request.RaiinNo);
            var output = _bus.Handle(input);
            var presenter = new GetReceptionCommentPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetReceptionCommentStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return Ok(presenter.Result);
        }

        [HttpGet(ApiPath.Get)]
        public ActionResult<Response<GetReceptionResponse>> Get([FromQuery] GetReceptionRequest request)
        {
            var input = new GetReceptionInputData(HpId, request.RaiinNo, request.Flag);
            var output = _bus.Handle(input);

            var presenter = new GetReceptionPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetReceptionStatus.Successed, GetReceptionStatus.ReceptionNotExisted };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetReceptionResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.GetLastRaiinInfs)]
        public ActionResult<Response<GetLastRaiinInfsResponse>> GetLastRaiinInfs([FromQuery] GetLastRaiinInfsRequest request)
        {
            var input = new GetLastRaiinInfsInputData(HpId, request.PtId, request.SinDate, request.IsLastVisit);
            var output = _bus.Handle(input);

            var presenter = new GetLastRaiinInfsPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetLastRaiinInfsStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetLastRaiinInfsResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.GetOutDrugOrderList)]
        public ActionResult<Response<GetOutDrugOrderListResponse>> GetOutDrugOrderList([FromQuery] GetOutDrugOrderListRequest request)
        {
            var input = new GetOutDrugOrderListInputData(HpId, request.IsPrintPrescription, request.IsPrintAccountingCard, request.FromDate, request.ToDate, request.SinDate);
            var output = _bus.Handle(input);

            var presenter = new GetOutDrugOrderListPresenter();
            presenter.Complete(output);

            // NOTE: Success only
            var okStatus = new[] { GetOutDrugOrderListStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetOutDrugOrderListResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.Insert)]
        public async Task<ActionResult<Response<InsertReceptionResponse>>> InsertAsync([FromBody] InsertReceptionRequest request)
        {
            var input = new InsertReceptionInputData(request.Dto, HpId, UserId);
            var output = _bus.Handle(input);
            if (output.Status == InsertReceptionStatus.Success)
            {
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionChangedMessageHasura(output.ReceptionInfos, output.SameVisitList));

            }

            var presenter = new InsertReceptionPresenter();
            presenter.Complete(output);

            var okStatus = new[] { InsertReceptionStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }
        
            return new ActionResult<Response<InsertReceptionResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.Update)]
        public async Task<ActionResult<Response<UpdateReceptionResponse>>> UpdateAsync([FromBody] UpdateReceptionRequest request)
        {
            var input = new UpdateReceptionInputData(request.Dto, HpId, UserId);
            var output = _bus.Handle(input);
            if (output.Status == UpdateReceptionStatus.Success)
            {
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionChangedMessageHasura(output.ReceptionInfos, output.SameVisitList));
            }

            var presenter = new UpdateReceptionPresenter();
            presenter.Complete(output);

            var okStatus = new[] { UpdateReceptionStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<UpdateReceptionResponse>>(presenter.Result);
        }

        [HttpGet("GetPatientRaiinKubun")]
        public ActionResult<Response<GetPatientRaiinKubunListResponse>> GetPatientRaiinKubun([FromQuery] GetPatientRaiinKubunListRequest request)
        {
            var input = new GetPatientRaiinKubunListInputData(HpId, request.PtId, request.RaiinNo, request.SinDate);
            var output = _bus.Handle(input);

            var presenter = new GetPatientRaiinKubunListPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetPatientRaiinKubunListStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetPatientRaiinKubunListResponse>>(presenter.Result);
        }

        [HttpGet("GetReceptionInsurance")]
        public ActionResult<Response<ReceptionInsuranceResponse>> GetReceptionInsurance([FromQuery] ReceptionInsuranceRequest request)
        {
            var input = new GetReceptionInsuranceInputData(HpId, request.PtId, request.SinDate, request.IsShowExpiredReception);
            var output = _bus.Handle(input);

            var presenter = new ReceptionInsurancePresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetReceptionInsuranceStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<ReceptionInsuranceResponse>>(presenter.Result);
        }

        [HttpGet("GetListSameVisit")]
        public ActionResult<Response<GetReceptionSameVisitResponse>> GetListSameVisit([FromQuery] GetReceptionSameVisitRequest request)
        {
            var input = new GetReceptionSameVisitInputData(HpId, request.PtId, request.SinDate);
            var output = _bus.Handle(input);

            var presenter = new GetReceptionSameVisitPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetReceptionSameVisitStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetReceptionSameVisitResponse>>(presenter.Result);
        }

        [HttpGet("GetMaxMoneyData")]
        public ActionResult<Response<GetMaxMoneyResponse>> GetMaxMoney([FromQuery] GetMaxMoneyRequest request)
        {
            var input = new GetMaxMoneyInputData(request.PtId, HpId, request.HokenKohiId, request.SinDate);
            var output = _bus.Handle(input);

            var presenter = new GetMaxMoneyPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetMaxMoneyStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetMaxMoneyResponse>>(presenter.Result);
        }

        [HttpPost("SaveMaxMoneyData")]
        public ActionResult<Response<SaveMaxMoneyResponse>> SaveMaxMoney([FromBody] SaveMaxMoneyRequest request)
        {
            var input = new SaveMaxMoneyInputData(request.ListLimits, HpId, request.PtId, request.KohiId, request.SinYm, UserId);
            var output = _bus.Handle(input);

            var presenter = new SaveMaxMoneyPresenter();
            presenter.Complete(output);

            var okStatus = new[] { SaveMaxMoneyStatus.Successful };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<SaveMaxMoneyResponse>>(presenter.Result);
        }

        [HttpPost("CheckPatternSelectedExpirated")]
        public ActionResult<Response<ValidPatternExpiratedResponse>> CheckPatternSelectedExpirated([FromBody] ValidPatternExpiratedRequest request)
        {
            var input = new ValidPatternExpiratedInputData(HpId, request.PtId, request.SinDate, request.PatternHokenPid, request.PatternIsExpirated, request.HokenInfIsJihi, request.HokenInfIsNoHoken, request.PatternConfirmDate,
                                                           request.HokenInfStartDate, request.HokenInfEndDate, request.IsHaveHokenMst, request.HokenMstStartDate, request.HokenMstEndDate, request.HokenMstDisplayTextMaster, request.IsEmptyKohi1,
                                                           request.IsKohiHaveHokenMst1, request.KohiConfirmDate1, request.KohiHokenMstDisplayTextMaster1, request.KohiHokenMstStartDate1, request.KohiHokenMstEndDate1,
                                                           request.IsEmptyKohi2, request.IsKohiHaveHokenMst2, request.KohiConfirmDate2, request.KohiHokenMstDisplayTextMaster2, request.KohiHokenMstStartDate2,
                                                           request.KohiHokenMstEndDate2, request.IsEmptyKohi3, request.IsKohiHaveHokenMst3, request.KohiConfirmDate3, request.KohiHokenMstDisplayTextMaster3, request.KohiHokenMstStartDate3,
                                                           request.KohiHokenMstEndDate3, request.IsEmptyKohi4, request.IsKohiHaveHokenMst4, request.KohiConfirmDate4, request.KohiHokenMstDisplayTextMaster4, request.KohiHokenMstStartDate4, request.KohiHokenMstEndDate4, request.PatientInfBirthday, request.PatternHokenKbn
                                                           , request.SelectedHokenInfIsEmptyModel);
            var output = _bus.Handle(input);

            var presenter = new ValidPatternExpiratedPresenter();
            presenter.Complete(output);

            return new ActionResult<Response<ValidPatternExpiratedResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.GetDataReceptionDefault)]
        public ActionResult<Response<GetReceptionDefaultResponse>> GetDataReceptionDefault([FromQuery] GetReceptionDefaultRequest request)
        {
            var input = new GetReceptionDefaultInputData(HpId, request.PtId, request.Sindate, request.DefaultDoctorSetting);
            var output = _bus.Handle(input);

            var presenter = new GetReceptionDefaultPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetReceptionDefaultStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetReceptionDefaultResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.GetDefaultSelectedTime)]
        public ActionResult<Response<GetDefaultSelectedTimeResponse>> GetDefaultSelectedTime([FromQuery] GetDefaultSelectedTimeRequest request)
        {
            var input = new GetDefaultSelectedTimeInputData(HpId, request.UketukeTime, request.SinDate, request.BirthDay);
            var output = _bus.Handle(input);

            var presenter = new GetDefaultSelectedTimePresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetDefaultSelectedTimeStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetDefaultSelectedTimeResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.UpdateTimeZoneDayInf)]
        public ActionResult<Response<UpdateTimeZoneDayInfResponse>> UpdateTimeZoneDayInf([FromBody] UpdateTimeZoneDayInfRequest request)
        {
            var input = new UpdateTimeZoneDayInfInputData(HpId, UserId, request.SinDate, request.CurrentTimeKbn, request.BeforeTimeKbn, request.UketukeTime);
            var output = _bus.Handle(input);

            var presenter = new UpdateTimeZoneDayInfPresenter();
            presenter.Complete(output);

            var okStatus = new[] { UpdateTimeZoneDayInfStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<UpdateTimeZoneDayInfResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.InitDoctorCombo)]
        public ActionResult<Response<InitDoctorComboResponse>> InitDoctorCombo([FromQuery] InitDoctorComboRequest request)
        {
            var input = new InitDoctorComboInputData(UserId, request.TantoId, request.PtId, HpId, request.SinDate);
            var output = _bus.Handle(input);

            var presenter = new InitDoctorComboPresenter();
            presenter.Complete(output);

            var okStatus = new[] { InitDoctorComboStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<InitDoctorComboResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.GetList)]
        public ActionResult<Response<GetListRaiinInfResponse>> GetList([FromQuery] GetListRaiinInfRequest req)
        {
            var input = new GetListRaiinInfInputData(HpId, req.PtId, req.PageIndex, req.PageSize, req.IsDeleted, req.IsAll);
            var output = _bus.Handle(input);
            var presenter = new GetListRaiinInfPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetListRaiinInfStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return Ok(presenter.Result);
        }

        [HttpGet(ApiPath.GetRaiinListWithKanInf)]
        public ActionResult<Response<GetRaiinListWithKanInfResponse>> GetList([FromQuery] GetRaiinListWithKanInfRequest request)
        {
            var input = new GetRaiinListWithKanInfInputData(HpId, request.PtId);
            var output = _bus.Handle(input);
            var presenter = new GetRaiinListWithKanInfPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetRaiinListWithKanInfStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return Ok(presenter.Result);
        }

        [HttpPost(ApiPath.Delete)]
        public async Task<ActionResult<Response<DeleteReceptionResponse>>> Delete([FromBody] DeleteReceptionRequest req)
        {
            var input = new DeleteReceptionInputData(HpId, req.PtId, req.SinDate, req.Flag, UserId, req.RaiinNos);
            var output = _bus.Handle(input);
            var deleteFirst = output.DeleteReceptionItems.FirstOrDefault();
            if (output.Status == DeleteReceptionStatus.Successed && deleteFirst != null)
            {
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionChangedMessage(output.ReceptionInfos, output.SameVisitList));
            }

            var presenter = new DeleteReceptionPresenter();
            presenter.Complete(output);

            var okStatus = new[] { DeleteReceptionStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return Ok(presenter.Result);
        }

        [HttpGet(ApiPath.GetLastKarute)]
        public ActionResult<Response<GetLastKaruteResponse>> GetLastKarute([FromQuery] GetLastKaruteRequest request)
        {
            var input = new GetLastKaruteInputData(HpId, request.PtNum);
            var output = _bus.Handle(input);
            var presenter = new GetLastKarutePresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetLastKaruteStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetLastKaruteResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.GetYoyakuRaiinInf)]
        public ActionResult<Response<GetYoyakuRaiinInfResponse>> GetYoyakuRaiinInf([FromQuery] GetYoyakuRaiinInfRequest request)
        {
            var input = new GetYoyakuRaiinInfInputData(HpId, request.SinDate, request.PtId);
            var output = _bus.Handle(input);
            var presenter = new GetYoyakuRaiinInfPresenter();
            presenter.Complete(output);

            // NOTE: Success only
            var okStatus = new[] { GetYoyakuRaiinInfStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetYoyakuRaiinInfResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.GetRaiinInfBySinDate)]
        public ActionResult<Response<GetRaiinInfBySinDateResponse>> GetRaiinInfBySinDate([FromQuery] GetRaiinInfBySinDateRequest request)
        {
            var input = new GetRaiinInfBySinDateInputData(HpId, request.SinDate, request.PtId);
            var output = _bus.Handle(input);
            var presenter = new GetRaiinInfBySinDatePresenter();
            presenter.Complete(output);

            // NOTE: Success only
            var okStatus = new[] { GetRaiinInfBySinDateStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetRaiinInfBySinDateResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.GetHpInf)]
        public ActionResult<Response<GetHpInfResponse>> GetHpInf([FromQuery] GetHpInfRequest request)
        {
            var input = new GetHpInfInputData(HpId, request.SinDate);
            var output = _bus.Handle(input);
            var presenter = new GetHpInfPresenter();
            presenter.Complete(output);

            // NOTE: Success only
            var okStatus = new[] { GetHpInfStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetHpInfResponse>>(presenter.Result);
        }

        [HttpPut(ApiPath.RevertDeleteNoRecept)]
        public async Task<ActionResult<Response<RevertDeleteNoReceptResponse>>> RevertDeleteNoRecept(RevertDeleteNoReceptRequest request)
        {
            var input = new RevertDeleteNoReceptInputData(HpId, request.RaiinNo, request.PtId, request.SinDate);
            var output = _bus.Handle(input);

            if (output.Status == RevertDeleteNoReceptStatus.Success)
            {
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionChangedMessage(output.receptionModel, new()));
            }

            var presenter = new RevertDeleteNoReceptPresenter();
            presenter.Complete(output);

            var okStatus = new[] { RevertDeleteNoReceptStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<RevertDeleteNoReceptResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.GetReceptionCombine)]
        public ActionResult<Response<GetReceptionCombineResponse>> GetReceptionCombine([FromQuery] ReceptionCombineRequest request)
        {
            var input = new GetReceptionCombineInputData(HpId, request.SinDate, request.RaiinNo, request.HokenPid, request.PtId, request.IsCombined);
            var output = _bus.Handle(input);

            var presenter = new GetReceptionCombinePresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetReceptionCombineStatus.Success, GetReceptionCombineStatus.NoData };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetReceptionCombineResponse>>(presenter.Result);
        }

        [HttpPut(ApiPath.CombineSplitBill)]
        public ActionResult<Response<CombineSplitBillResponse>> CombineSplitBill(CombineSplitBillRequest request)
        {
            var input = new SaveCombineBillInputData(request.receptionCombines, UserId, request.IsSplit, request.RaiinNo);
            var output = _bus.Handle(input);
            var presenter = new SaveCombineSplitBillPresenter();
            presenter.Complete(output);

            var okStatus = new[] { SaveCombineBillStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<CombineSplitBillResponse>>(presenter.Result);
        }

        [HttpPut(ApiPath.DeletePatientFile)]
        public ActionResult<Response<DeletePatientFileResponse>> DeletePatientFile([FromBody] DeletePatientFileRequest request)
        {
            var input = new DeletePatientFileInputData(request.PtId, HpId, request.CategoryCd, request.FileId, UserId);
            var output = _bus.Handle(input);

            var present = new DeletePatientFilePresenter();
            present.Complete(output);

            var okStatus = new[] { DeletePatientFileStatus.Succeeded };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(present.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<DeletePatientFileResponse>>(present.Result);
        }

        [HttpGet(ApiPath.GetListFile)]
        public ActionResult<Response<GetPatientFileListResponse>> GetPatientFile([FromQuery] GetPatientFileRequest request)
        {
            var input = new GetPatientFileInputData(request.PtId, HpId, request.CategoryCd, request.FileName);
            var output = _bus.Handle(input);

            var presenter = new GetPatientFilePresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetPatientFileStatus.Successed, GetPatientFileStatus.NoData };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetPatientFileListResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.AddPatientFile)]
        public  ActionResult<Response<AddPatientFileResponse>> AddPatientFile([FromForm] AddPatientFileRequest request)
        {
            var input = new AddPatientFileInputData(request.PatientFileItems, HpId, request.UploadDate, UserId);
            var output = _bus.Handle(input);

            var presenter = new AddPatientFilePresenter();
            presenter.Complete(output);

            var okStatus = new[] { AddPatientFileStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<AddPatientFileResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.BookingFromCalendarOrPortal)]
        public async Task<ActionResult<Response<BookingFromCalendarOrPortalResponse>>> BookingFromCalendarOrPortal([FromBody] BookingFromCalendarOrPortalRequest request)
        {
            var input = new InsertFromCalendarOrPortalInputData(request.ReceptionModel, request.RaiinComment);
            var output = _bus.Handle(input);
            if (output.Status == InsertFromCalendarOrPortalStatus.Success)
            {
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionChangedMessageHasura(output.ReceptionInfos, new()));
            }
            var presenter = new InsertFromCalendarOrPortalPresenter();
            presenter.Complete(output);

            var okStatus = new[] { InsertFromCalendarOrPortalStatus.Success };
            if(!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<BookingFromCalendarOrPortalResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.UpdateBookingInfo)]
        public ActionResult<Response<UpdateBookingInfoResponse>> UpdateBookingInfo([FromBody] UpdateBookingInfoRequest request)
        {
            var input = new UpdateBookingInfoInputData(
                hpId: request.HpId,
                reserveDetailIds: request.ReserveDetailIds,
                sinDate: request.SinDate,
                yoyakuTime: request.YoyakuTime,
                yoyakuEndTime: request.YoyakuEndTime,
                treatmentDepartmentId: request.TreatmentDepartmentId,
                userId: request.UserId
            );

            var output = _bus.Handle(input);
            if(output.Status == UpdateBookingInfoStatus.Success)
            {
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionUpdateMessage(output.ReceptionList, new(), new()));
            }

            var presenter = new UpdateBookingInfoPresenter();
            presenter.Complete(output);

            var okStatus = new[] { UpdateBookingInfoStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<UpdateBookingInfoResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.UpdatePrescription)]
        public ActionResult<Response<UpdatePrescriptionResponse>> UpdatePrescription([FromBody] UpdatePrescriptionRequest request)
        {
            var input = new UpdatePrescriptionInputData(HpId, request.RaiinNo, UserId, request.PrescriptionIssueType, request.PrintEpsReference, request.CheckStatus);
            var output = _bus.Handle(input);
            //if (output.Status == UpdatePrescriptionStatus.Success)
            //{
            //    _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionDrawerChange, new ReceptionDrawerChange(output.receptionDto));
            //}
            var presenter = new UpdatePrescriptionPresenter();
            presenter.Complete(output);

            var okStatus = new[] { UpdatePrescriptionStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<UpdatePrescriptionResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.GetDefaultPrescription)]
        public ActionResult<Response<GetDefaultPrescriptionResponse>> GetDefaultPrescription([FromQuery] GetDefaultPrescriptionRequest request)
        {
            var input = new GetDefaultPrescriptionInputData(HpId, request.PtId, request.SinDate, request.RaiinNo);
            var output = _bus.Handle(input);
            var presenter = new GetDefaultPrescriptionPresenter();
            presenter.Complete(output);

            var okStatus = new[] { GetDefaultPrescriptionStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetDefaultPrescriptionResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.CheckLinkCard)]
        public ActionResult<Response<CheckLinkCardResponse>> CheckLinkCard()
        {
            var input = new CheckLinkCardInputData(HpId);
            var output = _bus.Handle(input);
            var presenter = new CheckLinkCardPresenter();
            presenter.Complete(output);

            var okStatus = new[] { CheckLinkCardStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<CheckLinkCardResponse>>(presenter.Result);
        }

        [HttpGet(ApiPath.ValidateReservation)]
        public ActionResult<Response<ValidateReservationResponse>> ValidateReservation([FromQuery] ValidateReservationRequest request)
        {
            var input = new ValidateReservationInputData(HpId, UserId, request.PtId, request.RaiinNo, request.SinDate);
            var output = _bus.Handle(input);
            var presenter = new ValidateReservationPresenter();
            presenter.Complete(output);

            var okStatus = new[] { ValidateReservationStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<ValidateReservationResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.UpdateRaiinCmtByBooking)]
        public ActionResult<Response<UpdateRaiinCmtByBookingResponse>> UpdateRaiinCmtByBooking([FromBody] UpdateRaiinCmtByBookingRequest request)
        {
            var input = new UpdateRaiinCmtByBookingInputData(request.HpId,
                                                             request.ReserveDetailId,
                                                             request.UserId,
                                                             request.RaiinCmt);

            var output = _bus.Handle(input);
            if (output.Status == UpdateRaiinCmtByBookingStatus.Success)
            {
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionUpdateMessage(output.ReceptionList, new(), new()));
            }

            var presenter = new UpdateRaiinCmtByBookingPresenter();
            presenter.Complete(output);

            var okStatus = new[] { UpdateRaiinCmtByBookingStatus.Success };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<UpdateRaiinCmtByBookingResponse>>(presenter.Result);
        }
    }
}
