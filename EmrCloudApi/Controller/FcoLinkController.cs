﻿using EmrCloudApi.Constants;
using EmrCloudApi.Messages;
using EmrCloudApi.Presenters.FcoLink;
using EmrCloudApi.Realtime;
using EmrCloudApi.Requests.FcoLink;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.FcoLink;
using Helper.Responses;
using Microsoft.AspNetCore.Mvc;
using UseCase.Core.Sync;
using UseCase.FcoLink.GetFcoLinkList;
using UseCase.FcoLink.SaveDeposits;
using EmrCloudApi.Realtime.Subscription;

namespace EmrCloudApi.Controller;

[Route("fco")]
public class FcoLinkController : BaseParamControllerBase
{
    private readonly UseCaseBus _bus;
    private readonly ISubscriptionService _subscriptionService;
    
    public FcoLinkController(UseCaseBus bus, IHttpContextAccessor httpContextAccessor, ISubscriptionService subscriptionService) : base(httpContextAccessor)
    {
        _bus = bus;
        _subscriptionService = subscriptionService;
    }
    
    // 未会計一覧取得API
    [HttpGet(ApiPath.UnaccountedTransactions)]
    public ActionResult<ResponseFco<GetFcoLinkListResponse>> GetFcoList([FromQuery] GetFcoLinkListRequest request)
    {
        var presenter = new GetFcoLinkListPresenter();
        if (!Request.Headers.TryGetValue("X-Api-Key", out var apiKey))
        {
            var errorResponse = new FcoRequestErrorResponse("R9999", presenter.GetMessage(GetFcoLinkListStatus.Unauthorized));
            return StatusCode(StatusCodes.Status401Unauthorized, errorResponse.ErrorResponse );
        }

        var input = new GetFcoLinkListInputData(apiKey, GetHpId(), request.PtNum, request.Date);
        var output = _bus.Handle(input);

        presenter.Complete(output);

        // 返却ステータスコード制御
        switch(output.Status){
            case GetFcoLinkListStatus.InvalidInputData:
            case GetFcoLinkListStatus.PtNodata:
                var errorResponse = new FcoRequestErrorResponse("R0001", presenter.GetMessage(output.Status));
                return BadRequest(errorResponse.ErrorResponse);
            case GetFcoLinkListStatus.Successed:
                break;
            case GetFcoLinkListStatus.Unauthorized:
                errorResponse = new FcoRequestErrorResponse("R9999", presenter.GetMessage(output.Status));
                return StatusCode(StatusCodes.Status401Unauthorized, errorResponse.ErrorResponse );
            case GetFcoLinkListStatus.Failed:
            default:
                errorResponse = new FcoRequestErrorResponse("R9999", presenter.GetMessage(output.Status));
                return StatusCode(StatusCodes.Status500InternalServerError, errorResponse.ErrorResponse);
         }

        return new ActionResult<ResponseFco<GetFcoLinkListResponse>>(presenter.Result);
    }

    //未会計入金API
    [HttpPost(ApiPath.Deposits)]
    public ActionResult<ResponseFco<SaveDepositsResponse>> SaveDeposits([FromBody] SaveDepositsRequest request)
    {
        var presenter = new SaveDepositsPresenter();
        if (!Request.Headers.TryGetValue("X-Api-Key", out var apiKey))
        {
            return StatusCode(StatusCodes.Status401Unauthorized, new { message = presenter.GetMessage(SaveDepositsStatus.Unauthorized)});
        }

        var input = new SaveDepositsInputData(apiKey, request.HpId, request.PtId, request.RaiinNo, request.Amount, request.Date, request.DepositMethod);
        var output = _bus.Handle(input);

        presenter.Complete(output);

        if (output.Status == SaveDepositsStatus.Successed)
        {
            _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionUpdateMessage(output.ReceptionInfos, output.SameVisitList, new()));
        }

        // 返却ステータスコード制御
        switch(output.Status){
            case SaveDepositsStatus.Successed:
                break;
            case SaveDepositsStatus.InvalidInputData:
            case SaveDepositsStatus.Nodata:
                var errorResponse =  new FcoRequestErrorResponse("R1001", presenter.GetMessage(output.Status));
                return BadRequest(errorResponse.ErrorResponse);
            case SaveDepositsStatus.UnMatchSeikyuGaku:
                errorResponse =  new FcoRequestErrorResponse("R1002", presenter.GetMessage(output.Status));
                return BadRequest(errorResponse.ErrorResponse);
            case SaveDepositsStatus.PaymentNodata:
                errorResponse =  new FcoRequestErrorResponse("R1003", presenter.GetMessage(output.Status));
                return BadRequest(errorResponse.ErrorResponse);
            case SaveDepositsStatus.ChangePayment:
                errorResponse =  new FcoRequestErrorResponse("R1004", presenter.GetMessage(output.Status));
                return BadRequest(errorResponse.ErrorResponse);
            case SaveDepositsStatus.Unauthorized:
                errorResponse = new FcoRequestErrorResponse("R9999", presenter.GetMessage(output.Status));
                return StatusCode(StatusCodes.Status401Unauthorized, errorResponse.ErrorResponse);
            case SaveDepositsStatus.Failed:
            default:
                errorResponse = new FcoRequestErrorResponse("R9999", presenter.GetMessage(output.Status));
                return StatusCode(StatusCodes.Status500InternalServerError, errorResponse.ErrorResponse);
        }

        return new ActionResult<ResponseFco<SaveDepositsResponse>>(presenter.Result);
    }

    private int GetHpId()
    {
        return int.TryParse(HttpContext.Request.Query["hpId"], out int hpId) ? hpId : 0;
    }
}
