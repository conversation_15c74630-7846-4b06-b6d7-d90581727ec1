﻿using Domain.Models.Online.BatchOnlineConfirmation;
﻿using Domain.Models.RaiinStatusMst;
using Domain.Models.Reception;
using EmrCloudApi.Constants;
using EmrCloudApi.Messages;
using EmrCloudApi.Presenters.Online;
using EmrCloudApi.Presenters.Online.ConvertxmlToOQS;
using EmrCloudApi.Presenters.Online.ProcessXMLJson;
using EmrCloudApi.Realtime;
using EmrCloudApi.Realtime.HasuraClient;
using EmrCloudApi.Requests.Online;
using EmrCloudApi.Requests.Online.ProcessXML;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.Online;
using Helper.Responses;
using EmrCloudApi.Responses.Online.ConvertXmlToOQSResponse;
using EmrCloudApi.Responses.Online.ProcessXMLResponse;
using Microsoft.AspNetCore.Mvc;
using System.Xml;
using EmrCloudApi.Realtime.HasuraClient;
using UseCase.Core.Sync;
using UseCase.Online;
using UseCase.Online.CancelPatientHomeVisit;
using UseCase.Online.ConvertXmlToOQSXmlMsg;
using UseCase.Online.GetBatchOnlineCheck;
using UseCase.Online.CreateOnlineConfirmationByXml;
using UseCase.Online.DeletedConfirmOnline;
using UseCase.Online.GetListOnlineConfirmationHistoryModel;
using UseCase.Online.GetOnlineConsent;
using UseCase.Online.GetPatientHomeVisit;
using UseCase.Online.GetRegisterdPatientsFromOnline;
using UseCase.Online.InsertOnlineConfirmation;
using UseCase.Online.InsertOnlineConfirmHistory;
using UseCase.Online.ProcessXMLOQS;
using UseCase.Online.SaveAllOQConfirmation;
using UseCase.Online.SaveOnlineConfirmation;
using UseCase.Online.SaveOQConfirmation;
using UseCase.Online.UpdateOnlineConfirmationHistory;
using UseCase.Online.UpdateOnlineConsents;
using UseCase.Online.UpdateOnlineHistoryById;
using UseCase.Online.UpdateOnlineInRaiinInf;
using UseCase.Online.UpdateOQConfirmation;
using UseCase.Online.UpdatePtInfOnlineQualify;
using UseCase.Online.UpdateRefNo;
using EmrCloudApi.Realtime.Subscription;
using UseCase.Reception.UpdateDynamicCell;
using UseCase.Online.SaveOnlineConfirmationHistory;
using EmrCloudApi.Requests.DeletedOnlineConfirmation;
using Microsoft.AspNetCore.Authorization;
using UseCase.Online.OnlineViewResultUpdateConfirmInput;
using System.Linq;
using UseCase.Online.GetViewResult;
using Newtonsoft.Json;
using UseCase.Online.GetViewResultInputData;

namespace EmrCloudApi.Controller;

[Route("api/[controller]")]
public class OnlineController : BaseParamControllerBase
{
    private readonly UseCaseBus _bus;
    private readonly ISubscriptionService _subscriptionService;
    public OnlineController(UseCaseBus bus, IHttpContextAccessor httpContextAccessor, ISubscriptionService subscriptionService) : base(httpContextAccessor)
    {
        _bus = bus;
        _subscriptionService = subscriptionService;
    }

    [HttpPost(ApiPath.InsertOnlineConfirmHistory)]
    public ActionResult<Response<InsertOnlineConfirmHistoryResponse>> InsertOnlineConfirmHistory([FromBody] InsertOnlineConfirmHistoryRequest request)
    {
        var onlineList = request.OnlineConfirmList.Select(item => new OnlineConfirmationHistoryItem(
                                                                      item.PtId,
                                                                      DateTime.MinValue,
                                                                      item.ConfirmationType,
                                                                      string.Empty,
                                                                      item.ConfirmationResult,
                                                                      0,
                                                                      item.UketukeStatus))
                                                  .ToList();

        var input = new InsertOnlineConfirmHistoryInputData(UserId, onlineList, HpId);
        var output = _bus.Handle(input);

        var presenter = new InsertOnlineConfirmHistoryPresenter();
        presenter.Complete(output);

        var okStatus = new[] { InsertOnlineConfirmHistoryStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<InsertOnlineConfirmHistoryResponse>>(presenter.Result);
    }

    [HttpGet(ApiPath.GetRegisterdPatientsFromOnline)]
    public ActionResult<Response<GetRegisterdPatientsFromOnlineResponse>> GetRegisterdPatientsFromOnline([FromQuery] GetRegisterdPatientsFromOnlineRequest request)
    {
        var input = new GetRegisterdPatientsFromOnlineInputData(request.SinDate, request.ConfirmType, request.Id, HpId);
        var output = _bus.Handle(input);

        var presenter = new GetRegisterdPatientsFromOnlinePresenter();
        presenter.Complete(output);

        // NOTE: Success only        
        var okStatus = new[] { GetRegisterdPatientsFromOnlineStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<GetRegisterdPatientsFromOnlineResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.UpdateOnlineConfirmationHistory)]
    public ActionResult<Response<UpdateOnlineConfirmationHistoryResponse>> UpdateOnlineConfirmationHistory([FromBody] UpdateOnlineConfirmationHistoryRequest request)
    {
        var input = new UpdateOnlineConfirmationHistoryInputData(request.Id, UserId, request.IsDeleted);
        var output = _bus.Handle(input);

        var presenter = new UpdateOnlineConfirmationHistoryPresenter();
        presenter.Complete(output);

        // NOTE: Success only  
        var okStatus = new[] { UpdateOnlineConfirmationHistoryStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<UpdateOnlineConfirmationHistoryResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.UpdateOnlineHistoryById)]
    public ActionResult<Response<UpdateOnlineHistoryByIdResponse>> UpdateOnlineHistoryById([FromBody] UpdateOnlineHistoryByIdRequest request)
    {
        var input = new UpdateOnlineHistoryByIdInputData(HpId, UserId, request.Id, request.PtId, request.UketukeStatus, request.ConfirmationType);
        var output = _bus.Handle(input);

        var presenter = new UpdateOnlineHistoryByIdPresenter();
        presenter.Complete(output);

        var okStatus = new[] { UpdateOnlineHistoryByIdStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<UpdateOnlineHistoryByIdResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.SaveOQConfirmation)]
    public async Task<ActionResult<Response<SaveOQConfirmationResponse>>> SaveOQConfirmation([FromBody] SaveOQConfirmationRequest request)
    {
        var input = new SaveOQConfirmationInputData(HpId, UserId, request.OnlineHistoryId, request.PtId, request.ConfirmationResult, request.OnlineConfirmationDate, request.ConfirmationType, request.InfConsFlg, request.UketukeStatus, request.IsUpdateRaiinInf);
        var output = _bus.Handle(input);

        if (output.Status == SaveOQConfirmationStatus.Successed)
        {
            _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionChangedMessage(output.ReceptionInfos, new()));
        }

        var presenter = new SaveOQConfirmationPresenter();
        presenter.Complete(output);

        var okStatus = new[] { SaveOQConfirmationStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<SaveOQConfirmationResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.UpdateRefNo)]
    public ActionResult<Response<UpdateRefNoResponse>> UpdateRefNo([FromBody] UpdateRefNoRequest request)
    {
        var input = new UpdateRefNoInputData(HpId, request.PtId, UserId);
        var output = _bus.Handle(input);

        var presenter = new UpdateRefNoPresenter();
        presenter.Complete(output);

        var okStatus = new[] { UpdateRefNoStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<UpdateRefNoResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.UpdateOnlineInRaiinInf)]
    public async Task<ActionResult<Response<UpdateOnlineInRaiinInfResponse>>> UpdateOnlineInRaiinInf([FromBody] UpdateOnlineInRaiinInfRequest request)
    {
        var input = new UpdateOnlineInRaiinInfInputData(HpId, UserId, request.PtId, request.OnlineConfirmationDate, request.ConfirmationType, request.InfConsFlg);
        var output = _bus.Handle(input);

        if (output.Status == UpdateOnlineInRaiinInfStatus.Successed)
        {
            _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionChangedMessage(output.ReceptionInfos, new()));
        }

        var presenter = new UpdateOnlineInRaiinInfPresenter();
        presenter.Complete(output);

        var okStatus = new[] { UpdateOnlineInRaiinInfStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<UpdateOnlineInRaiinInfResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.UpdateOQConfirmation)]
    public ActionResult<Response<UpdateOQConfirmationResponse>> UpdateOQConfirmation([FromBody] UpdateOQConfirmationRequest request)
    {
        Dictionary<string, (int confirmationType, string infConsFlg)> onlQuaConfirmationTypeDict = new();
        foreach (var item in request.OnlQuaConfirmationTypeDict)
        {
            onlQuaConfirmationTypeDict.Add(item.Key, (item.Value.ConfirmationType, item.Value.InfConsFlg));
        }
        var input = new UpdateOQConfirmationInputData(HpId, UserId, request.OnlineHistoryId, request.OnlQuaResFileDict, onlQuaConfirmationTypeDict);
        var output = _bus.Handle(input);

        var presenter = new UpdateOQConfirmationPresenter();
        presenter.Complete(output);

        var okStatus = new[] { UpdateOQConfirmationStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<UpdateOQConfirmationResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.SaveAllOQConfirmation)]
    public ActionResult<Response<SaveAllOQConfirmationResponse>> SaveAllOQConfirmation([FromBody] SaveAllOQConfirmationRequest request)
    {
        Dictionary<string, (int confirmationType, string infConsFlg)> onlQuaConfirmationTypeDict = new();
        foreach (var item in request.OnlQuaConfirmationTypeDict)
        {
            onlQuaConfirmationTypeDict.Add(item.Key, (item.Value.ConfirmationType, item.Value.InfConsFlg));
        }
        var input = new SaveAllOQConfirmationInputData(HpId, UserId, request.PtId, request.OnlQuaResFileDict, onlQuaConfirmationTypeDict);
        var output = _bus.Handle(input);

        var presenter = new SaveAllOQConfirmationPresenter();
        presenter.Complete(output);

        var okStatus = new[] { SaveAllOQConfirmationStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<SaveAllOQConfirmationResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.UpdatePtInfOnlineQualify)]
    public ActionResult<Response<UpdatePtInfOnlineQualifyResponse>> UpdatePtInfOnlineQualify([FromBody] UpdatePtInfOnlineQualifyRequest request)
    {
        var resultList = request.ResultList.Select(item => new PtInfConfirmationItem(item.AttributeName, item.CurrentValue, item.XmlValue)).ToList();
        var input = new UpdatePtInfOnlineQualifyInputData(HpId, UserId, request.PtId, resultList);
        var output = _bus.Handle(input);

        var presenter = new UpdatePtInfOnlineQualifyPresenter();
        presenter.Complete(output);

        var okStatus = new[] { UpdatePtInfOnlineQualifyStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<UpdatePtInfOnlineQualifyResponse>>(presenter.Result);
    }

    [HttpGet(ApiPath.GetListOnlineConfirmationHistoryByPtId)]
    public ActionResult<Response<GetListOnlineConfirmationHistoryModelResponse>> GetListOnlineConfirmationHistoryByPtId([FromQuery] GetListOnlineConfirmationHistoryByPtIdRequest request)
    {
        var input = new GetListOnlineConfirmationHistoryModelInputData(HpId, UserId, request.PtId, 0, new(), new());
        var output = _bus.Handle(input);

        var presenter = new GetListOnlineConfirmationHistoryModelPresenter();
        presenter.Complete(output);

        var okStatus = new[] { GetListOnlineConfirmationHistoryModelStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<GetListOnlineConfirmationHistoryModelResponse>>(presenter.Result);
    }

    [HttpGet(ApiPath.GetListOnlineConfirmationHistoryModel + "ById")]
    public ActionResult<Response<GetListOnlineConfirmationHistoryModelResponse>> GetListOnlineConfirmationHistoryModelById([FromQuery] long OnlineConfirmationHisId)
    {
        var input = new GetListOnlineConfirmationHistoryModelInputData(HpId, UserId, 0, OnlineConfirmationHisId, new(), new());
        var output = _bus.Handle(input);

        var presenter = new GetListOnlineConfirmationHistoryModelPresenter();
        presenter.Complete(output);

        var okStatus = new[] { GetListOnlineConfirmationHistoryModelStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<GetListOnlineConfirmationHistoryModelResponse>>(presenter.Result);
    }

    [HttpGet(ApiPath.GetOnlineConsent)]
    public ActionResult<Response<GetOnlineConsentResponse>> GetOnlineConsent([FromQuery] GetOnlineConsentRequest request)
    {
        var input = new GetOnlineConsentInputData(request.PtId, HpId, request.IsContainAgreedConsent);
        var output = _bus.Handle(input);

        var presenter = new GetOnlineConsentPresenter();
        presenter.Complete(output);

        // NOTE: Success only
        var okStatus = new[] { GetOnlineConsentStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<GetOnlineConsentResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.GetListOnlineConfirmationHistoryModel)]
    public ActionResult<Response<GetListOnlineConfirmationHistoryModelResponse>> GetListOnlineConfirmationHistoryModel([FromBody] GetListOnlineConfirmationHistoryModelRequest request)
    {
        Dictionary<string, (int confirmationType, string infConsFlg)> onlQuaConfirmationTypeDict = new();
        foreach (var item in request.OnlQuaConfirmationTypeDict)
        {
            onlQuaConfirmationTypeDict.Add(item.Key, (item.Value.ConfirmationType, item.Value.InfConsFlg));
        }
        var input = new GetListOnlineConfirmationHistoryModelInputData(HpId, UserId, 0, 0, request.OnlQuaResFileDict, onlQuaConfirmationTypeDict);
        var output = _bus.Handle(input);

        var presenter = new GetListOnlineConfirmationHistoryModelPresenter();
        presenter.Complete(output);

        var okStatus = new[] { GetListOnlineConfirmationHistoryModelStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<GetListOnlineConfirmationHistoryModelResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.ConvertXmlToQCXmlMsg)]
    public ActionResult<Response<ConvertXmlToQCXmlMsgResponse>> ConvertXmlToQCXmlMsgResponse([FromBody] ConvertXmlToQCXmlMsgRequest request)
    {
        Response<ConvertXmlToQCXmlMsgResponse> response = new();
        try
        {
            XmlDocument xmlDoc = new();
            xmlDoc.LoadXml(request.XmlString);
            response.Data = new ConvertXmlToQCXmlMsgResponse(request.XmlString);
            response.Message = ResponseMessage.Success;
            response.Status = 1;
        }
        catch
        {
            response.Message = ResponseMessage.InvalidConfirmationResult;
            response.Status = 2;
        }
        return new ActionResult<Response<ConvertXmlToQCXmlMsgResponse>>(response);
    }

    [HttpPost(ApiPath.UpdateOnlineConsents)]
    public ActionResult<Response<UpdateOnlineConsentsResponse>> UpdateOnlineConsents([FromBody] UpdateOnlineConsentsRequest request)
    {
        var input = new UpdateOnlineConsentsInputData(HpId, UserId, request.PtId, request.ResponseList);
        var output = _bus.Handle(input);

        var presenter = new UpdateOnlineConsentsPresenter();
        presenter.Complete(output);

        var okStatus = new[] { UpdateOnlineConsentsStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<UpdateOnlineConsentsResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.UpdateOnlineConfirmation)]
    public async Task<ActionResult<Response<UpdateOnlineConfirmationResponse>>> UpdateOnlineConfirmation([FromBody] UpdateOnlineConfirmationRequest request)
    {
        var input = new UpdateOnlineConfirmationInputData(HpId, UserId, request.ReceptionNumber, request.YokakuDate, request.QCBIDXmlMsgResponse);
        var output = _bus.Handle(input);

        if (output.Status == UpdateOnlineConfirmationStatus.Successed)
        {
            _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionChangedMessage(output.Receptions, new()));
        }

        var presenter = new UpdateOnlineConfirmationPresenter();
        presenter.Complete(output);

        var okStatus = new[] { UpdateOnlineConfirmationStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<UpdateOnlineConfirmationResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.InsertOnlineConfirmation)]
    public ActionResult<Response<InsertOnlineConfirmationResponse>> InsertOnlineConfirmation([FromBody] InsertOnlineConfirmationRequest request)
    {
        var input = new InsertOnlineConfirmationInputData(UserId, request.SinDate, request.ArbitraryFileIdentifier, request.QCBIXmlMsgResponse, HpId);
        var output = _bus.Handle(input);

        var presenter = new InsertOnlineConfirmationPresenter();
        presenter.Complete(output);

        var okStatus = new[] { InsertOnlineConfirmationStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<InsertOnlineConfirmationResponse>>(presenter.Result);
    }

    [HttpGet(ApiPath.GetPatientHomeVisit)]
    public ActionResult<Response<GetPatientHomeVisitResponse>> GetPatientHomeVisit([FromQuery] GetPatientHomeVisitRequest request)
    {
        var input = new GetPatientHomeVisitInputData(HpId, request.PtNum);
        var output = _bus.Handle(input);

        var presenter = new GetPatientHomeVisitPresenter();
        presenter.Complete(output);

        return new ActionResult<Response<GetPatientHomeVisitResponse>>(presenter.Result);
    }


    [HttpGet(ApiPath.GetBatchOnlineCheck)]

    public ActionResult<Response<GetBatchOnlineCheckResponse>> GetBatchOnlineCheck([FromQuery] GetBatchOnlineCheckRequest request)
    {
        var input = new GetBatchOnlineCheckInputData(HpId, request.BatchConfirmationType);
        var output = _bus.Handle(input);
        var presenter = new GetBatchOnlineCheckPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<GetBatchOnlineCheckResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.CancelPatientHomeVisit)]

    public ActionResult<Response<CancelPatientHomeVisitResponse>> CancelPatientHomeVisit([FromBody] CancelPatientHomeVisitRequest request)
    {
        var input = new CancelPatientHomeVisitInputData(HpId, request.PtId, UserId);
        var output = _bus.Handle(input);
        var presenter = new CancelPatientHomeVisitPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<CancelPatientHomeVisitResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.ConvertXmlToOQSsihvd01XmlMsg)]
    public ActionResult<Response<ConvertXmlToOQSsihvd01resResponse>> ConvertXmlToOQSsihvd01res([FromBody] ConvertXmlToOQSXmlMsgRequest request)
    {
        var input = new ConvertXmlToOQSXmlMsgInputData(HpId, OQSTypeFile.OQSsihvd01res, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ConvertXmlToOQSsihvd01resXmlMsgPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<ConvertXmlToOQSsihvd01resResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.ConvertXmlToOQSmutic01XmlMsg)]
    public ActionResult<Response<ConvertXmlToOQSmutic01Response>> ConvertXmlToOQSmutic01res([FromBody] ConvertXmlToOQSXmlMsgRequest request)
    {
        var input = new ConvertXmlToOQSXmlMsgInputData(HpId, OQSTypeFile.OQSmutic01res, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ConvertXmlToOQSmutic01Presenter();
        presenter.Complete(output);
        return new ActionResult<Response<ConvertXmlToOQSmutic01Response>>(presenter.Result);
    }

    [HttpPost(ApiPath.ConvertXmlToOQSmuquc01XmlMsg)]
    public ActionResult<Response<ConvertXmlToOQSmuquc01Response>> ConvertXmlToOQSmuquc01res([FromBody] ConvertXmlToOQSXmlMsgRequest request)
    {
        var input = new ConvertXmlToOQSXmlMsgInputData(HpId, OQSTypeFile.OQSmuquc01res, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ConvertXmlToOQSmuquc01Presenter();
        presenter.Complete(output);
        return new ActionResult<Response<ConvertXmlToOQSmuquc01Response>>(presenter.Result);
    }

    [HttpPost(ApiPath.ConvertXmlToOQSmuhvq01XmlMsg)]
    public ActionResult<Response<ConvertXmlToOQSmuhvq01Response>> ConvertXmlToOQSmuhvq01res([FromBody] ConvertXmlToOQSXmlMsgRequest request)
    {
        var input = new ConvertXmlToOQSXmlMsgInputData(HpId, OQSTypeFile.OQSmuhvq01res, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ConvertXmlToOQSmuhvq01Presenter();
        presenter.Complete(output);
        return new ActionResult<Response<ConvertXmlToOQSmuhvq01Response>>(presenter.Result);
    }

    [HttpPost(ApiPath.ConvertXmlToOQSmuonq01XmlMsg)]
    public ActionResult<Response<ConvertXmlToOQSmuonq01Response>> ConvertXmlToOQSmuonq01res([FromBody] ConvertXmlToOQSXmlMsgRequest request)
    {
        var input = new ConvertXmlToOQSXmlMsgInputData(HpId, OQSTypeFile.OQSmuonq01res, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ConvertXmlToOQSmuonq01Presenter();
        presenter.Complete(output);
        return new ActionResult<Response<ConvertXmlToOQSmuonq01Response>>(presenter.Result);
    }

    [HttpPost(ApiPath.ConvertXmlToOQSmutic02XmlMsg)]
    public ActionResult<Response<ConvertXmlToOQSmutic02Response>> ConvertXmlToOQSmutic02res([FromBody] ConvertXmlToOQSXmlMsgRequest request)
    {
        var input = new ConvertXmlToOQSXmlMsgInputData(HpId, OQSTypeFile.OQSmutic02res, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ConvertXmlToOQSmutic02Presenter();
        presenter.Complete(output);
        return new ActionResult<Response<ConvertXmlToOQSmutic02Response>>(presenter.Result);
    }

    [HttpPost(ApiPath.ConvertXmlToOQSmuquc02XmlMsg)]
    public ActionResult<Response<ConvertXmlToOQSmuquc02Response>> ConvertXmlToOQSmuquc02res([FromBody] ConvertXmlToOQSXmlMsgRequest request)
    {
        var input = new ConvertXmlToOQSXmlMsgInputData(HpId, OQSTypeFile.OQSmuquc02res, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ConvertXmlToOQSmuquc02Presenter();
        presenter.Complete(output);
        return new ActionResult<Response<ConvertXmlToOQSmuquc02Response>>(presenter.Result);
    }

    [HttpPost(ApiPath.ConvertXmlToOQSmuhvq02XmlMsg)]
    public ActionResult<Response<ConvertXmlToOQSmuhvq02Response>> ConvertXmlToOQSmuhvq02res([FromBody] ConvertXmlToOQSXmlMsgRequest request)
    {
        var input = new ConvertXmlToOQSXmlMsgInputData(HpId, OQSTypeFile.OQSmuhvq02res, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ConvertXmlToOQSmuhvq02Presenter();
        presenter.Complete(output);
        return new ActionResult<Response<ConvertXmlToOQSmuhvq02Response>>(presenter.Result);
    }

    [HttpPost(ApiPath.ConvertXmlToOQSmuonq02XmlMsg)]
    public ActionResult<Response<ConvertXmlToOQSmuonq02Response>> ConvertXmlToOQSmuonq02res([FromBody] ConvertXmlToOQSXmlMsgRequest request)
    {
        var input = new ConvertXmlToOQSXmlMsgInputData(HpId, OQSTypeFile.OQSmuonq02res, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ConvertXmlToOQSmuonq02Presenter();
        presenter.Complete(output);
        return new ActionResult<Response<ConvertXmlToOQSmuonq02Response>>(presenter.Result);
    }

    [HttpPost(ApiPath.ProcessXMLOQSmutic01res)]
    public ActionResult<Response<ProcessXMLResponse>> ProcessXMLOQSmutic01res([FromBody] ProcessXMLOQSmutic01Request request)
    {
        var input = new ProcessXMLOQSInputData(HpId, OQSXMLJson.OQSmutic01, UserId, request.Yoyaku_date, request.Sin_ym, request.Consent_from, request.Consent_to, request.Examination_from, request.Examination_to, request.Batch_confirmation_type, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ProcessXMLPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<ProcessXMLResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.ProcessXMLOQSmuhvq01res)]
    public ActionResult<Response<ProcessXMLResponse>> ProcessXMLOQSmuhvq01res([FromBody] ProcessXMLOQSmuhvq01Request request)
    {
        var input = new ProcessXMLOQSInputData(HpId, OQSXMLJson.OQSmuhvq01, UserId, request.Yoyaku_date, request.Sin_ym, request.Consent_from, request.Consent_to, request.Examination_from, request.Examination_to, request.Batch_confirmation_type, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ProcessXMLPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<ProcessXMLResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.ProcessXMLOQSmuonq01res)]
    public ActionResult<Response<ProcessXMLResponse>> ProcessXMLOQSmuonq01res([FromBody] ProcessXMLOQSmuonq01Request request)
    {
        var input = new ProcessXMLOQSInputData(HpId, OQSXMLJson.OQSmuonq01, UserId, request.Yoyaku_date, request.Sin_ym, request.Consent_from, request.Consent_to, request.Examination_from, request.Examination_to, request.Batch_confirmation_type, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ProcessXMLPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<ProcessXMLResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.ProcessXMLOQSmuquc01res)]
    public ActionResult<Response<ProcessXMLResponse>> ProcessXMLOQSmuquc01res([FromBody] ProcessXMLOQSmuquc01Request request)
    {
        var input = new ProcessXMLOQSInputData(HpId, OQSXMLJson.OQSmuquc01, UserId, request.Yoyaku_date, request.Sin_ym, request.Consent_from, request.Consent_to, request.Examination_from, request.Examination_to, request.Batch_confirmation_type, request.XmlString);
        var output = _bus.Handle(input);
        var presenter = new ProcessXMLPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<ProcessXMLResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.ProcessXmlOQSmuquc02res)]
    public ActionResult<Response<ProcessXMLResponse>> ProcessXmlOQSmuquc02res([FromBody] ProcessXmlOQSmuquc02resRequest request)
    {
        var input = new ProcessXmlOQSmuquc02resInputData(HpId, UserId, request.XmlString, request.SinDate, request.ReceptionNo);
        var output = _bus.Handle(input);
        var presenter = new ProcessXmlOQSmuquc02resPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<ProcessXMLResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.ProcessXmlOQSmutic02res)]
    public ActionResult<Response<ProcessXMLResponse>> ProcessXmlOQSmutic02res([FromBody] ProcessXmlOQSmutic02resRequest request)
    {
        var input = new ProcessXmlOQSmutic02resInputData(HpId, UserId, request.XmlString, request.SinDate, request.ReceptionNo);
        var output = _bus.Handle(input);
        var presenter = new ProcessXmlOQSmutic02resPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<ProcessXMLResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.ProcessXmlOQSmuhvq02res)]
    public ActionResult<Response<ProcessXMLResponse>> ProcessXmlOQSmuhvq02res([FromBody] ProcessOQSmuhvq02resRequest request)
    {
        var input = new ProcessXmlOQSmuhvq02resInputData(HpId, UserId, request.XmlString, request.SinDate, request.ReceptionNo);
        var output = _bus.Handle(input);
        var presenter = new ProcessXmlOQSmuhvq02resPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<ProcessXMLResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.ProcessXmlOQSmuonq02res)]
    public ActionResult<Response<ProcessXMLResponse>> ProcessXmlOQSmuonq02res([FromBody] ProcessXmlOQSmuonq02resRequest request)
    {
        var input = new ProcessXmlOQSmuonq02resInputData(HpId, UserId, request.XmlString, request.SinDate, request.ReceptionNo);
        var output = _bus.Handle(input);
        var presenter = new ProcessXmlOQSmuonq02resPresenter();
        presenter.Complete(output);
        return new ActionResult<Response<ProcessXMLResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.InsertOnlineConfirmation + "ByXml")]
    public async Task<ActionResult<Response<InsertOnlineConfirmationByXmlResponse>>> InsertOnlineConfirmationByXml([FromBody] CreatOnlineConfirmationByXmlRequest request)
    {
        var input = new CreateOnlineConfirmationByXmlInputData(HpId, request.XmlFileInfo, UserId, request.PmhStatus ?? 0, request.PmhResult);
        var output = _bus.Handle(input);

        var presenter = new CreateConfirmationOnlineByXml();
        presenter.Complete(output);
        if (output.Status == CreateOnlineConfirmationByXmlStatus.Successed)
        {
            if (output.ListReceptionForViewDtos.Any())
            {
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionUpdateMessage(output.ListReceptionForViewDtos, new List<SameVisitModel>(), new()));
            }
            else if (output.ListReceptionRowModels.Any())
            {
                _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionChangedMessage(output.ListReceptionRowModels, new List<SameVisitModel>()));
            }
        }

        var okStatus = new[] { CreateOnlineConfirmationByXmlStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<InsertOnlineConfirmationByXmlResponse>>(presenter.Result);
    }

    [HttpDelete(ApiPath.DeletedOnlineConfirmation)]
    public ActionResult<Response<DeletedOnlineConfirmationHisResponse>> DeletedOnlineConfirmation([FromQuery] DeletedOnlineConfirmationRequest inputData)
    {
        var input = new DeletedConfirmOnlineHisInputData(HpId, inputData.ConfirmationOnlineHisId, UserId);
        var output = _bus.Handle(input);
     
        if (output.Status == DeletedConfirmOnlineHisStatus.Succeeded)
        {
            _subscriptionService.ExecuteSubscription(FunctionCodes.ReceptionChanged, new ReceptionUpdateMessage(output.ReceptionForViewDtos, 0));
        }

        var presenter = new DeletedOnlineConfimationHisPresenter();
        presenter.Complete(output);

        var okStatus = new[] { DeletedConfirmOnlineHisStatus.Succeeded };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<DeletedOnlineConfirmationHisResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.SaveOnlineConfirmHistory)]
    public ActionResult<Response<SaveOnlineConfirmationHistoryResponse>> SaveOnlineConfirmHistory([FromBody] SaveOnlineConfirmationHistoryRequest request)
    {
        var input = new SaveOnlineConfirmationHisInputData(
            UserId,
            request.PtId,
            request.ConfirmationType,
            request.InfoConsFlg,
            request.ConfirmationResult,
            request.PrescriptionIssueType,
            request.UketukeStatus,
            HpId);
        var output = _bus.Handle(input);

        var presenter = new SaveOnlineConfirmationHistoryPresenter();
        presenter.Complete(output);

        return new ActionResult<Response<SaveOnlineConfirmationHistoryResponse>>(presenter.Result);
    }

    [HttpPost(ApiPath.OnlineViewResultUpdateConfirm)]
    public ActionResult<Response<OnlineViewResultUpdateConfirmResponse>> OnlineViewResultUpdateConfirm([FromBody] OnlineViewResultUpdateConfirmRequest request)
    {
        var input = new OnlineViewResultUpdateConfirmInput(
                HpId,
                request.PtId,
                request.OnlineDetailId,
                UserId,
                request.ConfirmType,
                request.XmlValue,
                request.IsRegisteredPatient
           );
        var output = _bus.Handle(input);

        var presenter = new OnlineViewResultUpdateConfirmPresenter();
        presenter.Complete(output);

        var okStatus = new[] { OnlineViewResultUpdateConfirmStatus.Successed };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }
        return new ActionResult<Response<OnlineViewResultUpdateConfirmResponse>>(presenter.Result);
    }

    [HttpGet(ApiPath.GetViewResult)]
    public ActionResult<Response<GetViewResultResponse>> GetViewResult([FromQuery] GetViewResultRequest request)
    {
        var input = new GetViewResultInputData(HpId, request.ReceptionNo, request.BatchConfirmationType, request.IsConfirmState, request.IsStatus);
        var output = _bus.Handle(input);
        
        var presenter = new GetViewResultPresenter();
        presenter.Complete(output);

        var okStatus = new[] { GetViewResultStatus.Success };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<GetViewResultResponse>>(presenter.Result);
    }

    [HttpGet(ApiPath.OnlineConfirmationDetailById)]
    public ActionResult<Response<GetOnlineConfirmationDetailByIdResponse>> OnlineConfirmationDetailById([FromQuery] long id)
    {
        var input = new GetOnlineConfirmationDetailByIdInputData(id);
        var output = _bus.Handle(input);

        var presenter = new GetOnlineConfirmationDetailByIdPresenter();
        presenter.Complete(output);

        var okStatus = new[] { GetOnlineConfirmationDetailByIdStatus.Success };
        if (!okStatus.Contains(output.Status))
        {
            var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
            return BadRequest(errorResponse.ErrorResponse);
        }

        return new ActionResult<Response<GetOnlineConfirmationDetailByIdResponse>>(presenter.Result);
    }

}
