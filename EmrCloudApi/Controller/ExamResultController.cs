﻿using EmrCloudApi.Constants;
using EmrCloudApi.Presenters.ExamResults;
using EmrCloudApi.Presenters.KarteVSPHYS;
using EmrCloudApi.Requests.ExamResult;
using EmrCloudApi.Requests.ExamResults;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.ExamResults;
using EmrCloudApi.Responses.KarteVSPHYS;
using Helper.Responses;
using Microsoft.AspNetCore.Mvc;
using UseCase.Core.Sync;
using UseCase.ExamResults.GetExamResults;
using UseCase.KarteVSPHYS.SaveKarteVSPHYS;

namespace EmrCloudApi.Controller
{
    [Route("api/[controller]")]
    public class ExamResultController : BaseParamControllerBase
    {
        private readonly UseCaseBus _bus;
        public ExamResultController(UseCaseBus bus, IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        {
            _bus = bus;
        }
        
        [HttpGet(ApiPath.GetExamResults)]
        public ActionResult<Response<GetExamResultsListResponse>> GetExamResults([FromQuery] GetExamResultListRequest request)
        {
            var input = new GetExamResultListInputData(HpId, request.PtId, request.StartDate, request.EndDate, request.KeyWord ?? string.Empty, request.TimeSequence, request.KensaItemCds ?? new(), request.CenterCd);
            var output = _bus.Handle(input);

            var presenter = new GetExamResultListPresenter();
            presenter.Complete(output);
            var okStatus = new[] { GetExamResultListStatus.Successed };
            
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<GetExamResultsListResponse>>(presenter.Result);
        }

        [HttpPost(ApiPath.Save)]
        public ActionResult<Response<SaveKarteVSPHYSResponse>> SaveExamResults([FromBody] SaveExamResultsRequest request)
        {
            var input = new SaveKarteVSPHYSInputData(HpId, UserId, request.PtId, request.Map(HpId), request.IsAdd, false);
            var output = _bus.Handle(input);

            var presenter = new SaveKarteVSPHYSPresenter();
            presenter.Complete(output);
            
            var okStatus = new[] { SaveKarteVSPHYSStatus.Successed };
            if (!okStatus.Contains(output.Status))
            {
                var errorResponse = new BadRequestErrorResponse(presenter.Result.Message);
                return BadRequest(errorResponse.ErrorResponse);
            }

            return new ActionResult<Response<SaveKarteVSPHYSResponse>>(presenter.Result);
        }
    }
}