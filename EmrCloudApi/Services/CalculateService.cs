﻿using Domain.Models.CalculateModel;
using Helper.Constants;
using Helper.Enum;
using Helper.Exceptions;
using Helper.Messaging;
using Helper.Messaging.Data;
using Infrastructure.Interfaces;
using Infrastructure.Logger;
using Interactor.CalculateService;
using Newtonsoft.Json;
using System.Net.Http;
using System.Text;
using UseCase.Accounting.GetMeiHoGai;
using UseCase.Accounting.Recaculate;
using UseCase.MedicalExamination.Calculate;
using UseCase.MedicalExamination.GetCheckedOrder;
using UseCase.Receipt.GetListReceInf;
using UseCase.Receipt.Recalculation;

namespace EmrCloudApi.Services
{
    public class CalculateService : ICalculateService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly ITenantProvider _tenantProvider;
        private IMessenger? _messenger;

        public CalculateService(IConfiguration configuration, IHttpContextAccessor httpContextAccessor, ITenantProvider tenantProvider, IHttpClientFactory httpClientFactory)
        {
            _configuration = configuration;
            _tenantProvider = tenantProvider;
            _httpClientFactory = httpClientFactory;
        }

        public async Task<CalculateResponse> CallCalculate(CalculateApiPath path, object inputData)
        {
            var content = JsonContent.Create(inputData);

            var basePath = _configuration.GetSection("CalculateApi")["BasePath"]!;
            string functionName = string.Empty;
            switch (path)
            {
                case CalculateApiPath.GetSinMeiList:
                    functionName = "SinMei/GetSinMeiList";
                    break;
                case CalculateApiPath.RunCalculate:
                    functionName = "Calculate/RunCalculate";
                    break;
                case CalculateApiPath.GetListReceInf:
                    functionName = "ReceFutan/GetListReceInf";
                    break;
                case CalculateApiPath.RunTrialCalculate:
                    functionName = "Calculate/RunTrialCalculate";
                    break;
                case CalculateApiPath.RunCalculateOne:
                    functionName = "Calculate/RunCalculateOne";
                    break;
                case CalculateApiPath.ReceFutanCalculateMain:
                    functionName = "ReceFutan/ReceFutanCalculateMain";
                    break;
                case CalculateApiPath.RunCalculateMonth:
                    functionName = "Calculate/RunCalculateMonth";
                    break;
                default:
                    throw new NotImplementedException("The Api Path Is Incorrect: " + path.ToString());
            }

            try
            {
                var client = _httpClientFactory.CreateClient("LongTimeoutClient");
                content.Headers.Add("domain", GetPropertyValue(inputData, "HpId").ToString());
                var response = await client.PostAsync($"{basePath}{functionName}", content);
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return new CalculateResponse(responseContent, ResponseStatus.Successed);
                }

                return new CalculateResponse(response.StatusCode.ToString(), ResponseStatus.Successed);

            }
            catch (Exception ex)
            {
                Console.WriteLine("Function CallCalculate " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        public async Task<CalculateResponse> CallCalculate(CalculateApiPath path, dynamic inputData, CancellationToken cancellationToken, IMessenger? messenger)
        {
            _messenger = messenger;

            var content = JsonContent.Create(inputData);

            var basePath = _configuration.GetSection("CalculateApi")["BasePath"]!;
            string functionName = string.Empty;
            switch (path)
            {
                case CalculateApiPath.GetSinMeiList:
                    functionName = "SinMei/GetSinMeiList";
                    break;
                case CalculateApiPath.RunCalculate:
                    functionName = "Calculate/RunCalculate";
                    break;
                case CalculateApiPath.GetListReceInf:
                    functionName = "ReceFutan/GetListReceInf";
                    break;
                case CalculateApiPath.RunTrialCalculate:
                    functionName = "Calculate/RunTrialCalculate";
                    break;
                case CalculateApiPath.RunCalculateOne:
                    functionName = "Calculate/RunCalculateOne";
                    break;
                case CalculateApiPath.ReceFutanCalculateMain:
                    functionName = "ReceFutan/ReceFutanCalculateMain";
                    break;
                case CalculateApiPath.RunCalculateMonth:
                    functionName = "Calculate/RunCalculateMonth";
                    break;
                default:
                    throw new NotImplementedException("The Api Path Is Incorrect: " + path.ToString());
            }

            try
            {
                var client = _httpClientFactory.CreateClient("LongTimeoutClient");
                content.Headers.Add("domain", GetPropertyValue(inputData, "HpId").ToString());
                var request = new HttpRequestMessage(HttpMethod.Post, $"{basePath}{functionName}");
                request.Content = content;
                // Call API streaming using SendAsync
                using var response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    if (path == CalculateApiPath.RunCalculateMonth || path == CalculateApiPath.ReceFutanCalculateMain)
                    {
                        using var stream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false);
                        using var reader = new StreamReader(stream);

                        while (!reader.EndOfStream && !cancellationToken.IsCancellationRequested)
                        {
                            var data = await reader.ReadLineAsync().ConfigureAwait(false);
                            if (string.IsNullOrEmpty(data))
                            {
                                if (!reader.EndOfStream)
                                {
                                    await Task.Delay(50, cancellationToken); 
                                }
                                continue;
                            }
                            
                            try
                            {
                                var objectStatus = System.Text.Json.JsonSerializer.Deserialize<RecalculationStatus>(data);
                                if (objectStatus != null && objectStatus.UniqueKey.Equals(inputData.UniqueKey))
                                {
                                    if (objectStatus.Type == CalculateStatusConstant.Invalid)
                                    {
                                        SendMessager(objectStatus);
                                        _ = _messenger?.SendAsync(new StopCalcStatus());
                                        break;
                                    }
                                    SendMessager(objectStatus);
                                    if (objectStatus.Done)
                                    {
                                        _ = _messenger?.SendAsync(new StopCalcStatus());
                                        break;
                                    }
                                }
                            }
                            catch (System.Text.Json.JsonException ex)
                            {
                                Console.WriteLine($"JSON parsing error: {ex.Message}");
                                break;
                            }
                        }

                        if (cancellationToken.IsCancellationRequested)
                        {
                            _ = _messenger?.SendAsync(new StopCalcStatus());
                        }

                        reader.Close();
                        stream.Close();
                    }
                }
                response.Dispose();
                return new CalculateResponse(response.StatusCode.ToString(), ResponseStatus.Successed);
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine("Function CallCalculate " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        private static object GetPropertyValue(object obj, string propertyName)
        {
            if (obj == null) throw new ArgumentNullException(nameof(obj));
            if (string.IsNullOrWhiteSpace(propertyName)) throw new ArgumentException("Property name cannot be null or whitespace.", nameof(propertyName));

            var propertyInfo = obj.GetType().GetProperty(propertyName);
            if (propertyInfo == null) throw new ArgumentException($"Property '{propertyName}' does not exist on type {obj.GetType().Name}.");

            return propertyInfo?.GetValue(obj) ?? string.Empty;
        }

        public SinMeiDataModelDto GetSinMeiList(GetSinMeiDtoInputData inputData)
        {
            try
            {
                var task = CallCalculate(CalculateApiPath.GetSinMeiList, inputData);

                if (task.Result.ResponseStatus != ResponseStatus.Successed)
                    return new();
                Console.WriteLine("Step-Debug" + task.Result.ResponseMessage + task.Result.ResponseStatus);
                var result = JsonConvert.DeserializeObject<SinMeiDataModelDto>(task.Result.ResponseMessage);
                return result ?? new();
            }
            catch (Exception ex)
            {
                Console.WriteLine("Function GetSinMeiList " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        public bool RunCalculate(RecaculationInputDto inputData)
        {
            try
            {
                var task = CallCalculate(CalculateApiPath.RunCalculate, inputData);
                if (task.Result.ResponseStatus != ResponseStatus.Successed)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Function RunCalculate " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        public ReceInfModelDto GetListReceInf(GetInsuranceInfInputData inputData)
        {
            try
            {
                var task = CallCalculate(CalculateApiPath.GetListReceInf, inputData);

                if (task.Result.ResponseStatus != ResponseStatus.Successed)
                    return new();

                var result = JsonConvert.DeserializeObject<ReceInfModelDto>(task.Result.ResponseMessage);
                return result ?? new();
            }
            catch (Exception ex)
            {
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        public RunTraialCalculateResponse RunTrialCalculate(RunTraialCalculateRequest inputData)
        {
            try
            {
                var task = CallCalculate(CalculateApiPath.RunTrialCalculate, inputData);
                if (task.Result.ResponseStatus == ResponseStatus.Successed)
                {
                    var result = JsonConvert.DeserializeObject<RunTraialCalculateResponse>(task.Result.ResponseMessage);
                    return new RunTraialCalculateResponse(result?.SinMeiList ?? new(), result?.KaikeiInfList ?? new(), result?.CalcLogList ?? new());
                }
                else
                {
                    return new RunTraialCalculateResponse(new(), new(), new());
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Function RunTrialCalculate " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        public bool RunCalculateOne(CalculateOneRequest inputData)
        {
            try
            {
                var task = CallCalculate(CalculateApiPath.RunCalculateOne, inputData);
                if (task.Result.ResponseStatus != ResponseStatus.Successed)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Function RunCalculateOne " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        /// <summary>
        /// function calls ReceFutanCalculateMain to other functions
        /// </summary>
        /// <param name="inputData"></param>
        /// <returns></returns>
        public bool ReceFutanCalculateMain(ReceCalculateRequest inputData)
        {
            try
            {
                var task = CallCalculate(CalculateApiPath.ReceFutanCalculateMain, inputData);
                task.Wait();
                if (task.Result.ResponseStatus != ResponseStatus.Successed)
                {
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Function ReceFutanCalculateMain " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        /// <summary>
        /// function calls ReceFutanCalculateMain only to calculate runs in month Rece
        /// </summary>
        /// <param name="inputData"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<bool> ReceFutanCalculateMain(ReceCalculateRequest inputData, CancellationToken cancellationToken, IMessenger? messenger)
        {
            try
            {
                await CallCalculate(CalculateApiPath.ReceFutanCalculateMain, inputData, cancellationToken, messenger);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Function ReceFutanCalculateMain " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        /// <summary>
        /// function calls RunCalculateMonth to other functions
        /// </summary>
        /// <param name="inputData"></param>
        /// <returns></returns>
        public bool RunCalculateMonth(CalculateMonthRequest inputData)
        {
            try
            {
                var task = CallCalculate(CalculateApiPath.RunCalculateMonth, inputData);
                task.Wait();
                if (task.Result.ResponseStatus != ResponseStatus.Successed)
                {
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Function RunCalculateMonth " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        /// <summary>
        /// function calls RunCalculateMonth only to calculate runs in month Rece
        /// </summary>
        /// <param name="inputData"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<bool> RunCalculateMonth(CalculateMonthRequest inputData, CancellationToken cancellationToken, IMessenger? messenger)
        {
            try
            {
                await CallCalculate(CalculateApiPath.RunCalculateMonth, inputData, cancellationToken, messenger);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Function RunCalculateMonth " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        public SinMeiDataModelDto GetSinMeiInMonthList(GetSinMeiDtoInputData inputData)
        {
            try
            {
                var task = CallCalculate(CalculateApiPath.GetSinMeiList, inputData);
                if (task.Result.ResponseStatus != ResponseStatus.Successed)
                {
                    return new();
                }
                var result = JsonConvert.DeserializeObject<SinMeiDataModelDto>(task.Result.ResponseMessage);
                return result ?? new();
            }
            catch (Exception ex)
            {
                Console.WriteLine("Function RunCalculateMonth " + ex);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name, ex);
            }
        }

        private void SendMessager(RecalculationStatus status)
        {
            _messenger?.Send(status);
        }


        public void ReleaseSource()
        {
            _tenantProvider.DisposeDataContext();
        }
    }
}
