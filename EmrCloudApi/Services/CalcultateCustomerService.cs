﻿using Helper;
using Helper.Exceptions;
using Infrastructure.Interfaces;
using Infrastructure.Logger;
using Interactor.CalculateService;
using Newtonsoft.Json;

namespace EmrCloudApi.Services
{
    public class CalcultateCustomerService : ICalcultateCustomerService
    {
        private readonly HttpClient _httpClient = new HttpClient();
        private readonly ITenantProvider _tenantProvider;

        public CalcultateCustomerService(IConfiguration configuration, ITenantProvider tenantProvider)
        {
            _httpClient.BaseAddress = new Uri(configuration.GetSection("CalculateApi")["BasePath"] ?? "");
            if (int.TryParse(configuration.GetSection("CalculateApi")["TimeoutSeconds"], out var timeoutSeconds))
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
            }
            _tenantProvider = tenantProvider;
        }

        public async Task<CalcultateCustomerResponse<T>> RunCaculationPostAsync<T>(TypeCalculate type, object input)
        {
            try
            {
                var content = JsonContent.Create(input);
                content.Headers.Add("domain", _tenantProvider.GetDomainFromHeader());
                HttpResponseMessage result = await _httpClient.PostAsync(type.GetDescription(), content);
                result.EnsureSuccessStatusCode();

                if (result.IsSuccessStatusCode)
                {
                    string resultContentStr = await result.Content.ReadAsStringAsync();
                    T resultContent = JsonConvert.DeserializeObject<T>(resultContentStr) ?? Activator.CreateInstance<T>();
                    return new CalcultateCustomerResponse<T>(resultContent, result.StatusCode, result.IsSuccessStatusCode);
                }
                else return new CalcultateCustomerResponse<T>(Activator.CreateInstance<T>(), result.StatusCode, result.IsSuccessStatusCode);
            }
            catch (Exception ex)
            {
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name,ex);
            }
            finally
            {
                _tenantProvider.DisposeDataContext();
            }
        }


        public async Task RunCaculationPostAsync(TypeCalculate type, object input)
        {
            try
            {
                var content = JsonContent.Create(input);
                content.Headers.Add("domain", _tenantProvider.GetDomainFromHeader());
                HttpResponseMessage result = await _httpClient.PostAsync(type.GetDescription(), content);
                result.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                Console.WriteLine("Err when run calculatePost api . details : " + ex.Message + " " + ex.InnerException);
                string name = GetType().Namespace ?? string.Empty;
                throw new ServiceCustomException(name,ex);
            }
        }
    }
}
