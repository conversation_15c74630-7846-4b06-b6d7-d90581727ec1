﻿using Domain.Models.Online;
using Domain.Models.Online.PMHsimsm01res;
using Domain.Models.Online.QualificationConfirmation;
using System.Xml.Serialization;

namespace EmrCloudApi.Responses.Online.Dto;

public class OnlineConfirmationHistoryDto
{
    public OnlineConfirmationHistoryDto(OnlineConfirmationHistoryModel model)
    {
        Id = model.Id;
        PtId = model.PtId;
        OnlineConfirmationDate = model.OnlineConfirmationDate;
        ConfirmationType = model.ConfirmationType;
        InfoConsFlg = model.InfoConsFlg;
        ConfirmationResult = model.ConfirmationResult;
        PrescriptionIssueType = model.PrescriptionIssueType;
        UketukeStatus = model.UketukeStatus;
        PmhStatus = model.PmhStatus;
        PmhResult = model.PmhResult;
    }

    public long Id { get; private set; }

    public long PtId { get; private set; }

    public DateTime OnlineConfirmationDate { get; private set; }

    public int ConfirmationType { get; private set; }

    public string InfoConsFlg { get; private set; }

    public string ConfirmationResult { get; private set; }

    public int PrescriptionIssueType { get; private set; }

    public int UketukeStatus { get; private set; }

    public QCXmlMsgResponse QCXmlMsgResponse { get => FormatXml(ConfirmationResult); }

    public int PmhStatus { get; private set; }

    public string PmhResult { get; private set; }

    public PMHsimsm01Response PMHsimsm01Response { get => ConvertPmhResultToModel(PmhResult); }

    public List<CertificationDetailItemDto> ListCertificationDetailItem { get => GetListCertificationDetailItem(PMHsimsm01Response); }

    private QCXmlMsgResponse FormatXml(string confirmationResult)
    {
        try
        {
            var response = new XmlSerializer(typeof(QCXmlMsgResponse)).Deserialize(new StringReader(confirmationResult)) as QCXmlMsgResponse;
            return response ?? new();
        }
        catch
        {
            return new();
        }
    }

    #region PMH

    private PMHsimsm01Response ConvertPmhResultToModel(string pmhResult)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(pmhResult))
                return new();

            var response = new XmlSerializer(typeof(PMHsimsm01Response)).Deserialize(new StringReader(pmhResult)) as PMHsimsm01Response;
            return response ?? new();
        }
        catch
        {
            return new();
        }
    }

    private List<CertificationDetailItemDto> GetListCertificationDetailItem(PMHsimsm01Response pmhRes)
    {
        var listItem = new List<CertificationDetailItemDto>();
        var medicalSubsidies = pmhRes?.MessageBody?.MedicalSubsidies?.ToList();

        if (medicalSubsidies == null || medicalSubsidies.Count == 0)
        {
            return listItem;
        }

        foreach (var item in medicalSubsidies)
        {
            listItem.Add(new CertificationDetailItemDto(GetListCertificationDetailItemLayer1(item)));
        }

        return listItem;
    }

    private List<CertificationDetailItemLayer1Dto> GetListCertificationDetailItemLayer1(MedicalSubsidies medicalSubsidies)
    {
        var listItemLayer1 = new List<CertificationDetailItemLayer1Dto>();
        var listDetailItem = medicalSubsidies?.CertificationDetail?.Item?.ToList();

        if (listDetailItem == null || listDetailItem.Count == 0)
        {
            return listItemLayer1;
        }

        var listCertificationDetailItem = listDetailItem.Select(item => new CertificationDetailItemModel(item))
                                                        .Where(item => item.IndexLayer1 > 0)
                                                        .OrderBy(item => item.IndexLayer1)
                                                        .GroupBy(item => item.IndexLayer1)
                                                        .ToList();

        foreach (var detailItemLayer1 in listCertificationDetailItem)
        {
            var itemLayer1 = detailItemLayer1.FirstOrDefault(item => item.IndexLayer2 == 0 && item.IndexLayer3 == 0);
            if (itemLayer1 == null)
            {
                continue;
            }

            var label1 = itemLayer1.Label;
            var value1 = itemLayer1.Value;
            var index1 = itemLayer1.Index;
            var sort1 = itemLayer1.IndexLayer1;

            var listItemLayer2 = new List<CertificationDetailItemLayer2Dto>();
            var listDetailItemLayer2 = detailItemLayer1.Where(item => item.IndexLayer2 > 0)
                                                       .OrderBy(item => item.IndexLayer2)
                                                       .GroupBy(item => item.IndexLayer2)
                                                       .ToList();
            var sort2 = 0;
            foreach (var detailItemLayer2 in listDetailItemLayer2)
            {
                sort2++;
                var itemLayer2 = detailItemLayer2.FirstOrDefault(item => item.IndexLayer3 == 0);
                if (itemLayer2 == null)
                {
                    continue;
                }

                var label2 = itemLayer2.Label;
                var value2 = itemLayer2.Value;
                var index2 = itemLayer2.Index;

                var listDetailItemLayer3 = detailItemLayer2.Where(item => item.IndexLayer3 > 0)
                                                           .OrderBy(item => item.IndexLayer3)
                                                           .ToList();

                if (listDetailItemLayer3.Count == 0)
                {
                    listItemLayer2.Add(new CertificationDetailItemLayer2Dto(label2, value2, index2, sort2));
                }
                else
                {
                    sort2--;
                    foreach (var detailItemLayer3 in listDetailItemLayer3)
                    {
                        sort2++;
                        var label3 = $"{label2}（{detailItemLayer3.Label}）";
                        var value3 = detailItemLayer3.Value;
                        var index3 = detailItemLayer3.Index;
                        listItemLayer2.Add(new CertificationDetailItemLayer2Dto(label3, value3, index3, sort2));
                    }
                }
            }

            listItemLayer1.Add(new CertificationDetailItemLayer1Dto(label1, value1, index1, sort1, listItemLayer2));
        }

        return listItemLayer1;
    }

    #endregion
}
