﻿using Domain.Models.Online.PMHsimsm01res;
using Helper.Extension;

namespace EmrCloudApi.Responses.Online.Dto
{
    public class CertificationDetailItemModel
    {
        public CertificationDetailItemModel(Item item)
        {
            Label = item.Label;
            Value = item.Value;
            Index = item.Index;
            SplitIndex(Index);
        }

        public string Label { get; set; }

        public string Value { get; set; }

        public string Index { get; set; }

        public int IndexLayer1 { get; set; }

        public int IndexLayer2 { get; set; }

        public int IndexLayer3 { get; set; }

        private void SplitIndex(string index)
        {
            IndexLayer1 = 0;
            IndexLayer2 = 0;
            IndexLayer3 = 0;

            if (string.IsNullOrWhiteSpace(index))
            {
                return;
            }

            var split = Index.Split('.');
            if (split.Length <= 0)
            {
                return;
            }

            if (split.Length > 0)
            {
                IndexLayer1 = split[0].AsInteger();
            }

            if (split.Length > 1)
            {
                IndexLayer2 = split[1].AsInteger();
            }

            if (split.Length > 2)
            {
                IndexLayer3 = split[2].AsInteger();
            }
        }
    }
}
