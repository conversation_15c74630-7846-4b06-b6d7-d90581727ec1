﻿using UseCase.Receipt.ReceiptListAdvancedSearch;

namespace EmrCloudApi.Responses.Receipt;

public class ReceiptListAdvancedSearchResponse
{
    public ReceiptListAdvancedSearchResponse(List<ReceiptListAdvancedSearchItem> receiptList)
    {
        ReceiptList = receiptList;
    }

    public int totakCount { get; private set; }
    public int displaytensu { get; private set; }
    public List<ReceiptListAdvancedSearchItem> ReceiptList { get; private set; }
}
