﻿using UseCase.PatientInfor.Save;

namespace EmrCloudApi.Responses.PatientInfor.BasicPatientInfo
{
    public class SaveBasicPatientInfoResponse
    {
        public SaveBasicPatientInfoResponse(IEnumerable<SavePatientInfoValidationResult> validateDetails, SavePatientInfoStatus state, long ptID, long raiinNo)
        {
            ValidateDetails = validateDetails;
            State = state;
            PtID = ptID;
            RaiinNo = raiinNo;
        }

        public IEnumerable<SavePatientInfoValidationResult> ValidateDetails { get; private set; }

        public SavePatientInfoStatus State { get; private set; }

        public long PtID { get; private set; }

        public long RaiinNo { get; private set; }
    }
}
