﻿using UseCase.PatientInfor.Save;

namespace EmrCloudApi.Responses.PatientInfor.BasicPatientInfo
{
    public class SaveInsuranceInfoResponse
    {
        public SaveInsuranceInfoResponse(IEnumerable<SavePatientInfoValidationResult> validateDetails, SavePatientInfoStatus state, long ptID, int hokenId, long onlineConfirmationHisId)
        {
            ValidateDetails = validateDetails;
            State = state;
            PtID = ptID;
            HokenId = hokenId;
            OnlineConfirmationHisId = onlineConfirmationHisId;
        }

        public IEnumerable<SavePatientInfoValidationResult> ValidateDetails { get; private set; }

        public SavePatientInfoStatus State { get; private set; }

        public long PtID { get; private set; }

        public int HokenId { get; private set; }

        public long OnlineConfirmationHisId { get; private set; }
    }
}
