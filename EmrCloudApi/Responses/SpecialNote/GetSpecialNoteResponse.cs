﻿using Domain.Models.ExamResults;
using Domain.Models.Family;
using Domain.Models.KarteAllergy;
using Domain.Models.KarteMedicalHistory;
using Domain.Models.PtCmtInf;
using Domain.Models.SpecialNote.ImportantNote;
using Domain.Models.SpecialNote.PatientInfo;
using Domain.Models.SpecialNote.SummaryInf;

namespace EmrCloudApi.Responses.SpecialNote
{
    public class GetSpecialNoteResponse
    {
        public GetSpecialNoteResponse()
        {
            SummaryInf = null;
            CmtInfItem = null;
            SeikaturekiInfItem = null;
        }
        public GetSpecialNoteResponse(KarteAllergyModel allergies, List<PtOtherDrugModel> otherDrugs, List<PtOtcDrugModel> otcDrugs, List<PtSuppleModel> supples, List<PtKioRekiModel> kioRekis,
            List<PtSmokingRelatedModel> socialHistorys, List<PtPregnancyRelatedModel> pregnants, List<PtFamilyRekiModel> families, List<PhysicalInfoModel> physicalInfos, List<ExamResultsModel> examResults, SummaryInfModel summaryInf,
            List<PtInfectionModel> ptInfectionModels, PtCmtInfModel ptCmtInfModel, SeikaturekiInfModel seikaturekiInfModel, List<PtPregnancyModel> pregnancyItems)
        {
            AlrgyFoodItems = allergies.AlrgyFoodItems;
            AlrgyElseItems = allergies.AlrgyElseItems;
            AlrgyDrugItems = allergies.AlrgyDrugItems;
            OtherDrugs = otherDrugs;
            OtcDrugs = otcDrugs;
            Supples = supples;
            KioRekis = kioRekis;
            SocialHistorys = socialHistorys;
            Pregnants = pregnants;
            Families = families;
            PhysicalInfos = physicalInfos;
            ExamResults = examResults;
            SummaryInf = summaryInf;
            InfectionList = ptInfectionModels;
            CmtInfItem = ptCmtInfModel;
            SeikaturekiInfItem = seikaturekiInfModel;
            PregnancyItems = pregnancyItems;
        }

        public List<PtAlrgyFoodModel> AlrgyFoodItems { get; private set; } = new List<PtAlrgyFoodModel>();

        public List<PtAlrgyElseModel> AlrgyElseItems { get; private set; } = new List<PtAlrgyElseModel>();

        public List<PtAlrgyDrugModel> AlrgyDrugItems { get; private set; } = new List<PtAlrgyDrugModel>();

        public List<PtOtherDrugModel> OtherDrugs { get; private set; } = new List<PtOtherDrugModel>();

        public List<PtOtcDrugModel> OtcDrugs { get; private set; } = new List<PtOtcDrugModel>();

        public List<PtSuppleModel> Supples { get; private set; } = new List<PtSuppleModel>();

        public List<PtKioRekiModel> KioRekis { get; private set; } = new List<PtKioRekiModel>();

        public List<PtSmokingRelatedModel> SocialHistorys { get; private set; } = new List<PtSmokingRelatedModel>();

        public List<PtPregnancyRelatedModel> Pregnants { get; private set; } = new List<PtPregnancyRelatedModel>();

        public List<PtFamilyRekiModel> Families { get; private set; } = new List<PtFamilyRekiModel>();

        public List<PhysicalInfoModel> PhysicalInfos { get; private set; } = new List<PhysicalInfoModel>();

        public List<ExamResultsModel> ExamResults { get; private set; } = new List<ExamResultsModel>();

        public SummaryInfModel SummaryInf { get; private set; }

        public List<PtInfectionModel> InfectionList { get; set; } = new List<PtInfectionModel>();

        public PtCmtInfModel CmtInfItem { get; set; }

        public SeikaturekiInfModel SeikaturekiInfItem { get; set; }

        public List<PtPregnancyModel> PregnancyItems { get; set; }
    }
}
