﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <ServerGarbageCollection>true</ServerGarbageCollection>
    <ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
    <RetainVMGarbageCollection>false</RetainVMGarbageCollection>
    <TieredCompilation>true</TieredCompilation>
    <GCLatencyMode>Interactive</GCLatencyMode>
  </PropertyGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="env.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.102.0" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="2.18.0" />
    <PackageReference Include="FakeItEasy" Version="7.3.1" />
    <PackageReference Include="itext7" Version="8.0.0" />
    <PackageReference Include="itext7.bouncy-castle-adapter" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="7.0.9" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.MessagePack" Version="7.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.StackExchangeRedis" Version="6.0.26" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="NCrontab.Signed" Version="3.3.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.Lolita.PostgreSQL" Version="1.1.0" />
    <PackageReference Include="Sentry" Version="4.13.0" />
    <PackageReference Include="Sentry.Profiling" Version="4.13.0" />
    <PackageReference Include="Serilog" Version="2.11.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="6.0.1" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="3.3.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.0.1" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.6" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
    <ProjectReference Include="..\Interactor\Interactor.csproj" />
    <ProjectReference Include="..\Reporting\Reporting.csproj" />
    <ProjectReference Include="..\UseCase\UseCase.csproj" />
    <ProjectReference Include="..\ErrorCodeGenerator\ErrorCodeGenerator.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="UseCase.Core">
      <HintPath>lib\UseCase.Core.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="TempFiles\Receiptc\" />
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties appsettings_1development_1json__JsonSchema="{" /></VisualStudio></ProjectExtensions>

</Project>
