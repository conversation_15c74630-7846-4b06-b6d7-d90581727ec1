﻿using Domain.Enum;
using Domain.Models.SpecialNote.PatientInfo;
using Helper.Constants;

namespace EmrCloudApi.Requests.ExamResult
{
    public class SaveExamResultsRequest
    {
        public bool IsAdd { get; set; } = true;

        public long PtId { get; set; }

        public long IraiCd { get; set; }

        public long RaiinNo { get; set; }

        public int IsDeleted { get; set; }

        public string KensaTime { get; set; } = string.Empty;

        public int IraiDate { get; set; }

        public List<SaveExamResultRequest> ExamResults { get; set; } = new List<SaveExamResultRequest>();

        public KensaInfModel Map(int hpId)
        {
            return new KensaInfModel(hpId, PtId, IraiDate, RaiinNo, IraiCd, (sbyte)InouKbnEnums.InHospital, (sbyte)KensaInfStatusEnums.CheckingCompleted, (sbyte)TosekiKbnEnums.NonDialysis, (sbyte)SikyuKbnEnums.Normal, (sbyte)ResultCheckEnums.Unconfirmed, CommonConstants.InHospitalCenterCd, string.Empty, string.Empty, string.Empty, IsDeleted, new List<KensaInfDetailModel>(ExamResults.Select(x => x.Map(hpId)).ToList()), KensaTime);
        }
    }

    public class SaveExamResultRequest
    {
        public long PtId { get; set; }

        public long IraiCd { get; set; }

        public long SeqNo { get; set; }

        public int IraiDate { get; set; }

        public long RaiinNo { get; set; }

        public string KensaItemCd { get; set; } = string.Empty;

        public string ResultVal { get; set; } = string.Empty;

        public int IsDeleted { get; set; }

        public string ResultType { get; set; } = string.Empty;

        public string AbnormalKbn { get; set; } = string.Empty;

        public KensaInfDetailModel Map(int hpId)
        {
            return new KensaInfDetailModel(hpId, PtId, IraiCd, SeqNo, IraiDate, RaiinNo, KensaItemCd, ResultVal, ResultType, AbnormalKbn, IsDeleted, string.Empty, string.Empty, DateTime.MinValue, string.Empty, string.Empty, 0, string.Empty);
        }
    }
}
