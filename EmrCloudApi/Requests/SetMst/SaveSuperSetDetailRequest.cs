﻿using Domain.Models.SuperSetDetail;

namespace EmrCloudApi.Requests.SetMst;

public class SaveSuperSetDetailRequest
{
    public long PtId { get; set; }

    public long RaiinNo { get; set; }

    public int SinDate { get; set; }

    public int SetCd { get; set; } = 0;

    public bool IsAddNew { get; set; } = false;

    public List<SaveSetByomeiRequestItem> SaveSetByomeiRequestItems { get; set; } = new();

    public SaveSetKarteRequestItem? SaveSetKarteRequestItem { get; set; } = null;

    public List<SaveSetOrderMstRequestItem> SaveSetOrderMstRequestItems { get; set; } = new();

    public SetInfoRequest SetInfo { get; set; } = new();
}
