﻿using Domain.Models.Family;

namespace EmrCloudApi.Requests.Family;

public class PtFamilyRequest
{
    public long FamilyId { get; set; }

    public long PtId { get; set; }

    public string ZokugaraCd { get; set; } = string.Empty;

    public string Cmt { get; set; } = string.Empty;

    public string? ZokugaraElse { get; set; } = string.Empty;

    public string? Byomei { get; set; } = string.Empty;

    public string? ByomeiCd { get; set; } = string.Empty;

    public string? ByotaiCd { get; set; } = string.Empty;

    public int SortNo { get; set; }

    public int SeqNo { get; set; }

    public bool IsDeleted { get; set; }

    public long Id { get; set; }

    public PtFamilyRekiModel Map(int hpId)
    {
        return new PtFamilyRekiModel(
            Id,
            ByomeiCd ?? string.Empty,
            Byomei ?? string.Empty,
            Cmt,
            SortNo,
            IsDeleted,
            hpId,
            PtId,
            FamilyId,
            SeqNo,
            ByotaiCd ?? string.Empty,
            ZokugaraCd,
            ZokugaraElse ?? string.Empty
        );
    }
}
