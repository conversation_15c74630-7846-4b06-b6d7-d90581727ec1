﻿using Domain.Models.Family;
using EmrCloudApi.Requests.Family;

namespace EmrCloudApi.Requests.KarteMedicalHistory
{
    public class SaveKarteMedicalHistoryFamilyRequest
    {
        public List<PtFamilyRequest> FamilyList { get; set; } = new();

        public List<PtFamilyRekiModel> Map(int hpId)
        {
            return new List<PtFamilyRekiModel>(FamilyList.Select(x => x.Map(hpId)).ToList());
        }
    }
}