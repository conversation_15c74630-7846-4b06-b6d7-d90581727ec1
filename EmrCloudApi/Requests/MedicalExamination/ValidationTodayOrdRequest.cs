﻿using Domain.Models.TodayOdr;

namespace EmrCloudApi.Requests.MedicalExamination
{
    public class ValidationTodayOrdRequest
    {
        public int SyosaiKbn { get; set; }
        public int JikanKbn { get; set; }
        public int HokenPid { get; set; }
        public int SanteiKbn { get; set; }
        public string UketukeTime { get; set; } = string.Empty;
        public string SinStartTime { get; set; } = string.Empty;
        public string SinEndTime { get; set; } = string.Empty;
        public byte Status { get; set; }
        public int KarteStatus { get; set; }
        public List<OdrInfItem> OdrInfs { get; set; } = new();
        public KarteItem KarteItem { get; set; } = new();
    }
}
