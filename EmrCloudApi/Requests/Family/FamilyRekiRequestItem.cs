﻿namespace EmrCloudApi.Requests.Family;

public class FamilyRekiRequestItem
{
    public long Id { get; set; }

    public int HpId { get; set; }

    public long PtId { get; set; }

    public long FamilyId { get; set; }

    public string ByomeiCd { get; set; } = string.Empty;

    public string ByotaiCd { get; set; } = string.Empty;

    public string Byomei { get; set; } = string.Empty;

    public string Cmt { get; set; } = string.Empty;

    public int SortNo { get; set; }

    public int SeqNo { get; set; }

    public bool IsDeleted { get; set; }

    public string ZokugaraCd { get; set; } = string.Empty;

    public string ZokugaraElse { get; set; } = string.Empty;
}
