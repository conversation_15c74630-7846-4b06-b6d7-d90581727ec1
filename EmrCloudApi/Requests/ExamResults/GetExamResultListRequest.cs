namespace EmrCloudApi.Requests.ExamResults;

public class GetExamResultListRequest
{
    public long PtId { get; set; }

    public long StartDate { get; set; }

    public long EndDate { get; set; }

    public string? KeyWord {  get; set; }

    public List<string>? KensaItemCds { get; set; }

    public bool TimeSequence { get; set; } = false;

    public string CenterCd { get; set; } = string.Empty;
}
