﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.Accounting;
using UseCase.Accounting.SaveAccounting;

namespace EmrCloudApi.Presenters.Accounting
{
    public class SaveAccountingPresenter
    {
        public Response<SaveAccountingResponse> Result { get; private set; } = new();
        public void Complete(SaveAccountingOutputData outputData)
        {
            Result.Data = new SaveAccountingResponse(outputData.Status == SaveAccountingStatus.Success, outputData.RaiinNoPrint);
            Result.Message = GetMessage(outputData.Status, outputData.UserMessage);
            Result.Status = (int)outputData.Status;
        }
        private string GetMessage(SaveAccountingStatus status, string userMessage) => status switch
        {
            SaveAccountingStatus.Success => ResponseMessage.Success,
            SaveAccountingStatus.Failed => ResponseMessage.Failed,
            SaveAccountingStatus.InputDataNull => ResponseMessage.InputSeikyuDataNull,
            SaveAccountingStatus.InvalidSumAdjust => ResponseMessage.InvalidSumAdjust,
            SaveAccountingStatus.InvalidThisWari => ResponseMessage.InvalidThisWari,
            SaveAccountingStatus.InvalidPayType => ResponseMessage.InvalidPayType,
            SaveAccountingStatus.InvalidComment => ResponseMessage.InvalidComment,
            SaveAccountingStatus.InvalidSindate => ResponseMessage.InvalidSinDate,
            SaveAccountingStatus.InvalidRaiinNo => ResponseMessage.InvalidRaiinNo,
            SaveAccountingStatus.NoPermission => ResponseMessage.NoPermission,
            SaveAccountingStatus.NotAllowedPartialNyukin => ResponseMessage.NotAllowedPartialNyukin,
            SaveAccountingStatus.CardError => userMessage,
            SaveAccountingStatus.NoDataSeikyu => ResponseMessage.NoDataSeikyu,
            SaveAccountingStatus.UnMatchSeikyuGaku => ResponseMessage.UnMatchSeikyuGaku,
            SaveAccountingStatus.InvalidNyukinGaku => ResponseMessage.InvalidNyukinGaku,
            SaveAccountingStatus.UnCollectedBalanceInFCO => ResponseMessage.UnCollectedPaymentNotSelectFCO,
            SaveAccountingStatus.UnMatchSeikyuAndNyukinInFCO => ResponseMessage.UnMatchSeikyuAndNyukinNotSelectFCO,
            SaveAccountingStatus.ValidPaymentAmount => ResponseMessage.ValidPaymentAmount,
            _ => string.Empty
        };
    }
}