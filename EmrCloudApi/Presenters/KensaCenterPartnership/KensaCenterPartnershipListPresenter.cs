﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.KensaCenterPartnership;
using UseCase.KensaCenterPartnership;

namespace EmrCloudApi.Presenters.KensaCenterPartnership;


public class GetKensaCenterPartnershipListPresenter : IGetKensaCenterPartnershipOutputPort
{
    public Response<KensaCenterPartnershipListResponse> Result { get; private set; } = default!;

    public void Complete(GetKensaCenterPartnershipOutputData outputData)
    {
        Result = new Response<KensaCenterPartnershipListResponse>()
        {
            Data = new KensaCenterPartnershipListResponse(outputData.KensaCenterPartnerships),
            Status = (byte)outputData.Status
        };
        switch (outputData.Status)
        {
            case GetKensaCenterPartnershipStatus.Successful:
                Result.Message = ResponseMessage.Success;
                break;
            case GetKensaCenterPartnershipStatus.NoData:
                Result.Message = ResponseMessage.NoData;
                break;
        }
    }
}

public class RegisterKensaCenterPartnershipListPresenter : IRegisterKensaCenterPartnershipOutputPort
{
    public Response<KensaCenterPartnershipResponse> Result { get; private set; } = default!;

    public void Complete(RegisterKensaCenterPartnershipOutputData outputData)
    {
        Result = new Response<KensaCenterPartnershipResponse>()
        {
            Data = new KensaCenterPartnershipResponse(outputData.KensaCenterPartnership),
            Status = (byte)outputData.Status
        };
        switch (outputData.Status)
        {
            case RegisterKensaCenterPartnershipStatus.Successful:
                Result.Message = ResponseMessage.Success;
                break;
            case RegisterKensaCenterPartnershipStatus.NoData:
                Result.Message = ResponseMessage.NoData;
                break;
            case RegisterKensaCenterPartnershipStatus.AleadyExists:
                Result.Message = ResponseMessage.KensaCenterPartnershipAlreadyExists;
                break;
        }
    }
}

public class UpdateKensaCenterPartnershipPresenter : IUpdateKensaCenterPartnershipOutputPort
{
    public Response<KensaCenterPartnershipResponse> Result { get; private set; } = default!;

    public void Complete(UpdateKensaCenterPartnershipOutputData outputData)
    {
        Result = new Response<KensaCenterPartnershipResponse>()
        {
            Data = new KensaCenterPartnershipResponse(outputData.KensaCenterPartnership),
            Status = (byte)outputData.Status
        };
        switch (outputData.Status)
        {
            case UpdateKensaCenterPartnershipStatus.Successful:
                Result.Message = ResponseMessage.Success;
                break;
            case UpdateKensaCenterPartnershipStatus.NoData:
                Result.Message = ResponseMessage.NoData;
                break;
        }
    }
}

public class UnregisterKensaCenterPartnershipPresenter : IUnregisterKensaCenterPartnershipOutputPort
{
    public Response<KensaCenterPartnershipResponse> Result { get; private set; } = default!;

    public void Complete(UnregisterKensaCenterPartnershipOutputData outputData)
    {
        Result = new Response<KensaCenterPartnershipResponse>()
        {
            Status = (byte)outputData.Status
        };
        switch (outputData.Status)
        {
            case UnregisterKensaCenterPartnershipStatus.Successful:
                Result.Message = ResponseMessage.Success;
                break;
            case UnregisterKensaCenterPartnershipStatus.NoData:
                Result.Message = ResponseMessage.NoData;
                break;
            case UnregisterKensaCenterPartnershipStatus.AlreadyDeleted:
                Result.Message = ResponseMessage.KensaCenterPartnershipAlreadyDeleted;
                break;
        }
    }
}

public class UpdateKensaCenterPartnershipMstUpdateDatePresenter : IUpdateKensaCenterPartnershipMstUpdateDateOutputPort
    {
        public Response<KensaCenterPartnershipResponse> Result { get; private set; } = default!;

        public void Complete(UpdateKensaCenterPartnershipMstUpdateDateOutputData outputData)
        {
            Result = new Response<KensaCenterPartnershipResponse>()
            {
                Status = (byte)outputData.Status
            };
            switch (outputData.Status)
            {
                case UpdateKensaCenterPartnershipMstUpdateDateStatus.Successful:
                    Result.Message = ResponseMessage.Success;
                    break;
                case UpdateKensaCenterPartnershipMstUpdateDateStatus.NoData:
                    Result.Message = ResponseMessage.NoData;
                    break;
            }
        }
    }
