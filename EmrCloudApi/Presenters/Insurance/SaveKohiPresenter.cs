﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.Insurance;
using UseCase.Insurance.SaveKohi;

namespace EmrCloudApi.Presenters.Insurance
{
    public class SaveKohiPresenter : ISaveKohiOutputPort
    {
        public Response<SaveKohiResponse> Result { get; private set; } = new();

        public void Complete(SaveKohiOutputData outputData)
        {
            Result.Data = new SaveKohiResponse(outputData.Status == SaveKohiStatus.Successed, outputData.KohiId, outputData.OnlineConfirmationHisId);
            Result.Message = GetMessage(outputData.Status);
            Result.Status = (int)outputData.Status;
        }

        private string GetMessage(SaveKohiStatus status) => status switch
        {
            SaveKohiStatus.Successed => ResponseMessage.Success,
            SaveKohiStatus.InvalidPtId => ResponseMessage.InvalidPtId,
            _ => string.Empty
        };
    }
}