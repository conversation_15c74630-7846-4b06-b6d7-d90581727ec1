﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.Cache;
using UseCase.Cache.RemoveCache;

namespace EmrCloudApi.Presenters.Cache
{
    public class RemoveCachePresenter : IRemoveCacheOutputPort
    {
        public Response<RemoveCacheResponse> Result { get; private set; } = new();
        public void Complete(RemoveCacheOutputData outputData)
        {
            Result.Data = new RemoveCacheResponse(outputData.Status == RemoveCacheStatus.Successed);
            Result.Message = GetMessage(outputData.Status);
            Result.Status = (int)outputData.Status;
        }

        private string GetMessage(RemoveCacheStatus status) => status switch
        {
            RemoveCacheStatus.Successed => ResponseMessage.Success,
            RemoveCacheStatus.Failed => ResponseMessage.Failed,
            _ => string.Empty
        };
    }
}
