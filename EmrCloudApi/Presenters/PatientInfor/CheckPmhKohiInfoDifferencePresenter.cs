﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.PatientInfor;
using UseCase.PatientInfor.CheckPmhKohiInfoDifference;

namespace EmrCloudApi.Presenters.PatientInfor
{
    public class CheckPmhKohiInfoDifferencePresenter : ICheckPmhKohiInfoDifferenceOutputPort
    {
        public Response<CheckPmhKohiInfoDifferenceResponse> Result { get; private set; } = new();

        public void Complete(CheckPmhKohiInfoDifferenceOuputData outputData)
        {
            Result.Data = new CheckPmhKohiInfoDifferenceResponse(outputData.CheckPmhKohiInfoDifferenceDto);
            Result.Status = (int)outputData.Status;
            Result.Message = GetMessage(outputData.Status);
        }

        private string GetMessage(CheckPmhKohiInfoDifferenceStatus status) => status switch
        {
            CheckPmhKohiInfoDifferenceStatus.InvalidHpId => ResponseMessage.InvalidHpId,
            CheckPmhKohiInfoDifferenceStatus.InvalidPtId => ResponseMessage.InvalidPtId,
            CheckPmhKohiInfoDifferenceStatus.InvalidSinDate => ResponseMessage.InvalidSinDate,
            CheckPmhKohiInfoDifferenceStatus.Failed => ResponseMessage.Failed,
            CheckPmhKohiInfoDifferenceStatus.Success => ResponseMessage.Success,
            _ => string.Empty
        };
    }
}
