﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.PatientInfor;
using UseCase.PatientInfor.GetPmhKohiDefault;

namespace EmrCloudApi.Presenters.PatientInfor
{
    public class GetPmhKohiDefaultPresenter : IGetPmhKohiDefaultOutputPort
    {
        public Response<GetPmhKohiDefaultResponse> Result { get; private set; } = new();

        public void Complete(GetPmhKohiDefaultOuputData outputData)
        {
            Result.Data = new GetPmhKohiDefaultResponse(outputData.GetPmhKohiDefaultDto);
            Result.Status = (int)outputData.Status;
            Result.Message = GetMessage(outputData.Status);
        }

        private string GetMessage(GetPmhKohiDefaultStatus status) => status switch
        {
            GetPmhKohiDefaultStatus.InvalidHpId => ResponseMessage.InvalidHpId,
            GetPmhKohiDefaultStatus.InvalidSinDate => ResponseMessage.InvalidSinDate,
            GetPmhKohiDefaultStatus.Failed => ResponseMessage.Failed,
            GetPmhKohiDefaultStatus.Success => ResponseMessage.Success,
            _ => string.Empty
        };
    }
}
