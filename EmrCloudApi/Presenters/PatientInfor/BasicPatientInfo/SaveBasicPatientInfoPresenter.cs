﻿using EmrCloudApi.Constants;
using EmrCloudApi.Responses;
using EmrCloudApi.Responses.PatientInfor.BasicPatientInfo;
using UseCase.PatientInfor.Save;
using UseCase.PatientInfor.SaveBasicInfo.SavePatient;

namespace EmrCloudApi.Presenters.PatientInfor.BasicPatientInfo
{
    public class SaveBasicPatientInfoPresenter : ISaveBasicPatientInforOutputData
    {
        public Response<SaveBasicPatientInfoResponse> Result { get; private set; } = new Response<SaveBasicPatientInfoResponse>();

        private string GetMessage(SavePatientInfoStatus status) => status switch
        {
            SavePatientInfoStatus.Successful => ResponseMessage.Success,
            SavePatientInfoStatus.Failed => ResponseMessage.Failed,
            _ => string.Empty
        };

        public void Complete(SaveBasicPatientInforOutputData outputData)
        {
            Result.Data = new SaveBasicPatientInfoResponse(outputData.ValidateDetails, outputData.Status, outputData.PtID, outputData.RaiinNo);
            Result.Status = (int)outputData.Status;
            Result.Message = GetMessage(outputData.Status);
        }
    }
}
