﻿namespace EmrCloudApi.Constants
{
    public static class ResponseMessage
    {
        //Invalid parameter
/*
        public static readonly string InvalidKeyword = "Invalid keyword";
        public static readonly string InvalidHpId = "Invalid HpId";
        public static readonly string InvalidPtId = "Invalid PtId";
        public static readonly string InvalidFileId = "Invalid FileId";
        public static readonly string InvalidRaiinNo = "Invalid RaiinNo";
        public static readonly string InvalidSinDate = "Invalid SinDate";
        public static readonly string InvalidHokenId = "Invalid HokenId";
        public static readonly string InvalidPageIndex = "Invalid PageIndex";
        public static readonly string InvalidPageCount = "Invalid PageCount";
        public static readonly string InvalidPageSize = "Invalid PageSize";
        public static readonly string InvalidStartIndex = "Invalid StartIndex";
        public static readonly string InvalidKouiKbn = "Invalid KouiKbn";
        public static readonly string InvalidValueAdopted = "Invalid Value Adopted";
        public static readonly string InvalidItemCd = "Invalid ItemCd";
        public static readonly string InvalidFutansyaNo = "Invalid FutansyaNo";
        public static readonly string InvalidUsageKbn = "Invalid UsageKbn";
        public static readonly string InvalidKohiId = "Invalid HokenKohiId";
        public static readonly string InvalidGrpCd = "Invalid GrpCd";
        public static readonly string InvalidGrpEdaNo = "Invalid GrpEdaNo";
        public static readonly string InvalidDefaultValue = "Invalid DefaultValue";
        public static readonly string InvalidPresentDate = "Invalid PresentDate";
        public static readonly string InvalidHpIdNotExist = "HpId not exist";
        public static readonly string InvalidStartDate = "Invalid startDate";
        public static readonly string InvalidStartDateEndDate = "Invalid startDate endDate";
        public static readonly string InvalidYJCode = "Invalid YJCode";
        public static readonly string InvalidIsDeleted = "Invalid isDeleted";
        public static readonly string InvalidSeqNo = "Invalid SeqNo";
        public static readonly string InvalidDate = "Invalid Date";
        public static readonly string InvalidValue = "Invalid Value ";
        public static readonly string InvalidHokenEdraNo = "Invalid HokenEdraNo";
        public static readonly string InvalidTantoId = "Invalid TantoId";
        public static readonly string InvalidAdoptedValue = "Invalid Adopted Value";
        public static readonly string InvalidToken = "Invalid Token";
        public static readonly string InvalidCurrentIndex = "Invalid CurrentIndex";
        public static readonly string InvalidWindowType = "Invalid WindowType";
        public static readonly string InvalidFrameId = "Invalid FrameId";
        public static readonly string InvalidOyaRaiinNo = "Invalid OyaRaiinNo";
        public static readonly string InvalidPrimaryDoctor = "Invalid PrimaryDoctor";
        public static readonly string InvalidDetailInfs = "Invalid Details";
        public static readonly string InvalidOrderInfs = "Invalid OrderInfs";
        public static readonly string InvalidUserConfs = "Invalid UserConfs";
        public static readonly string DuplicateUserConf = "Duplicate UserConf";
        public static readonly string InvalidGrpItemCd = "Invalid GrpItemCd";
        public static readonly string InvalidGrpItemEdaNo = "Invalid GrpItemEdaNo";
        public static readonly string InvalidParam = "Invalid Param";
        public static readonly string InvalidInfoType = "Invalid InfoType";
        public static readonly string InvalidDayOfWeek = "Invalid DayOfWeek";
        public static readonly string InvalidOdrKouiKbn = "Invalid OdrKouiKbn";
        public static readonly string InvalidGrpKouiKbn = "Invalid GrpKouiKbn";
        public static readonly string InvalidDocCd = "Invalid DocCd";
        public static readonly string InvalidRenkeiId = "Invalid RenkeiId";
        public static readonly string InvalidCenterCd = "Invalid CenterCd";
        public static readonly string InvalidTreatmentDepartmentId = "Invalid TreatmentDepartmentId";
        public static readonly string ErrorBunkatuOdrDetail = "入力できる文字を超えました。\r\n 合計10文字以内に入力してください。";
        public static readonly string InvalidDrugName = "Invalid Drug Name.";
        public static readonly string InvalidRangeDate = "Invalid Range Date.";
        public static readonly string InvalidCmtLengthNotEquals100 = "Invalid Cmt Length Not Equals 100";
*/

        public static readonly string InvalidKeyword ="キーワードが誤っています。";
        public static readonly string InvalidHpId ="医療機関識別IDが誤っています。";
        public static readonly string InvalidPtId ="患者IDが誤っています。";
        public static readonly string InvalidOnlineConfirmationHistory = "無効なオンライン確認履歴ID";
        public static readonly string InvalidFileId = "ファイルIDが誤っています。";
        public static readonly string InvalidRaiinNo ="連番が誤っています。";
        public static readonly string InvalidSinDate ="診察日が誤っています。";
        public static readonly string InvalidHokenId ="保険IDが誤っています。";
        public static readonly string InvalidPageIndex ="ページ番号が誤っています。";
        public static readonly string InvalidPageCount ="ページ数が誤っています。";
        public static readonly string InvalidPageSize ="ページサイズが誤っています。";
        public static readonly string InvalidStartIndex ="開始位置が誤っています。";
        public static readonly string InvalidKouiKbn ="行為区分が誤っています。";
        public static readonly string InvalidValueAdopted ="入力値が誤っています。";
        public static readonly string InvalidItemCd ="項目コードが誤っています。";
        public static readonly string InvalidFutansyaNo ="負担者番号が誤っています。";
        public static readonly string InvalidUsageKbn ="使用法区分が誤っています。";
        public static readonly string InvalidKohiId ="公費IDが誤っています。";
        public static readonly string InvalidGrpCd ="グループCDが誤っています。";
        public static readonly string InvalidGrpEdaNo ="グループ枝番が誤っています。";
        public static readonly string InvalidDefaultValue ="入力値が誤っています。";
        public static readonly string InvalidPresentDate ="現在日が誤っています。";
        public static readonly string InvalidHpIdNotExist ="医療機関識別IDが存在しません。";
        public static readonly string InvalidStartDate ="開始日が誤っています。";
        public static readonly string InvalidStartDateEndDate = "開始・終了日が誤っています。";
        public static readonly string InvalidYJCode ="力価情報が誤っています。";
        public static readonly string InvalidIsDeleted ="削除区分が誤っています。";
        public static readonly string InvalidSeqNo ="シーケンス番号が誤っています。";
        public static readonly string InvalidDate ="日付指定が誤っています。";
        public static readonly string InvalidValue ="入力値が誤っています。";
        public static readonly string InvalidHokenEdraNo ="入力値が誤っています。";
        public static readonly string InvalidTantoId ="担当医IDが誤っています。";
        public static readonly string InvalidAdoptedValue ="入力値が誤っています。";
        public static readonly string InvalidToken ="トークンが誤っています。";
        public static readonly string InvalidCurrentIndex ="入力値が誤っています。";
        public static readonly string InvalidWindowType ="ウインドウタイプが誤っています。";
        public static readonly string InvalidFrameId ="フレームIDが誤っています。";
        public static readonly string InvalidOyaRaiinNo ="親来院番号が誤っています。";
        public static readonly string InvalidPrimaryDoctor ="主治医コードが誤っています。";
        public static readonly string InvalidDetailInfs ="入力値が誤っています。";
        public static readonly string InvalidOrderInfs ="オーダ情報が誤っています。";
        public static readonly string InvalidUserConfs ="ユーザ情報が誤っています。";
        public static readonly string DuplicateUserConf ="ユーザ情報が誤っています。";
        public static readonly string InvalidGrpItemCd ="グループ項目コードが誤っています。";
        public static readonly string InvalidGrpItemEdaNo ="グループ項目コード枝番が誤っています。";
        public static readonly string InvalidParam ="パラメータが誤っています。";
        public static readonly string InvalidInfoType ="入力値が誤っています。";
        public static readonly string InvalidDayOfWeek ="曜日が誤っています。";
        public static readonly string InvalidOdrKouiKbn ="オーダー行為区分が誤っています。";
        public static readonly string InvalidGrpKouiKbn ="グループ行為区分が誤っています。";
        public static readonly string InvalidDocCd ="医師コードが誤っています。";
        public static readonly string InvalidRenkeiId ="連携IDが誤っています。";
        public static readonly string InvalidCenterCd ="センターコードが誤っています。";
        public static readonly string InvalidTreatmentDepartmentId ="治療部門IDが誤っています。";
        public static readonly string ErrorBunkatuOdrDetail ="入力できる文字を超えました。\r\n 合計10文字以内に入力してください。";
        public static readonly string InvalidDrugName ="医薬品名が誤っています。";
        public static readonly string InvalidRangeDate ="日付の範囲指定が誤っています。";
        public static readonly string InvalidCmtLengthNotEquals100 ="入力値が誤っています。";
        public static readonly string InvalidReservation = "指定された予約は見つかりません。既に変更された可能性があります。";
        public static readonly string DeletedReservation = "この予約はすでに削除されています。";
        public static readonly string InputSeikyuDataNull = "指定された請求情報が見つかりません。既に変更された可能性があります。";

        //Common
        public static readonly string NotFound = "Not found";
        public static readonly string Success = "Success";
        public static readonly string NoData = "No data";
        public static readonly string Failed = "Failed";
        public static readonly string DuplicateId = "DuplicateId";
        public static readonly string ExistedId = "ExistedId";
        public static readonly string InputDataNull = "Input data is null";
        public static readonly string Valid = "Valid";
        public static readonly string InValid = "InValid";
        public static readonly string MedicalScreenLocked = "Medical Screen Locked!";
        public static readonly string None = "None";
        public static readonly string Locked = "Locked";

        public static readonly string CreateUserInvalidName = "Please input user name";
        public static readonly string CreateUserSuccessed = "User created!!!";
        public static readonly string Error = "Error";
        public static readonly string NoOutput = "No Output";
        public static readonly string InvalidFileType = "Invalid Csv Type";

        //Patient Infor
        public static readonly string InvalidPtNum = "Invalid PtNum";
        public static readonly string InvalidKanjiName = "Invalid Kanji Name";
        public static readonly string InvalidSex = "Invalid sex";
        public static readonly string CanNotDeleted = "Can Not Deleted.";
        public static readonly string NotExitPatient = "Not Exit Patient";
        public static readonly string IsExpired = "Is Expired";
        public static readonly string InvalidHomePostLength = "InvalidHomePostLength(7)";

        // RousaiJibai
        public static readonly string InvalidHokenKbn = "Invalid HokenKbn";
        public static readonly string InvalidSelectedHokenInfRousaiSaigaiKbn = "Invalid SelectedHokenInf RousaiSaigaiKbn";
        public static readonly string InvalidSelectedHokenInfRousaiSyobyoDate = "Invalid SelectedHokenInf RousaiSyobyoDate";
        public static readonly string InvalidSelectedHokenInfRyoyoStartDate = "Invalid SelectedHokenInf RyoyoStartDate";
        public static readonly string InvalidSelectedHokenInfRyoyoEndDate = "Invalid SelectedHokenInf RyoyoEndDate";

        //Group Infor
        public static readonly string DuplicateGroupId = "Can not duplicate GroupId";
        public static readonly string DuplicateGroupName = "Can not duplicate GroupName";
        public static readonly string DuplicateGroupDetailCode = "Can not Duplicate GroupDetail Code";
        public static readonly string DuplicateGroupDetailSeqNo = "Can not Duplicate GroupDetail SeqNo";
        public static readonly string DuplicateGroupDetailName = "Can not Duplicate GroupDetail Name";
        public static readonly string InvalidGroupId = "Invalid GroupId, GroupId > 0";
        public static readonly string InvalidGroupName = "Invalid GroupName, GroupName is required and length must be less than or equal 20";
        public static readonly string InvalidDetailGroupCode = "Invalid GroupCode, GroupCode is required and length must be less than or equal 2";
        public static readonly string InvalidGroupDetailName = "Invalid GroupDetailName, GroupDetailName is required and length must be less than or equal 30";

        //Reception controller
        public static readonly string ReceptionInvalidUserId = "Invalid UserId";

        //PtDisease controller
        public static readonly string PtDiseaseUpsertSuccess = "更新が成功しました";
        public static readonly string PtDiseaseUpsertFail = "更新に失敗しました。";
        public static readonly string PtDiseaseUpsertInputNoData = "無効なデータを受信しました。";

        //User controller
        public static readonly string UpsertInputNoData = "Input No Data";
        public static readonly string UpsertInvalidExistedLoginId = "Existed LoginId";
        public static readonly string UpsertInvalidNoExistedId = "No Exist Id";
        public static readonly string UpsertInvalidExistedUserId = "Existed UserId";
        public static readonly string UpsertInvalidId = "Invalid Id";
        public static readonly string UpsertInvalidUserId = "Invalid UserId";
        public static readonly string UpsertInvalidJobCd = "Invalid JobCd";
        public static readonly string UpsertInvalidManagerKbn = "Invalid ManagerKbn";
        public static readonly string UpsertInvalidKaId = "Invalid KaId";
        public static readonly string UpsertInvalidKanaName = "Invalid KanaName";
        public static readonly string UpsertInvalidName = "Invalid Name";
        public static readonly string UpsertInvalidSname = "Invalid Sname";
        public static readonly string UpsertInvalidLoginId = "Invalid LoginId";
        public static readonly string UpsertInvalidLoginPass = "Invalid LoginPass";
        public static readonly string UpsertInvalidStartDate = "Invalid StartDate";
        public static readonly string UpsertInvalidEndDate = "Invalid EndDate";
        public static readonly string UpsertInvalidSortNo = "Invalid SortNo";
        public static readonly string UpsertInvalidIsDeleted = "Invalid IsDeleted";
        public static readonly string UpsertInvalidRenkeiCd1 = "Invalid RenkeiCd1";
        public static readonly string UpsertInvalidDrName = "Invalid DrName";
        public static readonly string UpsertUserListSuccess = "UpsertUserListSuccess";
        public static readonly string UpsertKaIdNoExist = "No Exist KaId";
        public static readonly string UpsertJobCdNoExist = "No Exist JobCd";
        public static readonly string UpsertIdNoExist = "No Exist Id";
        public static readonly string UserListExistedInputData = "UserListExistedInputData";

        //ApprovalInfo
        public static readonly string InvalidStarDate = "InvalidStarDate";
        public static readonly string InvalidEndDate = "InvalidEndDate";
        public static readonly string InvalidId = "InvalidId";
        public static readonly string ApprovalInfoListInputNoData = "ApprovalInfoListInputNoData";
        public static readonly string ApprovalInfListExistedInputData = "ApprovalInfListExistedInputData";
        public static readonly string ApprovalInfListInvalidNoExistedId = "ApprovalInfListInvalidNoExistedId";
        public static readonly string ApprovalInfListInvalidNoExistedRaiinNo = "ApprovalInfListInvalidNoExistedRaiinNo";

        //TodoGrpMst
        public static readonly string InvalidGrpColor = "Invalid GrpColor";
        public static readonly string InvalidTodoGrpName = "Invalid TodoGrpName";
        public static readonly string InvalidTodoGrpNo = "Invalid TodoGrpNo";
        public static readonly string InvalidTodoGrpMst = "Invalid TodoGrpMst";
        public static readonly string InvalidExistedTodoGrpNoIsDeleted = "Invalid Existed TodoGrpNo IsDeleted";

        //TodoInf
        public static readonly string InvalidTodoNo = "Invalid TodoNo";
        public static readonly string InvalidTodoEdaNo = "Invalid TodoEdaNo";
        public static readonly string InvalidTodoKbnNo = "Invalid TodoKbnNo";
        public static readonly string InvalidTanto = "Invalid Tanto";
        public static readonly string InvalidTerm = "Invalid Term";
        public static readonly string InvalidIsDone = "Invalid IsDone";
        public static readonly string InvalidExistedInput = "Invalid Existed Input";

        //Insurance
        // Validate Main Insurance
        public static readonly string InvalidPtBirthday = "生年月日を入力してください。";
        public static readonly string InvalidSelectedHokenInfHokenNo = "Invalid SelectedHokenInf HokenNo";
        public static readonly string InvalidSelectedHokenInfStartDate = "Invalid SelectedHokenInf StartDate";
        public static readonly string InvalidSelectedHokenInfEndDate = "Invalid SelectedHokenInf EndDate";
        public static readonly string InvaliSelectedHokenInfHokensyaMstIsKigoNa = "Invalid SelectedHokenInf HokensyaMst IsKigoNa";
        public static readonly string InvalidSelectedHokenInfHonkeKbn = "Invalid SelectedHokenInf HonkeKbn";
        public static readonly string InvalidSelectedHokenInfTokureiYm1 = "Invalid SelectedHokenInf TokureiYm1";
        public static readonly string InvalidSelectedHokenInfTokureiYm2 = "Invalid SelectedHokenInf TokureiYm2";
        public static readonly string InvalidSelectedHokenInfConfirmDate = "Invalid SelectedHokenInf ConfirmDate";
        public static readonly string InvalidSelectedHokenMstHokenNo = "Invalid SelectedHokenMst HokenNo";
        public static readonly string InvalidSelectedHokenMstCheckDegit = "Invalid SelectedHokenMst CheckDegit";
        public static readonly string InvalidSelectedHokenMstAgeStart = "Invalid SelectedHokenMst AgeStart";
        public static readonly string InvalidSelectedHokenMstAgeEnd = "Invalid SelectedHokenMst AgeEnd";
        public static readonly string InvalidSelectedHokenMstStartDate = "Invalid SelectedHokenMst StartDate";
        public static readonly string InvalidSelectedHokenMstEndDate = "Invalid SelectedHokenMst EndDate";
        public static readonly string InvalidHistoryPid = "Invalid HistoryPid";
        public static readonly string InvalidSelectedHokenPid = "Invalid SelectedHokenPid";
        public static readonly string InvalidException = "Invalid Exception";
        public static readonly string InvalidNoDataSave = "Does not exist data to save";
        public static readonly string InvalidHistoryPidExistedInputData = "Invalid HistoryPid Existed InputData";
        public static readonly string HokenPidInvalidNoExisted = "HokenPid Invalid NoExisted";

        // Validate Pattern
        public static readonly string InvalidPatternJihiSelectedHokenInfHokenNoEquals0 = "Invalid SelectedHokenInf HokenNo Equals 0";
        public static readonly string InvalidPatternEmptyHoken = "Invalid Empty Hoken";
        public static readonly string InvalidPatternHokenNashiOnly = "Invalid Hoken Nashi Only";
        public static readonly string InvalidPatternTokkiValue1 = "Invalid TokkiValue1";
        public static readonly string InvalidPatternTokkiValue21 = "Invalid TokkiValue21";
        public static readonly string InvalidPatternTokkiValue31 = "Invalid TokkiValue31";
        public static readonly string InvalidPatternTokkiValue41 = "Invalid TokkiValue41";
        public static readonly string InvalidPatternTokkiValue51 = "Invalid TokkiValue51";
        public static readonly string InvalidPatternTokkiValue2 = "Invalid TokkiValue2";
        public static readonly string InvalidPatternTokkiValue23 = "Invalid TokkiValue23";
        public static readonly string InvalidPatternTokkiValue24 = "Invalid TokkiValue24";
        public static readonly string InvalidPatternTokkiValue25 = "Invalid TokkiValue25";
        public static readonly string InvalidPatternTokkiValue3 = "Invalid TokkiValue3";
        public static readonly string InvalidPatternTokkiValue34 = "Invalid TokkiValue34";
        public static readonly string InvalidPatternTokkiValue35 = "Invalid TokkiValue35";
        public static readonly string InvalidPatternTokkiValue4 = "Invalid TokkiValue4";
        public static readonly string InvalidPatternTokkiValue45 = "Invalid TokkiValue45";
        public static readonly string InvalidPatternTokkiValue5 = "Invalid TokkiValue5";
        public static readonly string InvalidPatternYukoKigen = "Invalid YukoKigen";
        public static readonly string InvalidPatternHokenSyaNoNullAndHokenNoNotEquals0 = "Invalid HokenSyaNo Null And HokenNo Not Equals 0 ";
        public static readonly string InvalidPatternHokenNoEquals0 = "Invalid HokenNo Equals 0";
        public static readonly string InvalidPatternHokenNoHaveHokenMst = "Invalid Hoken No Have HokenMst";
        public static readonly string InvalidPatternHoubetu = "Invalid Houbetu";
        public static readonly string InvalidPatternCheckDigitEquals1 = "Invalid CheckDigit Equals 1";
        public static readonly string InvalidPatternCheckAgeHokenMst = "Invalid CheckAgeHokenMst ";
        public static readonly string InvalidPatternHokensyaNoNull = "Invalid CheckAgeHokenMst ";
        public static readonly string InvalidPatternHokensyaNoEquals0 = "Invalid CheckAgeHokenMst ";
        public static readonly string InvalidPatternHokensyaNoLength8StartWith39 = "Invalid HokensyaNoLength8StartWith39";
        public static readonly string InvalidPatternKigoNull = "Invalid KigoNull";
        public static readonly string InvalidPatternBangoNull = "Invalid BangoNull";
        public static readonly string InvalidPatternHokenKbnEquals0 = "Invalid HokenKbnEquals0";
        public static readonly string InvalidPatternTokkurei = "Invalid Tokkurei";
        public static readonly string InvalidPatternConfirmDateAgeCheck = "Invalid ConfirmDateAgeCheck";
        public static readonly string InvalidPatternHokenMstStartDate = "Invalid HokenMstStartDate";
        public static readonly string InvalidPatternHokenMstEndDate = "Invalid HokenMstEndDate";
        public static readonly string InvalidPatternHokenRodoBangoNull = "Invalid RodoBangoNull";
        public static readonly string InvalidPatternRodoBangoLengthNotEquals14 = "Invalid RodoBangoLengthNotEquals14";
        public static readonly string InvalidPatternRousaiTenkiDefaultRow = "Invalid RousaiTenkiDefaultRow";
        public static readonly string InvalidPatternRousaiTenkiData = "Invalid RousaiTenkiData";
        public static readonly string InvalidPatternRousaiSaigaiKbn = "Invalid RousaiSaigaiKbn";
        public static readonly string InvalidPatternRousaiSyobyoDateEquals0 = "Invalid RousaiSyobyoDate Equals 0";
        public static readonly string InvalidPatternRousaiSyobyoCdNull = "Invalid RousaiSyobyoCdNull";
        public static readonly string InvalidPatternRousaiRyoyoDate = "Invalid RousaiRyoyoDate";
        public static readonly string InvalidPatternRosaiYukoDate = "Invalid RosaiYukoDate";
        public static readonly string InvalidPatternCheckHokenInfDate = "Invalid CheckHokenInfDate";
        public static readonly string InvalidPatternNenkinBangoNull = "Invalid NenkinBangoNull";
        public static readonly string InvalidPatternNenkinBangoLengthNotEquals9 = "Invalid NenkinBango Length Not Equals 9";
        public static readonly string InvalidPatternKenkoKanriBangoNull = "Invalid KenkoKanriBangoNull";
        public static readonly string InvalidPatternKenkoKanriBangoLengthNotEquals13 = "Invalid KenkoKanriBango Length Not Equals 13";
        public static readonly string InvalidPatternKohiEmptyModel1 = "Invalid Kohi1 Empty Model";
        public static readonly string InvalidPatternKohiHokenMstEmpty1 = "Invalid Kohi1 HokenMst Empty";
        public static readonly string InvalidPatternFutansyaNoEmpty1 = "Invalid Kohi1 FutansyaNo Empty";
        public static readonly string InvalidPatternJyukyusyaNo1 = "Invalid Kohi1 JyukyusyaNo";
        public static readonly string InvalidPatternTokusyuNo1 = "Invalid Kohi1 TokusyuNo";
        public static readonly string InvalidPatternFutansyaNo01 = "Invalid Kohi1 FutansyaNo Equals 0";
        public static readonly string InvalidPatternKohiYukoDate1 = "Invalid Kohi1 YukoDate";
        public static readonly string InvalidPatternKohiHokenMstStartDate1 = "Invalid Kohi1 HokenMst StartDate";
        public static readonly string InvalidPatternKohiHokenMstEndDate1 = "Invalid Kohi1 HokenMst EndDate";
        public static readonly string InvalidPatternKohiConfirmDate1 = "Invalid Kohi1 ConfirmDate";
        public static readonly string InvalidPatternKohiMstCheckHBT1 = "Invalid Kohi1 HokenMst CheckHBT";
        public static readonly string InvalidPatternKohiMstCheckDigitFutansyaNo1 = "Invalid Kohi1 HokenMst CheckDigitFutansyaNo";
        public static readonly string InvalidPatternKohiHokenMstCheckDigitJyukyusyaNo1 = "Invalid Kohi1 HokenMst CheckDigitJyukyusyaNo";
        public static readonly string InvalidPatternKohiHokenMstCheckAge1 = "Invalid Kohi1 HokenMst CheckAge";
        public static readonly string InvalidPatternKohiHokenMstFutanJyoTokuNull1 = "Invalid Kohi1 HokenMst FutanJyoToku Is Null";
        public static readonly string InvalidPatternKohiEmptyModel2 = "Invalid Kohi2 Empty Model";
        public static readonly string InvalidPatternKohiHokenMstEmpty2 = "Invalid Kohi2 HokenMst Empty";
        public static readonly string InvalidPatternFutansyaNoEmpty2 = "Invalid Kohi2 FutansyaNo Empty";
        public static readonly string InvalidPatternJyukyusyaNo2 = "Invalid Kohi2 JyukyusyaNo";
        public static readonly string InvalidPatternTokusyuNo2 = "Invalid Kohi2 TokusyuNo";
        public static readonly string InvalidPatternFutansyaNo02 = "Invalid Kohi2 FutansyaNo Equals 0";
        public static readonly string InvalidPatternKohiYukoDate2 = "Invalid Kohi2 YukoDate";
        public static readonly string InvalidPatternKohiHokenMstStartDate2 = "Invalid Kohi2 HokenMst StartDate";
        public static readonly string InvalidPatternKohiHokenMstEndDate2 = "Invalid Kohi2 HokenMst EndDate";
        public static readonly string InvalidPatternKohiConfirmDate2 = "Invalid Kohi2 ConfirmDate";
        public static readonly string InvalidPatternKohiMstCheckHBT2 = "Invalid Kohi2 HokenMst CheckHBT";
        public static readonly string InvalidPatternKohiMstCheckDigitFutansyaNo2 = "Invalid Kohi2 HokenMst CheckDigitFutansyaNo";
        public static readonly string InvalidPatternKohiHokenMstCheckDigitJyukyusyaNo2 = "Invalid Kohi2 HokenMst CheckDigitJyukyusyaNo";
        public static readonly string InvalidPatternKohiHokenMstCheckAge2 = "Invalid Kohi2 HokenMst CheckAge";
        public static readonly string InvalidPatternKohiHokenMstFutanJyoTokuNull2 = "Invalid Kohi2 HokenMst FutanJyoToku Is Null";
        public static readonly string InvalidPatternKohiEmptyModel3 = "Invalid Kohi3 Empty Model";
        public static readonly string InvalidPatternKohiHokenMstEmpty3 = "Invalid Kohi3 HokenMst Empty";
        public static readonly string InvalidPatternFutansyaNoEmpty3 = "Invalid Kohi3 FutansyaNo Empty";
        public static readonly string InvalidPatternJyukyusyaNo3 = "Invalid Kohi3 JyukyusyaNo";
        public static readonly string InvalidPatternTokusyuNo3 = "Invalid Kohi3 TokusyuNo";
        public static readonly string InvalidPatternFutansyaNo03 = "Invalid Kohi3 FutansyaNo Equals 0";
        public static readonly string InvalidPatternKohiYukoDate3 = "Invalid Kohi3 YukoDate";
        public static readonly string InvalidPatternKohiHokenMstStartDate3 = "Invalid Kohi3 HokenMst StartDate";
        public static readonly string InvalidPatternKohiHokenMstEndDate3 = "Invalid Kohi3 HokenMst EndDate";
        public static readonly string InvalidPatternKohiConfirmDate3 = "Invalid Kohi3 ConfirmDate";
        public static readonly string InvalidPatternKohiMstCheckHBT3 = "Invalid Kohi3 HokenMst CheckHBT";
        public static readonly string InvalidPatternKohiMstCheckDigitFutansyaNo3 = "Invalid Kohi3 HokenMst CheckDigitFutansyaNo";
        public static readonly string InvalidPatternKohiHokenMstCheckDigitJyukyusyaNo3 = "Invalid Kohi3 HokenMst CheckDigitJyukyusyaNo";
        public static readonly string InvalidPatternKohiHokenMstCheckAge3 = "Invalid Kohi3 HokenMst CheckAge";
        public static readonly string InvalidPatternKohiHokenMstFutanJyoTokuNull3 = "Invalid Kohi3 HokenMst FutanJyoToku Is Null";
        public static readonly string InvalidPatternKohiEmptyModel4 = "Invalid Kohi4 Empty Model";
        public static readonly string InvalidPatternKohiHokenMstEmpty4 = "Invalid Kohi4 HokenMst Empty";
        public static readonly string InvalidPatternFutansyaNoEmpty4 = "Invalid Kohi4 FutansyaNo Empty";
        public static readonly string InvalidPatternJyukyusyaNo4 = "Invalid Kohi4 JyukyusyaNo";
        public static readonly string InvalidPatternTokusyuNo4 = "Invalid Kohi4 TokusyuNo";
        public static readonly string InvalidPatternFutansyaNo04 = "Invalid Kohi4 FutansyaNo Equals 0";
        public static readonly string InvalidPatternKohiYukoDate4 = "Invalid Kohi4 YukoDate";
        public static readonly string InvalidPatternKohiHokenMstStartDate4 = "Invalid Kohi4 HokenMst StartDate";
        public static readonly string InvalidPatternKohiHokenMstEndDate4 = "Invalid Kohi4 HokenMst EndDate";
        public static readonly string InvalidPatternKohiConfirmDate4 = "Invalid Kohi4 ConfirmDate";
        public static readonly string InvalidPatternKohiMstCheckHBT4 = "Invalid Kohi4 HokenMst CheckHBT";
        public static readonly string InvalidPatternKohiMstCheckDigitFutansyaNo4 = "Invalid Kohi4 HokenMst CheckDigitFutansyaNo";
        public static readonly string InvalidPatternKohiHokenMstCheckDigitJyukyusyaNo4 = "Invalid Kohi4 HokenMst CheckDigitJyukyusyaNo";
        public static readonly string InvalidPatternKohiHokenMstCheckAge4 = "Invalid Kohi4 HokenMst CheckAge";
        public static readonly string InvalidPatternKohiHokenMstFutanJyoTokuNull4 = "Invalid Kohi4 HokenMst FutanJyoToku Is Null";
        public static readonly string InvalidPatternKohiEmpty21 = "Invalid Kohi1 Kohi2 Empty";
        public static readonly string InvalidPatternKohiEmpty31 = "Invalid Kohi3 Kohi1 Empty";
        public static readonly string InvalidPatternKohiEmpty32 = "Invalid Kohi3 Kohi2 Empty";
        public static readonly string InvalidPatternKohiEmpty41 = "Invalid Kohi4 Kohi1 Empty";
        public static readonly string InvalidPatternKohiEmpty42 = "Invalid Kohi4 Kohi2 Empty";
        public static readonly string InvalidPatternKohiEmpty43 = "Invalid Kohi4 Kohi3 Empty";
        public static readonly string InvalidPatternDuplicateKohi1 = "Invalid DuplicateKohi1";
        public static readonly string InvalidPatternDuplicateKohi2 = "Invalid DuplicateKohi2";
        public static readonly string InvalidPatternDuplicateKohi3 = "Invalid DuplicateKohi3";
        public static readonly string InvalidPatternDuplicateKohi4 = "Invalid DuplicateKohi4";
        public static readonly string InvalidPatternWarningDuplicatePattern = "Invalid Warning Duplicate Pattern";
        public static readonly string InvalidPatternWarningAge75 = "Invalid Warning Age75";
        public static readonly string InvalidPatternWarningAge65 = "Invalid Warning Age65";
        public static readonly string InvalidPatternMaruchoOnly = "Invalid Marucho Only";

        //KarteInf controller
        public static readonly string GetKarteInfInvalidRaiinNo = "Invalid RaiinNo";
        public static readonly string GetKarteInfInvalidPtId = "Invalid PtId";
        public static readonly string GetKarteInfInvalidSinDate = "Invalid SinDate";
        public static readonly string GetKarteInfNoData = "No Data";
        public static readonly string GetKarteInfSuccessed = "Successed";
        public static readonly string UpsertKarteInfInvalidHpId = "Invalid HpId";
        public static readonly string UpsertKarteInfInvalidRaiinNo = "Invalid RaiinNo";
        public static readonly string UpsertKarteInfInvalidKarteKbn = "Invalid KarteKbn";
        public static readonly string UpsertKarteInfInvalidPtId = "Invalid PtId";
        public static readonly string UpsertKarteInfInvalidSinDate = "Invalid SinDate";
        public static readonly string UpsertKarteInfInvalidIsDeleted = "Invalid IsDeleted";
        public static readonly string UpsertKarteInfRaiinNoNoExist = "指定された来院情報がないためオーダーは入力できません";
        public static readonly string UpsertKarteInfPtIdNoExist = "PtId No Exist";
        public static readonly string UpsertKarteInfKarteKbnNoExist = "KarteKbn No Exist";

        //OrdInf controller
        public static readonly string GetOrdInfInvalidRaiinNo = "Invalid RaiinNo";
        public static readonly string GetOrdInfInvalidHpId = "Invalid HpId";
        public static readonly string GetOrdInfInvalidPtId = "Invalid PtId";
        public static readonly string GetOrdInfInvalidSinDate = "Invalid SinDate";
        public static readonly string GetOrdInfNoData = "No Data";
        public static readonly string GetOrdInfSuccessed = "Successed";

        //RaiinKubun controller
        public static readonly string RaiinKubunInvalidKbnInf = "Invalid KbnInf";

        //Calculation Inf

        //Medical examination controller
        public static readonly string GetMedicalExaminationInvalidPtId = "Invalid PtId";
        public static readonly string GetMedicalExaminationInvalidHpId = "Invalid HpId";
        public static readonly string GetMedicalExaminationInvalidStartIndex = "Invalid Start Index Of Page";
        public static readonly string GetMedicalExaminationInvalidSinDate = "Invalid SinDate";
        public static readonly string GetMedicalExaminationNoData = "No Data";
        public static readonly string GetMedicalExaminationSuccessed = "Successed";
        public static readonly string GetMedicalExaminationInvalidDeleteCondition = "Invalid Delete Condition";
        public static readonly string GetMedicalExaminationInvalidFilterId = "Invalid FilterId";
        public static readonly string GetMedicalExaminationInvalidPageSize = "Invalid PageSize";
        public static readonly string GetMedicalExaminationInvalidSearchType = "Invalid Search Type";
        public static readonly string GetMedicalExaminationInvalidSearchCategory = "Invalid Search Category";
        public static readonly string GetMedicalExaminationInvalidSearchText = "Invalid Search Text";
        public static readonly string GetMedicalExaminationInvalidUserId = "Invalid UserId";
        public static readonly string GetMedicalExaminationInvalidDrugOrByomei = "DrugOrders Or Byomeis Is Null";
        public static readonly string GetMedicalExaminationInvalidByomei = "Byomeis Is Null";

        //OrdInf controller

        //RaiinKubun controller

        //TimeZone
        public static readonly string InvalidBirthDay = "Invalid BirthDay";
        public static readonly string InvalidCurrentTimeKbn = "Invalid CurrentTimeKbn, CurrentTimeKbn > 0";
        public static readonly string InvalidBeforeTimeKbn = "Invalid BeforeTimeKbn, BeforeTimeKbn >= 0";
        public static readonly string InvalidUketukeTime = "Invalid UketukeTime";
        public static readonly string CanNotUpdateTimeZoneInf = "CurrentTimeKbn = BeforeTimeKbn, Can Not Update TimeZoneInf";
        public static readonly string InvalidCheckSameTime = "開始時刻と終了時刻は同じ値を設定することはできません。";


        //SetMst
        public static readonly string GetSetListInvalidHpId = "Invalid HpId";
        public static readonly string GetSetListSinDate = "Invalid SinDate";
        public static readonly string GetSetListInvalidSetKbn = "Invalid SetKbn";
        public static readonly string GetSetListInvalidSetKbnEdaNo = "Invalid SetKbnEdaNo";
        public static readonly string GetSetListNoData = "No Data";
        public static readonly string GetSetListSuccessed = "Successed";
        public static readonly string InvalidLevel = "Invalid Level, can't move";
        public static readonly string InvalidUserId = "Invalid UserId, can't move";
        public static readonly string InvalidCopySetCd = "Invalid CopySetCd, CopySetCd >= 0";
        public static readonly string InvalidPasteSetCd = "Invalid PasteSetCd, PasteSetCd > 0";
        public static readonly string InvalidDragSetCd = "Invalid DragSetCd, DragSetCd >= 0";
        public static readonly string InvalidDropSetCd = "Invalid DropSetCd, DropSetCd > 0";
        public static readonly string InvalidPasteSetKbn = "Invalid PasteSetKbn";
        public static readonly string InvalidPasteSetKbnEdaNo = "Invalid PasteSetKbnEdaNo";
        public static readonly string InvalidCopySetKbn = "Invalid CopySetKbn";
        public static readonly string InvalidCopySetKbnEdaNo = "Invalid CopySetKbnEdaNo";
        public static readonly string InvalidSetCd = "Invalid SetCd, this SetCd is not exist.";
        public static readonly string InvalidSetKbn = "Invalid SetKbn, SetKbn >= 1 and SetKbn <= 10";
        public static readonly string InvalidSetKbnEdaNo = "Invalid SetKbnEdaNo, SetKbnEdaNo >= 1 and SetKbnEdaNo <= 6";
        public static readonly string InvalidGenarationId = "Invalid GenarationId, GenarationId >= 0";
        public static readonly string InvalidLevel1 = "Invalid Level1, Level1 > 0";
        public static readonly string InvalidLevel2 = "Invalid Level2, Level2 >= 0";
        public static readonly string InvalidLevel3 = "Invalid Level3, Level3 >= 0";
        public static readonly string InvalidSetNameMaxlength = "60文字以内で入力してください。";
        public static readonly string InvalidSetKanaMaxlength = "60文字以内で入力してください。";
        public static readonly string InvalidFolderNameEmpty = "フォルダ名を入力してください。";
        public static readonly string InvalidSetNameEmpty = "セット名を入力してください。";
        public static readonly string InvalidWeightKbn = "Invalid WeightKbn, WeightKbn >= 0";
        public static readonly string InvalidColor = "Invalid Color, Color >= 0";
        public static readonly string InvalidMemo = "Invalid Memo";
        public static readonly string InvalidSourceItemCd = "Invalid SourceItemCd.";
        public static readonly string InvalidConversionItemCd = "Invalid ConversionItemCd.";
        public static readonly string InvalidDeleteConversionItemCd = "Invalid DeleteConversionItemCd.";
        public static readonly string InvalidQuanlity = "Invalid Quanlity.";
        public static readonly string CannotEditMyFolder = "Can't Edit My Folder.";
        public static readonly string NotExistFolder = "登録先フォルダが削除されているため、保存できません。";

        //Set
        public static readonly string GetSetKbnListInvalidHpId = "Invalid HpId";
        public static readonly string GetSetKbnListSinDate = "Invalid SinDate";
        public static readonly string GetSetKbnListInvalidSetKbnFrom = "Invalid SetKbnFrom";
        public static readonly string GetSetKbnListInvalidSetKbnTo = "Invalid SetKbnTo";
        public static readonly string GetSetKbnListInvalidSetKbn = "SetKbnTo must more than SetKbnFrom";
        public static readonly string GetSetKbnListNoData = "No Data";
        public static readonly string GetSetKbntListSuccessed = "Successed";
        public static readonly string SaveSetByomeiFailed = "Save SetByomei Failed.";
        public static readonly string SaveSetOrderInfFailed = "Save SetOrderInf Failed.";
        public static readonly string SaveSetKarteInfFailed = "Save SetKarteInf Failed.";
        public static readonly string SetKbnListInvalidSetKbn = "Invalid SetKbn";
        public static readonly string SetKbnListInvalidSetKbnName = "Invalid SetKbnName";
        public static readonly string SaveSetInfoFailed = "Save SetInfo Failed.";

        //Calculation Inf


        // Visiting controller
        //  - UpdateStaticCell
        public static readonly string UpdateReceptionStaticCellUnknownError = "予期しないエラーが発生しました。";
        public static readonly string UpdateReceptionStaticCellSuccess = "Cell value updated successfully.";
        public static readonly string UpdateReceptionStaticCellInvalidHpId = "HpId must be greater than 0.";
        public static readonly string UpdateReceptionStaticCellInvalidSinDate = "SinDate must be greater than 0.";
        public static readonly string UpdateReceptionStaticCellInvalidRaiinNo = "RaiinNo must be greater than 0.";
        public static readonly string UpdateReceptionStaticCellInvalidPtId = "PtId must be greater than 0.";
        //  - UpdateDynamicCell
        public static readonly string UpdateReceptionDynamicCellSuccess = "Cell value updated successfully.";
        public static readonly string UpdateReceptionDynamicCellInvalidHpId = "HpId must be greater than 0.";
        public static readonly string UpdateReceptionDynamicCellInvalidSinDate = "SinDate must be greater than 0.";
        public static readonly string UpdateReceptionDynamicCellInvalidRaiinNo = "RaiinNo must be greater than 0.";
        public static readonly string UpdateReceptionDynamicCellInvalidPtId = "PtId must be greater than 0.";
        public static readonly string UpdateReceptionDynamicCellInvalidGrpId = "GrpId cannot be negative.";

        //Flowsheet
        public static readonly string UpsertFlowSheetSuccess = "Upsert value successfully.";
        public static readonly string UpsertFlowSheetInvalidPtId = "PtId must be greater than 0.";
        public static readonly string UpsertFlowSheetInvalidSinDate = "SinDate is no valid.";
        public static readonly string UpsertFlowSheetInvalidRaiinNo = "RaiinNo must be greater than 0.";
        public static readonly string UpsertFlowSheetInvalidTagNo = "TagNo is no valid";
        public static readonly string UpsertFlowSheetUpdateNoSuccess = "Update is no successful.";
        public static readonly string UpsertFlowSheetInputDataNoValid = "Input data no valid.";
        public static readonly string UpsertFlowSheetRainNoNoExist = "RainNo No Exist.";
        public static readonly string UpsertFlowSheetPtIdNoExist = "PtId No Exist.";

        // Schema
        public static readonly string InvalidOldImage = "Invalid old image.";
        public static readonly string DeleteSuccessed = "Delete image successed.";
        public static readonly string InvalidFileImage = "File image is not null.";
        public static readonly string InvalidTypeUpload = "Invalid type upload.";

        // Today Validate Order
        public static readonly string TodayOrdInvalidSpecialItem = "Special item doesn't contain drug, injection and other";
        public static readonly string TodayOrdIInvalidSpecialStadardUsage = "Special item doesn't contain standard usage";
        public static readonly string TodayOrdInvalidOdrKouiKbn = "Value of OdrKouiKbn is invalid ";
        public static readonly string TodayOrdInvalidSpecialSuppUsage = "Special item doesn't contain supply usage";
        public static readonly string TodayOrdInvalidHasUsageButNotDrug = "Item which differs drug item, it doesn't have drug usage";
        public static readonly string TodayOrdInvalidHasUsageButNotInjectionOrDrug = "Item which differs drug item or injection item, it doesn't have injection usage";
        public static readonly string TodayOrdInvalidHasDrugButNotUsage = "Drug item doesn't have usage";
        public static readonly string TodayOrdInvalidHasInjectionButNotUsage = "Injection item doesn't have usage";
        public static readonly string TodayOrdInvalidHasNotBothInjectionAndUsageOf28 = "Self Injection doesn't have self injection detail and usage";
        public static readonly string TodayOrdInvalidStandardUsageOfDrugOrInjection = "Standard usage of drug item or usage of injection item don't more than 1";
        public static readonly string TodayOrdInvalidSuppUsageOfDrugOrInjection = "Supply usage of drug item or usage of injection item don't more than 1";
        public static readonly string TodayOrdInvalidBunkatu = "Bunkatu item of drug item doesn't more than 1";
        public static readonly string TodayOrdInvalidUsageWhenBuntakuNull = "Bunkatu item doesn't have usage";
        public static readonly string TodayOrdInvalidSumBunkatuDifferentSuryo = "Bunkatu item has sum of suryo not equal bunkatu";
        public static readonly string TodayOrdInvalidQuantityUnit = "Has unit but doesn't have quantity";
        public static readonly string TodayOrdInvalidSuryoAndYohoKbnWhenDisplayedUnitNotNull = "Has unit but yohoKbn and Suryo don't invalid (YohoKbn != 1 and Suryo > 999)";
        public static readonly string TodayOrdInvalidSuryoBunkatuWhenIsCon_TouyakuOrSiBunkatu = "Bunkatu item doesn't have suryo and bunkatu";
        public static readonly string TodayOrdInvalidPrice = "Price must more than 0 and (suryo * price) <= 999999999";
        public static readonly string TodayOrdInvalidCmt840 = "CmtOpt is not null and CmtName is not null when CmtCol1 of Cmt840 > 0";
        public static readonly string TodayOrdInvalidCmt842 = "CmtOpt of Cmt842 is not null and CmtName is not null";
        public static readonly string TodayOrdInvalidCmt842CmtOptMoreThan38 = "CmtOpt of Cmt842 is not null and has length less than or equal 38";
        public static readonly string TodayOrdInvalidCmt830CmtOpt = "CmtOpt of Cmt830 is not null and not white space";
        public static readonly string TodayOrdInvalidCmt830CmtOptMoreThan38 = "CmtOpt of Cmt830 is not null and has length less than or equal 38";
        public static readonly string TodayOrdInvalidCmt831 = "CmtOpt of Cmt831 is not null and CmtName is not null";
        public static readonly string TodayOrdInvalidCmt850Date = "CmtOpt of Cmt850 is not map format and CmtName is not null when CmtName contain day";
        public static readonly string TodayOrdInvalidCmt850OtherDate = "CmtOpt of Cmt850 is not map format and CmtName is not null when CmtName doesn't contain day";
        public static readonly string TodayOrdInvalidCmt851 = "CmtOpt of Cmt851 is not map format and CmtName is not null";
        public static readonly string TodayOrdInvalidCmt852 = "CmtOpt of Cmt852 is not map format and CmtName is not null";
        public static readonly string TodayOrdInvalidCmt853 = "CmtOpt of Cmt853 is not map format and CmtName is not null";
        public static readonly string TodayOrdInvalidCmt880 = "CmtOpt of Cmt880 is not null and CmtName is not null";
        public static readonly string TodayOrdDuplicateTodayOrd = "Duplicate RpNo and RpNoEdaNo";
        public static readonly string TodayOrdInvalidKohatuKbn = "Value of KohatuKbn is not invalid";
        public static readonly string TodayOrdInvalidDrugKbn = "Value of DrugKbn is not invalid";
        public static readonly string TodayOrdInvalidSuryoOfReffill = "Suryo must  more than refill setting";
        public static readonly string TodayOrdInvalidRowNo = "RowNo must more than 0";
        public static readonly string TodayOrdInvalidSinKouiKbn = "SinKouiKbn must more than 0 or equal 0";
        public static readonly string TodayOrdInvalidItemCd = "Length of ItemCd must less than 10 or equal 10";
        public static readonly string TodayOrdInvalidItemName = "Length of ItemName must less than 240 or equal 240";
        public static readonly string TodayOrdInvalidSuryo = "Invalid Suryo";
        public static readonly string TodayOrdInvalidUnitName = "Length of UnitName must less than 24 or equal 24";
        public static readonly string TodayOrdInvalidUnitSbt = "UnitSbt must more than 0 or equal 0 and less than 2 or equal 2";
        public static readonly string TodayOrdInvalidTermVal = "TermVal must more than 0 or equal 0";
        public static readonly string TodayOrdInvalidSyohoKbn = "SyohoKbn must more than 0 or equal 0 and less than 3 or equal 3";
        public static readonly string TodayOrdInvalidSyohoLimitKbn = "SyohoLimitKbn must more than 0 or equal 0 and less than 3 or equal 3";
        public static readonly string TodayOrdInvalidYohoKbn = "YohoKbn must more than 0 or equal 0 and less than 2 or equal 2";
        public static readonly string TodayOrdInvalidIsNodspRece = "IsNodspRece  must more than 0 or equal 0 and less than 1 or equal 1";
        public static readonly string TodayOrdInvalidIpnCd = "Length of IpnCd must less than 12 or equal 12";
        public static readonly string TodayOrdInvalidIpnName = "Length of IpnName must less than 120 or equal 120";
        public static readonly string TodayOrdInvalidJissiKbn = "JissiKbn must more than 0 or equal 0 and less than 1 or equal 1";
        public static readonly string TodayOrdInvalidJissiId = "JissiId must more than 0 equal 0";
        public static readonly string TodayOrdInvalidJissiMachine = "Length of JissiMachine must less than 60 or equal 60";
        public static readonly string TodayOrdInvalidReqCd = "Length of ReqCd must less than 10 or equal 10";
        public static readonly string TodayOrdInvalidCmtName = "Length of CmtName must less than 240 or equal 240";
        public static readonly string TodayOrdInvalidCmtOpt = "Length of CmtOpt must less than 38 or equal 38";
        public static readonly string TodayOrdInvalidFontColor = "Length of FontColor must less than 8 or equal 8";
        public static readonly string TodayOrdInvalidCommentNewline = "CommentNewline must more than 0 or equal 0 and less than 1 or equal 1";
        public static readonly string TodayOrdInvalidRpNo = "RpNo must more than 0";
        public static readonly string TodayOrdInvalidRpEdaNo = "RpEdaNo must more than 0";
        public static readonly string TodayOrdInvalidHokenPId = "HokenPId must more than 0";
        public static readonly string TodayOrdInvalidRpName = "Length of RpName must less than 240 or equal 240";
        public static readonly string TodayOrdInvalidInoutKbn = "InoutKbn must more than 0 or equal 0 and less than 1 or equal 1";
        public static readonly string TodayOrdInvalidSikyuKbn = "SikyuKbn must more than 0 or equal 0 and less than 1 or equal 1";
        public static readonly string TodayOrdInvalidSyohoSbt = "SyohoSbt must more than 0 or equal 0 and less than 2 or equal 2";
        public static readonly string TodayOrdInvalidSanteiKbn = "SanteiKbn must more than 0 or equal 0 and less than 2 or equal 2";
        public static readonly string TodayOrdInvalidTosekiKbn = "TosekiKbn must more than 0 or equal 0 and less than 2 or equal 2";
        public static readonly string TodayOrdInvalidDaysCnt = "DaysCnt must more than 0 or equal 0";
        public static readonly string TodayOrdInvalidSortNo = "SortNo must more than 0";
        public static readonly string TodayOrdInvalidId = "Id of OrdInf must more than 0 or equal 0";
        public static readonly string TodayOrdInvalidPtId = "PtId must more than 0";
        public static readonly string TodayOrdInvalidRaiinNo = "RaiinNo must more than 0";
        public static readonly string TodayOrdInvalidSinDate = "SinDate must more than 0";
        public static readonly string TodayOrdInvalidHpId = "HpId must more than 0";
        public static readonly string TodayOrdInvalidBunkatuLength = "Length of Bunkatu must lest than 10 or equal 10";
        public static readonly string TodayOrdInvalidIsDeleted = "IsDeleted must more than 0 or equal 0 and less than 2 or equal 2";
        public static readonly string TodayOrdInvalidInsertedExist = "This Rp has been exited";
        public static readonly string TodayOrdInvalidUpdatedNoExist = "This Rp hasn't been exited to update";
        public static readonly string TodayOrdInvalidAddedAutoItem = "Invalid Added Auto Item List";
        public static readonly string NoPermissionSaveSummary = "No Permission Save Summary.";

        // SuperSetDetail
        public static readonly string InvalidSetByomeiId = "Invalid SetByomeiId, SetByomeiId > 0.";
        public static readonly string InvalidSikkanKbn = "Invalid SikkanKbn, SikkanKbn >.0";
        public static readonly string InvalidNanByoCd = "Invalid NanByoCd, NanByoCd > 0.";
        public static readonly string InvalidByomeiCdOrSyusyokuCd = "Invalid ByomeiCd or SyusyokuCd, ByomeiCd or SyusyokuCd not found.";
        public static readonly string SetCdNotExist = "{0} はすでに削除されているため、保存できません。";
        public static readonly string FullByomeiMaxlength160 = "Length of FullByomei must less than 160 or equal 160.";
        public static readonly string ByomeiCmtMaxlength80 = "Length of ByomeiCmt must less than 80 or equal 80.";
        public static readonly string RpNameMaxLength240 = "Length of SetOrder RpName must less than 240 or equal 240.";
        public static readonly string InvalidSetOrderInfId = "Id of SetOrderInf must more than 0 or equal 0";
        public static readonly string InvalidSetOrderInfRpNo = "SetOrder RpNo must more than 0 or equal 0";
        public static readonly string InvalidSetOrderInfRpEdaNo = "SetOrder RpEdaNo must more than 0 or equal 0";
        public static readonly string InvalidSetOrderInfKouiKbn = "SetOrder KouiKbn must more than 0 or equal 0";
        public static readonly string InvalidSetOrderInfInoutKbn = "SetOrder InoutKbn must more than 0 or equal 0";
        public static readonly string InvalidSetOrderInfSikyuKbn = "SetOrder SikyuKbn must more than 0 or equal 0";
        public static readonly string InvalidSetOrderInfSyohoSbt = "SetOrder SyohoSbt must more than 0 or equal 0";
        public static readonly string InvalidSetOrderInfSanteiKbn = "SetOrder SanteiKbn must more than 0 or equal 0";
        public static readonly string InvalidSetOrderInfTosekiKbn = "SetOrder TosekiKbn must more than 0 or equal 0";
        public static readonly string InvalidSetOrderInfDaysCnt = "SetOrder DaysCnt must more than 0 or equal 0";
        public static readonly string InvalidSetOrderInfSortNo = "SetOrder SortNo must more than 0 or equal 0";
        public static readonly string InvalidSetOrderSinKouiKbn = "SetOrderDetail SinKouiKbn must more than 0 or equal 0";
        public static readonly string ItemCdMaxLength10 = "Length of SetOrderDetail ItemCd must less than 10 or equal 10.";
        public static readonly string ItemNameMaxLength240 = "Length of SetOrderDetail ItemCd must less than 240 or equal 240.";
        public static readonly string UnitNameMaxLength24 = "Length of SetOrderDetail UnitName must less than 24 or equal 24.";
        public static readonly string InvalidSetOrderSuryo = "SetOrderDetail Suryo must more than 0 or equal 0";
        public static readonly string InvalidSetOrderUnitSBT = "SetOrderDetail UnitSBT must more than 0 or equal 0";
        public static readonly string InvalidSetOrderTermVal = "SetOrderDetail TermVal must more than 0 or equal 0";
        public static readonly string InvalidSetOrderKohatuKbn = "SetOrderDetail KohatuKbn must more than 0 or equal 0";
        public static readonly string InvalidSetOrderSyohoKbn = "SetOrderDetail SyohoKbn must more than 0 or equal 0";
        public static readonly string InvalidSetOrderSyohoLimitKbn = "SetOrderDetail SyohoLimitKbn must more than 0 or equal 0";
        public static readonly string InvalidSetOrderDrugKbn = "SetOrderDetail DrugKbn must more than 0 or equal 0";
        public static readonly string InvalidSetOrderYohoKbn = "SetOrderDetail YohoKbn must more than 0 or equal 0";
        public static readonly string Kokuji1MaxLength1 = "Length of SetOrderDetail Kokuji1 must less than 1 or equal 1.";
        public static readonly string Kokuji2MaxLength1 = "Length of SetOrderDetail Kokuji2 must less than 1 or equal 1.";
        public static readonly string InvalidSetOrderIsNodspRece = "SetOrderDetail IsNodspRece must more than 0 or equal 0";
        public static readonly string IpnCdMaxLength12 = "Length of SetOrderDetail IpnCd must less than 12 or equal 12.";
        public static readonly string IpnNameMaxLength120 = "Length of SetOrderDetail IpnName must less than 120 or equal 120.";
        public static readonly string BunkatuMaxLength10 = "Length of SetOrderDetail Bunkatu must less than 10 or equal 10.";
        public static readonly string CmtNameMaxLength240 = "Length of SetOrderDetail CmtName must less than 240 or equal 240.";
        public static readonly string CmtOptMaxLength38 = "Length of SetOrderDetail CmtOpt must less than 38 or equal 38.";
        public static readonly string FontColorMaxLength8 = "Length of SetOrderDetail FontColor must less than 8 or equal 8.";
        public static readonly string InvalidSetOrderCommentNewline = "SetOrderDetail CommentNewline must more than 0 or equal 0";
        public static readonly string RpNoOrRpEdaNoIsNotExist = "RpNo or RpEdaNo is not exist";

        // KaMst
        public static readonly string InvalidKaId = "Invalid KaId, KaId > 0";
        public static readonly string KaSnameMaxLength20 = "Length of KaSname must lest than 20 or equal 20";
        public static readonly string KaNameMaxLength40 = "Length of KaName must lest than 40 or equal 40";
        public static readonly string ReceKaCdNotFound = "ReceKaCd is NotFound";
        public static readonly string CanNotDuplicateKaId = "Can not duplicate KaId";

        //UketukeMst
        public static readonly string InvalidKbnId = "Invalid KbnId";
        public static readonly string InvalidKbnName = "Invalid KbnName";
        public static readonly string InputNoData = "Input no data";
        public static readonly string InputDataDuplicateKbnId = "InputData duplicate KbnId";

        //TodayOdr Field
        public static readonly string TodayOdrSuryo = "Suryo";
        public static readonly string TodayOdrCmt = "CmtOpt,CmtName";
        public static readonly string TodayOdrCmt842_830 = "CmtOpt";
        public static readonly string TodayOdrKohatuKbn = "KohatuKbn";
        public static readonly string TodayOdrDrugKbn = "DrugKbn";
        public static readonly string TodayOdrId = "Id";
        public static readonly string TodayOdrHpId = "HpId";
        public static readonly string TodayOdrRaiinNo = "RaiinNo";
        public static readonly string TodayOdrRpNo = "RpNo";
        public static readonly string TodayOdrRpEdaNo = "RpEdaNo";
        public static readonly string TodayOdrPtId = "PtId";
        public static readonly string TodayOdrSinDate = "SinDate";
        public static readonly string TodayOdrHokenPid = "HokenPId";
        public static readonly string TodayOdrRpName = "RpName";
        public static readonly string TodayOdrInOutKbn = "InOutKbn";
        public static readonly string TodayOdrSikyuKbn = "SikyuKbn";
        public static readonly string TodayOdrSyohoSbt = "SyohoSbt";
        public static readonly string TodayOdrSanteiKbn = "SanteiKbn";
        public static readonly string TodayOdrTosekiKbn = "TosekiKbn";
        public static readonly string TodayOdrDaysCnt = "DaysCnt";
        public static readonly string TodayOdrSortNo = "SortNo";
        public static readonly string TodayOdrRowNo = "RowNo";
        public static readonly string TodayOdrSinKouiKbn = "SinKouiKbn";
        public static readonly string TodayOdrItemCd = "ItemCd";
        public static readonly string TodayOdrItemName = "ItemName";
        public static readonly string TodayOdrUnitName = "UnitName";
        public static readonly string TodayOdrUnitSbt = "UnitSbt";
        public static readonly string TodayOdrTermVal = "TermVal";
        public static readonly string TodayOdrSyohoKbn = "SyohoKbn";
        public static readonly string TodayOdrSyohoLimitKbn = "SyohoLimitKbn";
        public static readonly string TodayOdrYohoKbn = "YohoKbn";
        public static readonly string TodayOdrIsNodspRece = "IsNodspRece";
        public static readonly string TodayOdrIpnCd = "IpnCd";
        public static readonly string TodayOdrIpnName = "IpnName";
        public static readonly string TodayOdrJissiKbn = "JissiKbn";
        public static readonly string TodayOdrJissiId = "JissiId";
        public static readonly string TodayOdrJissiMachine = "JissiMachine";
        public static readonly string TodayOdrReqCd = "ReqCd";
        public static readonly string TodayOdrBunkatu = "Bunkatu";
        public static readonly string TodayOdrCmtName = "CmtName";
        public static readonly string TodayOdrCmtOpt = "CmtOpt";
        public static readonly string TodayOdrFontColor = "FontColor";
        public static readonly string TodayOdrCommentNewline = "CommentNewline";
        public static readonly string TodayOdrIsDeleted = "IsDeleted";
        public static readonly string TodayOdrSuryoYohoKbn = "Suryo,YohoKbn";
        public static readonly string TodayOdrSuryoBunkatu = "Suryo,Bunkatu";
        public static readonly string TodayOdrPriceSuryo = "Suryo,Price";

        //Raiin Info TodayOdr
        public static readonly string RaiinInfTodayOdrInvalidSyosaiKbn = "Invalid SyosaiKbn";
        public static readonly string RaiinInfTodayOdrInvalidJikanKbn = "Invalid JikanKbn";
        public static readonly string RaiinInfTodayOdrInvalidHokenPid = "Invalid HokenPid";
        public static readonly string RaiinInfTodayOdrHokenPidNoExist = "HokenPid no exist";
        public static readonly string RaiinInfTodayOdrInvalidSanteiKbn = "Invalid SanteiKbn";
        public static readonly string RaiinInfTodayOdrInvalidTantoId = "Invalid TantoId";
        public static readonly string RaiinInfTodayOdrTatoIdNoExist = "TantoId no exist";
        public static readonly string RaiinInfTodayOdrInvalidKaId = "Invalid KaId";
        public static readonly string RaiinInfTodayOdrKaIdNoExist = "KaId no exist";
        public static readonly string RaiinInfTodayOdrInvalidUKetukeTime = "Invalid UKetukeTime";
        public static readonly string RaiinInfTodayOdrInvalidSinStartTime = "Invalid SinStartTime";
        public static readonly string RaiinInfTodayOdrInvalidSinEndTime = "Invalid SinEndTime";
        public static readonly string RaiinInfTodayOdrPtIdNoExist = "PtId no exist";
        public static readonly string RaiinInfTodayOdrHpIdNoExist = "HpId no exist";
        public static readonly string RaiinInfTodayOdrRaiinNoExist = "指定された来院情報がないためオーダーは入力できません";
        public static readonly string RaiinInfTodayOdrHokenDeleted = "hoken deleted";
        public static readonly string RaiinInfTodayOdrInvalidStatus = "Invalid Status";
        public static readonly string RaiinInfTodayOdrInvalidKarteStatus = "Invalid KarteStatus";

        //Monshin
        public static readonly string InputDataDoesNotExists = "Input Data does not exist";

        //Alrgy Drug
        public static readonly string AddAlrgyDrugInvalidCmt = "Invalid Cmt";
        public static readonly string AddAlrgyDrugInvalidPtId = "Invalid PtId";
        public static readonly string AddAlrgyDrugHpIdNoExist = "No exist HpId";
        public static readonly string AddAlrgyDrugPtIdNoExist = "No exist PtId";
        public static readonly string AddAlrgyDrugInvalidItemCd = "Invalid ItemCd";
        public static readonly string AddAlrgyDrugInvalidSortNo = "Invalid SortNo";
        public static readonly string AddAlrgyDrugInvalidStartDate = "Invalid StartDate";
        public static readonly string AddAlrgyDrugInvalidEndDate = "Invalid EndDate";
        public static readonly string AddAlrgyDrugInvalidDrugName = "Invalid DrugName";
        public static readonly string InvalidDrugNameLengthNotEquals20 = "Invalid DrugName Length Not Equals 20";
        public static readonly string InvalidItemCdLengthNotEquals10 = "Invalid ItemCd Length Not Equals 10";
        public static readonly string AddAlrgyDrugDuplicate = "This drug has existed";
        public static readonly string AddAlrgyDrugInputNoData = "Hasn't input data";
        public static readonly string AddAlrgyDrugItemCd = "ItemCd no existed";
        public static readonly string InvalidAlrgyNameLengthNotEquals100 = "Invalid AlrgyName Length Not Equals 20";
        public static readonly string InvalidByomeiCdLengthNotEquals7 = "Invalid ByomeiCd Length Not Equals 7";
        public static readonly string InvalidByomeiLengthNotEquals400 = "Invalid Byomei Length Not Equals 400";

        //HokenSyamst
        public static readonly string InvalidHokenSyaNo = "HokenSyaNo is null or empty";

        //DetailHokenMst
        public static readonly string DetailHokenMstInvalidHokenEdaNo = "HokenEdaNo is not valid";
        public static readonly string DetailHokenMstInvalidHokenNo = "HokenNo is not valid";
        public static readonly string DetailHokenMstInvalidPrefNo = "PrefNo is not valid";

        //PostCode
        public static readonly string InvalidPostCode = "Invalid PostCode";

        // Export
        public static readonly string NotFoundPtInf = "Invalid PtId, PtInf Not Found.";
        public static readonly string HokenNotFound = "Invalid HokenPid, Hoken Not Found.";
        public static readonly string CanNotExportPdf = "Can not export file Pdf.";
        public static readonly string CanNotReturnPdfFile = "Can not return file Pdf.";

        //TenMst
        public static readonly string InvalidMasterSbt = "Invalid MasterSbt";
        public static readonly string InvalidMinAge = "Invalid MinAge";
        public static readonly string InvalidMaxAge = "Invalid MaxAge";
        public static readonly string InvalidCdKbn = "Invalid CdKbn";
        public static readonly string InvalidKokuji1 = "Invalid Kokuji1";
        public static readonly string InvalidKokuji2 = "Invalid Kokuji2";
        public static readonly string DuplicateKeyMessage = "すでに項目が登録されています。";

        //Valid Kohi
        public static readonly string InvalidKohiEmptyModel1 = "Invalid kohi1 empty model";
        public static readonly string InvalidKohiHokenMstEmpty1 = "Invalid kohi1 hokenMst empty model";
        public static readonly string InvalidFutansyaNoEmpty1 = "Invalid kohi1 futansyaNo empty";
        public static readonly string InvalidJyukyusyaNo1 = "Invalid kohi1 jyukyusyaNo empty";
        public static readonly string InvalidTokusyuNo1 = "Invalid kohi1 tokusyuNo empty";
        public static readonly string InvalidFutansyaNo01 = "Invalid kohi1 futansyaNo equal 0";
        public static readonly string InvalidKohiYukoDate1 = "Invalid kohi1 YukoDate";
        public static readonly string InvalidKohiHokenMstStartDate1 = "Invalid kohi1 hokenMst startDate";
        public static readonly string InvalidKohiHokenMstEndDate1 = "Invalid kohi1 hokenMst endDate";
        public static readonly string InvalidKohiConfirmDate1 = "Invalid kohi1 hokenMst confirmDate";
        public static readonly string InvalidMstCheckHBT1 = "Invalid check HBT";
        public static readonly string InvalidMstCheckDigitFutansyaNo1 = "Invalid kohi1 check degit futansyaNo";
        public static readonly string InvalidMstCheckDigitJyukyusyaNo1 = "Invalid kohi1 check degit jyukyusyaNo";
        public static readonly string InvalidMstCheckAge1 = "Invalid kohi1 check date hokenMst age";
        public static readonly string InvalidFutanJyoTokuNull1 = "Invalid kohi1 futansyaNo jyukyusyaNo tokusyuNo null";
        public static readonly string InvalidKohiEmptyModel2 = "Invalid kohi2 empty model";
        public static readonly string InvalidKohiHokenMstEmpty2 = "Invalid kohi2 hokenMst empty model";
        public static readonly string InvalidFutansyaNoEmpty2 = "Invalid kohi2 futansyaNo empty";
        public static readonly string InvalidJyukyusyaNo2 = "Invalid kohi2 jyukyusyaNo empty";
        public static readonly string InvalidTokusyuNo2 = "Invalid kohi2 tokusyuNo empty";
        public static readonly string InvalidFutansyaNo02 = "Invalid kohi2 futansyaNo equal 0";
        public static readonly string InvalidKohiYukoDate2 = "Invalid kohi2 YukoDate";
        public static readonly string InvalidKohiHokenMstStartDate2 = "Invalid kohi2 hokenMst startDate";
        public static readonly string InvalidKohiHokenMstEndDate2 = "Invalid kohi2 hokenMst endDate";
        public static readonly string InvalidKohiConfirmDate2 = "Invalid kohi2 hokenMst confirmDate";
        public static readonly string InvalidMstCheckHBT2 = "Invalid check HBT";
        public static readonly string InvalidMstCheckDigitFutansyaNo2 = "Invalid kohi2 check degit futansyaNo";
        public static readonly string InvalidMstCheckDigitJyukyusyaNo2 = "Invalid kohi2 check degit jyukyusyaNo";
        public static readonly string InvalidMstCheckAge2 = "Invalid kohi2 check date hokenMst age";
        public static readonly string InvalidFutanJyoTokuNull2 = "Invalid kohi2 futansyaNo jyukyusyaNo tokusyuNo null";
        public static readonly string InvalidKohiEmptyModel3 = "Invalid kohi3 empty model";
        public static readonly string InvalidKohiHokenMstEmpty3 = "Invalid kohi3 hokenMst empty model";
        public static readonly string InvalidFutansyaNoEmpty3 = "Invalid kohi3 futansyaNo empty";
        public static readonly string InvalidJyukyusyaNo3 = "Invalid kohi3 jyukyusyaNo empty";
        public static readonly string InvalidTokusyuNo3 = "Invalid kohi3 tokusyuNo empty";
        public static readonly string InvalidFutansyaNo03 = "Invalid kohi3 futansyaNo equal 0";
        public static readonly string InvalidKohiYukoDate3 = "Invalid kohi3 YukoDate";
        public static readonly string InvalidKohiHokenMstStartDate3 = "Invalid kohi3 hokenMst startDate";
        public static readonly string InvalidKohiHokenMstEndDate3 = "Invalid kohi3 hokenMst endDate";
        public static readonly string InvalidKohiConfirmDate3 = "Invalid kohi3 hokenMst confirmDate";
        public static readonly string InvalidMstCheckHBT3 = "Invalid check HBT";
        public static readonly string InvalidMstCheckDigitFutansyaNo3 = "Invalid kohi3 check degit futansyaNo";
        public static readonly string InvalidMstCheckDigitJyukyusyaNo3 = "Invalid kohi3 check degit jyukyusyaNo";
        public static readonly string InvalidMstCheckAge3 = "Invalid kohi3 check date hokenMst age";
        public static readonly string InvalidFutanJyoTokuNull3 = "Invalid kohi3 futansyaNo jyukyusyaNo tokusyuNo null";
        public static readonly string InvalidKohiEmptyModel4 = "Invalid kohi4 empty model";
        public static readonly string InvalidKohiHokenMstEmpty4 = "Invalid kohi4 hokenMst empty model";
        public static readonly string InvalidFutansyaNoEmpty4 = "Invalid kohi4 futansyaNo empty";
        public static readonly string InvalidJyukyusyaNo4 = "Invalid kohi4 jyukyusyaNo empty";
        public static readonly string InvalidTokusyuNo4 = "Invalid kohi4 tokusyuNo empty";
        public static readonly string InvalidFutansyaNo04 = "Invalid kohi4 futansyaNo equal 0";
        public static readonly string InvalidKohiYukoDate4 = "Invalid kohi4 YukoDate";
        public static readonly string InvalidKohiHokenMstStartDate4 = "Invalid kohi4 hokenMst startDate";
        public static readonly string InvalidKohiHokenMstEndDate4 = "Invalid kohi4 hokenMst endDate";
        public static readonly string InvalidKohiConfirmDate4 = "Invalid kohi4 hokenMst confirmDate";
        public static readonly string InvalidMstCheckHBT4 = "Invalid check HBT";
        public static readonly string InvalidMstCheckDigitFutansyaNo4 = "Invalid kohi4 check degit futansyaNo";
        public static readonly string InvalidMstCheckDigitJyukyusyaNo4 = "Invalid kohi4 check degit jyukyusyaNo";
        public static readonly string InvalidMstCheckAge4 = "Invalid kohi4 check date hokenMst age";
        public static readonly string InvalidFutanJyoTokuNull4 = "Invalid kohi4 futansyaNo jyukyusyaNo tokusyuNo null";
        public static readonly string InvalidKohiEmpty21 = "Invalid kohi2 kohi1 empty model";
        public static readonly string InvalidKohiEmpty31 = "Invalid kohi3 kohi1 empty model";
        public static readonly string InvalidKohiEmpty32 = "Invalid kohi3 kohi2 empty model";
        public static readonly string InvalidKohiEmpty41 = "Invalid kohi4 kohi1 empty model";
        public static readonly string InvalidKohiEmpty42 = "Invalid kohi4 kohi2 empty model";
        public static readonly string InvalidKohiEmpty43 = "Invalid kohi4 kohi3 empty model";
        public static readonly string InvalidDuplicateKohi1 = "Invalid duplicate kohi1 empty model";
        public static readonly string InvalidDuplicateKohi2 = "Invalid duplicate kohi2 empty model";
        public static readonly string InvalidDuplicateKohi3 = "Invalid duplicate kohi3 empty model";
        public static readonly string InvalidDuplicateKohi4 = "Invalid duplicate kohi4 empty model";

        // Invalid Insurance Other
        public static readonly string InvalidPatternOtherAge75 = "Warning hokenInf age >= 75 and hokensyaNo length 8 and start 39";
        public static readonly string InvalidPatternOtherAge65 = "Warning hokenInf age < 65 and hokensyaNo length 8 and start 39";
        public static readonly string InvalidCheckDuplicatePattern = "Warning pattern duplicate";
        public static readonly string InvalidInsuranceList = "Invalid Insurance List";

        //Message Error Common
        public static readonly string MInp00010 = "{0}を入力してください。";
        public static readonly string MConf01020 = "{0}ため、{1}が確定できません。";
        public static readonly string MEnt01020 = "既に登録されているため、{0}は登録できません。";
        public static readonly string MInp00041 = "{0}は {1}を入力してください。";
        public static readonly string MFree00030 = "{0}";
        public static readonly string MInp00070 = "{0}は {1}以下を入力してください。";
        public static readonly string MInp00040 = "{0}ため、{1}は登録できません。";
        public static readonly string MInp00160_1 = "{0}が入力されていません。";
        public static readonly string MEnt00040_1 = "補足コメントが全角20文字を超えています。";

        //Sup Message
        public static readonly string MDrug = "薬剤";
        public static readonly string MInjection = "手技";
        public static readonly string MUsage = "用法";
        public static readonly string MSupUsage1 = "用法";
        public static readonly string MSupUsage2 = "補助用法";
        public static readonly string MEnt01040 = "{0}ため、{1}は登録できません。";
        public static readonly string ErrorCaptionDrugOrInject = "行為や加算が登録されている";
        public static readonly string MQuantity = "数量";
        public static readonly string MTooLargeQuantity = "数量が大きすぎます。";
        public static readonly string MUsageQuantity = "用法の数量";
        public static readonly string MMaxQuantity = "999";
        public static readonly string MMaxLengthOfCmt = "文字以内で入力してください。";
        public static readonly string MCmtOptOf830 = "文字情報";
        public static readonly string MCmt831 = "診療行為コード";
        public static readonly string MDateInfor850_1 = "年月日情報";
        public static readonly string MDateInfor850_2 = "年月情報";
        public static readonly string MTimeInfor851 = "時刻情報";
        public static readonly string MTimeInfor852 = "時間（分）情報";
        public static readonly string MDateTimeInfor853 = "日時情報（日、時間及び分を6桁）";
        public static readonly string MDateTimeInfor880 = "年月日情報及び数字情報を入力してください。" + "\r\n" + "※区切り文字「/」スラッシュを間に入力" + "\r\n" + @"　数字情報は数字または次の文字　．－＋≧≦＞＜±";
        public static readonly string MBunkatu = "分割調剤";
        public static readonly string MSumBunkatu = "分割調剤の合計";
        public static readonly string MCommonError = "無効なデータを受信しました。";
        public static readonly string MProcedure = "行為が未確定のため、入力が確定できません。\r\n・手技が入力されているか確認してください。";
        public static readonly string MDisease = "病名";
        public static readonly string MTenkiContinue = "転帰区分";
        public static readonly string MInp00110 = "{0}は {1}以降を入力してください。";
        public static readonly string MTenkiDate = "転帰日";
        public static readonly string MTenkiStartDate = "開始日";
        public static readonly string MTenkiStartDate_2 = "開始日に無効な日付を指定しました。";
        public static readonly string MNoInputData = "数量を入力してください。";
        public static readonly string MNoInputDataCmt = "数字情報を入力してください。";
        public static readonly string InvalidSelectiveComment = "コメントを追加してください";

        //Message full
        public static readonly string ErrorHasDrug = "行為や加算が登録されているため、薬剤は登録できません。";
        public static readonly string ErrorHasUsage = "行為や加算が登録されているため、用法は登録できません。";

        // Valid default settings
        public static readonly string InvalidDefaultSettingDoctor = "Invalid DefaultSettingDoctor < 0";

        //Insurance Master Linkage
        public static readonly string InvalidDigit1 = "Invalid Digit 1";
        public static readonly string InvalidDigit2 = "Invalid Digit 2";
        public static readonly string InvalidDigit3 = "Invalid Digit 3";
        public static readonly string InvalidDigit4 = "Invalid Digit 4";
        public static readonly string InvalidDigit5 = "Invalid Digit 5";
        public static readonly string InvalidDigit6 = "Invalid Digit 6";
        public static readonly string InvalidDigit7 = "Invalid Digit 7";
        public static readonly string InvalidDigit8 = "Invalid Digit 8";
        public static readonly string InvalidHokenNo = "Invalid HokenNo";
        public static readonly string InvalidHokenEdaNo = "Invalid HokenEdaNo";

        //DeletePatient
        public static readonly string NotAllowDeletePatient = "This patient is not allowed to delete";

        //SwapHoken
        public static readonly string SwapHokenSourceInsuranceHasNotSelected = "Please select the source insurance.";
        public static readonly string SwapHokenDesInsuranceHasNotSelected = "Please select a destination insurance.";
        public static readonly string SwapHokenStartDateGreaterThanEndDate = "Enter the end date after the start date.";
        public static readonly string SwapHokenCantExecNotValidDate = "Cannot be executed because the source policy has never been used in StartDate ~ EndDate.";

        //Account Due
        public static readonly string InvalidNyukinKbn = "入金区分が誤っています。";
        public static readonly string InvalidSortNo = "違法ソート番号.";
        public static readonly string InvalidAdjustFutan = "合計請求額(未収金の請求額も含む)が誤っています。合計請求額は0 以上である必要があります。";
        public static readonly string InvalidNyukinGaku = "入金額が誤っています。入金額は0 以上である必要があります。";
        public static readonly string InvalidPaymentMethodCd = "支払方法が誤っています。";
        public static readonly string InvalidNyukinDate = "入金日が誤っています。";
        public static readonly string InvalidUketukeSbt = "受付種別が誤っています。";
        public static readonly string NyukinCmtMaxLength100 = "受付種別が誤っています。, 受付種別の最大文字は１００文字です。";
        public static readonly string InvalidSeikyuGaku = "請求額が誤っています。";
        public static readonly string InvalidSeikyuAdjustFutan = "請求情報が見つかりません。";
        public static readonly string InvalidSeikyuTensu = "点数が誤っています。点数は0 以上である必要があります。";
        public static readonly string NoItemChange = "入力された会計情報が見つかりません。";
        public static readonly string NotAllowedPartialNyukin = "オンライン診療では一部精算が出来ません。";
        public static readonly string NotAllowedPaymentMethodChange = "オンライン診療では、支払方法の変更は出来ません。";
        public static readonly string NotAllowedNyukinDateChange = "オンライン診療では、入金日の変更は出来ません。";
        public static readonly string CardError = "クレジットカードによる支払が失敗しました。";
        public static readonly string NoDataSeikyu = "請求情報がありません";
        public static readonly string UnMatchSeikyuGaku = "請求額が一致しません。ご確認ください。";
        public static readonly string NotAllowedPaymentMethodChangeToFCO = "支払方法をFCO連携以外からFCO連携へ変更は出来ません。";
        public static readonly string NotAllowedBulkPaymentMethodSelectFCO = "FCO連携されているため免除できません。";
        public static readonly string UnCollectedPaymentNotSelectFCO = "未収金が発生しているため、支払方法に「FCO連携」を選択できません。";
        public static readonly string UnMatchSeikyuAndNyukinNotSelectFCO = "請求額と入金額が一致しないのでFCO連携は選択できません。";
        public static readonly string FcoApiKeyAlreadyExists = "FCO連携キーはすでに存在しています";
        public static readonly string FcoApiKeyAlreadyDeleted = "FCO連携キーはすでに削除されています";
        public static readonly string KensaCenterPartnershipAlreadyExists = "検査管理会社の登録がすでに存在しています";
        public static readonly string KensaCenterPartnershipAlreadyDeleted = "検査管理会社の登録はすでに削除されています";
        public static readonly string KensaMstAlreadyExists = "院内検査がすでに登録されています";
        public static readonly string KensaMstNotFound = "院内検査の登録が見つかりません";

        // Valid Pattern Expirated
        public static readonly string InvalidPatternHokenPid = "Invalid Pattern HokenPid";
        public static readonly string InvalidPatternConfirmDate = "Invalid Pattern ConfirmDate";
        public static readonly string InvalidHokenInfStartDate = "Invalid HokenInf StartDate";
        public static readonly string InvalidHokenInfEndDate = "Invalid HokenInf EndDate";
        public static readonly string InvalidConfirmDateAgeCheck = "Invalid ConfirmDate Age Check";
        public static readonly string InvalidConfirmDateHoken = "Invalid ConfirmDate Hoken";
        public static readonly string InvalidHokenMstDate = "Invalid HokenMst Date";
        public static readonly string InvalidConfirmDateKohi1 = "Invalid Kohi1 ConfirmDate";
        public static readonly string InvalidMasterDateKohi1 = "Invalid Kohi1 MasterDate";
        public static readonly string InvalidConfirmDateKohi2 = "Invalid Kohi2 ConfirmDate";
        public static readonly string InvalidMasterDateKohi2 = "Invalid Kohi2 MasterDate";
        public static readonly string InvalidConfirmDateKohi3 = "Invalid Kohi3 ConfirmDate";
        public static readonly string InvalidMasterDateKohi3 = "Invalid Kohi3 MasterDate";
        public static readonly string InvalidConfirmDateKohi4 = "Invalid Kohi4 ConfirmDate";
        public static readonly string InvalidMasterDateKohi4 = "Invalid Kohi4 MasterDate";
        public static readonly string InvalidPatternIsExpirated = "Invalid Pattern Expirated";
        public static readonly string InvalidHasElderHoken = "Invalid Has ElderHoken";

        // Validate Rousai Jibai
        public static readonly string InvalidRodoBangoNull = "Invalid RodoBango Null";
        public static readonly string InvalidRodoBangoLengthNotEquals14 = "Invalid RodoBango Length Not Equals 14";
        public static readonly string InvalidCheckItemFirstListRousaiTenki = "Invalid Check Item First Of ListRousaiTenki";
        public static readonly string InvalidCheckRousaiTenkiSinkei = "Invalid Check RousaiTenki Sinkei";
        public static readonly string InvalidCheckRousaiTenkiTenki = "Invalid Check RousaiTenki Tenki";
        public static readonly string InvalidCheckRousaiTenkiEndDate = "Invalid Check RousaiTenki EndDate";
        public static readonly string InvalidCheckRousaiSaigaiKbnNotEquals1And2 = "Invalid Check RousaiSaigaiKbn Not Equals 1 And 2";
        public static readonly string InvalidCheckRousaiSyobyoDateEquals0 = "Invalid Check Rousai SyobyoDate Equals 0";
        public static readonly string InvalidCheckHokenKbnEquals13AndRousaiSyobyoCdIsNull = "Invalid Check HokenKbn Equals 13 And RousaiSyobyoCd Is Null";
        public static readonly string InvalidCheckRousaiRyoyoDate = "Invalid Check RousaiRyoyoDate";
        public static readonly string InvalidCheckDateExpirated = "Invalid Check Date Expirated";
        public static readonly string InvalidNenkinBangoIsNull = "Invalid NenkinBango Is Null";
        public static readonly string InvalidNenkinBangoLengthNotEquals9 = "Invalid NenkinBango Length Not Equals 9";
        public static readonly string InvalidKenkoKanriBangoIsNull = "Invalid KenkoKanri Bango Is Null";
        public static readonly string InvalidKenkoKanriBangoLengthNotEquals13 = "Invalid KenkoKanri Bango Length Not Equals 13";

        //Next Order
        public static readonly string InvalidRsvkrtNo = "Invalid RsvkrtNo";
        public static readonly string InvalidRsvkrtKbn = "Invalid RsvkrtKbn";
        public static readonly string InvalidRsvDate = "Invalid RsvDate";
        public static readonly string InvalidRsvkrtName = "Invalid RsvkrtName";
        public static readonly string InvalidRsvkrtIsDeleted = "Invalid RsvkrtName";

        // Document
        public static readonly string InvalidDocumentCategoryCd = "指定されたカテゴリーは存在しないため、変更できません。";
        public static readonly string MoveDocCategoryNotFound = "Invalid Document MoveCategoryCd!";
        public static readonly string InvalidDocumentCategoryName = "Invalid Document CategoryName, CategoryName is required and not duplicate!";
        public static readonly string InvalidMoveInDocCategoryCd = "Invalid Document move in CategoryCd, CategoryCd is required and exist in DB!";
        public static readonly string InvalidMoveOutDocCategoryCd = "Invalid Document move out CategoryCd, CategoryCd is required and exist in DB!";
        public static readonly string InvalidDocInfFileName = "Invalid DocInf FileName, FileName is required!";
        public static readonly string InvalidFileInput = "Invalid File Input!";
        public static readonly string TemplateNotFound = "Template Not Found!";
        public static readonly string ExistFileTemplateName = "Exist FileTemplateName!";
        public static readonly string InvalidNewCategoryCd = "Invalid NewCategoryCd, CategoryCd is required and exist in DB!";
        public static readonly string InvalidOldCategoryCd = "Invalid OldCategoryCd, CategoryCd is required and exist in DB!";
        public static readonly string FileTemplateNotFould = "File template not fould!";
        public static readonly string FileInfoNotFound = "File info not found!";
        public static readonly string FileAttached = "File already attached to this Raiin!";
        public static readonly string FileTemplateIsExistInNewFolder = "File template is exist in new folder!";
        public static readonly string DocInfNotFound = "DocInf Not Found!";
        public static readonly string TemplateLinkIsNotExists = "TemplateLink is not exists!";
        public static readonly string InvalidExtentionFile = "Extention file is must .docx or .xlsx!";
        public static readonly string InvalidGetDate = "Invalid GetDate!";

        //Check Special Item InvalidCheckAge
        public static readonly string InvalidCheckAge = "Invalid Check Age";
        public static readonly string InvalidOdrInfDetail = "Invalid OdrInfDetail";
        public static readonly string InvalidIBirthDay = "Invalid IBirthDay";

        //Exception
        public static readonly string ExceptionError = "Exception error";

        //InsuranceScan
        public static readonly string SaveInsuranceScanFailedSaveToDb = "Failed save scan image to database";
        public static readonly string InvalidImageScan = "Image scan is invalid";
        public static readonly string OldScanImageIsNotFound = "Old scan image is not found";
        public static readonly string RemoveOldScanImageFailed = "Remove old scan image is failed";
        public static readonly string RemoveOldScanImageSuccessful = "Remove old scan image is succesful";

        //Drug Menu
        public static readonly string DrugMenuInvalidIndexMenu = "Invalid Menu Index";

        //PtGrpMaster
        public static readonly string InvalidInputGroupMst = "SortNo,GrpId,GrpName,GrpCode or GrpCodeCodeName is invalid";

        // SanteiInf
        public static readonly string InvalidAlertDays = "Invalid AlertDays!";
        public static readonly string InvalidAlertTerm = "Invalid AlertTerm!";
        public static readonly string InvalidKisanSbt = "Invalid KisanSbt!";
        public static readonly string InvalidKisanDate = "Invalid KisanDate!";
        public static readonly string InvalidByomei = "違法病名";
        public static readonly string InvalidHosokuComment = "Invalid HosokuComment, maxlength is 80!";
        public static readonly string ThisSanteiInfDoesNotAllowSanteiInfDetail = "This SanteiInf does not allow have SanteiInfDetail!";
        public static readonly string InvalidSanteiInfDetail = "Invalid SanteiInfDetail, SanteiInf does not contain some SanteiInfDetail!";

        //Insurance Mst Detail
        public static readonly string InvalidFHokenSbtKbn = "FHokenSbtKbn is in valid";
        public static readonly string InvalidFHokenNo = "FHokenNo is in valid";
        public static readonly string InvalidPrefNo = "PrefNo is in valid";

        // Family
        public static readonly string InvalidPtIdOrFamilyPtId = "予期しないエラーが発生しました。";
        public static readonly string InvalidFamilyId = "予期しないエラーが発生しました。";
        public static readonly string InvalidZokugaraCd = "続柄が重複しています。";
        public static readonly string InvalidFamilyBirthday = "予期しないエラーが発生しました。";
        public static readonly string InvalidFamilyIsSeparated = "予期しないエラーが発生しました。";
        public static readonly string InvalidFamilyBiko = "備考は120文字以内に入力してください。";
        public static readonly string InvalidFamilyRekiId = "予期しないエラーが発生しました。";
        public static readonly string InvalidByomeiCd = "検索対象が見つかりません。\n・あいまい検索する場合は、全角文字で入力してください。";
        public static readonly string InvalidFamilyCmt = "病名の備考は100文字以内に入力してください。";
        public static readonly string DuplicateFamily = "続柄が重複しています。";
        public static readonly string InvalidNameMaxLength = "氏名は100文字以内に入力してください。";
        public static readonly string InvalidKanaNameMaxLength = "氏名は100文字以内に入力してください。";
        public static readonly string FamilyNotAllow = "患者番号が重複しています。";

        // Rece
        public static readonly string InvalidSinYm = "Invalid SinYm!";
        public static readonly string InvalidReceCmtId = "Invalid ReceCmt Id!";
        public static readonly string InvalidCmtKbn = "Invalid ReceCmt CmtKbn!";
        public static readonly string InvalidCmtSbt = "Invalid ReceCmt CmtSbt!";
        public static readonly string InvalidCmt = "Invalid ReceCmt Cmt!";
        public static readonly string InvalidCmtData = "Invalid ReceCmt CmtData!";
        public static readonly string InvalidSyoukiKbn = "Invalid SyoukiKbn!";
        public static readonly string InvalidKeika = "Invalid Keika!";
        public static readonly string InvalidSinDay = "Invalid SinDay!";
        public static readonly string InvalidStatusColor = "Invalid StatusColor!";
        public static readonly string InvalidErrCd = "Invalid ErrCd!";
        public static readonly string InvalidCheckOpt = "Invalid CheckOpt!";
        public static readonly string InvalidSeikyuYm = "Invalid SeikyuYm!";
        public static readonly string InvalidNissuItem = "Invalid NissuItem!";
        public static readonly string InvalidTokkiItem = "Invalid TokkiItem!";
        public static readonly string InvalidFusenKbn = "Invalid FusenKbn!";
        public static readonly string InvalidStatusKbn = "Invalid StatusKbn!";
        public static readonly string InvalidReceCheckErrorItem = "Invalid ReceCheckErrorItem!";

        //Accounting
        public static readonly string InvalidSumAdjust = "合計請求額(未収金の請求額も含む)が誤っています。";
        public static readonly string InvalidThisWari = "調整額が誤っています。";
        public static readonly string InvalidPayType = "支払方法が誤っています。";
        public static readonly string InvalidComment = "コメントが誤っています。";
        public static readonly string StateChanged = "State Changed";
        public static readonly string VisitRemoved = "Visit Removed";
        public static readonly string BillUpdated = "情報が更新されているため、処理出来ません。";
        public static readonly string ValidPaymentAmount = "入金額が正しくありません。入金額を確認し、再実行してください。";
        public static readonly string DateNotVerify = "対象の入金情報が見つかりません。";
        public static readonly string InvalidThisCredit = "入金額が誤っています。";
        public static readonly string NoPaymentInfo = "支払情報が見つかりません。";
        public static readonly string TryAgainLater = "時間をおいて再度実行してください。";

        //SystemConfig
        public static readonly string InvalidCheckDrugSameName = "Invalid CheckDrugSameName.";
        public static readonly string InvalidAgentCheckSetting = "Invalid AgentCheckSetting.";
        public static readonly string InvalidDosageRatioSetting = "Invalid DosageRatioSetting.";
        public static readonly string InvalidFoodAllergyLevelSetting = "Invalid FoodAllergyLevelSetting.";
        public static readonly string InvalidDiseaseLevelSetting = "Invalid DiseaseLevelSetting.";
        public static readonly string InvalidKinkiLevelSetting = "Invalid KinkiLevelSetting.";
        public static readonly string InvalidDosageMinCheckSetting = "Invalid DosageMinCheckSetting.";
        public static readonly string InvalidAgeLevelSetting = "Invalid AgeLevelSetting.";

        public static readonly string InvalidTypeItem = "Invalid type item";

        //Chart Apporval
        public static readonly string InvalidInputListApporoval = "List ApprovalInf input is invalid";

        //Statistic
        public static readonly string InvalidMenuId = "Invalid MenuId!";
        public static readonly string InvalidGrpId = "Invalid GrpId!";
        public static readonly string InvalidReportId = "Invalid ReportId!";
        public static readonly string InvalidMenuName = "Invalid MenuName!";
        public static readonly string InvalidIraiCd = "Invalid IraiCd!";
        public static readonly string InvalidInputFile = "Invalid InputFile!";
        public static readonly string InvalidAbnormalKbn = "Invalid AbnormalKbn!";
        public static readonly string InvalidResultType = "Invalid ResultType!";
        public static readonly string InvalidKensaItemCd = "Invalid KensaItemCd!";

        public static readonly string NoPermission = "No permission";
        public static readonly string InvalidContentFile = "Invalid file content";

        // Online
        public static readonly string InvalidConfirmationResult = "Invalid ConfirmationResult.";
        public static readonly string InvalidOnlineId = "Invalid OnlineId.";
        public static readonly string InvalidUketukeStatus = "Invalid UketukeStatus.";
        public static readonly string InvalidOnlineConfirmationDate = "Invalid OnlineConfirmationDate.";
        public static readonly string InvalidXmlFile = "Invalid XmlFile.";
        public static readonly string InvalidArbitraryFileIdentifier = "Invalid InvalidArbitraryFileIdentifier.";
        public static readonly string InvalidBatchConfirmType = "Invalid Batch Confirm Type";

        // KensaIrai
        public static readonly string IsDeleteFile = "IsDeleteFile.";

        // Set Sendai Generation
        public static readonly string DeleteRowIndex0 = "Cannot delete row have index 0.";
        public static readonly string InvalidRowIndex = "Invalid row index.";
        public static readonly string InvalidGenerationId = "Invalid GenerationId.";

        // Renkei
        public static readonly string InvalidTemplateId = "Invalid TemplateId.";
        public static readonly string InvalidIsInvalid = "Invalid IsInvalid.";
        public static readonly string InvalidBiko = "Invalid Biko, max length is 300 characters.";
        public static readonly string InvalidPath = "Invalid Path, max length is 300 characters.";
        public static readonly string InvalidMachine = "Invalid Machine, max length is 60 characters.";
        public static readonly string InvalidParamRenki = "Invalid Machine, max length is 1000 characters.";
        public static readonly string InvalidWorkPath = "Invalid WorkPath, max length is 300 characters.";
        public static readonly string InvalidUser = "Invalid User, max length is 100 characters.";
        public static readonly string InvalidPassWord = "Invalid PassWord, max length is 100 characters.";
        public static readonly string InvalidEventCd = "Invalid EventCd.";
        public static readonly string InvalidRenkeiSbt = "Invalid RenkeiSbt.";
        public static readonly string InvalidRenkeiTimingModelList = "Invalid RenkeiTimingModelList.";


        //Main Menu
        public static readonly string InvalidConFName = "Invalid ConfName.";
        public static readonly string InvalidColumnName = "Invalid ColumnName.";

        // InrekiFilter
        public static readonly string InvalidSinrekiFilterMstName = "Invalid SinrekiFilterMst Name.";
        public static readonly string InvalidSinrekiFilterMstGrpCd = "Invalid SinrekiFilterMst GrpCd.";
        public static readonly string InvalidSinrekiFilterMstDetailId = "Invalid SinrekiFilterMstDetail Id.";
        public static readonly string InvalidSinrekiFilterMstKouiKbnId = "Invalid SinrekiFilterMstKoui KouiKbnId.";
        public static readonly string InvalidSinrekiFilterMstKouiSeqNo = "Invalid SinrekiFilterMstKoui SeqNo.";
        public static readonly string InvalidSinrekiFilterMstDetaiDuplicateItemCd = "Invalid SinrekiFilterMstDetai Duplicate ItemCd.";

        //Yousiki
        public static readonly string InvalidYousikiSinYm = "登録する受診年月日を指定してください。";
        public static readonly string InvalidYousikiSelectDataType0 = "種別を入力してください。";
        public static readonly string InvalidYousikiSelectDataType1 = "生活習慣病管理料をオーダーしている患者を一括で追加します。よろしいですか？";
        public static readonly string InvalidYousikiSelectDataType2 = "訪問診療料、在医総、施医総および在がん医総をオーダーしている患者を一括で追加します。よろしいですか？";
        public static readonly string InvalidYousikiSelectDataType3 = "疾患別リハビリテーション料をオーダーしている患者を一括で追加します。よろしいですか？";
        public static readonly string IsYousikiExist = "既に登録があるため、追加できません。";
        public static readonly string InvalidHealthInsuranceAccepted = "指定した受診年月に来院がないため、追加できません。";
        public static readonly string InvalidYousikiInf = "Invalid YousikiInf, YousikiInf does not exist or has been deleted";
        public static readonly string InvalidMode = "Invalid Mode, mode is between 1 and 3.";

        //KarteMedicalHistory
        public static readonly string InvalidDrinkingDetail = "Invalid Drinking Detail";
        public static readonly string InvalidSmokingDetail = "Invalid Smoking Detail";
        public static readonly string InvalidSmokingStartAge = "Invalid Smoking Start Age, Should be between 0 - 100";
        public static readonly string InvalidSmokingEndAge = "Invalid Smoking End Age, Should be between 0 - 100";
        public static readonly string InvalidSmokingAgeRange = "Invalid Smoking Age Range, Should be between 0 - 100";
        public static readonly string InvalidSmokingStartEndAgeRange = "Smoking age is invalid, must be younger than age";
        public static readonly string InvalidOnSetDate = "Invalid On Set Date";
        public static readonly string InvalidPregnancyStatus = "Invalid Pregnancy Status, Pregnancy status is between -1 and 2.";
        public static readonly string InvalidBreastfeedStatus = "Invalid Breastfeed Status, Breastfeed Status is between -1 and 1.";
        public static readonly string InvalidOtherDrugNameLengthNotEquals20 = "Invalid OtherDrugName Length Not Equals 20";

        //KarteVSPHYS
        public static readonly string IsExistedKensaTimeWithThisRaiinNo = "この検査日時は既に登録されています";

        //Summary
        public static readonly string OverLengthText = "Over Length Text";

        public static readonly string BadRequest = "Bad Request";

        public static readonly string InvalidReceptionNo = "Invalid ReceptionNo";
        //FCO連携
        public static readonly string FcoPtNoData = "指定された患者情報が存在しません。";

        public static readonly string FcoSeikyuNoData = "請求情報がありません。";

        public static readonly string FcoUnMatchSeikyuGaku = "請求額と入金額が合いません。";

        public static readonly string FcoChangePayment = "請求金額が変更されています。";
        public static readonly string FcoUnpaidSeikyuNoData = "未精算の請求情報がありません。";

        public static readonly string FcoSystemErr = "システムエラーが発生しました。システム管理者に連絡してください。";


        // Eps
        public static readonly string InvalidRefileCount = "Invalid Refile Count";
        public static readonly string InvalidHokensyaNo = "Invalid Hokensya No";
        public static readonly string InvalidKigo = "Invalid Kigo";
        public static readonly string InvalidBango = "Invalid Bango";
        public static readonly string InvalidEdaNo = "Invalid Eda No";
        public static readonly string InvalidKohiJyukyusyaNo = "Invalid Jyukyusya No";
        public static readonly string InvalidPrescriptionId = "Invalid Prescription Id";
        public static readonly string InvalidAccessCode = "Invalid Access Code";
        public static readonly string InvalidIssueType = "Invalid Issue Type";
        public static readonly string InvalidPrescriptionDocument = "Invalid Prescription Document";
        public static readonly string InvalidStatus = "Invalid Status";
        public static readonly string InvalidDeletedReason = "Invalid Deleted Reason";
        public static readonly string InvalidDeletedDate = "Invalid Deleted Date";
        public static readonly string InvalidReqDate = "Invalid Request Date";
        public static readonly string InvalidDateSeqNo = "Invalid Date Sequence No";
        public static readonly string InvalidDispensingResultId = "Invalid Dispensing Result Id";
        public static readonly string InvalidReqType = "Invalid Request Type";
        public static readonly string InvalidResultCode = "Invalid Result Code";
        public static readonly string InvalidResultMessage = "Invalid Result Message";
        public static readonly string InvalidResult = "Invalid Result";
        public static readonly string InvalidCheckResult = "Invalid Check Result";
        public static readonly string InvalidSameMedicalInstitutionAlertFlg = "Invalid Same Medical Institution Alert Flag";
        public static readonly string InvalidOnlineConsent = "Invalid Online Consent";
        public static readonly string InvalidOralBrowsingConsent = "Invalid Oral Browsing Consent";
        public static readonly string InvalidEpsChk = "Invalid Eps Check";
        public static readonly string InvalidEpsChkDetail = "Invalid Eps Check Detail";
        public static readonly string InvalidMessageId = "Invalid Message Id";
        public static readonly string InvalidMessageCategory = "Invalid Message Category";
        public static readonly string InvalidPharmaceuticalsIngredientName = "Invalid Pharmaceuticals Ingredient Name";
        public static readonly string InvalidMessage = "Invalid Message";
        public static readonly string InvalidTargetPharmaceuticalCodeType = "Invalid Target Pharmaceutical Code Type";
        public static readonly string InvalidTargetPharmaceuticalCode = "Invalid Target Pharmaceutical Code";
        public static readonly string InvalidTargetPharmaceuticalName = "Invalid Target Pharmaceutical Name";
        public static readonly string InvalidTargetDispensingQuantity = "Invalid Target Dispensing Quantity";
        public static readonly string InvalidTargetUsage = "Invalid Target Usage";
        public static readonly string InvalidTargetDosageForm = "Invalid Target Dosage Form";
        public static readonly string InvalidPastDate = "Invalid Past Date";
        public static readonly string InvalidPastPharmaceuticalCodeType = "Invalid Past Pharmaceutical Code Type";
        public static readonly string InvalidPastPharmaceuticalCode = "Invalid Past Pharmaceutical Code";
        public static readonly string InvalidPastPharmaceuticalName = "Invalid Past Pharmaceutical Name";
        public static readonly string InvalidPastMedicalInstitutionName = "Invalid Past Medical Institution Name";
        public static readonly string InvalidPastInsurancePharmacyName = "Invalid Past Insurance Pharmacy Name";
        public static readonly string InvalidPastUsage = "Invalid Past Usage";
        public static readonly string InvalidPastDispensingQuantity = "Invalid Past Dispensing Quantity";
        public static readonly string InvalidPastDosageForm = "Invalid Past Dosage Form";
        public static readonly string InvalidEpsPrescription = "Invalid Eps Prescription";
        public static readonly string InvalidKohiFutansyaNo = "Invalid Kohi Futansya No";
        public static readonly string InvalidPrescriptionStatus = "Invalid Prescription Status";
        public static readonly string InvalidEpsReference = "Invalid Eps Reference";
        public static readonly string InvalidEpsDispensingResultType = "Invalid Eps Dispensing Result Type";
        public static readonly string InvalidReceptionPharmacyName = "Invalid Reception Pharmacy Name";
        public static readonly string InvalidCancelReason = "Invalid Cancel Reason";
        public static readonly string InvalidEpsDispensingMessageFlag = "Invalid Eps Dispensing Message Flag";
        public static readonly string InvalidDispensingDate = "Invalid Dispensing Date";
        public static readonly string InvalidDispensingDateRange = "Invalid Dispensing Date Range";
        public static readonly string InvalidSinDateRange = "Invalid Sin Date Range";
        public static readonly string InvalidEpsDispensing = "Invalid Eps Dispensing";
        public static readonly string InvalidDispensingReqId = "Invalid Eps Dispensing Req Id";
        public static readonly string InvalidEpsDispensingReq = "Invalid Eps Dispensing Req";
        public static readonly string InvalidEpsReq = "Invalid Eps Req";
        public static readonly string NotFoundEpsReq = "Not Found Eps Req";
        public static readonly string InvalidEpsReqStatus = "Invalid Eps Req Status";
        public static readonly string InvalidEpsDispensingReqStatus = "Invalid Eps Dispensing Req Status";
        public static readonly string EndDateMustGreaterThanOrEqualStartDate = "End Date Must Be Greater Than Or Equal To Start Date";
        public static readonly string DateOverlap = "設定期間が重複しています";
    }
}
