﻿namespace EmrCloudApi.Constants;

// Reference: https://wiki.sotatek.com/display/SMAR/1.1+Auto+refresh+code
public static class FunctionCodes
{
    public const string ReceptionChanged = "ReceptionChanged";
    public const string PatientInfChanged = "PatientInfChanged";
    public const string LockChanged = "LockChanged";
    public const string MedicalLockChanged = "MedicalLockChanged";
    public const string SupserSetSaveChanged = "SupserSetSaveChanged";
    public const string SupserSetReorderChanged = "SupserSetReorderChanged";
    public const string SuperCopyPasteChanged = "SuperCopyPasteChanged";
    public const string RunCalculateMonth = "RunCalculateMonth";
    public const string ReceCheckCmtChanged = "ReceCheckCmtChanged";
    public const string RunCalculate = "RunCalculate";
    public const string TodoInfIsDeleted = "TodoInfIsDeleted";
    public const string TodoInfInserted = "TodoInfInserted";
    public const string TodoInfUpdated = "TodoInfUpdated";
    public const string KarteMedicalHistory = "KarteMedicalHistory";
    public const string RequestMedicalLock = "RequestMedicalLock";
    public const string ConfirmAcceptMedicalLock = "ConfirmAcceptMedicalLock";
    public const string TodayOdrChanged = "TodayOdrChanged";
    public const string FcoLinkNyukinChanged = "FcoLinkChanged";
}
