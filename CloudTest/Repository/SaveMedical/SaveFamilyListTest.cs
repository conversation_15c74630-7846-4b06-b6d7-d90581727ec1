﻿using Domain.Models.Family;
using Entity.Tenant;
using Infrastructure.Repositories.KarteMedicalHistory;
using Microsoft.Extensions.Configuration;
using Moq;

namespace CloudUnitTest.Repository.SaveMedical;

public class SaveFamilyListTest : BaseUT
{
    private Mock<IConfiguration>? _mockConfiguration;
    #region SaveFamilyList
    [Test]
    public void TC_001_SaveFamilyList_TestCreateFamilySuccess()
    {
        _mockConfiguration = new Mock<IConfiguration>();
        _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("localhost");
        _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();
        Random random = new();
        int hpId = random.Next(999, 99999);
        int userId = random.Next(999, 99999);
        long ptId = random.Next(999, 99999);

        PtFamilyRekiModel familyModel = new PtFamilyRekiModel(0, "byoCd", "byomei", "cmt", 0, false, hpId, ptId, 0, 0, string.Empty, "zokugaraCd", string.Empty);
        var ptFamily = new PtFamilyReki();
        KarteMedicalHistoryFamilyRepository familyRepository = new KarteMedicalHistoryFamilyRepository(TenantProvider, _mockConfiguration.Object);
        try
        {
            tenant.SaveChanges();

            // Act
            bool result = familyRepository.SavePtFamilies(userId, hpId, ptId, new List<PtFamilyRekiModel> { familyModel });

            // Assert
            ptFamily = tenant.PtFamilyRekis.FirstOrDefault(item => item.HpId == hpId
                                                               && item.PtId == familyModel.PtId
                                                               && item.ZokugaraCd == familyModel.ZokugaraCd
                                                               && item.SortNo == familyModel.SortNo
                                                               && item.Byomei == familyModel.Byomei
                                                               && item.ByomeiCd == familyModel.ByomeiCd
                                                               && item.Cmt == familyModel.Cmt
                                                               && item.ZokugaraElse == familyModel.ZokugaraElse
                                                               && item.ByotaiCd == familyModel.ByotaiCd
                                                               && item.IsDeleted == 0);

            result = result && ptFamily != null;

            Assert.True(result);
        }
        finally
        {
            familyRepository.ReleaseResource();
            if (ptFamily != null)
            {
                tenant.PtFamilyRekis.Remove(ptFamily);
            }
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_002_SaveFamilyList_TestUpdateFamilySuccess()
    {
        // Arrange
        _mockConfiguration = new Mock<IConfiguration>();
        _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("localhost");
        _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();
        Random random = new();
        int hpId = random.Next(999, 99999);
        int userId = random.Next(999, 99999);
        long ptId = random.Next(999, 99999);
        PtFamilyRekiModel familyModel = new PtFamilyRekiModel(0, "byoCd", "byomei", "cmt", 0, false, hpId, ptId, 0, 0, string.Empty, "zokugaraCd", string.Empty);
        PtFamilyReki? ptFamily = new PtFamilyReki()
        {
            FamilyId = familyModel.FamilyId,
            HpId = hpId,
            PtId = familyModel.PtId,
        };

        var ptFamilyEntity = tenant.PtFamilyRekis.Add(ptFamily);
        tenant.SaveChanges();
        familyModel.Id = ptFamilyEntity.Entity.Id;

        KarteMedicalHistoryFamilyRepository familyRepository = new KarteMedicalHistoryFamilyRepository(TenantProvider, _mockConfiguration.Object);
        try
        {
            tenant.SaveChanges();

            // Act
            bool result = familyRepository.SavePtFamilies(userId, hpId, ptId, new List<PtFamilyRekiModel> { familyModel });

            // Assert
            var ptFamilyAfter = tenant.PtFamilyRekis.FirstOrDefault(item => item.HpId == hpId
                                                               && item.PtId == familyModel.PtId
                                                               && item.ZokugaraCd == familyModel.ZokugaraCd
                                                               && item.SortNo == familyModel.SortNo
                                                               && item.Byomei == familyModel.Byomei
                                                               && item.ByomeiCd == familyModel.ByomeiCd
                                                               && item.Cmt == familyModel.Cmt
                                                               && item.ZokugaraElse == familyModel.ZokugaraElse
                                                               && item.ByotaiCd == familyModel.ByotaiCd
                                                               && item.Id == familyModel.Id
                                                               && item.IsDeleted == 0);

            result = result && ptFamilyAfter != null;

            Assert.True(result);
        }
        finally
        {
            familyRepository.ReleaseResource();

            if (ptFamily != null)
            {
                tenant.PtFamilyRekis.Remove(ptFamily);
            }
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_003_SaveFamilyList_TestDeleteFamilySuccess()
    {
        // Arrange
        _mockConfiguration = new Mock<IConfiguration>();
        _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("localhost");
        _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();
        Random random = new();
        int hpId = random.Next(999, 99999);
        int userId = random.Next(999, 99999);
        long ptId = random.Next(999, 99999);
        PtFamilyRekiModel familyModel = new PtFamilyRekiModel(0, "byoCd", "byomei", "cmt", 0, true, hpId, ptId, 0, 0, string.Empty, "zokugaraCd", string.Empty);
        PtFamilyReki? ptFamily = new PtFamilyReki()
        {
            FamilyId = familyModel.FamilyId,
            HpId = hpId,
            PtId = familyModel.PtId,
        };

        var ptFamilyEntity = tenant.PtFamilyRekis.Add(ptFamily);
        tenant.SaveChanges();
        familyModel.Id = ptFamilyEntity.Entity.Id;

        KarteMedicalHistoryFamilyRepository familyRepository = new KarteMedicalHistoryFamilyRepository(TenantProvider, _mockConfiguration.Object);
        try
        {
            tenant.SaveChanges();

            // Act
            bool result = familyRepository.SavePtFamilies(userId, hpId, ptId, new List<PtFamilyRekiModel> { familyModel });

            // Assert
            var ptFamilyAfter = tenant.PtFamilyRekis.FirstOrDefault(item => item.HpId == hpId
                                                               && item.PtId == familyModel.PtId
                                                               && item.Id == familyModel.Id
                                                               && item.IsDeleted == 1);

            result = result && ptFamilyAfter != null;

            Assert.True(result);
        }
        finally
        {
            familyRepository.ReleaseResource();

            if (ptFamily != null)
            {
                tenant.PtFamilyRekis.Remove(ptFamily);
            }
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_004_SaveFamilyList_TestUpdateFamilyFalse()
    {
        // Arrange
        _mockConfiguration = new Mock<IConfiguration>();
        _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("localhost");
        _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        // Arrange
        var tenant = TenantProvider.GetNoTrackingDataContext();
        Random random = new();
        int hpId = random.Next(999, 99999);
        int userId = random.Next(999, 99999);
        long ptId = random.Next(999, 99999);
        PtFamilyRekiModel familyModel = new PtFamilyRekiModel(0, "byoCd", "byomei", "cmt", 0, true, hpId, ptId, 0, 0, string.Empty, "zokugaraCd", string.Empty);
        PtFamilyReki? ptFamily = new PtFamilyReki()
        {
            FamilyId = familyModel.FamilyId,
            HpId = hpId,
            PtId = familyModel.PtId,
        };

        var ptFamilyEntity = tenant.PtFamilyRekis.Add(ptFamily);
        tenant.SaveChanges();
        familyModel.Id = ptFamilyEntity.Entity.Id + 1;

        KarteMedicalHistoryFamilyRepository familyRepository = new KarteMedicalHistoryFamilyRepository(TenantProvider, _mockConfiguration.Object);
        try
        {
            tenant.SaveChanges();

            // Act
            bool result = familyRepository.SavePtFamilies(userId, hpId, ptId, new List<PtFamilyRekiModel> { familyModel });

            // Assert
            var ptFamilyAfter = tenant.PtFamilyRekis.FirstOrDefault(item => item.HpId == hpId
                                                               && item.PtId == familyModel.PtId
                                                               && item.Id == familyModel.Id
                                                               && item.IsDeleted == 1);

            result = result && ptFamilyAfter == null;

            Assert.True(result);
        }
        finally
        {
            familyRepository.ReleaseResource();

            if (ptFamily != null)
            {
                tenant.PtFamilyRekis.Remove(ptFamily);
            }
            tenant.SaveChanges();
        }
    }
    #endregion SaveFamilyList

    //#region SaveFamilyRekiList
    //[Test]
    //public void TC_005_SaveFamilyList_TestSaveFamilyRekiListSuccess()
    //{
    //    // Arrange
    //    var tenant = TenantProvider.GetNoTrackingDataContext();
    //    Random random = new();
    //    int hpId = random.Next(999, 99999);
    //    int userId = random.Next(999, 99999);

    //    PtFamilyRekiModel ptFamilyRekiModel = new PtFamilyRekiModel(0, "byoCdUT", "byomeiPtFamilyReki", "cmtPtFamilyReki", random.Next(999, 99999999), false);
    //    PtFamilyRekiModel familyModel = new PtFamilyRekiModel(random.Next(999, 99999999), random.Next(999, 99999999), "zokugaraCd", random.Next(999, 99999), "nameFamily", "kanaNameFamily", 1, 20000202, 0, 0, "bikoFamily", random.Next(999, 99999), false, new() { ptFamilyRekiModel });
    //    PtFamily? ptFamily = new PtFamily()
    //    {
    //        FamilyId = familyModel.FamilyId,
    //        HpId = hpId,
    //        PtId = familyModel.PtId,
    //    };
    //    PtFamilyReki? ptFamilyReki = null;

    //    tenant.PtFamilys.Add(ptFamily);
    //    FamilyRepository familyRepository = new FamilyRepository(TenantProvider);
    //    try
    //    {
    //        tenant.SaveChanges();

    //        // Act
    //        bool result = familyRepository.SaveFamilyList(hpId, userId, new() { familyModel });

    //        // Assert
    //        var ptFamilyAfter = tenant.PtFamilys.FirstOrDefault(item => item.HpId == hpId
    //                                                            && item.FamilyId == familyModel.FamilyId
    //                                                            && item.PtId == familyModel.PtId
    //                                                            && item.ZokugaraCd == familyModel.ZokugaraCd
    //                                                            && item.SortNo == familyModel.SortNo
    //                                                            && item.FamilyPtId == familyModel.FamilyPtId
    //                                                            && item.KanaName == familyModel.KanaName
    //                                                            && item.Name == familyModel.Name
    //                                                            && item.Sex == familyModel.Sex
    //                                                            && item.Birthday == familyModel.Birthday
    //                                                            && item.IsDead == familyModel.IsDead
    //                                                            && item.IsSeparated == familyModel.IsSeparated
    //                                                            && item.Biko == familyModel.Biko
    //                                                            && item.IsDeleted == 0);

    //        if (ptFamilyAfter == null)
    //        {
    //            Assert.True(false);
    //        }
    //        ptFamilyReki = tenant.PtFamilyRekis.FirstOrDefault(item => item.HpId == hpId
    //                                                                   && item.PtId == ptFamilyAfter!.FamilyPtId
    //                                                                   && item.FamilyId == ptFamilyAfter.FamilyId
    //                                                                   && item.SortNo == ptFamilyRekiModel.SortNo
    //                                                                   && item.ByomeiCd == ptFamilyRekiModel.ByomeiCd
    //                                                                   && item.Byomei == ptFamilyRekiModel.Byomei
    //                                                                   && item.Cmt == ptFamilyRekiModel.Cmt
    //                                                                   && item.IsDeleted == 0);

    //        result = result && ptFamilyAfter != null && ptFamilyReki != null;

    //        Assert.True(result);
    //    }
    //    finally
    //    {
    //        familyRepository.ReleaseResource();

    //        if (ptFamily != null)
    //        {
    //            tenant.PtFamilys.Remove(ptFamily);
    //        }
    //        if (ptFamilyReki != null)
    //        {
    //            tenant.PtFamilyRekis.Remove(ptFamilyReki);
    //        }
    //        tenant.SaveChanges();
    //    }
    //}

    //[Test]
    //public void TC_006_SaveFamilyList_TestUpdateFamilyRekiListSuccess()
    //{
    //    // Arrange
    //    var tenant = TenantProvider.GetNoTrackingDataContext();
    //    Random random = new();
    //    int hpId = random.Next(999, 99999);
    //    int userId = random.Next(999, 99999);

    //    PtFamilyRekiModel ptFamilyRekiModel = new PtFamilyRekiModel(random.Next(999, 99999999), "byoCdUT", "byomeiPtFamilyReki", "cmtPtFamilyReki", random.Next(999, 99999999), false);
    //    PtFamilyRekiModel familyModel = new PtFamilyRekiModel(random.Next(999, 99999999), random.Next(999, 99999999), "zokugaraCd", random.Next(999, 99999), "nameFamily", "kanaNameFamily", 1, 20000202, 0, 0, "bikoFamily", random.Next(999, 99999), false, new() { ptFamilyRekiModel });
    //    PtFamily? ptFamily = new()
    //    {
    //        FamilyId = familyModel.FamilyId,
    //        HpId = hpId,
    //        PtId = familyModel.PtId,
    //    };
    //    PtFamilyReki? ptFamilyReki = new()
    //    {
    //        HpId = hpId,
    //        Id = ptFamilyRekiModel.Id,
    //        FamilyId = ptFamily.FamilyId
    //    };

    //    tenant.PtFamilys.Add(ptFamily);
    //    tenant.PtFamilyRekis.Add(ptFamilyReki);
    //    FamilyRepository familyRepository = new FamilyRepository(TenantProvider);
    //    try
    //    {
    //        tenant.SaveChanges();

    //        // Act
    //        bool result = familyRepository.SaveFamilyList(hpId, userId, new() { familyModel });

    //        // Assert
    //        var ptFamilyAfter = tenant.PtFamilys.FirstOrDefault(item => item.HpId == hpId
    //                                                            && item.FamilyId == familyModel.FamilyId
    //                                                            && item.PtId == familyModel.PtId
    //                                                            && item.ZokugaraCd == familyModel.ZokugaraCd
    //                                                            && item.SortNo == familyModel.SortNo
    //                                                            && item.FamilyPtId == familyModel.FamilyPtId
    //                                                            && item.KanaName == familyModel.KanaName
    //                                                            && item.Name == familyModel.Name
    //                                                            && item.Sex == familyModel.Sex
    //                                                            && item.Birthday == familyModel.Birthday
    //                                                            && item.IsDead == familyModel.IsDead
    //                                                            && item.IsSeparated == familyModel.IsSeparated
    //                                                            && item.Biko == familyModel.Biko
    //                                                            && item.IsDeleted == 0);

    //        if (ptFamilyAfter == null)
    //        {
    //            Assert.True(false);
    //        }
    //        var ptFamilyRekiAfter = tenant.PtFamilyRekis.FirstOrDefault(item => item.HpId == hpId
    //                                                                    && item.FamilyId == ptFamilyAfter!.FamilyId
    //                                                                    && item.Id == ptFamilyRekiModel.Id
    //                                                                    && item.SortNo == ptFamilyRekiModel.SortNo
    //                                                                    && item.ByomeiCd == ptFamilyRekiModel.ByomeiCd
    //                                                                    && item.Byomei == ptFamilyRekiModel.Byomei
    //                                                                    && item.Cmt == ptFamilyRekiModel.Cmt
    //                                                                    && item.IsDeleted == 0);

    //        result = result && ptFamilyAfter != null && ptFamilyRekiAfter != null;

    //        Assert.True(result);
    //    }
    //    finally
    //    {
    //        familyRepository.ReleaseResource();

    //        if (ptFamily != null)
    //        {
    //            tenant.PtFamilys.Remove(ptFamily);
    //        }
    //        if (ptFamilyReki != null)
    //        {
    //            tenant.PtFamilyRekis.Remove(ptFamilyReki);
    //        }
    //        tenant.SaveChanges();
    //    }
    //}

    //[Test]
    //public void TC_007_SaveFamilyList_TestDeleteFamilyRekiListSuccess()
    //{
    //    // Arrange
    //    var tenant = TenantProvider.GetNoTrackingDataContext();
    //    Random random = new();
    //    int hpId = random.Next(999, 99999);
    //    int userId = random.Next(999, 99999);

    //    PtFamilyRekiModel ptFamilyRekiModel = new PtFamilyRekiModel(random.Next(999, 99999999), "byoCdUT", "byomeiPtFamilyReki", "cmtPtFamilyReki", random.Next(999, 99999999), true);
    //    PtFamilyRekiModel familyModel = new PtFamilyRekiModel(random.Next(999, 99999999), random.Next(999, 99999999), "zokugaraCd", random.Next(999, 99999), "nameFamily", "kanaNameFamily", 1, 20000202, 0, 0, "bikoFamily", random.Next(999, 99999), false, new() { ptFamilyRekiModel });
    //    PtFamily? ptFamily = new()
    //    {
    //        FamilyId = familyModel.FamilyId,
    //        HpId = hpId,
    //        PtId = familyModel.PtId,
    //    };
    //    PtFamilyReki? ptFamilyReki = new()
    //    {
    //        HpId = hpId,
    //        Id = ptFamilyRekiModel.Id,
    //        FamilyId = ptFamily.FamilyId
    //    };

    //    tenant.PtFamilys.Add(ptFamily);
    //    tenant.PtFamilyRekis.Add(ptFamilyReki);
    //    FamilyRepository familyRepository = new FamilyRepository(TenantProvider);
    //    try
    //    {
    //        tenant.SaveChanges();

    //        // Act
    //        bool result = familyRepository.SaveFamilyList(hpId, userId, new() { familyModel });

    //        // Assert
    //        var ptFamilyAfter = tenant.PtFamilys.FirstOrDefault(item => item.HpId == hpId
    //                                                            && item.FamilyId == familyModel.FamilyId
    //                                                            && item.PtId == familyModel.PtId
    //                                                            && item.ZokugaraCd == familyModel.ZokugaraCd
    //                                                            && item.SortNo == familyModel.SortNo
    //                                                            && item.FamilyPtId == familyModel.FamilyPtId
    //                                                            && item.KanaName == familyModel.KanaName
    //                                                            && item.Name == familyModel.Name
    //                                                            && item.Sex == familyModel.Sex
    //                                                            && item.Birthday == familyModel.Birthday
    //                                                            && item.IsDead == familyModel.IsDead
    //                                                            && item.IsSeparated == familyModel.IsSeparated
    //                                                            && item.Biko == familyModel.Biko
    //                                                            && item.IsDeleted == 0);

    //        if (ptFamilyAfter == null)
    //        {
    //            Assert.True(false);
    //        }
    //        var ptFamilyRekiAfter = tenant.PtFamilyRekis.FirstOrDefault(item => item.HpId == hpId
    //                                                                            && item.FamilyId == ptFamilyAfter!.FamilyId
    //                                                                            && item.Id == ptFamilyRekiModel.Id
    //                                                                            && item.IsDeleted == 1);

    //        result = result && ptFamilyAfter != null && ptFamilyRekiAfter != null;

    //        Assert.True(result);
    //    }
    //    finally
    //    {
    //        familyRepository.ReleaseResource();

    //        if (ptFamily != null)
    //        {
    //            tenant.PtFamilys.Remove(ptFamily);
    //        }
    //        if (ptFamilyReki != null)
    //        {
    //            tenant.PtFamilyRekis.Remove(ptFamilyReki);
    //        }
    //        tenant.SaveChanges();
    //    }
    //}

    //[Test]
    //public void TC_008_SaveFamilyList_TestSaveFamilyRekiListFalse()
    //{
    //    // Arrange
    //    var tenant = TenantProvider.GetNoTrackingDataContext();
    //    Random random = new();
    //    int hpId = random.Next(999, 99999);
    //    int userId = random.Next(999, 99999);

    //    PtFamilyRekiModel ptFamilyRekiModel = new PtFamilyRekiModel(random.Next(999, 99999999), "byoCdUT", "byomeiPtFamilyReki", "cmtPtFamilyReki", random.Next(999, 99999999), true);
    //    PtFamilyRekiModel familyModel = new PtFamilyRekiModel(random.Next(999, 99999999), random.Next(999, 99999999), "zokugaraCd", random.Next(999, 99999), "nameFamily", "kanaNameFamily", 1, 20000202, 0, 0, "bikoFamily", random.Next(999, 99999), false, new() { ptFamilyRekiModel });
    //    PtFamily? ptFamily = new()
    //    {
    //        FamilyId = familyModel.FamilyId,
    //        HpId = hpId,
    //        PtId = familyModel.PtId,
    //    };
    //    PtFamilyReki? ptFamilyReki = new()
    //    {
    //        HpId = hpId,
    //        Id = random.Next(999, 99999999),
    //        FamilyId = ptFamily.FamilyId
    //    };

    //    tenant.PtFamilys.Add(ptFamily);
    //    tenant.PtFamilyRekis.Add(ptFamilyReki);
    //    FamilyRepository familyRepository = new FamilyRepository(TenantProvider);
    //    try
    //    {
    //        tenant.SaveChanges();

    //        // Act
    //        bool result = familyRepository.SaveFamilyList(hpId, userId, new() { familyModel });

    //        // Assert
    //        var ptFamilyAfter = tenant.PtFamilys.FirstOrDefault(item => item.HpId == hpId
    //                                                            && item.FamilyId == familyModel.FamilyId
    //                                                            && item.PtId == familyModel.PtId
    //                                                            && item.ZokugaraCd == familyModel.ZokugaraCd
    //                                                            && item.SortNo == familyModel.SortNo
    //                                                            && item.FamilyPtId == familyModel.FamilyPtId
    //                                                            && item.KanaName == familyModel.KanaName
    //                                                            && item.Name == familyModel.Name
    //                                                            && item.Sex == familyModel.Sex
    //                                                            && item.Birthday == familyModel.Birthday
    //                                                            && item.IsDead == familyModel.IsDead
    //                                                            && item.IsSeparated == familyModel.IsSeparated
    //                                                            && item.Biko == familyModel.Biko
    //                                                            && item.IsDeleted == 0);

    //        if (ptFamilyAfter == null)
    //        {
    //            Assert.True(false);
    //        }
    //        var ptFamilyRekiAfter = tenant.PtFamilyRekis.FirstOrDefault(item => item.HpId == hpId
    //                                                                            && item.FamilyId == ptFamilyAfter!.FamilyId
    //                                                                            && item.Id == ptFamilyRekiModel.Id);

    //        result = result && ptFamilyAfter != null && ptFamilyRekiAfter == null;

    //        Assert.True(result);
    //    }
    //    finally
    //    {
    //        familyRepository.ReleaseResource();


    //        if (ptFamily != null)
    //        {
    //            tenant.PtFamilys.Remove(ptFamily);
    //        }
    //        if (ptFamilyReki != null)
    //        {
    //            tenant.PtFamilyRekis.Remove(ptFamilyReki);
    //        }
    //        tenant.SaveChanges();
    //    }
    //}
    //#endregion SaveFamilyRekiList

    // #region UpdatePtInf
    // [Test]
    // public void TC_009_SaveFamilyList_TestUpdatePtInfSuccess()
    // {
    //     // Arrange
    //     var tenant = TenantProvider.GetNoTrackingDataContext();
    //     Random random = new();
    //     int hpId = random.Next(999, 99999);
    //     int userId = random.Next(999, 99999);

    //     FamilyModel familyModel = new FamilyModel(random.Next(999, 99999999), random.Next(999, 99999999), "zokugaraCd", random.Next(999, 99999), "nameFamily", "kanaNameFamily", 1, 20000202, 1, 0, "bikoFamily", random.Next(999, 99999), false, new() { });
    //     PtFamily? ptFamily = new PtFamily()
    //     {
    //         FamilyId = familyModel.FamilyId,
    //         HpId = hpId,
    //         PtId = familyModel.PtId,
    //     };
    //     PtInf ptInf = new PtInf()
    //     {
    //         HpId = hpId,
    //         PtId = familyModel.FamilyPtId,
    //         IsDead = 0
    //     };
    //     tenant.PtFamilys.Add(ptFamily);
    //     tenant.PtInfs.Add(ptInf);
    //     FamilyRepository familyRepository = new FamilyRepository(TenantProvider);
    //     try
    //     {
    //         tenant.SaveChanges();

    //         // Act
    //         bool result = familyRepository.SaveFamilyList(hpId, userId, new() { familyModel });

    //         // Assert
    //         var ptFamilyAfter = tenant.PtFamilys.FirstOrDefault(item => item.HpId == hpId
    //                                                             && item.FamilyId == familyModel.FamilyId
    //                                                             && item.PtId == familyModel.PtId
    //                                                             && item.ZokugaraCd == familyModel.ZokugaraCd
    //                                                             && item.SortNo == familyModel.SortNo
    //                                                             && item.FamilyPtId == familyModel.FamilyPtId
    //                                                             && item.KanaName == familyModel.KanaName
    //                                                             && item.Name == familyModel.Name
    //                                                             && item.Sex == familyModel.Sex
    //                                                             && item.Birthday == familyModel.Birthday
    //                                                             && item.IsDead == familyModel.IsDead
    //                                                             && item.IsSeparated == familyModel.IsSeparated
    //                                                             && item.Biko == familyModel.Biko
    //                                                             && item.IsDeleted == 0);
    //         if (ptFamilyAfter == null)
    //         {
    //             Assert.True(false);
    //         }

    //         var ptInfAfter = tenant.PtInfs.FirstOrDefault(item => item.HpId == hpId
    //                                                               && item.PtId == ptFamilyAfter!.FamilyPtId
    //                                                               && item.IsDead == ptFamilyAfter.IsDead);

    //         result = result && ptFamilyAfter != null && ptInfAfter != null;

    //         Assert.True(result);
    //     }
    //     finally
    //     {
    //         familyRepository.ReleaseResource();

    //         if (ptFamily != null)
    //         {
    //             tenant.PtFamilys.Remove(ptFamily);
    //         }
    //         if (ptInf != null)
    //         {
    //             tenant.PtInfs.Remove(ptInf);
    //         }
    //         tenant.SaveChanges();
    //     }
    // }
    // #endregion UpdatePtInf
}
