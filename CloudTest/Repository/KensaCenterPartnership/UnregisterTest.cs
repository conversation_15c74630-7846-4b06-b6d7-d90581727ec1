using Helper.Constants;
using Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;
using PostgreDataContext;

namespace CloudUnitTest.Repository.KensaCenterPartnership;

public class UnregisterTest : BaseUT
{
    [Test]
    public void TC_001_UnregisterKensaCenterPartnership_Success_WhenOneRecordDeleted()
    {
        // Arrange
        SetupTestEnvironment(out KensaCenterPartnershipRepository repository, out TenantDataContext tenant);

        int hpId = 1;
        string centerCd = "TEST001";
        int startDate = 20240101;
        int endDate = 20241231;

        // テストデータを作成
        var testPartnership = new Entity.Tenant.KensaCenterPartnership
        {
            HpId = hpId,
            CenterCd = centerCd,
            StartDate = startDate,
            EndDate = endDate,
            IsDeleted = DeleteTypes.None,
            MasterUpdateDate = DateTime.UtcNow,
            CreateDate = DateTime.UtcNow,
            CreateId = 0,
            UpdateDate = DateTime.UtcNow,
            UpdateId = 0
        };

        try
        {
            // テストデータを挿入
            tenant.KensaCenterPartnership.Add(testPartnership);
            tenant.SaveChanges();

            // Act
            var result = repository.UnregisterKensaCenterPartnership(hpId, centerCd, startDate);

            // Assert
            Assert.IsTrue(result, "正常に削除された場合はtrueが返される");

            // 新しいコンテキストで削除状態を確認
            using var verifyContext = TenantProvider.GetNoTrackingDataContext();
            var deletedPartnership = verifyContext.KensaCenterPartnership
                .FirstOrDefault(p => p.HpId == hpId && p.CenterCd == centerCd && p.StartDate == startDate);

            Assert.IsNotNull(deletedPartnership, "削除されたデータが存在する");
            Assert.AreEqual(DeleteTypes.Deleted, deletedPartnership.IsDeleted, "is_deletedフラグが1に設定されている");
        }
        finally
        {
            // テストデータを物理削除
            var sqlCommand = "DELETE FROM kensa_center_partnerships WHERE hp_id = {0} AND center_cd = {1} AND start_date = {2}";
            tenant.Database.ExecuteSqlRaw(sqlCommand, hpId, centerCd, startDate);
            repository.ReleaseResource();
        }
    }

    [Test]
    public void TC_002_UnregisterKensaCenterPartnership_ThrowsException_WhenNoRecordExists()
    {
        // Arrange
        SetupTestEnvironment(out KensaCenterPartnershipRepository repository, out TenantDataContext tenant);

        int hpId = 1;
        string centerCd = "NOTEXIST";
        int startDate = 20240101;

        try
        {
            // Act & Assert
            var exception = Assert.Throws<KensaCenterPartnershipSpecialException>(() =>
                repository.UnregisterKensaCenterPartnership(hpId, centerCd, startDate)
            );

            Assert.IsNotNull(exception, "削除対象が0件の場合はKensaCenterPartnershipSpecialExceptionがスローされる");
        }
        finally
        {
            repository.ReleaseResource();
        }
    }

    [Test]
    public void TC_003_UnregisterKensaCenterPartnership_ThrowsException_WhenAlreadyDeleted()
    {
        // Arrange
        SetupTestEnvironment(out KensaCenterPartnershipRepository repository, out TenantDataContext tenant);

        int hpId = 1;
        string centerCd = "TEST002";
        int startDate = 20240101;
        int endDate = 20241231;

        // 既に削除済みのテストデータを作成
        var testPartnership = new Entity.Tenant.KensaCenterPartnership
        {
            HpId = hpId,
            CenterCd = centerCd,
            StartDate = startDate,
            EndDate = endDate,
            IsDeleted = DeleteTypes.Deleted,  // 最初から削除済み
            MasterUpdateDate = DateTime.UtcNow,
            CreateDate = DateTime.UtcNow,
            CreateId = 0,
            UpdateDate = DateTime.UtcNow,
            UpdateId = 0
        };

        try
        {
            // テストデータを挿入
            tenant.KensaCenterPartnership.Add(testPartnership);
            tenant.SaveChanges();

            // Act & Assert
            var exception = Assert.Throws<KensaCenterPartnershipSpecialException>(() =>
                repository.UnregisterKensaCenterPartnership(hpId, centerCd, startDate)
            );

            Assert.IsNotNull(exception, "既に削除済みのデータに対してはKensaCenterPartnershipSpecialExceptionがスローされる");
        }
        finally
        {
            // テストデータを物理削除
            var sqlCommand = "DELETE FROM kensa_center_partnerships WHERE hp_id = {0} AND center_cd = {1} AND start_date = {2}";
            tenant.Database.ExecuteSqlRaw(sqlCommand, hpId, centerCd, startDate);
            repository.ReleaseResource();
        }
    }

    private void SetupTestEnvironment(out KensaCenterPartnershipRepository repository, out TenantDataContext tenant)
    {
        repository = new KensaCenterPartnershipRepository(TenantProvider);
        tenant = TenantProvider.GetTrackingTenantDataContext();
    }
}
