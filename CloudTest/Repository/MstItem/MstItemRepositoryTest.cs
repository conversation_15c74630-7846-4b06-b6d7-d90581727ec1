using Domain.Models.MstItem;
using Infrastructure.Repositories;
using Microsoft.Extensions.Options;
using Infrastructure.Options;
using Moq;
using Microsoft.Extensions.Configuration;

namespace CloudUnitTest.Repository.MstItem
{
    public class MstItemRepositoryTest : BaseUT
    {
        private MstItemRepository _repository;

        [SetUp]
        public void SetUpRepository()
        {
            var _mockConfiguration = new Mock<IConfiguration>();
            _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("localhost");
            _mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");

            // AmazonS3Optionsのモックを作成
            var _mockAmazonS3Options = new Mock<IOptions<AmazonS3Options>>();
            _mockAmazonS3Options.Setup(x => x.Value).Returns(new AmazonS3Options());
            _repository = new MstItemRepository(Tenant<PERSON>rov<PERSON>, _mockAmazonS3Options.Object, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider);


        }

        [Test]
        public void SearchAddress_ValidPostCode_ReturnsExpectedResults()
        {
            // Arrange
            var tenant = TenantProvider.GetTrackingTenantDataContext();
            var postCode1 = "100";   // 前3桁
            var postCode2 = "0002"; // 後ろ4桁（1000002の例）
            var address = "";
            var pageIndex = Math.Max(1, 1);
            var pageSize = 10;

            // Act
            var (resultsCount, resultsList) = _repository.SearchAddress(postCode1, postCode2, address, pageIndex, pageSize);

            // Assert
            Assert.That(resultsList, Is.Not.Null);
            Assert.That(resultsList.Any(x => x.PostCd == (postCode1 + postCode2)), Is.True);
        }

        [Test]
        public void SearchAddress_EmptyParameters_ReturnsPagedResults()
        {
            // Arrange
            var postCode1 = "";
            var postCode2 = "";
            var address = "";
            var pageIndex = Math.Max(1, 0);
            var pageSize = 5;

            // Act
            var (totalCount, results) = _repository.SearchAddress(postCode1, postCode2, address, pageIndex, pageSize);

            // Assert
            Assert.That(results, Is.Not.Null);
            Assert.That(results, Is.InstanceOf<List<PostCodeMstModel>>());
            Assert.That(results.Count, Is.LessThanOrEqualTo(pageSize));
            Assert.That(totalCount, Is.GreaterThanOrEqualTo(0));
        }

        [Test]
        public void SearchAddress_InvalidPostCode_ReturnsEmptyResults()
        {
            // Arrange
            var postCode1 = "999";
            var postCode2 = "9999";
            var address = "";
            var pageIndex = Math.Max(1, 0);
            var pageSize = 10;

            // Act
            var (totalCount, results) = _repository.SearchAddress(postCode1, postCode2, address, pageIndex, pageSize);

            // Assert
            Assert.That(results, Is.Not.Null);
            Assert.That(results, Is.Empty);
            Assert.That(totalCount, Is.EqualTo(0));
        }

        [Test]
        public void CheckPostCodeExist_ExistingPostCode_ReturnsTrue()
        {
            // Arrange
            var tenant = TenantProvider.GetTrackingTenantDataContext();
            var zipCD = "1000002"; // 既存データ

            // Act
            var exists = _repository.CheckPostCodeExist(zipCD);

            // Assert
            Assert.That(exists, Is.True);
        }

        [Test]
        public void CheckPostCodeExist_NonExistingPostCode_ReturnsFalse()
        {
            // Arrange
            var zipCD = "9999999"; // 存在しない郵便番号

            // Act
            var result = _repository.CheckPostCodeExist(zipCD);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void PostCodeMst_UniqueConstraint_WorksWithoutHpId()
        {
            // Arrange
            var tenant = TenantProvider.GetTrackingTenantDataContext();
            var postCd = "1000002"; // 既存データ

            // Act
            var exists = tenant.PostCodeMsts.Any(x => x.PostCd == postCd && x.IsDeleted == 0);

            // Assert
            Assert.That(exists, Is.True);
        }

        [TearDown]
        public void TearDown()
        {
            _repository?.ReleaseResource();
        }
    }
}
