using Domain.Models.SystemConf;
using Helper.Constants;
using Infrastructure.Repositories;
using Microsoft.Extensions.Configuration;
using Moq;

namespace CloudUnitTest.Repository.SystemConfTest;

public class SaveSystemSettingTest : BaseUT
{
    [Test]
    public void SaveSystemSetting_ReturnsNotFoundModels_WhenUpdateTargetNotFound()
    {
        // Arrange
        SetupTestEnvironment(out SystemConfRepository repository);

        var hpId = 1;
        var userId = 1;

        // 存在しない GrpCd, GrpEdaNo を指定してテストデータを作成
        // SystemSettingModelStatus=2 (Modified) のもののみテスト
        var nonExistentGrpCd = 99999;
        var nonExistentGrpEdaNo = 99999;

        var systemConfModel = new SystemConfModel(
            hpId,
            nonExistentGrpCd,
            nonExistentGrpEdaNo,
            1.0,
            "test_param",
            "test_biko",
            false,
            ModelStatus.Modified
        );

        var systemConfMenuModel = new SystemConfMenuModel(
            hpId,
            1, // MenuId
            1, // MenuGrp
            1, // SortNo
            "Test Menu",
            nonExistentGrpCd,
            nonExistentGrpEdaNo,
            0, // PathGrpCd
            0, // IsParam
            0, // ParamMask
            0, // ParamType
            string.Empty, // ParamHint
            0, // ValMin
            0, // ValMax
            0, // ParamMin
            0, // ParamMax
            string.Empty, // ItemCd
            0, // PrefNo
            1, // IsVisible
            0, // ManagerKbn
            0, // IsValue
            0, // ParamMaxLength
            new List<SystemConfItemModel>(),
            systemConfModel
        );

        var systemConfMenuModels = new List<SystemConfMenuModel> { systemConfMenuModel };

        try
        {
            // Act
            var notFoundModels = repository.SaveSystemSetting(hpId, userId, systemConfMenuModels);

            // Assert
            Assert.IsNotNull(notFoundModels, "戻り値がnullではない");
            Assert.That(notFoundModels.Count, Is.EqualTo(1), "見つからなかったモデルが1件返される");

            var notFoundModel = notFoundModels.First();
            Assert.That(notFoundModel.HpId, Is.EqualTo(hpId), "HpIdが一致する");
            Assert.That(notFoundModel.GrpCd, Is.EqualTo(nonExistentGrpCd), "GrpCdが一致する");
            Assert.That(notFoundModel.GrpEdaNo, Is.EqualTo(nonExistentGrpEdaNo), "GrpEdaNoが一致する");
            Assert.That(notFoundModel.Val, Is.EqualTo(1.0), "Valが一致する");
            Assert.That(notFoundModel.Param, Is.EqualTo("test_param"), "Paramが一致する");
            Assert.That(notFoundModel.Biko, Is.EqualTo("test_biko"), "Bikoが一致する");
        }
        finally
        {
            repository.ReleaseResource();
        }
    }

    private void SetupTestEnvironment(out SystemConfRepository repository)
    {
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("localhost");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");

        repository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    }
}
