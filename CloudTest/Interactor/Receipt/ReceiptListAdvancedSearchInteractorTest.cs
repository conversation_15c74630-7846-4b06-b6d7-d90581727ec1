﻿using Domain.Models.Receipt;
using Domain.Models.Receipt.ReceiptListAdvancedSearch;
using Helper.Common;
using Helper.Enum;
using Interactor.Receipt;
using Microsoft.Extensions.Logging;
using Moq;
using UseCase.Receipt.ReceiptListAdvancedSearch;

namespace CloudUnitTest.Interactor.Receipt;

public class ReceiptListAdvancedSearchInteractorTest : BaseUT
{
    [Test]
    public void TC_001_ConvertToInputAdvancedSearch_TestSuccess()
    {
        // Arrange
        Random random = new();
        int hpId = random.Next(999, 999999);
        int seikyuYm = 0;
        bool isAdvanceSearch = false;
        string tokki = "tokkiUT";
        List<int> hokenSbts = new() { random.Next(999, 999999) };
        bool isAll = false;
        bool isNoSetting = false;
        bool isSystemSave = false;
        bool isSave1 = false;
        bool isSave2 = false;
        bool isSave3 = false;
        bool isTempSave = false;
        bool isDone = false;
        int receSbtCenter = random.Next(999, 999999);
        int receSbtRight = random.Next(999, 999999);
        string hokenHoubetu = "hokenHoubetuUT";
        int kohi1Houbetu = random.Next(999, 999999);
        int kohi2Houbetu = random.Next(999, 999999);
        int kohi3Houbetu = random.Next(999, 999999);
        int kohi4Houbetu = random.Next(999, 999999);
        bool isIncludeSingle = false;
        string hokensyaNoFrom = "20220201";
        string hokensyaNoTo = "20220230";
        long hokensyaNoFromLong = random.Next(999, 999999);
        long hokensyaNoToLong = random.Next(999, 999999);
        string ptId = random.Next(999, 999999).ToString();
        long ptIdFrom = random.Next(999, 999999);
        long ptIdTo = random.Next(999, 999999);
        int ptSearchOption = (int)PtIdSearchOptionEnum.RangeSearch;
        long tensuFrom = random.Next(999, 999999);
        long tensuTo = random.Next(999, 999999);
        int lastRaiinDateFrom = random.Next(999, 999999);
        int lastRaiinDateTo = random.Next(999, 999999);
        int birthDayFrom = random.Next(999, 999999);
        int birthDayTo = random.Next(999, 999999);
        string itemCd = "itemCdUT";
        string inputName = "inputNameUT";
        string rangeSeach = "=";
        int amount = random.Next(999, 999999);
        int orderStatus = random.Next(999, 999999);
        bool isComment = false;
        List<ItemSearchInputItem> itemList = new() { new ItemSearchInputItem(itemCd, inputName, rangeSeach, amount, orderStatus, isComment) };
        int itemQuery = 1;
        bool isOnlySuspectedDisease = false;
        int byomeiQuery = 1;
        string byomeiCd = "byomeiCd";
        List<SearchByoMstInputItem> byomeiCdList = new() { new SearchByoMstInputItem(byomeiCd, inputName, isComment) };
        bool isFutanIncludeSingle = false;
        long futansyaNoFromLong = random.Next(999, 999999);
        long futansyaNoToLong = random.Next(999, 999999);
        int kaId = random.Next(999, 999999);
        int doctorId = random.Next(999, 999999);
        string name = "nameUT";
        bool isTestPatientSearch = false;
        bool isNotDisplayPrinted = false;
        Dictionary<int, string> groupSearchModels = new();
        bool seikyuKbnAll = false;
        bool seikyuKbnDenshi = false;
        bool seikyuKbnPaper = false;

        var mockIReceiptRepository = new Mock<IReceiptRepository>();
        var mockLogger = new Mock<ILogger<ReceiptListAdvancedSearchInteractor>>();
        var interactor = new ReceiptListAdvancedSearchInteractor(mockIReceiptRepository.Object, mockLogger.Object);

        // Act
        var inputData = new ReceiptListAdvancedSearchInputData(hpId, seikyuYm, tokki, isAdvanceSearch, hokenSbts, isAll, isNoSetting, isSystemSave, isSave1, isSave2, isSave3, isTempSave, isDone, receSbtCenter, receSbtRight, hokenHoubetu, kohi1Houbetu, kohi2Houbetu, kohi3Houbetu, kohi4Houbetu, isIncludeSingle, hokensyaNoFrom, hokensyaNoTo, hokensyaNoFromLong, hokensyaNoToLong, ptId, ptIdFrom, ptIdTo, ptSearchOption, tensuFrom, tensuTo, lastRaiinDateFrom, lastRaiinDateTo, birthDayFrom, birthDayTo, itemList, itemQuery, isOnlySuspectedDisease, byomeiQuery, byomeiCdList, isFutanIncludeSingle, futansyaNoFromLong, futansyaNoToLong, kaId, doctorId, name, isTestPatientSearch, isNotDisplayPrinted, groupSearchModels, seikyuKbnAll, seikyuKbnDenshi, seikyuKbnPaper, 0, 0, true, null, null, null, null);
        var output = interactor.ConvertToInputAdvancedSearch(inputData);

        // Assert
        var success = output.IsAdvanceSearch == isAdvanceSearch
                      && output.Tokki == tokki
                      && output.HokenSbts == hokenSbts
                      && output.IsAll == isAll
                      && output.IsNoSetting == isNoSetting
                      && output.IsSystemSave == isSystemSave
                      && output.IsSave1 == isSave1
                      && output.IsSave2 == isSave2
                      && output.IsSave3 == isSave3
                      && output.IsTempSave == isTempSave
                      && output.IsDone == isDone
                      && output.ReceSbtCenter == receSbtCenter
                      && output.ReceSbtRight == receSbtRight
                      && output.HokenHoubetu == hokenHoubetu
                      && output.Kohi1Houbetu == kohi1Houbetu
                      && output.Kohi2Houbetu == kohi2Houbetu
                      && output.Kohi3Houbetu == kohi3Houbetu
                      && output.Kohi4Houbetu == kohi4Houbetu
                      && output.IsIncludeSingle == isIncludeSingle
                      && output.HokensyaNoFrom == hokensyaNoFrom
                      && output.HokensyaNoTo == hokensyaNoTo
                      && output.HokensyaNoFromLong == hokensyaNoFromLong
                      && output.HokensyaNoToLong == hokensyaNoToLong
                      && output.PtId == ptId
                      && output.PtIdFrom == ptIdFrom
                      && output.PtIdTo == ptIdTo
                      && (int)output.PtSearchOption == ptSearchOption
                      && output.TensuFrom == tensuFrom
                      && output.TensuTo == tensuTo
                      && output.LastRaiinDateFrom == lastRaiinDateFrom
                      && output.LastRaiinDateTo == lastRaiinDateTo
                      && output.BirthDayFrom == birthDayFrom
                      && output.BirthDayTo == birthDayTo
                      && output.ItemList.Any(output => itemList.Any(item => item.ItemCd == output.ItemCd
                                                                            && item.IsComment == output.IsComment
                                                                            && item.InputName == output.InputName
                                                                            && item.RangeSeach == output.RangeSeach
                                                                            && item.Amount == output.Amount
                                                                            && item.OrderStatus == output.OrderStatus))
                      && (int)output.ItemQuery == itemQuery
                      && output.IsOnlySuspectedDisease == isOnlySuspectedDisease
                      && (int)output.ByomeiQuery == byomeiQuery
                      && output.ByomeiCdList.Any(output => byomeiCdList.Any(item => item.IsComment == output.IsComment
                                                                                    && item.ByomeiCd == byomeiCd
                                                                                    && item.InputName == inputName))
                      && output.IsFutanIncludeSingle == isFutanIncludeSingle
                      && output.FutansyaNoFromLong == futansyaNoFromLong
                      && output.FutansyaNoToLong == futansyaNoToLong
                      && output.KaId == kaId
                      && output.DoctorId == doctorId
                      && output.Name == name
                      && output.IsTestPatientSearch == isTestPatientSearch
                      && output.IsNotDisplayPrinted == isNotDisplayPrinted
                      && output.GroupSearchModels == groupSearchModels
                      && output.SeikyuKbnAll == seikyuKbnAll
                      && output.SeikyuKbnDenshi == seikyuKbnDenshi
                      && output.SeikyuKbnPaper == seikyuKbnPaper;
        Assert.True(success);
    }

    [Test]
    public void TC_002_Handle_TestSuccess()
    {
        // Arrange
        Random random = new();
        int hpId = random.Next(999, 999999);
        int seikyuYm = 0;
        bool isAdvanceSearch = false;
        string tokki = "tokkiUT";
        List<int> hokenSbts = new() { random.Next(999, 999999) };
        bool isAll = false;
        bool isNoSetting = false;
        bool isSystemSave = false;
        bool isSave1 = false;
        bool isSave2 = false;
        bool isSave3 = false;
        bool isTempSave = false;
        bool isDone = false;
        int receSbtCenter = random.Next(999, 999999);
        int receSbtRight = random.Next(999, 999999);
        string hokenHoubetu = "hokenHoubetuUT";
        int kohi1Houbetu = random.Next(999, 999999);
        int kohi2Houbetu = random.Next(999, 999999);
        int kohi3Houbetu = random.Next(999, 999999);
        int kohi4Houbetu = random.Next(999, 999999);
        bool isIncludeSingle = false;
        string hokensyaNoFrom = "20220201";
        string hokensyaNoTo = "20220230";
        long hokensyaNoFromLong = random.Next(999, 999999);
        long hokensyaNoToLong = random.Next(999, 999999);
        long ptIdFrom = random.Next(999, 999999);
        long ptIdTo = random.Next(999, 999999);
        int ptSearchOption = (int)PtIdSearchOptionEnum.RangeSearch;
        long tensuFrom = random.Next(999, 999999);
        long tensuTo = random.Next(999, 999999);
        int lastRaiinDateFrom = random.Next(999, 999999);
        int lastRaiinDateTo = random.Next(999, 999999);
        int birthDayFrom = random.Next(999, 999999);
        int birthDayTo = random.Next(999, 999999);
        string itemCd = "itemCdUT";
        string inputName = "inputNameUT";
        string rangeSeach = "=";
        int amount = random.Next(999, 999999);
        int orderStatus = random.Next(999, 999999);
        bool isComment = false;
        List<ItemSearchInputItem> itemList = new() { new ItemSearchInputItem(itemCd, inputName, rangeSeach, amount, orderStatus, isComment) };
        int itemQuery = 1;
        bool isOnlySuspectedDisease = false;
        int byomeiQuery = 1;
        string byomeiCd = "byomeiCd";
        List<SearchByoMstInputItem> byomeiCdList = new() { new SearchByoMstInputItem(byomeiCd, inputName, isComment) };
        bool isFutanIncludeSingle = false;
        long futansyaNoFromLong = random.Next(999, 999999);
        long futansyaNoToLong = random.Next(999, 999999);
        int kaId = random.Next(999, 999999);
        int doctorId = random.Next(999, 999999);
        string name = "nameUT";
        bool isTestPatientSearch = false;
        bool isNotDisplayPrinted = false;
        Dictionary<int, string> groupSearchModels = new();
        bool seikyuKbnAll = false;
        bool seikyuKbnDenshi = false;
        bool seikyuKbnPaper = false;

        int seikyuKbn = random.Next(999, 999999);
        int sinYm = random.Next(111111, 999999);
        int isReceInfDetailExist = random.Next(999, 999999);
        int isPaperRece = random.Next(999, 999999);
        int hokenId = random.Next(999, 999999);
        int hokenKbn = random.Next(999, 999999);
        int output = random.Next(999, 999999);
        int fusenKbn = random.Next(999, 999999);
        int statusKbn = random.Next(999, 999999);
        int isPending = random.Next(999, 999999);
        long ptId = random.Next(999, 999999);
        long ptNum = random.Next(999, 999999);
        string kanaName = "KanaName";
        int sex = 1;
        int lastSinDateByHokenId = random.Next(999, 999999);
        int birthDay = random.Next(10000000, 99999999);
        string receSbt = "receSbt";
        string hokensyaNo = "hokenSyano";
        int tensu = random.Next(999, 999999);
        int hokenSbtCd = random.Next(999, 999999);
        int kohi1Nissu = random.Next(999, 999999);
        int isSyoukiInfExist = random.Next(999, 999999);
        int isReceCmtExist = random.Next(999, 999999);
        int isSyobyoKeikaExist = random.Next(999, 999999);
        string receSeikyuCmt = "receSeikyuCmt";
        int lastVisitDate = random.Next(999, 999999);
        string kaName = "kaName";
        string sName = "sName";
        int isPtKyuseiExist = random.Next(999, 999999);
        string futansyaNoKohi1 = "futansyaNoKohi1";
        string futansyaNoKohi2 = "futansyaNoKohi2";
        string futansyaNoKohi3 = "futansyaNoKohi3";
        string futansyaNoKohi4 = "futansyaNoKohi4";
        bool isPtTest = false;
        int kohi1ReceKisai = random.Next(999, 999999);
        int kohi2ReceKisai = random.Next(999, 999999);
        int kohi3ReceKisai = random.Next(999, 999999);
        int kohi4ReceKisai = random.Next(999, 999999);
        int hokenNissu = random.Next(999, 999999);
        string receCheckCmt = "receCheckCmt";

        // Act
        List<ReceiptListModel> outputList = new()
        {
            new ReceiptListModel(seikyuKbn, sinYm, isReceInfDetailExist, isPaperRece, hokenId, hokenKbn, output, fusenKbn, statusKbn, isPending, ptId, ptNum, kanaName, name, sex, lastSinDateByHokenId, birthDay, receSbt, hokensyaNo, tensu, hokenSbtCd, kohi1Nissu, isSyoukiInfExist, isReceCmtExist, isSyobyoKeikaExist, receSeikyuCmt, lastVisitDate, kaName, sName, isPtKyuseiExist, futansyaNoKohi1, futansyaNoKohi2, futansyaNoKohi3, futansyaNoKohi4, isPtTest, kohi1ReceKisai, kohi2ReceKisai, kohi3ReceKisai, kohi4ReceKisai, tokki, hokenNissu, receCheckCmt)
        };
        var mockIReceiptRepository = new Mock<IReceiptRepository>();
        mockIReceiptRepository.Setup(repo => repo.GetReceiptList(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<ReceiptListAdvancedSearchInput>()))
                              .Returns(outputList);
        var mockLogger = new Mock<ILogger<ReceiptListAdvancedSearchInteractor>>();
        var interactor = new ReceiptListAdvancedSearchInteractor(mockIReceiptRepository.Object, mockLogger.Object);
        var inputData = new ReceiptListAdvancedSearchInputData(hpId, seikyuYm, tokki, isAdvanceSearch, hokenSbts, isAll, isNoSetting, isSystemSave, isSave1, isSave2, isSave3, isTempSave, isDone, receSbtCenter, receSbtRight, hokenHoubetu, kohi1Houbetu, kohi2Houbetu, kohi3Houbetu, kohi4Houbetu, isIncludeSingle, hokensyaNoFrom, hokensyaNoTo, hokensyaNoFromLong, hokensyaNoToLong, ptId.ToString(), ptIdFrom, ptIdTo, ptSearchOption, tensuFrom, tensuTo, lastRaiinDateFrom, lastRaiinDateTo, birthDayFrom, birthDayTo, itemList, itemQuery, isOnlySuspectedDisease, byomeiQuery, byomeiCdList, isFutanIncludeSingle, futansyaNoFromLong, futansyaNoToLong, kaId, doctorId, name, isTestPatientSearch, isNotDisplayPrinted, groupSearchModels, seikyuKbnAll, seikyuKbnDenshi, seikyuKbnPaper, 0, 0, true, null, null, null, null);

        var result = interactor.Handle(inputData);

        // Assert
        var success = result.ReceiptList.Any(item => item.SeikyuKbn == seikyuKbn
                                                     && item.SinYm == sinYm
                                                     && item.IsReceInfDetailExist == isReceInfDetailExist
                                                     && item.IsPaperRece == isPaperRece
                                                     && item.HokenKbn == hokenKbn
                                                     && item.HokenId == hokenId
                                                     && item.Output == output
                                                     && item.FusenKbn == fusenKbn
                                                     && item.StatusKbn == statusKbn
                                                     && item.IsPending == isPending
                                                     && item.PtNum == ptNum
                                                     && item.PtId == ptId
                                                     && item.KanaName == kanaName
                                                     && item.Name == name
                                                     && item.Sex == sex
                                                     && item.Age == CIUtil.SDateToAge(birthDay, lastSinDateByHokenId)
                                                     && item.LastSinDateByHokenId == lastSinDateByHokenId
                                                     && item.BirthDay == birthDay
                                                     && item.ReceSbt == receSbt
                                                     && item.HokensyaNo == hokensyaNo
                                                     && item.Tensu == tensu
                                                     && item.HokenSbtCd == hokenSbtCd
                                                     && item.Kohi1Nissu == kohi1Nissu
                                                     && item.IsSyoukiInfExist == isSyoukiInfExist
                                                     && item.IsReceCmtExist == isReceCmtExist
                                                     && item.IsSyobyoKeikaExist == isSyobyoKeikaExist
                                                     && item.ReceSeikyuCmt == receSeikyuCmt
                                                     && item.LastVisitDate == lastVisitDate
                                                     && item.KaName == kaName
                                                     && item.SName == sName
                                                     && item.IsPtKyuseiExist == isPtKyuseiExist
                                                     && item.FutansyaNoKohi1 == futansyaNoKohi1
                                                     && item.FutansyaNoKohi2 == futansyaNoKohi2
                                                     && item.FutansyaNoKohi3 == futansyaNoKohi3
                                                     && item.FutansyaNoKohi4 == futansyaNoKohi4
                                                     && item.IsPtTest == isPtTest
                                                     && item.HokenNissu == hokenNissu
                                                     && item.ReceCheckCmt == receCheckCmt);
        Assert.True(success);
    }

    [Test]
    public void TC_003_Handle_SortAndPaging_TestSuccess()
    {
        // Arrange
        int hpId = 12345;
        string tokki = "tokkiUT";
        List<int> hokenSbts = new() { 1, 2, 3 };
        int receSbtCenter = 1;
        int receSbtRight = 2;
        string hokenHoubetu = "hokenHoubetuUT";
        int kohi1Houbetu = 1, kohi2Houbetu = 2, kohi3Houbetu = 3, kohi4Houbetu = 4;
        string hokensyaNoFrom = "20220101", hokensyaNoTo = "20221231";
        long hokensyaNoFromLong = 100000, hokensyaNoToLong = 200000;
        string ptId = "1001";
        long ptIdFrom = 1001, ptIdTo = 1002;
        long futansyaNoFromLong = 1000, futansyaNoToLong = 2000;
        int ptSearchOption = (int)PtIdSearchOptionEnum.RangeSearch;
        long tensuFrom = 10, tensuTo = 100;
        int lastRaiinDateFrom = 20220101, lastRaiinDateTo = 20221231;
        int birthDayFrom = 19500101, birthDayTo = 20100101;
        string itemCd = "itemCdUT", inputName = "inputNameUT", rangeSeach = "=";
        int amount = 50, orderStatus = 1;
        List<ItemSearchInputItem> itemList = new() { new ItemSearchInputItem(itemCd, inputName, rangeSeach, amount, orderStatus, false) };
        int itemQuery = (int)QuerySearchEnum.AND;
        int byomeiQuery = (int)QuerySearchEnum.OR;
        string byomeiCd = "byomeiCd";
        List<SearchByoMstInputItem> byomeiCdList = new() { new SearchByoMstInputItem(byomeiCd, inputName, false) };
        int kaId = 1, doctorId = 2;
        string name = "nameUT";
        Dictionary<int, string> groupSearchModels = new();
        int filterType = 1, sortKey = 1;
        bool sortOrder = false;
        int? limit = 2;
        long? cursorPtId = 1001;
        int? cursorSinYm = 202401;
        int? cursorHokenId = 1;

        var outputList = new List<ReceiptListModel>
        {
            new ReceiptListModel(1, 202401, 1, 0, 1, 1, 1, 1, 1, 0, 1001, 2001, "KanaA", "NameA", 1, 20240101, 19500101, "receSbtA", "hokensyaNoA", 10, 1, 1, 1, 1, 1, "cmtA", 20240101, "kaA", "sA", 1, "f1A", "f2A", "f3A", "f4A", false, 1, 2, 3, 4, "tokkiA", 10, "checkCmtA"),
            new ReceiptListModel(2, 202401, 1, 0, 2, 2, 2, 2, 2, 0, 1002, 2002, "KanaB", "NameB", 2, 20240102, 19600101, "receSbtB", "hokensyaNoB", 20, 2, 2, 2, 2, 2, "cmtB", 20240102, "kaB", "sB", 2, "f1B", "f2B", "f3B", "f4B", false, 2, 3, 4, 5, "tokkiB", 20, "checkCmtB"),
            new ReceiptListModel(3, 202401, 1, 0, 3, 3, 3, 3, 3, 0, 1003, 2003, "KanaC", "NameC", 1, 20240103, 19700101, "receSbtC", "hokensyaNoC", 30, 3, 3, 3, 3, 3, "cmtC", 20240103, "kaC", "sC", 3, "f1C", "f2C", "f3C", "f4C", false, 3, 4, 5, 6, "tokkiC", 30, "checkCmtC"),
        };

        var mockIReceiptRepository = new Mock<IReceiptRepository>();
        mockIReceiptRepository.Setup(repo => repo.GetReceiptList(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<ReceiptListAdvancedSearchInput>()))
                            .Returns(outputList);

        var mockLogger = new Mock<ILogger<ReceiptListAdvancedSearchInteractor>>();
        var interactor = new ReceiptListAdvancedSearchInteractor(mockIReceiptRepository.Object, mockLogger.Object);

        var inputData = new ReceiptListAdvancedSearchInputData(
            hpId, 202401, tokki, true, hokenSbts, false, false, false, false, false, false, false, false,
            receSbtCenter, receSbtRight, hokenHoubetu, kohi1Houbetu, kohi2Houbetu, kohi3Houbetu, kohi4Houbetu, false,
            hokensyaNoFrom, hokensyaNoTo, hokensyaNoFromLong, hokensyaNoToLong, ptId, ptIdFrom, ptIdTo, ptSearchOption, tensuFrom, tensuTo,
            lastRaiinDateFrom, lastRaiinDateTo, birthDayFrom, birthDayTo, itemList, itemQuery, false, byomeiQuery, byomeiCdList,
            false, futansyaNoFromLong, futansyaNoToLong, kaId, doctorId, name, false, false,
            groupSearchModels, false, false, false, filterType, sortKey, sortOrder, limit, cursorPtId, cursorSinYm, cursorHokenId
        );

        // Act
        var result = interactor.Handle(inputData);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(3, result.TotalCount);
        Assert.AreEqual(outputList.Sum(x => x.Tensu), result.DisplayTensu);
        Assert.AreEqual(3, result.ReceiptList.Count);

        var sorted = outputList.OrderBy(x => x.Name).Take(limit ?? outputList.Count).ToList();
        for (int i = 0; i < (limit ?? sorted.Count); i++)
        {
            Assert.AreEqual(sorted[i].Name, result.ReceiptList[i].Name);
            Assert.AreEqual(sorted[i].PtId, result.ReceiptList[i].PtId);
        }

        Assert.AreEqual(filterType, inputData.FilterType);
        Assert.AreEqual(cursorPtId, inputData.CursorPtId);
        Assert.AreEqual(cursorSinYm, inputData.CursorSinYm);
        Assert.AreEqual(cursorHokenId, inputData.CursorHokenId);
    }

    [Test]
    public void TC_004_Handle_FilterHokenSbt_TestSuccess()
    {
        // Arrange
        int hpId = 12345;
        string tokki = "tokkiUT";
        int receSbtCenter = 1;
        int receSbtRight = 2;
        string hokenHoubetu = "hokenHoubetuUT";
        int kohi1Houbetu = 1, kohi2Houbetu = 2, kohi3Houbetu = 3, kohi4Houbetu = 4;
        string hokensyaNoFrom = "20220101", hokensyaNoTo = "20221231";
        long hokensyaNoFromLong = 100000, hokensyaNoToLong = 200000;
        string ptId = "1001";
        long ptIdFrom = 1001, ptIdTo = 1003;
        long futansyaNoFromLong = 1000, futansyaNoToLong = 2000;
        int ptSearchOption = (int)PtIdSearchOptionEnum.RangeSearch;
        long tensuFrom = 10, tensuTo = 100;
        int lastRaiinDateFrom = 20220101, lastRaiinDateTo = 20221231;
        int birthDayFrom = 19500101, birthDayTo = 20100101;
        string itemCd = "itemCdUT", inputName = "inputNameUT", rangeSeach = "=";
        int amount = 50, orderStatus = 1;
        List<ItemSearchInputItem> itemList = new() { new ItemSearchInputItem(itemCd, inputName, rangeSeach, amount, orderStatus, false) };
        int itemQuery = (int)QuerySearchEnum.AND;
        int byomeiQuery = (int)QuerySearchEnum.OR;
        string byomeiCd = "byomeiCd";
        List<SearchByoMstInputItem> byomeiCdList = new() { new SearchByoMstInputItem(byomeiCd, inputName, false) };
        int kaId = 1, doctorId = 2;
        string name = "nameUT";
        Dictionary<int, string> groupSearchModels = new();
        int filterType = 1, sortKey = 1;
        bool sortOrder = false;
        int? limit = null;
        long? cursorPtId = null;
        int? cursorSinYm = null;
        int? cursorHokenId = null;

        // 社保=1, 国保=2, その他=3 と仮定
        var outputList = new List<ReceiptListModel>
        {
            new ReceiptListModel(1, 202401, 1, 0, 1, 1, 1, 1, 1, 0, 1001, 2001, "KanaA", "NameA", 1, 20240101, 19500101, "receSbtA", "hokensyaNoA", 10, 1, 1, 1, 1, 1, "cmtA", 20240101, "kaA", "sA", 1, "f1A", "f2A", "f3A", "f4A", false, 1, 2, 3, 4, "tokkiA", 10, "checkCmtA"), // 社保
            new ReceiptListModel(2, 202401, 1, 0, 2, 2, 2, 2, 2, 0, 1002, 2002, "KanaB", "NameB", 2, 20240102, 19600101, "receSbtB", "hokensyaNoB", 20, 2, 2, 2, 2, 2, "cmtB", 20240102, "kaB", "sB", 2, "f1B", "f2B", "f3B", "f4B", false, 2, 3, 4, 5, "tokkiB", 20, "checkCmtB"), // 国保
            new ReceiptListModel(3, 202401, 1, 0, 3, 3, 3, 3, 3, 0, 1003, 2003, "KanaC", "NameC", 1, 20240103, 19700101, "receSbtC", "hokensyaNoC", 30, 3, 3, 3, 3, 3, "cmtC", 20240103, "kaC", "sC", 3, "f1C", "f2C", "f3C", "f4C", false, 3, 4, 5, 6, "tokkiC", 30, "checkCmtC"), // その他
        };

        var mockIReceiptRepository = new Mock<IReceiptRepository>();
        mockIReceiptRepository.Setup(repo => repo.GetReceiptList(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<ReceiptListAdvancedSearchInput>()))
                            .Returns((int a, int b, ReceiptListAdvancedSearchInput input) =>
                            {
                                if (input.HokenSbts != null && input.HokenSbts.Any())
                                    return outputList.Where(x => input.HokenSbts.Contains(x.HokenKbn)).ToList();
                                return outputList;
                            });
        var mockLogger = new Mock<ILogger<ReceiptListAdvancedSearchInteractor>>();
        var interactor = new ReceiptListAdvancedSearchInteractor(mockIReceiptRepository.Object, mockLogger.Object);

        // 社保のみ
        var inputDataShaho = new ReceiptListAdvancedSearchInputData(
            hpId, 202401, tokki, true, new List<int> { 1 }, false, false, false, false, false, false, false, false,
            receSbtCenter, receSbtRight, hokenHoubetu, kohi1Houbetu, kohi2Houbetu, kohi3Houbetu, kohi4Houbetu, false,
            hokensyaNoFrom, hokensyaNoTo, hokensyaNoFromLong, hokensyaNoToLong, ptId, ptIdFrom, ptIdTo, ptSearchOption, tensuFrom, tensuTo,
            lastRaiinDateFrom, lastRaiinDateTo, birthDayFrom, birthDayTo, itemList, itemQuery, false, byomeiQuery, byomeiCdList,
            false, futansyaNoFromLong, futansyaNoToLong, kaId, doctorId, name, false, false,
            groupSearchModels, false, false, false, filterType, sortKey, sortOrder, limit, cursorPtId, cursorSinYm, cursorHokenId
        );
        var resultShaho = interactor.Handle(inputDataShaho);
        Assert.AreEqual(1, resultShaho.ReceiptList.Count);
        Assert.AreEqual(1, resultShaho.ReceiptList[0].HokenKbn);

        // 国保のみ
        var inputDataKokuho = new ReceiptListAdvancedSearchInputData(
            hpId, 202401, tokki, true, new List<int> { 2 }, false, false, false, false, false, false, false, false,
            receSbtCenter, receSbtRight, hokenHoubetu, kohi1Houbetu, kohi2Houbetu, kohi3Houbetu, kohi4Houbetu, false,
            hokensyaNoFrom, hokensyaNoTo, hokensyaNoFromLong, hokensyaNoToLong, ptId, ptIdFrom, ptIdTo, ptSearchOption, tensuFrom, tensuTo,
            lastRaiinDateFrom, lastRaiinDateTo, birthDayFrom, birthDayTo, itemList, itemQuery, false, byomeiQuery, byomeiCdList,
            false, futansyaNoFromLong, futansyaNoToLong, kaId, doctorId, name, false, false,
            groupSearchModels, false, false, false, filterType, sortKey, sortOrder, limit, cursorPtId, cursorSinYm, cursorHokenId
        );
        var resultKokuho = interactor.Handle(inputDataKokuho);
        Assert.AreEqual(1, resultKokuho.ReceiptList.Count);
        Assert.AreEqual(2, resultKokuho.ReceiptList[0].HokenKbn);

        // その他のみ
        var inputDataSonota = new ReceiptListAdvancedSearchInputData(
            hpId, 202401, tokki, true, new List<int> { 3 }, false, false, false, false, false, false, false, false,
            receSbtCenter, receSbtRight, hokenHoubetu, kohi1Houbetu, kohi2Houbetu, kohi3Houbetu, kohi4Houbetu, false,
            hokensyaNoFrom, hokensyaNoTo, hokensyaNoFromLong, hokensyaNoToLong, ptId, ptIdFrom, ptIdTo, ptSearchOption, tensuFrom, tensuTo,
            lastRaiinDateFrom, lastRaiinDateTo, birthDayFrom, birthDayTo, itemList, itemQuery, false, byomeiQuery, byomeiCdList,
            false, futansyaNoFromLong, futansyaNoToLong, kaId, doctorId, name, false, false,
            groupSearchModels, false, false, false, filterType, sortKey, sortOrder, limit, cursorPtId, cursorSinYm, cursorHokenId
        );
        var resultSonota = interactor.Handle(inputDataSonota);
        Assert.AreEqual(1, resultSonota.ReceiptList.Count);
        Assert.AreEqual(3, resultSonota.ReceiptList[0].HokenKbn);

        // 未指定時：すべて
        var inputDataAll = new ReceiptListAdvancedSearchInputData(
            hpId, 202401, tokki, true, new List<int>(), false, false, false, false, false, false, false, false,
            receSbtCenter, receSbtRight, hokenHoubetu, kohi1Houbetu, kohi2Houbetu, kohi3Houbetu, kohi4Houbetu, false,
            hokensyaNoFrom, hokensyaNoTo, hokensyaNoFromLong, hokensyaNoToLong, ptId, ptIdFrom, ptIdTo, ptSearchOption, tensuFrom, tensuTo,
            lastRaiinDateFrom, lastRaiinDateTo, birthDayFrom, birthDayTo, itemList, itemQuery, false, byomeiQuery, byomeiCdList,
            false, futansyaNoFromLong, futansyaNoToLong, kaId, doctorId, name, false, false,
            groupSearchModels, false, false, false, filterType, sortKey, sortOrder, limit, cursorPtId, cursorSinYm, cursorHokenId
        );
        var resultAll = interactor.Handle(inputDataAll);
        Assert.AreEqual(3, resultAll.ReceiptList.Count);
    }
}
