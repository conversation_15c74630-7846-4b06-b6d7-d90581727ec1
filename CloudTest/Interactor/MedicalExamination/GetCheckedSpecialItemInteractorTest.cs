﻿using Amazon.Runtime.Internal.Transform;
using CloudUnitTest.SampleData;
using Domain.Models.Insurance;
using Domain.Models.InsuranceInfor;
using Domain.Models.KarteInfs;
using Domain.Models.MstItem;
using Domain.Models.OrdInfDetails;
using Domain.Models.Reception;
using Domain.Models.SystemConf;
using Domain.Models.TodayOdr;
using Helper.Common;
using Infrastructure.Options;
using Infrastructure.Repositories;
using Interactor.MedicalExamination;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Moq;
using System.Globalization;
using UseCase.MedicalExamination.UpsertTodayOrd;
using UseCase.OrdInfs.CheckedSpecialItem;

namespace CloudUnitTest.Interactor.MedicalExamination;

public class GetCheckedSpecialItemInteractorTest : BaseUT
{
    #region AgeLimitCheck
    [Test]
    public void TC_001_AgeLimitCheck_True()
    {
        // Arrange
        int sinDate = 20221111, iBirthDay = 20221201, checkAge = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();

        var tenMstAA = new TenItemModel(
            1,
            "140064650",
            "0",
            "AA",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstAA);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetailAA = new OrdInfDetailModel(
            1,
            "140064650",
            20221111
            );
        odrDetails.Add(odrDetailAA);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.AgeLimitCheck(sinDate, iBirthDay, checkAge, tenMstItems, odrDetails);

        // Assert
        Assert.True(!output.Any());
    }

    [Test]
    public void TC_002_AgeLimitCheck_CheckAge()
    {
        // Arrange
        int sinDate = 20221111, iBirthDay = 20221201, checkAge = 0;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();

        #region Data Example
        //MaxAge = "AA"
        var tenMstAA = new TenItemModel(
            1,
            "140064650",
            "0",
            "AA",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstAA);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetailAA = new OrdInfDetailModel(
            1,
            "140064650",
            20221111
            );
        odrDetails.Add(odrDetailAA);
        #endregion  

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.AgeLimitCheck(sinDate, iBirthDay, checkAge, tenMstItems, odrDetails);

        // Assert
        Assert.True(!output.Any());
    }

    /// <summary>
    /// Check ItemCd null
    /// </summary>
    [Test]
    public void TC_003_AgeLimitCheck_CheckItemCd()
    {
        // Arrange
        int sinDate = 20221111, iBirthDay = 20221201, checkAge = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();

        #region Data Example
        //MaxAge = "AA"
        var tenMstAA = new TenItemModel(
            1,
            "140064650",
            "0",
            "AA",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstAA);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetailAA = new OrdInfDetailModel(
            1,
            "",
            20221111
            );
        odrDetails.Add(odrDetailAA);
        #endregion  

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.AgeLimitCheck(sinDate, iBirthDay, checkAge, tenMstItems, odrDetails);

        // Assert
        Assert.True(!output.Any());
    }

    [Test]
    public void TC_004_AgeLimitCheck_MaxAge_WithAgeDiffer0()
    {
        // Arrange
        int sinDate = 20221111, iBirthDay = 19930903, checkAge = 29;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();

        var tenMstB3 = new TenItemModel(
            1,
            "629901101",
            "15",
            "B3",
            "629901101",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstB3);

        var odrDetails = new List<OrdInfDetailModel>();

        var odrDetailB3 = new OrdInfDetailModel(
            1,
            "629901101",
            20221111
            );
        odrDetails.Add(odrDetailB3);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.AgeLimitCheck(sinDate, iBirthDay, checkAge, tenMstItems, odrDetails);

        // Assert
        Assert.True(output.Count == odrDetails.Count);
    }

    [Test]
    public void TC_005_AgeLimitCheck_MinAge_WithAgeDiffer0()
    {
        // Arrange
        int sinDate = 20221111, iBirthDay = 20221221, checkAge = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();

        var tenMstB6 = new TenItemModel(
            1,
            "629901201",
             "B6",
            "15",
            "629901201",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstB6);

        var odrDetails = new List<OrdInfDetailModel>();

        var odrDetailB6 = new OrdInfDetailModel(
           1,
           "629901201",
           20221111
           );
        odrDetails.Add(odrDetailB6);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.AgeLimitCheck(sinDate, iBirthDay, checkAge, tenMstItems, odrDetails);

        // Assert
        Assert.True(output.Count == odrDetails.Count);
    }

    [Test]
    public void TC_006_AgeLimitCheck_MaxAge_WithMinAgeEqual0()
    {
        // Arrange
        int sinDate = 20221111, iBirthDay = 19930903, checkAge = 29;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();

        var tenMstBF = new TenItemModel(
            1,
            "629901301",
            "0",
            "BF",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstBF);

        var odrDetails = new List<OrdInfDetailModel>();

        var odrDetailBF = new OrdInfDetailModel(
           1,
           "629901301",
           20221111
           );
        odrDetails.Add(odrDetailBF);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.AgeLimitCheck(sinDate, iBirthDay, checkAge, tenMstItems, odrDetails);

        // Assert
        Assert.True(output.Count == odrDetails.Count);
    }

    [Test]
    public void TC_007_AgeLimitCheck_MinAge_WithMaxAgeEqual0()
    {
        // Arrange
        int sinDate = 20221111, iBirthDay = 20221221, checkAge = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();

        var tenMstBF = new TenItemModel(
            1,
            "629901301",
            "BF",
            "0",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstBF);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetailBF = new OrdInfDetailModel(
           1,
           "629901301",
           20221111
           );
        odrDetails.Add(odrDetailBF);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.AgeLimitCheck(sinDate, iBirthDay, checkAge, tenMstItems, odrDetails);

        // Assert
        Assert.True(output.Count == odrDetails.Count);
    }

    [Test]
    public void TC_008_AgeLimitCheck_MinAge_WithMaxAgeEqual0_Continue_ItemCd()
    {
        // Arrange
        int sinDate = 20221111, iBirthDay = 20221221, checkAge = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();

        #region Data Example
        //MaxAge = "BF"
        var tenMstBF = new TenItemModel(
            1,
            "629901301",
            "BF",
            "0",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstBF);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetailBF = new OrdInfDetailModel(
           1,
           "629901301",
           20221111
           );
        odrDetails.Add(odrDetailBF);
        odrDetails.Add(odrDetailBF);
        #endregion  

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.AgeLimitCheck(sinDate, iBirthDay, checkAge, tenMstItems, odrDetails);

        // Assert
        Assert.True(output.Count == 1);
    }

    #endregion

    #region ExpiredCheck
    [Test]
    public void TC_009_ExpiredCheck_True()
    {
        // Arrange
        var sinDate = 20221101;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMstError = new TenItemModel(
            1,
            "140064650",
            "00",
            "AA",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstError);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail = new OrdInfDetailModel(
            1,
            "140064650",
            20221111
            );
        odrDetails.Add(odrDetail);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.ExpiredCheck(sinDate, tenMstItems, odrDetails);

        // Assert
        Assert.True(!output.Any());
    }

    /// <summary>
    /// Check ItemCd null
    /// </summary>
    [Test]
    public void TC_010_ExpiredCheck_CheckItemCd()
    {
        // Arrange
        var sinDate = 20221101;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMstError = new TenItemModel(
            1,
            "140064650",
            "00",
            "AA",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstError);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail = new OrdInfDetailModel(
            1,
            "",
            20221111
            );
        odrDetails.Add(odrDetail);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.ExpiredCheck(sinDate, tenMstItems, odrDetails);

        // Assert
        Assert.True(!output.Any());
    }

    

    /// <summary>
    /// tenMstItemList empty
    /// </summary>
    [Test]
    public void TC_011_ExpiredCheck_TenMstItemList()
    {
        // Arrange
        var sinDate = 20221101;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail = new OrdInfDetailModel(
            1,
            "test",
            20221111
            );
        odrDetails.Add(odrDetail);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.ExpiredCheck(sinDate, tenMstItems, odrDetails);

        // Assert
        Assert.True(!output.Any());
    }

    [Test]
    public void TC_012_ExpiredCheck_MinStartDate()
    {
        // Arrange
        var sinDate = 20220101;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMstError = new TenItemModel(
            1,
            "140064650",
            "00",
            "AA",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstError);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail = new OrdInfDetailModel(
            1,
            "140064650",
            20221111
            );
        odrDetails.Add(odrDetail);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.ExpiredCheck(sinDate, tenMstItems, odrDetails);

        // Assert
        Assert.True(output.Any());
    }

    [Test]
    public void TC_013_ExpiredCheck_MaxEndDate()
    {
        // Arrange
        var sinDate = 20221101;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMstError = new TenItemModel(
            1,
            "140064650",
            "00",
            "AA",
            "140064650",
            20220401,
            20221001
            );
        tenMstItems.Add(tenMstError);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail = new OrdInfDetailModel(
            1,
            "140064650",
            20221111
            );
        odrDetails.Add(odrDetail);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.ExpiredCheck(sinDate, tenMstItems, odrDetails);

        // Assert
        Assert.True(output.Any());
    }
    #endregion

    #region DuplicateCheck
    [Test]
    public void TC_014_DuplicateCheck_True()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMstError = new TenItemModel(
            1,
            "140064650",
            "00",
            "AA",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstError);

        var odrDetails = new List<OrdInfDetailModel>();

        var odrDetail1 = new OrdInfDetailModel(
            1,
            "140037030",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
           1,
           "140038410",
           20221111
           );
        odrDetails.Add(odrDetail2);

        var odrDetail3 = new OrdInfDetailModel(
           1,
           "Y0001",
           20221111
           );
        odrDetails.Add(odrDetail3);

        var odrDetail4 = new OrdInfDetailModel(
          1,
          "Z0001",
          20221111
          );
        odrDetails.Add(odrDetail4);

        var odrDetail5 = new OrdInfDetailModel(
          1,
          "@BUNKATU",
          20221111
          );
        odrDetails.Add(odrDetail5);

        var odrDetail6 = new OrdInfDetailModel(
          1,
          "@REFILL",
          20221111
          );
        odrDetails.Add(odrDetail6);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.DuplicateCheck(tenMstItems, odrDetails);

        // Assert
        Assert.True(!output.Any());
    }

    [Test]
    public void TC_015_DuplicateCheck_Continue()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMstError = new TenItemModel(
            1,
            "140064650",
            "00",
            "AA",
            "140064650",
            20220401,
            99999999,
            1
            );
        tenMstItems.Add(tenMstError);

        var odrDetails = new List<OrdInfDetailModel>();

        var odrDetail1 = new OrdInfDetailModel(
            1,
            "140064650",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
           1,
           "140038410",
           20221111
           );
        odrDetails.Add(odrDetail2);

        var odrDetail3 = new OrdInfDetailModel(
           1,
           "Y0001",
           20221111
           );
        odrDetails.Add(odrDetail3);

        var odrDetail4 = new OrdInfDetailModel(
          1,
          "Z0001",
          20221111
          );
        odrDetails.Add(odrDetail4);

        var odrDetail5 = new OrdInfDetailModel(
          1,
          "@BUNKATU",
          20221111
          );
        odrDetails.Add(odrDetail5);

        var odrDetail6 = new OrdInfDetailModel(
          1,
          "@REFILL",
          20221111
          );
        odrDetails.Add(odrDetail6);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.DuplicateCheck(tenMstItems, odrDetails);

        // Assert
        Assert.True(!output.Any());
    }
    [Test]
    public void TC_016_DuplicateCheck_Fail()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMstError = new TenItemModel(
            1,
            "140064650",
            "00",
            "AA",
            "140064650",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMstError);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail = new OrdInfDetailModel(
            1,
            "140064650",
            20221111
            );
        odrDetails.Add(odrDetail);
        odrDetails.Add(odrDetail);

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.DuplicateCheck(tenMstItems, odrDetails);

        // Assert
        Assert.True(output.Any());
    }
    #endregion

    #region ItemCommentCheck
    [Test]
    public void TC_017_ItemCommentCheck_True()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        var items = new Dictionary<string, string>()
        {
            new ("140038410", "ストーマ処置２"),
            new("140039650", "人工呼吸（鼻マスク式人工呼吸器）（５時間超）")
        };

        var allCmtCheckMst = new List<ItemCmtModel>()
        {
            new ItemCmtModel ("140038410",1, 1, "comment abc", 0),
            new ItemCmtModel ("140039650",1, 2, "comment abc", 1)
        };

        var karteInf = new KarteInfModel(1, 901072057, 1, 1, 1, 20221111, "comment abc", 0, "abc", DateTime.MinValue, DateTime.MinValue, "abc");

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.ItemCommentCheck(items, allCmtCheckMst, karteInf);

        var output2 = interactor.ItemCommentCheck(items, new(), karteInf);

        // Assert
        Assert.True(!output1.Any() && !output2.Any());
    }

    [Test]
    public void TC_018_ItemCommentCheck_Fail()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        var items = new Dictionary<string, string>()
        {
            new ("140038410", "ストーマ処置２"),
            new("140039650", "人工呼吸（鼻マスク式人工呼吸器）（５時間超）")
        };

        var allCmtCheckMst = new List<ItemCmtModel>()
        {
            new ItemCmtModel ("140038410",1, 1, "comment abc", 0),
            new ItemCmtModel ("140039650",1, 2, "comment bcd", 1)
        };

        var karteInf = new KarteInfModel(1, 901072057, 1, 1, 1, 20221111, "comment abc", 0, "abc", DateTime.MinValue, DateTime.MinValue, "abc");

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.ItemCommentCheck(items, allCmtCheckMst, karteInf);

        // Assert
        Assert.True(output.Any());
    }
    #endregion

    #region CalculationCountCheck
    [Test]
    public void TC_019_CalculationCountCheck_UnitCd_997_998()
    {
        // Arrange
        int hpId = 999, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            hpId,
            "111014210",
            "0",
            "AA",
            "111014210",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            hpId,
            "113003510",
            "0",
            "B3",
            "113003510",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            hpId,
            "111014210",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            hpId,
            "113003510",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
                1,
                hpId,
                "111014210",
                997,
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                1,
                1,
                1
            );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               hpId,
               "113003510",
               998,
               10,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel1);
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel2);

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                hpId,
                1,
                1,
                20220101,
                99999999,
                "111014210",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                hpId,
                1,
                2,
                20220101,
                99999999,
                "113003510",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(output.Count == 4);
    }

    [Test]
    public void TC_020_CalculationCountCheck_002_UnitCd_997_998()
    {
        // Arrange
        int hpId = 999, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        #region Data Example
        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            hpId,
            "111014210",
            "0",
            "AA",
            "111014210",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            hpId,
            "113003510",
            "0",
            "B3",
            "113003510",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            hpId,
            "",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            hpId,
            "113003510",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
                1,
                hpId,
                "111014210",
                997,
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                1,
                1,
                1
            );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               hpId,
               "113003510",
               998,
               10,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel1);
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel2);

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                hpId,
                1,
                1,
                20220101,
                99999999,
                "111014210",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                hpId,
                1,
                2,
                20220101,
                99999999,
                "113003510",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };
        #endregion

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(output.Count == 0);
    }

    [Test]
    public void TC_021_CalculationCountCheck_003_UnitCd_997_998()
    {
        // Arrange
        int hpId = 999, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        #region Data Example
        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            hpId,
            "111014210",
            "0",
            "AA",
            "111014211",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            hpId,
            "113003510",
            "0",
            "B3",
            "113003510",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            hpId,
            "111014210",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            hpId,
            "113003510",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
                1,
                hpId,
                "111014210",
                997,
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                1,
                1,
                1
            );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               hpId,
               "113003510",
               998,
               10,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel1);
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel2);

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                hpId,
                1,
                1,
                20220101,
                99999999,
                "111014210",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                hpId,
                1,
                2,
                20220101,
                99999999,
                "113003510",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };
        #endregion

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(output.Count == 4);
    }

    [Test]
    public void TC_022_CalculationCountCheck_005_MulTi_UnitCd()
    {
        // Arrange
        int hpId = 999, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        #region Data Example
        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            hpId,
            "111014210",
            "0",
            "AA",
            "111014211",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            hpId,
            "113003510",
            "0",
            "B3",
            "113003510",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            hpId,
            "111014210",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            hpId,
            "113003510",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        #region #example densiSanteiKaisuModel

        int[] unitCds = { 121, 131, 138, 141, 142, 143, 144, 145, 146, 147, 148 };
        for (int i = 0; i < unitCds.Length; i++)
        {
            DensiSanteiKaisuModel densiSanteiKaisuModel = new DensiSanteiKaisuModel(
                1,
                hpId,
                "113003510",
                unitCds[i],
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                1,
                1,
                2
            );

            // Thêm đối tượng mới vào danh sách
            densiSanteiKaisuModels.Add(densiSanteiKaisuModel);
        }
        #endregion #example densiSanteiKaisuModel1

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                hpId,
                1,
                1,
                20220101,
                99999999,
                "111014210",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                hpId,
                1,
                2,
                20220101,
                99999999,
                "113003510",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };
        #endregion

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(output.Count == 0);
    }

    [Test]
    public void TC_023_CalculationCountCheck_006_UnitCd_999_Multi_TermSbt()
    {
        // Arrange
        int hpId = 999, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        #region Data Example
        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            hpId,
            "111014210",
            "0",
            "AA",
            "111014211",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            hpId,
            "113003510",
            "0",
            "B3",
            "113003510",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            hpId,
            "111014210",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            hpId,
            "113003510",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        #region #example densiSanteiKaisuModel

        int[] termSbt = { 2, 3, 4, 5 };
        for (int i = 0; i < termSbt.Length; i++)
        {
            DensiSanteiKaisuModel densiSanteiKaisuModel = new DensiSanteiKaisuModel(
                1,
                hpId,
                "113003510",
                999,
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                termSbt[i],
                1,
                2
            );

            // Thêm đối tượng mới vào danh sách
            densiSanteiKaisuModels.Add(densiSanteiKaisuModel);
        }
        #endregion #example densiSanteiKaisuModel1

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                hpId,
                1,
                1,
                20220101,
                99999999,
                "111014210",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                hpId,
                1,
                2,
                20220101,
                99999999,
                "113003510",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };
        #endregion

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(output.Count == 0);
    }

    [Test]
    public void TC_024_CalculationCountCheck_007_UnitCd_999_Multi_TermSbt()
    {
        // Arrange
        int hpId = 999, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        #region Data Example
        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            hpId,
            "111014210",
            "0",
            "AA",
            "111014211",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            hpId,
            "113003510",
            "0",
            "B3",
            "113003510",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            hpId,
            "111014210",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            hpId,
            "113003510",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        #region #example densiSanteiKaisuModel

        int[] termSbt = { 2, 3, 4, 5 };
        for (int i = 0; i < termSbt.Length; i++)
        {
            DensiSanteiKaisuModel densiSanteiKaisuModel = new DensiSanteiKaisuModel(
                1,
                hpId,
                "113003510",
                999,
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                0,
                termSbt[i],
                1,
                2
            );

            // Thêm đối tượng mới vào danh sách
            densiSanteiKaisuModels.Add(densiSanteiKaisuModel);
        }
        #endregion #example densiSanteiKaisuModel1

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                hpId,
                1,
                1,
                20220101,
                99999999,
                "111014210",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                hpId,
                1,
                2,
                20220101,
                99999999,
                "113003510",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };
        #endregion

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(output.Count == 0);
    }

    [Test]
    public void TC_025_CalculationCountCheck_008_UnitCd_997()
    {
        // Arrange
        int hpId = 999, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        #region Data Example
        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            hpId,
            "111014210",
            "0",
            "AA",
            "111014211",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            hpId,
            "113003510",
            "0",
            "B3",
            "113003510",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            hpId,
            "111014210",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            hpId,
            "113003510",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        #region #example densiSanteiKaisuModel

        DensiSanteiKaisuModel densiSanteiKaisuModel = new DensiSanteiKaisuModel(
            1,
            hpId,
            "113003510",
            997,
            10,
            1,
            20220101,
            99999999,
            1,
            1,
            1,
            0,
            1,
            1,
            2
        );
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel);
        #endregion #example densiSanteiKaisuModel1

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                hpId,
                1,
                1,
                20220101,
                99999999,
                "111014210",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                hpId,
                1,
                2,
                20220101,
                99999999,
                "113003510",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };
        #endregion

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(output.Count == 2);
    }

    [Test]
    public void TC_026_CalculationCountCheck_UnitCd_Other()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            1,
            "113003710",
            "0",
            "AA",
            "113003710",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            1,
            "113019710",
            "0",
            "B3",
            "113019710",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "113003710",
            20221111,
            120
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "113019710",
            20221111,
            120
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
                1,
                1,
                "113003710",
                4,
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                1,
                1,
                1
            );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "113019710",
               5,
               10,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel1);
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel2);

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                1,
                1,
                1,
                20220101,
                99999999,
                "113003710",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                1,
                1,
                2,
                20220101,
                99999999,
                "113019710",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(output.Count == 1);
    }

    [Test]
    public void TC_027_CalculationCountCheck_UnitCd_Other_StartDateMoreThan0()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            1,
            "113019910",
            "0",
            "AA",
            "113019910",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            1,
            "113037210",
            "0",
            "B3",
            "113037210",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "113019910",
            20221111,
            120
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "113037210",
            20221111,
            120
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
                1,
                1,
                "113019910",
                53,
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                1,
                1,
                1
            );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "113037210",
               53,
               10,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel1);
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel2);

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                1,
                1,
                1,
                20220101,
                99999999,
                "113019910",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                1,
                1,
                2,
                20220101,
                99999999,
                "113037210",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(output.Count == 1);
    }

    [Test]
    public void TC_028_CalculationCountCheck_UnitCd_997_998_True()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            1,
            "1110001112",
            "0",
            "AA",
            "1110001112",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            1,
            "111013857",
            "0",
            "B3",
            "111013857",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "1110001112",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013857",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
                1,
                1,
                "1110001112",
                997,
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                1,
                1,
                1
            );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013857",
               998,
               10,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel1);
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel2);

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                1,
                1,
                1,
                20220101,
                99999999,
                "1110001112",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                1,
                1,
                2,
                20220101,
                99999999,
                "111013857",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(!output.Any());
    }

    [Test]
    public void TC_029_CalculationCountCheck_008_UnitCd_999()
    {
        // Arrange
        int hpId = 999, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        var optionsAccessorMock = new Mock<IOptions<AmazonS3Options>>();
        var tenant = TenantProvider.GetNoTrackingDataContext();

        MstItemRepository mstItemRepository = new MstItemRepository(TenantProvider, optionsAccessorMock.Object, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider);
        var itemGrpMsts = CheckedSpecialItemInteractorData.ReadItemGrpMst(999);
        #region Data Example
        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            hpId,
            "111014210",
            "0",
            "AA",
            "111014211",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            hpId,
            "113003510",
            "0",
            "B3",
            "113003510",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            hpId,
            "111014210",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            hpId,
            "113003510",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        #region #example densiSanteiKaisuModel

        DensiSanteiKaisuModel densiSanteiKaisuModel = new DensiSanteiKaisuModel(
            1,
            hpId,
            "113003510",
            999,
            10,
            1,
            20220101,
            99999999,
            1,
            1,
            1,
            0,
            1,
            1,
            2
        );
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel);
        #endregion #example densiSanteiKaisuModel1

        var itemGrpMstModels = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                hpId,
                1,
                1,
                20220101,
                99999999,
                "111014210",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                hpId,
                1,
                2,
                20220101,
                99999999,
                "113003510",
                1
           );
        itemGrpMstModels.Add(itemGrpMst1);
        itemGrpMstModels.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };
        #endregion
        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mstItemRepository, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);
        try
        {
            tenant.AddRange(itemGrpMsts);
            tenant.SaveChanges();
            // Act
            var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMstModels, hokenIds);
            // Assert
            Assert.True(output.Count == 0);
        }
        finally
        {
            tenant.RemoveRange(itemGrpMsts);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void TC_030_CalculationCountCheck_UnitCd_Other_True()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110;
        long raiinNo = 400201159, ptId = 54109;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var tenMstItems = new List<TenItemModel>();
        var tenMst1 = new TenItemModel(
            1,
            "113037410",
            "0",
            "AA",
            "113037410",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst1);

        var tenMst2 = new TenItemModel(
            1,
            "113037610",
            "0",
            "B3",
            "113037610",
            20220401,
            99999999
            );
        tenMstItems.Add(tenMst2);

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "113037410",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "113037610",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var santeiTenMsts = new List<TenItemModel>();
        santeiTenMsts.Add(tenMst1);
        santeiTenMsts.Add(tenMst2);

        var densiSanteiKaisuModels = new List<DensiSanteiKaisuModel>();

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
                1,
                1,
                "113037410",
                53,
                100,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                1,
                1,
                1
            );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "113037610",
               53,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel1);
        densiSanteiKaisuModels.Add(densiSanteiKaisuModel2);

        var itemGrpMsts = new List<ItemGrpMstModel>();

        var itemGrpMst1 = new ItemGrpMstModel(
                1,
                1,
                1,
                20220101,
                99999999,
                "113037410",
                1
            );

        var itemGrpMst2 = new ItemGrpMstModel(
                1,
                1,
                2,
                20220101,
                99999999,
                "113037610",
                1
           );
        itemGrpMsts.Add(itemGrpMst1);
        itemGrpMsts.Add(itemGrpMst2);

        var hokenIds = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 20) };

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.CalculationCountCheck(hpId, sinDate, raiinNo, ptId, santeiTenMsts, densiSanteiKaisuModels, tenMstItems, odrDetails, itemGrpMsts, hokenIds);

        // Assert
        Assert.True(!output.Any());
    }
    #endregion

    #region Check Age
    [Test]
    public void TC_031_CheckAge_AA()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        string tenMstAgeCheck = "AA";
        int iDays1 = 28, iDays2 = 1, sinDate = 20221111, iBirthDay = 19930903, iYear = 29;

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.CheckAge(tenMstAgeCheck, iDays1, sinDate, iBirthDay, iYear);
        var output2 = interactor.CheckAge(tenMstAgeCheck, iDays2, sinDate, iBirthDay, iYear);

        // Assert
        Assert.True(output1 && !output2);
    }

    [Test]
    public void TC_032_CheckAge_B3()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        string tenMstAgeCheck = "B3";
        var now = CIUtil.GetJapanDateTimeNow();
        var year = now.Year - 3;
        int iDays = 28, sinDate = year * 10000 + 1110, iBirthDay1 = year * 10000 + 1009, iBirthDay2 = year * 10000 + 1211, iYear1 = 29, iYear2 = 3, iYear3 = 1;

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear1);
        var output2 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear2);
        var output3 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear3);
        var output4 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay2, iYear2);

        // Assert
        Assert.True(output1 && output2 && !output3 && !output4);
    }

    [Test]
    public void TC_033_CheckAge_B6()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        string tenMstAgeCheck = "B6";
        var now = CIUtil.GetJapanDateTimeNow();
        var year = now.Year - 6;
        int iDays = 28, sinDate = year * 10000 + 1110, iBirthDay1 = year * 10000 + 1009, iBirthDay2 = year * 10000 + 1211, iYear1 = 29, iYear2 = 6, iYear3 = 1;

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear1);
        var output2 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear2);
        var output3 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear3);
        var output4 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay2, iYear2);

        // Assert
        Assert.True(output1 && output2 && !output3 && !output4);
    }

    [Test]
    public void TC_034_CheckAge_BF()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        string tenMstAgeCheck = "BF";
        var now = CIUtil.GetJapanDateTimeNow();
        var year = now.Year - 15;
        int iDays = 28, sinDate = year * 10000 + 1110, iBirthDay1 = year * 10000 + 1009, iBirthDay2 = year * 10000 + 1211, iYear1 = 29, iYear2 = 15, iYear3 = 1;

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear1);
        var output2 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear2);
        var output3 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear3);
        var output4 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay2, iYear2);

        // Assert
        Assert.True(output1 && output2 && !output3 && !output4);
    }

    [Test]
    public void TC_035_CheckAge_BK()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        string tenMstAgeCheck = "BK";
        var now = CIUtil.GetJapanDateTimeNow();
        var year = now.Year - 20;
        int iDays = 28, sinDate = year * 10000 + 1110, iBirthDay1 = year * 10000 + 1009, iBirthDay2 = year * 10000 + 1211, iYear1 = 29, iYear2 = 20, iYear3 = 1;

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear1);
        var output2 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear2);
        var output3 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear3);
        var output4 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay2, iYear2);

        // Assert
        Assert.True(output1 && output2 && !output3 && !output4);
    }

    [Test]
    public void TC_036_CheckAge_AE()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        string tenMstAgeCheck = "AE";
        int iDays1 = 90, iDays2 = 1, sinDate = 20221111, iBirthDay = 19930903, iYear = 29;

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.CheckAge(tenMstAgeCheck, iDays1, sinDate, iBirthDay, iYear);
        var output2 = interactor.CheckAge(tenMstAgeCheck, iDays2, sinDate, iBirthDay, iYear);

        // Assert
        Assert.True(output1 && !output2);
    }

    [Test]
    public void TC_037_CheckAge_MG()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        string tenMstAgeCheck = "MG";
        var now = CIUtil.GetJapanDateTimeNow();
        var year = now.Year - 20;
        int iDays = 28, sinDate = year * 10000 + 1110, iBirthDay1 = (year - 7) * 10000 + 1009, iBirthDay2 = (year + 7) * 10000 + 1211,
           iBirthDay3 = (year - 6) * 10000 + 0301, iBirthDay4 = (year + 6) * 10000 + 0301, iYear = 29;

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay1, iYear);
        var output2 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay2, iYear);
        var output3 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay3, iYear);
        var output4 = interactor.CheckAge(tenMstAgeCheck, iDays, sinDate, iBirthDay4, iYear);

        // Assert
        Assert.True(output1 && !output2 && output3 && !output4);
    }

    [Test]
    public void TC_038_CheckAge_Other()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        string tenMstAgeCheck1 = "30", tenMstAgeCheck2 = "28", tenMstAgeCheck3 = "B";
        int iDays1 = 90, iDays2 = 1, sinDate = 20221111, iBirthDay = 19930903, iYear1 = 29, iYear2 = 0;

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.CheckAge(tenMstAgeCheck1, iDays1, sinDate, iBirthDay, iYear1);
        var output2 = interactor.CheckAge(tenMstAgeCheck2, iDays2, sinDate, iBirthDay, iYear1);
        var output3 = interactor.CheckAge(tenMstAgeCheck3, iDays2, sinDate, iBirthDay, iYear1);
        var output4 = interactor.CheckAge(tenMstAgeCheck3, iDays2, sinDate, iBirthDay, iYear2);

        // Assert
        Assert.True(!output1 && output2 && output3 && output4);
    }
    #endregion

    #region Common DensiSantei
    [Test]
    public void TC_039_CommonDensiSantei_53()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               53,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;
        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);

        // Assert
        Assert.True(sTerm == "患者あたり");
    }

    [Test]
    public void TC_040_CommonDensiSantei_121()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               121,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);

        // Assert
        Assert.True(sTerm == "日" && startDate == sinDate);
    }

    [Test]
    public void TC_041_CommonDensiSantei_131()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "113037810",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "113037810",
               131,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;
        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);
        var newSinDate = sinDate / 100 * 100 + 1;
        // Assert
        Assert.True(sTerm == "月" && startDate == newSinDate);
    }

    [Test]
    public void TC_042_CommonDensiSantei_138()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               138,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);
        var newSinDate = interactor.WeeksBefore(sinDate, 1);

        // Assert
        Assert.True(sTerm == "週" && startDate == newSinDate);
    }

    [Test]
    public void TC_043_CommonDensiSantei_141()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               141,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);

        // Assert
        Assert.True(sTerm == "一連" && startDate == -1);
    }

    [Test]
    public void TC_044_CommonDensiSantei_142()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               142,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);
        var newSinDate = interactor.WeeksBefore(sinDate, 2);

        // Assert
        Assert.True(sTerm == "2週" && startDate == newSinDate);
    }

    [Test]
    public void TC_045_CommonDensiSantei_143()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               143,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);
        var newSinDate = interactor.MonthsBefore(sinDate, 1);

        // Assert
        Assert.True(sTerm == "2月" && startDate == newSinDate);
    }

    [Test]
    public void TC_046_CommonDensiSantei_144()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               144,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);
        var newSinDate = interactor.MonthsBefore(sinDate, 2);

        // Assert
        Assert.True(sTerm == "3月" && startDate == newSinDate);
    }

    [Test]
    public void TC_047_CommonDensiSantei_145()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               145,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);
        var newSinDate = interactor.MonthsBefore(sinDate, 3);

        // Assert
        Assert.True(sTerm == "4月" && startDate == newSinDate);
    }

    [Test]
    public void TC_048_CommonDensiSantei_146()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               146,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);
        var newSinDate = interactor.MonthsBefore(sinDate, 5);

        // Assert
        Assert.True(sTerm == "6月" && startDate == newSinDate);
    }

    [Test]
    public void TC_049_CommonDensiSantei_147()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               147,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);
        var newSinDate = interactor.MonthsBefore(sinDate, 11);

        // Assert
        Assert.True(sTerm == "12月" && startDate == newSinDate);
    }

    [Test]
    public void TC_050_CommonDensiSantei_148()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               148,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm, sinDate, sysyosinDate);
        var newSinDate = interactor.YearsBefore(sinDate, 5);

        // Assert
        Assert.True(sTerm == "5年" && startDate == newSinDate);
    }

    [Test]
    public void TC_051_CommonDensiSantei_997()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        mockTodayRepo.Setup(repo => repo.MonthsAfterExcludeHoliday(1, sysyosinDate, 1))
        .Returns(20191212);
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "11101385131",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
               1,
               1,
               "111014210",
               997,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
             1,
             1,
             "1110142111",
             997,
             100,
             0,
             20220101,
             99999999,
             1,
             1,
             1,
             1,
             1,
             1,
             2
         );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate1 = 0, endDate2 = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel1, odrDetail2, odrDetails, ref startDate, ref endDate1, ref sTerm, sinDate, sysyosinDate);

        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate2, ref sTerm, sinDate, sysyosinDate);

        // Assert
        Assert.True(endDate1 == 99999999 && endDate2 == 20191212);
    }

    [Test]
    public void TC_052_CommonDensiSantei_998()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "11101385131",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
               1,
               1,
               "111014210",
               998,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
           );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
             1,
             1,
             "1110142111",
             998,
             100,
             0,
             20220101,
             99999999,
             1,
             1,
             1,
             1,
             1,
             1,
             2
         );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate1 = 0, endDate2 = 0;
        string sTerm = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel1, odrDetail2, odrDetails, ref startDate, ref endDate1, ref sTerm, sinDate, sysyosinDate);

        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate, ref endDate2, ref sTerm, sinDate, sysyosinDate);
        var newSysyosinDate = interactor.MonthsAfter(sysyosinDate, 1);

        // Assert
        Assert.True(endDate1 == 99999999 && endDate2 == newSysyosinDate);
    }

    [Test]
    public void TC_053_CommonDensiSantei_999_TermSbt2()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               999,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               2,
               1,
               2
           );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
         1,
         1,
         "111013850",
         999,
         100,
         0,
         20220101,
         99999999,
         1,
         1,
         1,
         2,
         2,
         1,
         2
     );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate1 = 0, startDate2 = 0, endDate = 0;
        string sTerm1 = string.Empty, sTerm2 = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel1, odrDetail1, odrDetails, ref startDate1, ref endDate, ref sTerm1, sinDate, sysyosinDate);
        var newSinDate1 = interactor.DaysBefore(sinDate, densiSanteiKaisuModel1.TermCount);

        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate2, ref endDate, ref sTerm2, sinDate, sysyosinDate);
        var newSinDate2 = interactor.DaysBefore(sinDate, densiSanteiKaisuModel2.TermCount);

        // Assert
        Assert.True(sTerm1 == "日" && startDate1 == newSinDate1 && sTerm2 == densiSanteiKaisuModel2.TermCount + "日" && startDate2 == newSinDate2);
    }

    [Test]
    public void TC_054_CommonDensiSantei_999_TermSbt3()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               999,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               3,
               1,
               2
           );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
         1,
         1,
         "111013850",
         999,
         100,
         0,
         20220101,
         99999999,
         1,
         1,
         1,
         2,
         3,
         1,
         2
     );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate1 = 0, startDate2 = 0, endDate = 0;
        string sTerm1 = string.Empty, sTerm2 = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel1, odrDetail1, odrDetails, ref startDate1, ref endDate, ref sTerm1, sinDate, sysyosinDate);
        var newSinDate1 = interactor.WeeksBefore(sinDate, densiSanteiKaisuModel1.TermCount);

        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate2, ref endDate, ref sTerm2, sinDate, sysyosinDate);
        var newSinDate2 = interactor.WeeksBefore(sinDate, densiSanteiKaisuModel2.TermCount);

        // Assert
        Assert.True(sTerm1 == "週" && startDate1 == newSinDate1 && sTerm2 == densiSanteiKaisuModel2.TermCount + "週" && startDate2 == newSinDate2);
    }

    [Test]
    public void TC_055_CommonDensiSantei_999_TermSbt4()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               999,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               4,
               1,
               2
           );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
         1,
         1,
         "111013850",
         999,
         100,
         0,
         20220101,
         99999999,
         1,
         1,
         1,
         2,
         4,
         1,
         2
     );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate1 = 0, startDate2 = 0, endDate = 0;
        string sTerm1 = string.Empty, sTerm2 = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel1, odrDetail1, odrDetails, ref startDate1, ref endDate, ref sTerm1, sinDate, sysyosinDate);
        var newSinDate1 = interactor.MonthsBefore(sinDate, densiSanteiKaisuModel1.TermCount);

        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate2, ref endDate, ref sTerm2, sinDate, sysyosinDate);
        var newSinDate2 = interactor.MonthsBefore(sinDate, densiSanteiKaisuModel2.TermCount);

        // Assert
        Assert.True(sTerm1 == "月" && startDate1 == newSinDate1 && sTerm2 == densiSanteiKaisuModel2.TermCount + "月" && startDate2 == newSinDate2);
    }

    [Test]
    public void TC_056_CommonDensiSantei_999_TermSbt5()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel1 = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               999,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               5,
               1,
               2
           );

        var densiSanteiKaisuModel2 = new DensiSanteiKaisuModel(
         1,
         1,
         "111013850",
         999,
         100,
         0,
         20220101,
         99999999,
         1,
         1,
         1,
         2,
         5,
         1,
         2
     );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate1 = 0, startDate2 = 0, endDate = 0;
        string sTerm1 = string.Empty, sTerm2 = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel1, odrDetail1, odrDetails, ref startDate1, ref endDate, ref sTerm1, sinDate, sysyosinDate);
        var newSinDate1 = (sinDate / 10000 - (densiSanteiKaisuModel1.TermCount - 1)) * 10000 + 101;

        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel2, odrDetail1, odrDetails, ref startDate2, ref endDate, ref sTerm2, sinDate, sysyosinDate);
        var newSinDate2 = (sinDate / 10000 - (densiSanteiKaisuModel2.TermCount - 1)) * 10000 + 101;

        // Assert
        Assert.True(sTerm1 == "年間" && startDate1 == newSinDate1 && sTerm2 == densiSanteiKaisuModel2.TermCount + "年間" && startDate2 == newSinDate2);
    }

    [Test]
    public void TC_057_CommonDensiSantei_Other()
    {
        // Arrange
        int hpId = 1, sinDate = 20221110, sysyosinDate = 20191111;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var odrDetails = new List<OrdInfDetailModel>();
        var odrDetail1 = new OrdInfDetailModel(
            1,
            "111000110",
            20221111
            );
        odrDetails.Add(odrDetail1);

        var odrDetail2 = new OrdInfDetailModel(
            1,
            "111013850",
            20221111
            );
        odrDetails.Add(odrDetail2);

        var densiSanteiKaisuModel = new DensiSanteiKaisuModel(
               1,
               1,
               "111013850",
               1,
               100,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               5,
               1,
               2
           );

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        int startDate = 0, endDate = 0;
        string sTerm1 = string.Empty;

        // Act
        interactor.CommonDensiSantei(hpId, densiSanteiKaisuModel, odrDetail1, odrDetails, ref startDate, ref endDate, ref sTerm1, sinDate, sysyosinDate);

        // Assert
        Assert.True(startDate == -1);
    }
    #endregion

    #region util function
    [Test]
    public void TC_058_WeekBefore()
    {
        // Arrange
        int baseDate = 20191111, term = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        string s = baseDate.ToString("D8");
        var dt1 = DateTime.ParseExact(s, "yyyyMMdd", CultureInfo.InvariantCulture);
        dt1 = dt1.AddDays((int)dt1.DayOfWeek * -1 + -7 * (term - 1));
        var retDate = int.Parse(dt1.ToString("yyyyMMdd"));

        var output = interactor.WeeksBefore(baseDate, term);

        // Assert
        Assert.True(output == retDate);
    }

    [Test]
    public void TC_059_MonthsBefore()
    {
        // Arrange
        int baseDate = 20191111, term = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        string s = baseDate.ToString("D8");
        var dt1 = DateTime.ParseExact(s, "yyyyMMdd", CultureInfo.InvariantCulture);
        dt1 = dt1.AddMonths(term * -1);
        var retDate = int.Parse(dt1.ToString("yyyyMMdd"));
        retDate = retDate / 100 * 100 + 1;

        var output = interactor.MonthsBefore(baseDate, term);

        // Assert
        Assert.True(output == retDate);
    }

    [Test]
    public void TC_060_YearsBefore()
    {
        // Arrange
        int baseDate = 20191111, term = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        string s = baseDate.ToString("D8");
        var dt1 = DateTime.ParseExact(s, "yyyyMMdd", CultureInfo.InvariantCulture);
        dt1 = dt1.AddYears(term * -1);
        var retDate = int.Parse(dt1.ToString("yyyyMMdd"));
        retDate = retDate / 100 * 100 + 1;

        var output = interactor.YearsBefore(baseDate, term);

        // Assert
        Assert.True(output == retDate);
    }

    [Test]
    public void TC_061_DaysBefore()
    {
        // Arrange
        int baseDate = 20191111, term = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        string s = baseDate.ToString("D8");
        var dt1 = DateTime.ParseExact(s, "yyyyMMdd", CultureInfo.InvariantCulture);
        dt1 = dt1.AddDays((term - 1) * -1);
        var retDate = int.Parse(dt1.ToString("yyyyMMdd"));

        var output = interactor.DaysBefore(baseDate, term);

        // Assert
        Assert.True(output == retDate);
    }

    [Test]
    public void TC_062_MonthsAfter()
    {
        // Arrange
        int baseDate = 20191111, term = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        string s = baseDate.ToString("D8");
        var dt1 = DateTime.ParseExact(s, "yyyyMMdd", CultureInfo.InvariantCulture);
        dt1 = dt1.AddMonths(term);
        var retDate = int.Parse(dt1.ToString("yyyyMMdd"));

        var output = interactor.MonthsAfter(baseDate, term);

        // Assert
        Assert.True(output == retDate);
    }

    [Test]
    public void TC_063_GetHokenKbn()
    {
        // Arrange
        int odrHokenKbn1 = 0, odrHokenKbn2 = 1, odrHokenKbn3 = 2, odrHokenKbn4 = 11, odrHokenKbn5 = 12, odrHokenKbn6 = 13, odrHokenKbn7 = 14;
        var mockMstItemRepo = new Mock<IMstItemRepository>();

        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.GetHokenKbn(odrHokenKbn1);
        var output2 = interactor.GetHokenKbn(odrHokenKbn2);
        var output3 = interactor.GetHokenKbn(odrHokenKbn3);
        var output4 = interactor.GetHokenKbn(odrHokenKbn4);
        var output5 = interactor.GetHokenKbn(odrHokenKbn5);
        var output6 = interactor.GetHokenKbn(odrHokenKbn6);
        var output7 = interactor.GetHokenKbn(odrHokenKbn7);

        // Assert
        Assert.True(output1 == 4 && output2 == 0 && output3 == 0 && output4 == 1 && output5 == 1 && output6 == 2 && output7 == 3);
    }

    [Test]
    public void TC_064_GetCheckSanteiKbns()
    {
        // Arrange
        int odrHokenKbn = 0, hokensyuHandling = 1;
        var mockMstItemRepo = new Mock<IMstItemRepository>();

        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.GetCheckSanteiKbns(odrHokenKbn, hokensyuHandling);

        // Assert
        Assert.True(output.Count == 2 && output.Contains(2));
    }

    [Test]
    public void TC_065_GetCheckHokenKbns()
    {
        // Arrange
        int hokensyuHandling1 = 0, hokensyuHandling2 = 1, hokensyuHandling3 = 2, odrHokenKbn1 = 0, odrHokenKbn2 = 1, odrHokenKbn3 = 2, odrHokenKbn4 = 11, odrHokenKbn5 = 12, odrHokenKbn6 = 13, odrHokenKbn7 = 14;

        var mockMstItemRepo = new Mock<IMstItemRepository>();

        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.GetCheckHokenKbns(odrHokenKbn2, hokensyuHandling1);
        var output2 = interactor.GetCheckHokenKbns(odrHokenKbn3, hokensyuHandling1);
        var output3 = interactor.GetCheckHokenKbns(odrHokenKbn4, hokensyuHandling1);
        var output4 = interactor.GetCheckHokenKbns(odrHokenKbn5, hokensyuHandling1);
        var output5 = interactor.GetCheckHokenKbns(odrHokenKbn6, hokensyuHandling1);
        var output6 = interactor.GetCheckHokenKbns(odrHokenKbn7, hokensyuHandling1);
        var output7 = interactor.GetCheckHokenKbns(odrHokenKbn1, hokensyuHandling1);
        var output8 = interactor.GetCheckHokenKbns(odrHokenKbn1, hokensyuHandling2);
        var output9 = interactor.GetCheckHokenKbns(odrHokenKbn1, hokensyuHandling3);

        // Assert
        Assert.That(new List<int> { 0, 1, 2, 3 }, Is.EqualTo(output1));
        Assert.That(new List<int> { 0, 1, 2, 3 }, Is.EqualTo(output2));
        Assert.That(new List<int> { 0, 1, 2, 3 }, Is.EqualTo(output3));
        Assert.That(new List<int> { 0, 1, 2, 3 }, Is.EqualTo(output4));
        Assert.That(new List<int> { 0, 1, 2, 3 }, Is.EqualTo(output5));
        Assert.That(new List<int> { 0, 1, 2, 3 }, Is.EqualTo(output6));
        Assert.Contains(4, output7);
        Assert.That(new List<int> { 0, 1, 2, 3, 4, 0 }, Is.EqualTo(output8));
        Assert.True(output9.Contains(4) && output9.Contains(0));
    }

    [Test]
    public void TC_066_GetPtHokenKbn()
    {
        // Arrange
        int hpId = 1, ptId = 5, sinDate = 20221111, rpNo = 1, edano = 1;
        List<(long rpno, long edano, int hokenId)> hokenIds1 = new List<(long rpno, long edano, int hokenId)> { new(1, 1, 10), new(2, 1, 11) };

        List<(long rpno, long edano, int hokenId)> hokenIds2 = new List<(long rpno, long edano, int hokenId)> { new(1, 2, 10), new(1, 3, 11) };

        var mockMstItemRepo = new Mock<IMstItemRepository>();

        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        mockInsuranceRepo.Setup(repo => repo.GetPtHokenInf(1, 10, 5, sinDate))
    .Returns(new InsuranceModel(1, 5, 1, 1, 10, 1, 1, 1, 1, 1, 1, 1, 1, 0));
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output1 = interactor.GetPtHokenKbn(hpId, ptId, sinDate, rpNo, edano, hokenIds1);
        var output2 = interactor.GetPtHokenKbn(hpId, ptId, sinDate, rpNo, edano, hokenIds2);

        // Assert
        Assert.True(output1 == 1 && output2 == 0);
    }
    #endregion

    #region Main
    [Test]
    public void TC_067_Handle_HpId()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var checkedSpecialItemInputData = new CheckedSpecialItemInputData(-1, 1, 1, 20221111, 1, 1, 1, new(), new(), new(), true, true);
        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.Handle(checkedSpecialItemInputData);

        // Assert
        Assert.True(output.Status == CheckedSpecialItemStatus.InvalidHpId && !output.CheckSpecialItemModels.Any());
    }

    [Test]
    public void TC_068_Handle_PtId()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var checkedSpecialItemInputData = new CheckedSpecialItemInputData(1, 1, 0, 20221111, 1, 1, 1, new(), new(), new(), true, true);
        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.Handle(checkedSpecialItemInputData);

        // Assert
        Assert.True(output.Status == CheckedSpecialItemStatus.InvalidPtId && !output.CheckSpecialItemModels.Any());
    }

    [Test]
    public void TC_069_Handle_SinDate()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var checkedSpecialItemInputData = new CheckedSpecialItemInputData(1, 1, 1, 0, 1, 1, 1, new(), new(), new(), true, true);
        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.Handle(checkedSpecialItemInputData);

        // Assert
        Assert.True(output.Status == CheckedSpecialItemStatus.InvalidSinDate && !output.CheckSpecialItemModels.Any());
    }

    [Test]
    public void TC_070_Handle_IBirthDay()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var checkedSpecialItemInputData = new CheckedSpecialItemInputData(1, 1, 1, 20221111, 0, 1, 1, new(), new(), new(), true, true);
        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.Handle(checkedSpecialItemInputData);

        // Assert
        Assert.True(output.Status == CheckedSpecialItemStatus.InvalidIBirthDay && !output.CheckSpecialItemModels.Any());
    }

    [Test]
    public void TC_071_Handle_CheckAge()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var checkedSpecialItemInputData = new CheckedSpecialItemInputData(1, 1, 1, 20221111, 1, -1, 1, new(), new(), new(), true, true);
        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.Handle(checkedSpecialItemInputData);

        // Assert
        Assert.True(output.Status == CheckedSpecialItemStatus.InvalidCheckAge && !output.CheckSpecialItemModels.Any());
    }

    [Test]
    public void TC_072_Handle_RaiinNo()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var checkedSpecialItemInputData = new CheckedSpecialItemInputData(1, 1, 1, 20221111, 1, 1, -1, new(), new(), new(), true, true);
        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.Handle(checkedSpecialItemInputData);

        // Assert
        Assert.True(output.Status == CheckedSpecialItemStatus.InvalidRaiinNo && !output.CheckSpecialItemModels.Any());
    }

    [Test]
    public void TC_073_Handle_InvalidOrderAndKarte()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();

        var checkedSpecialItemInputData = new CheckedSpecialItemInputData(1, 1, 1, 20221111, 1, 1, 1, new(), new(), new(), true, true);
        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        // Act
        var output = interactor.Handle(checkedSpecialItemInputData);

        // Assert
        Assert.True(output.Status == CheckedSpecialItemStatus.InvalidOdrInfDetail && !output.CheckSpecialItemModels.Any());
    }

    [Test]
    public void TC_074_Handle_EnabledInputCheck_True()
    {
        // Arrange
        var mockMstItemRepo1 = new Mock<IMstItemRepository>();
        var mockMstItemRepo2 = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        mockMstItemRepo1.Setup(repo => repo.FindTenMst(1, new List<string>() { "1110001101", "111000110", "111013850", "111014210", "113019710" }, 20220101, 20220101)).Returns(new List<TenItemModel>() {
            new TenItemModel(
            1,
            "1110001101",
            "15",
            "AA",
            "1110001101",
            20210101,
            99999999
            ),
            new TenItemModel(
            1,
            "111000110",
            "15",
            "AA",
            "111000110",
            20220101,
            99999999
            ),
            new TenItemModel(
            1,
           "111013850",

           "00",
           "AA",
           "111013850",
           20220401,
           99999999
           ),
           new TenItemModel(
             1,
            "111014210",

            "0",
            "AA",
            "111014210",
            20220401,
            99999999
           ),
           new TenItemModel(
            1,
           "113019710",
           "00",
           "AA",
           "113019710",
           20220401,
           99999999
           )
        });
        mockMstItemRepo2.Setup(repo => repo.FindTenMst(1, new List<string>() { "1110001101", "111000110", "111013850", "111014210", "113019710" }, 20220101, 20220101)).Returns(new List<TenItemModel>() {
            new TenItemModel(
            1,
            "1110001101",
            "15",
            "AA",
            "1110001101",
            20221001,
            99999999
            )
        });
        mockTodayRepo.Setup(repo => repo.FindDensiSanteiKaisuList(1, new List<string>() { "1110001101", "111000110", "111013850", "111014210", "113019710" }, 20220101, 20220101)).Returns(new List<DensiSanteiKaisuModel> { new DensiSanteiKaisuModel(
                1,
                1,
                "111014210",
                997,
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                1,
                1,
                1
          ),
          new DensiSanteiKaisuModel(
               1,
               1,
               "113019710",
               998,
               10,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
          )
        });
        mockMstItemRepo1.Setup(repo => repo.FindItemGrpMst(1, 20220101, 1, new List<long>() { 1, 2 })).Returns(new List<ItemGrpMstModel>() { new ItemGrpMstModel(
                    1,
                    1,
                    1,
                    20220101,
                    99999999,
                    "111014210",
                    1
                ),
                new ItemGrpMstModel(
                    1,
                    1,
                    2,
                    20220101,
                    99999999,
                    "113019710",
                    1
                )
        });
        mockMstItemRepo2.Setup(repo => repo.FindItemGrpMst(1, 20220101, 1, new List<long>() { 1, 2 })).Returns(new List<ItemGrpMstModel>() { new ItemGrpMstModel(
                    1,
                    1,
                    1,
                    20220101,
                    99999999,
                    "111014210",
                    1
                ),
                new ItemGrpMstModel(
                    1,
                    1,
                    2,
                    20220101,
                    99999999,
                    "113019710",
                    1
                )
        });

        var interactor1 = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo1.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);
        var interactor2 = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo2.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        var odrDetails = new List<OdrInfDetailItemInputData>();
        var odrDetail = new OdrInfDetailItemInputData(
            1,
            "1110001101",
            20220101
            );
        odrDetails.Add(odrDetail);

        var odrDetailAgeLimitCheck = new OdrInfDetailItemInputData(
            1,
            "111000110",
            20220101
            );
        odrDetails.Add(odrDetailAgeLimitCheck);

        var odrDetailExpiredCheck = new OdrInfDetailItemInputData(
            1,
            "111013850",
            20220101
            );
        odrDetails.Add(odrDetailExpiredCheck);

        var odrDetailCalculationCheck = new OdrInfDetailItemInputData(
            1,
            "111014210",
            20220101
            );
        odrDetails.Add(odrDetailCalculationCheck);

        var odrDetailDuplicateCheck = new OdrInfDetailItemInputData(
            1,
            "113019710",
            20220101
            );
        odrDetails.Add(odrDetailDuplicateCheck);
        odrDetails.Add(odrDetailDuplicateCheck);

        var checkedSpecialItemInputData1 = new CheckedSpecialItemInputData(1, 1, 1, 20220101, 19930903, 1, 1, new List<OdrInfItemInputData>() { new OdrInfItemInputData(1, 11111000, 1, 1, 1, 20220101, 1, 1, "abc", 1, 1, 1, 1, 1, 1, 1, 1, odrDetails, 0) }, new(), new(), true, false);
        var checkedSpecialItemInputData2 = new CheckedSpecialItemInputData(1, 1, 1, 20220101, 19930903, 1, 1, new List<OdrInfItemInputData>() { new OdrInfItemInputData(1, 11111000, 1, 1, 1, 20220101, 1, 1, "abc", 1, 1, 1, 1, 1, 1, 1, 1, odrDetails, 0) }, new(), new(), true, false);

        // Act
        var output1 = interactor1.Handle(checkedSpecialItemInputData1);
        var output2 = interactor2.Handle(checkedSpecialItemInputData2);

        // Assert
        Assert.True(output1.CheckSpecialItemModels.Any(o => o.CheckingType == Helper.Enum.CheckSpecialType.AgeLimit));
        Assert.True(output1.CheckSpecialItemModels.Any(o => o.CheckingType == Helper.Enum.CheckSpecialType.Duplicate));
        Assert.True(output1.CheckSpecialItemModels.Any(o => o.CheckingType == Helper.Enum.CheckSpecialType.CalculationCount));
        Assert.True(output2.CheckSpecialItemModels.Any(o => o.CheckingType == Helper.Enum.CheckSpecialType.Expiration));
        Assert.True(output1.Status == CheckedSpecialItemStatus.Successed && output2.Status == CheckedSpecialItemStatus.Successed);
    }

    [Test]
    public void TC_075_Handle_EnabledCommentCheck_True()
    {
        // Arrange
        var mockMstItemRepo = new Mock<IMstItemRepository>();
        var mockTodayRepo = new Mock<ITodayOdrRepository>();
        var mockInsuranceRepo = new Mock<IInsuranceRepository>();
        var mockSystemConfigRepo = new Mock<ISystemConfRepository>();
        var mockReceptionRepo = new Mock<IReceptionRepository>();
        mockMstItemRepo.Setup(repo => repo.FindTenMst(1, new List<string>() { "1110001101", "111000110", "111013850", "111014210", "113019710" }, 20220101, 20220101)).Returns(new List<TenItemModel>() {
            new TenItemModel(
            1,
            "1110001101",
            "15",
            "AA",
            "1110001101",
            20210101,
            99999999
            ),
            new TenItemModel(
            1,
            "111000110",
            "15",
            "AA",
            "111000110",
            20220101,
            99999999
            ),
            new TenItemModel(
            1,
           "111013850",
           "00",
           "AA",
           "111013850",
           20220401,
           99999999
           ),
           new TenItemModel(
             1,
            "111014210",
            "0",
            "AA",
            "111014210",
            20220401,
            99999999
           ),
           new TenItemModel(
            1,
           "113019710",
           "00",
           "AA",
           "113019710",
           20220401,
           99999999
           )
        });
        mockTodayRepo.Setup(repo => repo.FindDensiSanteiKaisuList(1, new List<string>() { "1110001101", "111000110", "111013850", "111014210", "113019710" }, 20220101, 20220101)).Returns(new List<DensiSanteiKaisuModel> { new DensiSanteiKaisuModel(
                1,
                1,
                "111014210",
                997,
                10,
                0,
                20220101,
                99999999,
                1,
                1,
                1,
                1,
                1,
                1,
                1
          ),
          new DensiSanteiKaisuModel(
               1,
               1,
               "113019710",
               998,
               10,
               0,
               20220101,
               99999999,
               1,
               1,
               1,
               1,
               1,
               1,
               2
          )
        });
        mockMstItemRepo.Setup(repo => repo.FindItemGrpMst(1, 20220101, 1, new List<long>() { 1, 2 })).Returns(new List<ItemGrpMstModel>() { new ItemGrpMstModel(
                    1,
                    1,
                    1,
                    20220101,
                    99999999,
                    "111014210",
                    1
                ),
                new ItemGrpMstModel(
                    1,
                    1,
                    2,
                    20220101,
                    99999999,
                    "113019710",
                    1
                )
        });
        mockMstItemRepo.Setup(repo => repo.FindItemGrpMst(1, 20220101, 1, new List<long>() { 1, 2 })).Returns(new List<ItemGrpMstModel>() { new ItemGrpMstModel(
                    1,
                    1,
                    1,
                    20220101,
                    99999999,
                    "111014210",
                    1
                ),
                new ItemGrpMstModel(
                    1,
                    1,
                    2,
                    20220101,
                    99999999,
                    "113019710",
                    1
                )
        });
        mockMstItemRepo.Setup(repo => repo.GetCmtCheckMsts(1, 1, new List<string>() { "1110001101", "111000110", "111013850", "111014210", "113019710" })).Returns(new List<ItemCmtModel>() {
            new ItemCmtModel(
                   "111000110",
                   1,
                   1,
                   "comment abc",
                   0
                ),
            new ItemCmtModel(
                   "111013850",
                   1,
                   2,
                   "comment bcd",
                    1
                )
        });

        var interactor = new CheckedSpecialItemInteractor(mockTodayRepo.Object, mockMstItemRepo.Object, mockInsuranceRepo.Object, mockSystemConfigRepo.Object, mockReceptionRepo.Object);

        var odrDetails = new List<OdrInfDetailItemInputData>();
        var odrDetail = new OdrInfDetailItemInputData(
            1,
            "1110001101",
            20220101
            );
        odrDetails.Add(odrDetail);

        var odrDetailAgeLimitCheck = new OdrInfDetailItemInputData(
            1,
            "111000110",
            20220101
            );
        odrDetails.Add(odrDetailAgeLimitCheck);

        var odrDetailExpiredCheck = new OdrInfDetailItemInputData(
            1,
            "111013850",
            20220101
            );
        odrDetails.Add(odrDetailExpiredCheck);

        var odrDetailCalculationCheck = new OdrInfDetailItemInputData(
            1,
            "111014210",
            20220101
            );
        odrDetails.Add(odrDetailCalculationCheck);

        var odrDetailDuplicateCheck = new OdrInfDetailItemInputData(
            1,
            "113019710",
            20220101
            );
        odrDetails.Add(odrDetailDuplicateCheck);
        odrDetails.Add(odrDetailDuplicateCheck);

        var checkedSpecialItemInputData = new CheckedSpecialItemInputData(1, 1, 1, 20220101, 19930903, 1, 1, new List<OdrInfItemInputData>() { new OdrInfItemInputData(1, 11111000, 1, 1, 1, 20220101, 1, 1, "abc", 1, 1, 1, 1, 1, 1, 1, 1, odrDetails, 0) }, new(), new KarteItemInputData(1, 901072057, 1, 20221111, "comment abc", 0, "abc"), false, true);

        // Act
        var output = interactor.Handle(checkedSpecialItemInputData);

        // Assert
        Assert.True(output.CheckSpecialItemModels.Any(o => o.CheckingType == Helper.Enum.CheckSpecialType.ItemComment));
        Assert.True(output.Status == CheckedSpecialItemStatus.Successed);
    }
    #endregion
}