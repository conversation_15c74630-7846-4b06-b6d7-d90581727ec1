﻿using Domain.Constant;
using Domain.Models.Eps.CheckErrorPreRegistration;
using Domain.Models.KensaMst;
using Domain.Models.OrdInf;
using Emr.Report.OutDrug.Model;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Helper.Extension;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Reporting.OutDrug.Model;
using Reporting.Receipt.Models;
using System.Linq.Expressions;
using PtHokenInfModel = Reporting.OutDrug.Model.PtHokenInfModel;
using PtKohiModel = Reporting.OutDrug.Model.PtKohiModel;

namespace Reporting.OutDrug.DB;

public class CoOutDrugFinder : RepositoryBase, ICoOutDrugFinder
{
    public CoOutDrugFinder(ITenantProvider tenantProvider) : base(tenantProvider)
    {
    }

    public void ReleaseResource()
    {
        DisposeDataContext();
    }

    /// <summary>
    /// オーダー情報取得
    /// </summary>
    /// <param name="ptId">患者ID</param>
    /// <param name="sinDate">診療日</param>
    /// <returns>
    /// 指定の患者の指定の診療日のオーダー情報
    /// 削除分は除く
    /// </returns>
    public List<CoOdrInfModel> FindOdrInfData(int hpId, long ptId, int sinDate, long raiinNo)
    {
        var odrInfs = NoTrackingDataContext.OdrInfs.Where(o =>
            o.HpId == hpId &&
            o.PtId == ptId &&
            o.SinDate == sinDate &&
            o.RaiinNo == raiinNo &&
            o.InoutKbn == 1 &&
            OrderInfConst.TodayOdrKouiKbn.Contains(o.OdrKouiKbn) &&
            o.IsDeleted == DeleteStatus.None);
        var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(o =>
            o.HpId == hpId);

        var joinQuery = (
            from odrInf in odrInfs
            join PtHokenPattern in ptHokenPatterns on
                new { odrInf.HpId, odrInf.PtId, HokenPid = odrInf.HokenPid } equals
                new { PtHokenPattern.HpId, PtHokenPattern.PtId, PtHokenPattern.HokenPid }
            where
                odrInf.HpId == hpId &&
                odrInf.PtId == ptId &&
                odrInf.IsDeleted == DeleteStatus.None
            orderby
                odrInf.RaiinNo, odrInf.OdrKouiKbn, odrInf.SortNo, odrInf.RpNo
            select new
            {
                odrInf,
                PtHokenPattern
            }
        );
        var entities = joinQuery.AsEnumerable().Select(
            data =>
                new CoOdrInfModel(data.odrInf, data.PtHokenPattern)
            )
            .ToList();

        List<CoOdrInfModel> results = new List<CoOdrInfModel>();

        entities?.ForEach(entity =>
        {
            results.Add(new CoOdrInfModel(entity.OdrInf, entity.PtHokenPattern));
        });

        return results;
    }

    /// <summary>
    /// オーダー詳細情報を取得する
    /// </summary>
    /// <param name="ptId">患者ID</param>
    /// <param name="sinDate">診療日</param>
    /// <param name="raiinNo">来院番号</param>
    /// <returns></returns>
    public List<CoOdrInfDetailModel> FindOdrInfDetailData(int hpId, long ptId, int sinDate, long raiinNo)
    {
        var odrInfs = NoTrackingDataContext.OdrInfs.Where(item => item.HpId == hpId &&
                                                                  item.PtId == ptId &&
                                                                  item.SinDate == sinDate &&
                                                                  item.RaiinNo == raiinNo &&
                                                                  item.IsDeleted == DeleteStatus.None)
                                                   .ToList();
        // return if odrInfDetails not exits data
        if (!odrInfs.Any())
        {
            return new();
        }

        var hokenPidList = odrInfs.Select(item => item.HokenPid).Distinct().ToList();
        var odrInfDetails = NoTrackingDataContext.OdrInfDetails.Where(item => item.HpId == hpId
                                                                              && item.PtId == ptId
                                                                              && item.SinDate == sinDate
                                                                              && !(item.ItemCd != null
                                                                                   && item.ItemCd.StartsWith("8") && item.ItemCd.Length == 9))
                                                               .ToList();
        // return if odrInfDetails not exits data
        if (!odrInfDetails.Any())
        {
            return new();
        }
        var itemCdList = odrInfDetails.Select(item => item.ItemCd).Distinct().ToList();
        var tenMsts = NoTrackingDataContext.TenMsts.Where(item => item.HpId == hpId
                                                                  && item.StartDate <= sinDate
                                                                  && (item.EndDate >= sinDate || item.EndDate == 12341234)
                                                                  && itemCdList.Contains(item.ItemCd))
                                                   .ToList();

        var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(item => item.HpId == hpId
                                                                                  && item.PtId == ptId
                                                                                  && hokenPidList.Contains(item.HokenPid))
                                                                   .ToList();
        var yohoMsts = NoTrackingDataContext.YohoMsts.Where(item => item.HpId == hpId
                                                                    && item.StartDate <= sinDate
                                                                    && (item.EndDate >= sinDate || item.EndDate == 12341234))
                                                     .ToList();

        var tenJoins = (
            from tenMst in tenMsts
            join yohoMst in yohoMsts on
              new { tenMst.HpId, tenMst.YohoCd } equals
              new { yohoMst.HpId, yohoMst.YohoCd } into yJoin
            from yj in yJoin.DefaultIfEmpty()
            select new
            {
                tenMst,
                yohoMst = yj
            }).ToList();

        var entities = (
            from odrInf in odrInfs
            join odrInfDetail in odrInfDetails on
                new { odrInf.HpId, odrInf.PtId, odrInf.RaiinNo, odrInf.RpNo, odrInf.RpEdaNo } equals
                new { odrInfDetail.HpId, odrInfDetail.PtId, odrInfDetail.RaiinNo, odrInfDetail.RpNo, odrInfDetail.RpEdaNo }
            join tenJoin in tenJoins on
                new { odrInfDetail.HpId, ItemCd = odrInfDetail.ItemCd ?? string.Empty.Trim() } equals
                new { tenJoin.tenMst.HpId, tenJoin.tenMst.ItemCd } into oJoin
            join PtHokenPattern in ptHokenPatterns on
                new { odrInf.HpId, odrInf.PtId, odrInf.HokenPid } equals
                new { PtHokenPattern.HpId, PtHokenPattern.PtId, PtHokenPattern.HokenPid }
            from oj in oJoin.DefaultIfEmpty()
            orderby
                odrInf.RaiinNo, odrInf.OdrKouiKbn, odrInf.SortNo, odrInfDetail.RpNo, odrInfDetail.RpEdaNo, odrInfDetail.RowNo
            select new CoOdrInfDetailModel(
                    odrInfDetail,
                    odrInf,
                    oj?.tenMst,
                    PtHokenPattern,
                    oj?.yohoMst
                )).ToList();

        List<CoOdrInfDetailModel> results = new();

        entities?.ForEach(entity =>
        {
            results.Add(
                new CoOdrInfDetailModel(
                    entity.OdrInfDetail,
                    entity.OdrInf,
                    entity.TenMst,
                    entity.PtHokenPattern,
                    entity.YohoMst
                    ));
        });

        return results;
    }

    /// <summary>
    /// 患者公費情報を取得する
    /// </summary>
    /// <param name="ptId">患者ID</param>
    /// <param name="sinDate">診療日</param>
    /// <param name="kohiIds">公費IDリスト</param>
    /// <returns>患者公費情報のリスト</returns>
    public List<CoPtKohiModel> FindPtKohi(int hpId, long ptId, int sinDate, HashSet<int> kohiIds)
    {
        var hokenMsts = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId);
        //診療日基準で保険番号マスタのキー情報を取得
        var hokenMstKeys = NoTrackingDataContext.HokenMsts.Where(
            h => h.HpId == hpId && h.StartDate <= sinDate
        ).GroupBy(
            x => new { x.HpId, x.PrefNo, x.HokenNo, x.HokenEdaNo }
        ).Select(
            x => new
            {
                x.Key.HpId,
                x.Key.PrefNo,
                x.Key.HokenNo,
                x.Key.HokenEdaNo,
                StartDate = x.Max(d => d.StartDate)
            }
        );

        var kohiPriorities = NoTrackingDataContext.KohiPriorities;
        var ptKohis = NoTrackingDataContext.PtKohis.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptId &&
            kohiIds.Contains(p.HokenId)
        );
        //保険番号マスタの取得
        var houbetuMsts = (
            from hokenMst in hokenMsts
            join hokenKey in hokenMstKeys on
                new { hokenMst.HpId, hokenMst.HokenNo, hokenMst.HokenEdaNo, hokenMst.PrefNo, hokenMst.StartDate } equals
                new { hokenKey.HpId, hokenKey.HokenNo, hokenKey.HokenEdaNo, hokenKey.PrefNo, hokenKey.StartDate }
            select new
            {
                hokenMst
            }
        );

        //公費の優先順位を取得
        var ptKohiQuery = (
            from ptKohi in ptKohis
            join houbetuMst in houbetuMsts on
                new { ptKohi.HpId, ptKohi.HokenNo, ptKohi.HokenEdaNo, ptKohi.PrefNo } equals
                new { houbetuMst.hokenMst.HpId, houbetuMst.hokenMst.HokenNo, houbetuMst.hokenMst.HokenEdaNo, houbetuMst.hokenMst.PrefNo }
            join kPriority in kohiPriorities on
                new { houbetuMst.hokenMst.PrefNo, houbetuMst.hokenMst.Houbetu } equals
                new { kPriority.PrefNo, kPriority.Houbetu } into kohiPriorityJoin
            from kohiPriority in kohiPriorityJoin.DefaultIfEmpty()
            where
                ptKohi.HpId == hpId &&
                ptKohi.PtId == ptId &&
                ptKohi.IsDeleted == DeleteStatus.None
            select new
            {
                ptKohi,
                hokenMst = houbetuMst.hokenMst,
                kohiPriority
            }
        ).ToList();

        var entities = ptKohiQuery.AsEnumerable().Select(
            data =>
                new CoPtKohiModel(
                    data.ptKohi,
                    data.hokenMst,
                    data.kohiPriority
                )
            )
            .ToList();
        List<CoPtKohiModel> results = new List<CoPtKohiModel>();

        entities?.ForEach(entity =>
        {

            results.Add(
                new CoPtKohiModel(
                    entity.PtKohi,
                    entity.HokenMst,
                    entity.KohiPriority
                ));

        }
        );

        return results;
    }

    /// <summary>
    /// 医療機関情報を取得する
    /// </summary>
    /// <param name="sinDate">診療日</param>
    /// <returns></returns>
    public CoHpInfModel FindHpInf(int hpId, int sinDate)
    {
        return new CoHpInfModel(
            NoTrackingDataContext.HpInfs.Where(p =>
                p.HpId == hpId &&
                p.StartDate <= sinDate)
                .OrderByDescending(p => p.StartDate)
                .FirstOrDefault() ?? new());
    }
    /// <summary>
    /// 来院情報を取得する
    /// </summary>
    /// <param name="ptId">患者ID</param>
    /// <param name="sinDate">診療日</param>
    /// <param name="raiinNo">来院番号</param>
    /// <returns></returns>
    public CoRaiinInfModel FindRaiinInf(int hpId, long ptId, int sinDate, long raiinNo)
    {
        var raiinInfs =
            NoTrackingDataContext.RaiinInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.SinDate == sinDate &&
                p.RaiinNo == raiinNo &&
                p.IsDeleted == DeleteStatus.None);
        var userMsts =
            NoTrackingDataContext.UserMsts.Where(p =>
                p.HpId == hpId &&
                p.JobCd == 1 &&
                p.IsDeleted == DeleteStatus.None
            );

        var join = (
            from raiinInf in raiinInfs
            join userMst in userMsts on
                new { raiinInf.HpId, UserId = raiinInf.TantoId } equals
                new { userMst.HpId, userMst.UserId } into userMstjoins
            from userMstJoin in userMstjoins.DefaultIfEmpty()
            select new
            {
                raiinInf,
                userMstJoin

            }
            ).ToList();

        var entities = join.AsEnumerable().Select(
            data =>
                new CoRaiinInfModel(
                    data.raiinInf,
                    data.userMstJoin
                )
            )
            .ToList();
        List<CoRaiinInfModel> results = new();

        entities?.ForEach(entity =>
        {

            results.Add(
                new CoRaiinInfModel(
                    entity.RaiinInf,
                    entity.UserMst
                ));

        }
        );

        if (results != null && results.Any())
        {
            return results.First();
        }
        else
        {
            return new();
        }
    }

    /// <summary>
    /// 患者情報を取得する
    /// </summary>
    /// <param name="ptId">患者ID</param>
    /// <param name="sinDate">診療日</param>
    /// <returns></returns>
    public CoPtInfModel FindPtInf(int hpId, long ptId, int sinDate)
    {
        return new CoPtInfModel(
            NoTrackingDataContext.PtInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId)
                .FirstOrDefault() ?? new(),
                sinDate
            );
    }

    /// <summary>
    /// 患者保険情報を取得する
    /// </summary>
    /// <param name="ptId">患者ID</param>
    /// <param name="hokenId">保険ID</param>
    /// <param name="sinDate">診療日</param>
    /// <returns></returns>
    public CoPtHokenInfModel FindPtHoken(int hpId, long ptId, int hokenId, int sinDate)
    {
        var hokenMsts = NoTrackingDataContext.HokenMsts.Where(p => p.HpId == hpId && p.PrefNo == 0 && new int[] { 0, 1, 3, 4, 8 }.Contains(p.HokenSbtKbn));
        //診療日基準で保険番号マスタのキー情報を取得
        var hokenMstKeys = NoTrackingDataContext.HokenMsts.Where(
            h => h.HpId == hpId && h.StartDate <= sinDate
        ).GroupBy(
            x => new { x.HpId, x.PrefNo, x.HokenNo, x.HokenEdaNo }
        ).Select(
            x => new
            {
                x.Key.HpId,
                x.Key.PrefNo,
                x.Key.HokenNo,
                x.Key.HokenEdaNo,
                StartDate = x.Max(d => d.StartDate)
            }
        );
        var ptHokenInfs = NoTrackingDataContext.PtHokenInfs.Where(p =>
                p.HpId == hpId &&
                p.PtId == ptId &&
                p.HokenId == hokenId);
        var houbetuMsts = (
            from hokenMst in hokenMsts
            join hokenKey in hokenMstKeys on
                new { hokenMst.HpId, hokenMst.HokenNo, hokenMst.HokenEdaNo, hokenMst.PrefNo, hokenMst.StartDate } equals
                new { hokenKey.HpId, hokenKey.HokenNo, hokenKey.HokenEdaNo, hokenKey.PrefNo, hokenKey.StartDate }
            select new
            {
                hokenMst
            }
        );
        var ptHokenInfQuery = (
            from ptHokenInf in ptHokenInfs
            join houbetuMst in houbetuMsts on
                new { ptHokenInf.HpId, ptHokenInf.HokenNo, ptHokenInf.HokenEdaNo } equals
                new { houbetuMst.hokenMst.HpId, houbetuMst.hokenMst.HokenNo, houbetuMst.hokenMst.HokenEdaNo }
            where
                ptHokenInf.HpId == hpId &&
                ptHokenInf.PtId == ptId &&
                ptHokenInf.IsDeleted == DeleteStatus.None
            select new
            {
                ptHokenInf,
                hokenMst = houbetuMst.hokenMst
            }
        ).ToList();


        var entities = ptHokenInfQuery.AsEnumerable().Select(
            data =>
                new CoPtHokenInfModel(
                    data.ptHokenInf,
                    data.hokenMst
                )
            )
            .ToList();

        return entities?.FirstOrDefault() ?? new();

    }

    /// <summary>
    /// 指定の期間に指定の項目が算定されているかチェックする
    /// ※複数項目用
    /// </summary>
    /// <param name="ptId">患者ID</param>
    /// <param name="startDate">チェック開始日</param>
    /// <param name="endDate">チェック終了日</param>
    /// <param name="sinDate">診療日（除外する日）</param>
    /// <param name="itemCds">チェックする項目のリスト</param>
    /// <param name="santeiKbn">算定区分</param>
    /// <returns>true: 算定あり</returns>
    public bool CheckSanteiTerm(int hpId, long ptId, int startDate, int endDate, List<string> itemCds)
    {
        int startYm = startDate / 100;
        int endYm = endDate / 100;

        var sinRpInfs = NoTrackingDataContext.SinRpInfs.Where(o =>
            o.HpId == hpId &&
            o.PtId == ptId &&
            o.SinYm >= startYm &&
            o.SinYm <= endYm &&
            o.SanteiKbn == 0 &&
            o.IsDeleted == DeleteStatus.None
        );
        var sinKouiCounts = NoTrackingDataContext.SinKouiCounts.Where(o =>
            o.HpId == hpId &&
            o.PtId == ptId &&
            o.SinYm * 100 + o.SinDay >= startDate &&
            o.SinYm * 100 + o.SinDay <= endDate
            );
        var sinKouiDetails = NoTrackingDataContext.SinKouiDetails.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptId &&
            p.SinYm >= startYm &&
            p.SinYm <= endYm &&
            p.ItemCd != null &&
            itemCds.Contains(p.ItemCd) &&
            p.IsDeleted == DeleteStatus.None);

        var joinQuery = (
            from sinKouiDetail in sinKouiDetails
            join sinKouiCount in sinKouiCounts on
                new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.SinYm, sinKouiDetail.RpNo, sinKouiDetail.SeqNo } equals
                new { sinKouiCount.HpId, sinKouiCount.PtId, sinKouiCount.SinYm, sinKouiCount.RpNo, sinKouiCount.SeqNo }
            join sinRpInf in sinRpInfs on
                new { sinKouiDetail.HpId, sinKouiDetail.PtId, sinKouiDetail.SinYm, sinKouiDetail.RpNo } equals
                new { sinRpInf.HpId, sinRpInf.PtId, sinRpInf.SinYm, sinRpInf.RpNo }
            where
                sinKouiDetail.HpId == hpId &&
                sinKouiDetail.PtId == ptId &&
                sinKouiDetail.SinYm >= startYm &&
                sinKouiDetail.SinYm <= endYm &&
                sinKouiDetail.ItemCd != null &&
                itemCds.Contains(sinKouiDetail.ItemCd) &&
                sinKouiCount.SinYm * 100 + sinKouiCount.SinDay >= startDate &&
                sinKouiCount.SinYm * 100 + sinKouiCount.SinDay <= endDate
            group sinKouiDetail by sinKouiDetail.HpId
        );

        return joinQuery.Any();
    }

    /// <summary>
    /// 指定の期間に指定の項目がオーダーされているかチェックする
    /// ※複数項目用
    /// </summary>
    /// <param name="ptId">患者ID</param>
    /// <param name="startDate">チェック開始日</param>
    /// <param name="endDate">チェック終了日</param>
    /// <param name="sinDate">診療日（除外する日）</param>
    /// <param name="itemCds">チェックする項目のリスト</param>
    /// <param name="santeiKbn">算定区分</param>
    /// <returns>true: 算定あり</returns>
    public bool CheckOdrTerm(int hpId, long ptId, int startDate, int endDate, List<string> itemCds)
    {
        var odrInfs = NoTrackingDataContext.OdrInfs.Where(o =>
            o.HpId == hpId &&
            o.PtId == ptId &&
            o.SinDate >= startDate &&
            o.SinDate <= endDate &&
            o.IsDeleted == DeleteStatus.None);
        var odrInfDetails = NoTrackingDataContext.OdrInfDetails.Where(o =>
            o.HpId == hpId &&
            o.PtId == ptId &&
            o.ItemCd != null &&
            itemCds.Contains(o.ItemCd) &&
            o.SinDate >= startDate &&
            o.SinDate <= endDate);

        var joinQuery = (
            from odrInf in odrInfs
            join odrInfDetail in odrInfDetails on
                new { odrInf.HpId, odrInf.PtId, odrInf.RaiinNo, odrInf.RpNo, odrInf.RpEdaNo } equals
                new { odrInfDetail.HpId, odrInfDetail.PtId, odrInfDetail.RaiinNo, odrInfDetail.RpNo, odrInfDetail.RpEdaNo }
            orderby
                odrInf.RaiinNo, odrInf.OdrKouiKbn, odrInf.SortNo, odrInfDetail.RpNo, odrInfDetail.RpEdaNo, odrInfDetail.RowNo
            select new
            {
                odrInfDetail,
            }
        );

        return joinQuery.Any();

    }
    /// <summary>
    /// 指定の来院に指定の項目がオーダーされているかチェックする
    /// </summary>
    /// <param name="ptId">患者ID</param>
    /// <param name="raiinNo">来院番号</param>
    /// <param name="itemCds">検索する項目の診療行為コードのリスト</param>
    /// <returns></returns>
    public bool CheckOdrRaiin(int hpId, long ptId, long raiinNo, List<string> itemCds)
    {
        var odrInfs = NoTrackingDataContext.OdrInfs.Where(o =>
            o.HpId == hpId &&
            o.PtId == ptId &&
            o.RaiinNo == raiinNo &&
            o.IsDeleted == DeleteStatus.None);
        var odrInfDetails = NoTrackingDataContext.OdrInfDetails.Where(o =>
            o.HpId == hpId &&
            o.PtId == ptId &&
            o.ItemCd != null &&
            itemCds.Contains(o.ItemCd) &&
            o.RaiinNo == raiinNo);

        var joinQuery = (
            from odrInf in odrInfs
            join odrInfDetail in odrInfDetails on
                new { odrInf.HpId, odrInf.PtId, odrInf.RaiinNo, odrInf.RpNo, odrInf.RpEdaNo } equals
                new { odrInfDetail.HpId, odrInfDetail.PtId, odrInfDetail.RaiinNo, odrInfDetail.RpNo, odrInfDetail.RpEdaNo }
            orderby
                odrInf.RaiinNo, odrInf.OdrKouiKbn, odrInf.SortNo, odrInfDetail.RpNo, odrInfDetail.RpEdaNo, odrInfDetail.RowNo
            select new
            {
                odrInfDetail,
            }
        );

        return joinQuery.Any();

    }
    public int ExistMarucyo(int hpId, long ptId, int sinDate, int hokenId)
    {
        int ret = 0;

        if (hokenId == 0) return ret;

        var ptHokenPatterns = NoTrackingDataContext.PtHokenPatterns.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptId &&
            p.HokenId == hokenId &&
            p.IsDeleted == DeleteStatus.None).ToList();

        var ptKohis = NoTrackingDataContext.PtKohis.Where(p =>
            p.HpId == hpId &&
            p.PtId == ptId &&
            p.StartDate <= sinDate &&
            p.EndDate >= sinDate &&
            p.IsDeleted == DeleteStatus.None);

        var hokenMsts = NoTrackingDataContext.HokenMsts.Where(x => x.HpId == hpId);
        //診療日基準で保険番号マスタのキー情報を取得
        var hokenMstKeys = NoTrackingDataContext.HokenMsts.Where(
            h => h.HpId == hpId && h.StartDate <= sinDate
        ).GroupBy(
            x => new { x.HpId, x.PrefNo, x.HokenNo, x.HokenEdaNo }
        ).Select(
            x => new
            {
                x.Key.HpId,
                x.Key.PrefNo,
                x.Key.HokenNo,
                x.Key.HokenEdaNo,
                StartDate = x.Max(d => d.StartDate)
            }
        );

        //保険番号マスタの取得
        var houbetuMsts = (
            from hokenMst in hokenMsts
            join hokenKey in hokenMstKeys on
                new { hokenMst.HpId, hokenMst.HokenNo, hokenMst.HokenEdaNo, hokenMst.PrefNo, hokenMst.StartDate } equals
                new { hokenKey.HpId, hokenKey.HokenNo, hokenKey.HokenEdaNo, hokenKey.PrefNo, hokenKey.StartDate }
            select new
            {
                hokenMst
            }
        );

        //公費の優先順位を取得
        var ptKohiQuery = (
            from ptKohi in ptKohis
            join houbetuMst in houbetuMsts on
                new { ptKohi.HpId, ptKohi.HokenNo, ptKohi.HokenEdaNo, ptKohi.PrefNo } equals
                new { houbetuMst.hokenMst.HpId, houbetuMst.hokenMst.HokenNo, houbetuMst.hokenMst.HokenEdaNo, houbetuMst.hokenMst.PrefNo }
            where
                ptKohi.HpId == hpId &&
                ptKohi.PtId == ptId &&
                houbetuMst.hokenMst.HokenSbtKbn == 2 &&
                ptKohi.IsDeleted == DeleteStatus.None
            select new
            {
                ptKohi,
                hokenMst = houbetuMst.hokenMst
            }
        ).ToList();

        ptHokenPatterns?.ForEach(ptHokenPattern =>
        {
            if (ptKohiQuery.Any(p =>
                (p.ptKohi.HokenId == ptHokenPattern.Kohi1Id && p.ptKohi.HokenEdaNo == 1) ||
                (p.ptKohi.HokenId == ptHokenPattern.Kohi2Id && p.ptKohi.HokenEdaNo == 1) ||
                (p.ptKohi.HokenId == ptHokenPattern.Kohi3Id && p.ptKohi.HokenEdaNo == 1) ||
                (p.ptKohi.HokenId == ptHokenPattern.Kohi4Id && p.ptKohi.HokenEdaNo == 1)))
            {
                if (ret < 2)
                {
                    ret = 2;
                }
            }
            else if (ptKohiQuery.Any(p =>
                (p.ptKohi.HokenId == ptHokenPattern.Kohi1Id && p.ptKohi.HokenEdaNo == 0) ||
                (p.ptKohi.HokenId == ptHokenPattern.Kohi2Id && p.ptKohi.HokenEdaNo == 0) ||
                (p.ptKohi.HokenId == ptHokenPattern.Kohi3Id && p.ptKohi.HokenEdaNo == 0) ||
                (p.ptKohi.HokenId == ptHokenPattern.Kohi4Id && p.ptKohi.HokenEdaNo == 0)) && ret < 1)
            {
                ret = 1;
            }
        }
        );

        return ret;
    }

    /// <summary>
    /// 補足用法情報を取得する
    /// </summary>
    public List<CoYohoHosoku> FindYohoHosoku(int hpId, string itemCd, int sinDate)
    {
        var tenMsts = NoTrackingDataContext.TenMsts.Where(t =>
            t.HpId == hpId &&
            t.StartDate <= sinDate &&
            (t.EndDate >= sinDate || t.EndDate == 12341234));

        var hosokuYohos = NoTrackingDataContext.YohoHosokus.Where(h =>
                 h.HpId == hpId &&
                 h.IsDeleted == DeleteStatus.None &&
                 h.ItemCd == itemCd &&
                 h.StartDate <= sinDate);

        //最新世代の用法補足に絞る
        var grpHosokuYohos = hosokuYohos
            .GroupBy(h => new { h.HpId, h.ItemCd, h.StartDate })
            .Select(h => new { h.Key.HpId, h.Key.ItemCd, MaxStartDate = h.Max(y => y.StartDate) });

        var latestHosokuYohos = (
            from hosokuYoho in hosokuYohos
            join grpHosokuYoho in grpHosokuYohos on
                new { hosokuYoho.HpId, hosokuYoho.ItemCd, hosokuYoho.StartDate } equals
                new { grpHosokuYoho.HpId, grpHosokuYoho.ItemCd, StartDate = grpHosokuYoho.MaxStartDate }
            select new { hosokuYoho }
            );

        //点マスタを外部結合する
        var joinQuery = (
            from latestHosokuYoho in latestHosokuYohos
            join tenMst in tenMsts on
                new { latestHosokuYoho.hosokuYoho.HpId, latestHosokuYoho.hosokuYoho.HosokuItemCd } equals
                new { tenMst.HpId, HosokuItemCd = tenMst.ItemCd } into tenMstJoins
            from tenMstJoin in tenMstJoins.DefaultIfEmpty()
            select new { latestHosokuYoho.hosokuYoho, tenMstJoin }
            ).ToList();

        List<CoYohoHosoku> results = new List<CoYohoHosoku>();

        joinQuery?.ForEach(entity =>
        {
            results.Add(
                new CoYohoHosoku(
                    entity.hosokuYoho,
                    entity.tenMstJoin
                    ));
        }
        );

        return results.OrderBy(r => r.SortNo).ToList();
    }

    public List<CoEpsChk> FindEPSChecks(int hpId, long ptId, long raiinNo)
    {
        var epsChks = NoTrackingDataContext.EpsChks.Where(e =>
            e.HpId == hpId &&
            e.PtId == ptId &&
            e.RaiinNo == raiinNo &&
            e.IsDeleted == 0);

        var epsChkDetails = NoTrackingDataContext.EpsChkDetails.Where(d =>
                 d.HpId == hpId);

        var joinQuery = from epsChk in epsChks
                        join epsChkDetail in epsChkDetails on
                            new { epsChk.HpId, epsChk.PtId, epsChk.RaiinNo, epsChk.SeqNo } equals
                            new { epsChkDetail.HpId, epsChkDetail.PtId, epsChkDetail.RaiinNo, epsChkDetail.SeqNo }
                        select new
                        {
                            epsChk,
                            epsChkDetail
                        };

        var entities = joinQuery.AsEnumerable().Select(
            data =>
                new CoEpsChk(data.epsChk, data.epsChkDetail)
            )
            .ToList();

        List<CoEpsChk> results = new List<CoEpsChk>();

        entities?.ForEach(entity =>
        {
            results.Add(new CoEpsChk(entity.EpsChk, entity.EpsChkDetail));
        });

        return results;
    }

    public CoYohoMstModel? FindYohoMst(int hpId, string yohoCd, int sinDate)
    {
        var coYohoMst = NoTrackingDataContext.YohoMsts.Where(y =>
           y.HpId == hpId &&
           y.YohoCd == yohoCd &&
           y.StartDate <= sinDate &&
           (y.EndDate >= sinDate || y.EndDate == 12341234))
            .OrderByDescending(y => y.StartDate)
            .FirstOrDefault();
        return coYohoMst != null ? new CoYohoMstModel(coYohoMst) : null;
    }

    public bool IsSingleDosageUnit(int hpId, string unitName)
    {
        return NoTrackingDataContext.SingleDoseMsts.Any(s => s.HpId == hpId && s.UnitName == unitName);
    }

    public CoDosageDrugModel GetDosageDrugModel(int hpId, string yjCode)
    {
        var dosageDrug = NoTrackingDataContext.DosageDrugs.FirstOrDefault(item => item.YjCd == yjCode);
        return new CoDosageDrugModel(dosageDrug ?? new());

    }

    /// <summary>
    /// 今月オンライン資格確認した保険
    /// </summary>
    /// <param name="ptId">患者番号</param>
    /// <param name="sinDate">診療日</param>
    /// <returns>今月オンライン資格確認した保険ID</returns>
    public int GetOnlineConfirmedHokenId(int hpId, long ptId, int sinDate)
    {
        DateTime dtSinDate = new DateTime(sinDate / 10000, sinDate / 100 % 100, sinDate % 100).ToUniversalTime();
        DateTime firstDate = new DateTime(sinDate / 10000, sinDate / 100 % 100, 1).ToUniversalTime();

        var ptHokenCheck = NoTrackingDataContext.PtHokenChecks.Where(p =>
          p.HpId == hpId &&
          p.PtID == ptId &&
          firstDate <= p.CheckDate &&
          p.CheckDate <= dtSinDate &&
          p.IsDeleted == DeleteStatus.None &&
          p.OnlineConfirmationId > 0
          )
          .OrderByDescending(p => p.CheckDate)
          .Select(p => p.HokenId)
          .FirstOrDefault();

        return ptHokenCheck;
    }

    /// <summary>
    /// 電子処方箋登録情報
    /// </summary>
    /// <param name="ptId">患者番号</param>
    /// <param name="raiinNo">来院番号</param>
    /// <returns>電子処方箋登録情報のリスト</returns>
    public List<CoEpsPrescription> FindEpsPrescription(int hpId, long ptId, long raiinNo)
    {
        var epsPrescriptions = NoTrackingDataContext.EpsPrescriptions.Where(e =>
            e.HpId == hpId &&
            e.PtId == ptId &&
            e.RaiinNo == raiinNo &&
            e.Status == 0).ToList();

        List<CoEpsPrescription> results = new();

        epsPrescriptions?.ForEach(entity =>
        {
            results.Add(new CoEpsPrescription(entity));
        });

        return results;
    }

    /// <summary>
    /// 電子処方箋の処方内容控え取得
    /// </summary>
    /// <param name="ptId">患者番号</param>
    /// <param name="raiinNo">来院番号</param>
    /// <returns>電子処方箋の処方内容控えのリスト</returns>
    public List<CoEpsReference> GetEpsReferences(int hpId, long ptId, long raiinNo)
    {
        var epsPrescriptions = NoTrackingDataContext.EpsPrescriptions.Where(e =>
            e.HpId == hpId &&
            e.PtId == ptId &&
            e.RaiinNo == raiinNo &&
            e.Status == 0 &&
            e.IssueType == 1);

        var epsReferences = NoTrackingDataContext.EpsReferences.Where(e => e.HpId == hpId);

        var joinQuery = from epsPrescription in epsPrescriptions
                        join epsReference in epsReferences on
                            new { epsPrescription.HpId, epsPrescription.PrescriptionId } equals
                            new { epsReference.HpId, epsReference.PrescriptionId }
                        orderby epsPrescription.RefileCount
                        select new
                        {
                            epsReference
                        };

        var entities = joinQuery.AsEnumerable().Select(
            data =>
                new CoEpsReference(data.epsReference)
            )
            .ToList();

        List<CoEpsReference> results = new List<CoEpsReference>();

        entities?.ForEach(entity =>
        {
            results.Add(new CoEpsReference(entity.EpsReference));
        });

        return results;
    }

    public bool IsExcludedIpnName(string ipnNameCd, int sinDate)
    {
        var ipnKasanExcludes = NoTrackingDataContext.ipnKasanExcludes.Where(i => i.IpnNameCd == ipnNameCd
            && i.StartDate <= sinDate
            && i.EndDate >= sinDate)
            .ToList();
        return ipnKasanExcludes.Count() > 0;
    }

    /// <summary>
    /// 一般名処方加算対象項目
    /// </summary>
    /// <param name="ipnNameCd">一般名コード</param>
    /// <param name="sinDate">診療日</param>
    /// <returns>true: 一般名処方加算対象項目</returns>
    public bool ExistsIpnKasanMst(string ipnNameCd, int sinDate)
    {
        var ipnKasanMst = NoTrackingDataContext.IpnKasanMsts.Where(i => i.IpnNameCd == ipnNameCd
            && i.StartDate <= sinDate
            && i.EndDate >= sinDate
            && i.IsDeleted == 0
            && (i.Kasan1 > 0 || i.Kasan2 > 0))
            .ToList();
        return ipnKasanMst.Count() > 0;
    }

    public void SaveRaiinData(CoRaiinInfModel raiinInf)
    {
        if (raiinInf != null)
        {
            if (raiinInf.IsUpdate)
            {
                raiinInf.ChangeUpdate(raiinInf.UserMst.UserId, DateTime.Now);
            }
        }

        TrackingDataContext.SaveChanges();

    }



    public EpsPrescription GetPrescriptionByStatus(int hpId, long ptId, long raiinNo, int sinDate, int status)
    {
        var epsPrescription = NoTrackingDataContext.EpsPrescriptions
            .FirstOrDefault(item => item.HpId == hpId && item.PtId == ptId && item.RaiinNo == raiinNo && item.SinDate == sinDate && item.Status == status);
        return epsPrescription ?? new();
    }

    public List<EpsPrescription> RegistrationEpsPrescriptions(int hpId, long ptId, long raiinNo)
    {
        var result = NoTrackingDataContext.EpsPrescriptions.Where(x => x.HpId == hpId &&
                                                                          x.PtId == ptId &&
                                                                          x.RaiinNo == raiinNo &&
                                                                          x.Status == 0).ToList();
        return result;
    }

    public List<TodayOdrInfModel> GetAllOdrInfs(int hpId, long ptId, long raiinNo, int sinDate)
    {
        List<TodayOdrInfModel> todayOdrInfModels = new List<TodayOdrInfModel>();
        var allHokenGrpOdr = GetOdrInfs(hpId, ptId, raiinNo, sinDate);
        foreach (var hokenGroup in allHokenGrpOdr)
        {
            foreach (var groupOdr in hokenGroup.GroupOdrInfModels)
            {
                foreach (var order in groupOdr.OdrInfModels)
                {
                    if (order.IsDeleted == 0)
                    {
                        todayOdrInfModels.Add(order);
                    }
                }
            }
        }

        return todayOdrInfModels;
    }

    private List<TodayHokenOdrInfModel> GetOdrInfs(int hpId, long PtId, long raiinNo, int sinDate)
    {
        var odrInfListQuery = NoTrackingDataContext.OdrInfs.Where(odr => odr.HpId == hpId && odr.PtId == PtId && odr.RaiinNo == raiinNo && odr.SinDate == sinDate && odr.OdrKouiKbn != 10 && odr.IsDeleted == 0)
                                                           .OrderBy(odr => odr.OdrKouiKbn)
                                                           .ThenBy(odr => odr.RpNo)
                                                           .ThenBy(odr => odr.RpEdaNo)
                                                           .ThenBy(odr => odr.SortNo);

        List<OdrInf> allOdrInfs = odrInfListQuery.ToList();

        var odrInfDetailQuery = NoTrackingDataContext.OdrInfDetails.Where(odrDetail => odrDetail.HpId == hpId &&
                                                                                       odrDetail.PtId == PtId &&
                                                                                       odrDetail.RaiinNo == raiinNo &&
                                                                                       odrDetail.SinDate == sinDate &&
                                                                                       odrDetail.ItemCd != ItemCdConst.JikanKihon &&
                                                                                       odrDetail.ItemCd != ItemCdConst.SyosaiKihon)
                                                                   .OrderBy(odrDetail => odrDetail.RpNo)
                                                                   .ThenBy(odrDetail => odrDetail.RpEdaNo)
                                                                   .ThenBy(odrDetail => odrDetail.RowNo);

        var tenMstQuery = NoTrackingDataContext.TenMsts.Where(e => e.HpId == hpId && e.StartDate <= sinDate && e.EndDate >= sinDate)
                                                       .OrderByDescending(t => t.StartDate);

        var kensaMstQuery = NoTrackingDataContext.KensaMsts.Where(p => p.HpId == hpId && p.IsDelete == DeleteTypes.None);

        var ipnKasanExcludeQuery = NoTrackingDataContext.ipnKasanExcludes.Where(u => u.StartDate <= sinDate && u.EndDate >= sinDate);

        var ipnKasanExcludeItemQuery = NoTrackingDataContext.ipnKasanExcludeItems.Where(u => u.StartDate <= sinDate && u.EndDate >= sinDate);

        var ipnMinYakkaMstQuery = NoTrackingDataContext.IpnMinYakkaMsts.Where(e => e.StartDate <= sinDate && e.EndDate >= sinDate && e.IsDeleted == 0)
                                                                       .OrderByDescending(e => e.StartDate);

        var detailJoinTenMstQueryRaw = from odrDetail in odrInfDetailQuery
                                       join odrInf in odrInfListQuery on new { odrDetail.RpNo, odrDetail.RpEdaNo } equals new { odrInf.RpNo, odrInf.RpEdaNo }
                                       join tenMst in tenMstQuery on odrDetail.ItemCd equals tenMst.ItemCd into tenMstList
                                       select new
                                       {
                                           OdrDetail = odrDetail,
                                           TenMstList = tenMstList
                                       };

        var detailJoinTenMstQuery = detailJoinTenMstQueryRaw.AsEnumerable()
                                                            .Select(x => new
                                                            {
                                                                OdrDetail = x.OdrDetail,
                                                                TenMst = x.TenMstList.FirstOrDefault()
                                                            });

        var detailWithTenMstQuery = from odrDetail in detailJoinTenMstQuery
                                    orderby odrDetail.OdrDetail.RpNo,
                                            odrDetail.OdrDetail.RpEdaNo,
                                            odrDetail.OdrDetail.RowNo
                                    join ipnKensa in ipnKasanExcludeQuery
                                        on odrDetail.OdrDetail.IpnCd equals ipnKensa.IpnNameCd into ipnKasanExcludeList
                                    join ipnKensaItem in ipnKasanExcludeItemQuery
                                        on odrDetail.OdrDetail.ItemCd equals ipnKensaItem.ItemCd into ipnKasanExcludeItemList
                                    join ipnMinYakka in ipnMinYakkaMstQuery
                                        on odrDetail.OdrDetail.IpnCd equals ipnMinYakka.IpnNameCd into ipnMinYakkaList
                                    select new
                                    {
                                        OdrDetail = odrDetail.OdrDetail,
                                        KensaItemCd = odrDetail.TenMst == null ? "" : odrDetail.TenMst.KensaItemCd,
                                        Ten = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.Ten,
                                        HandanGrpKbn = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.HandanGrpKbn,
                                        MasterSbt = odrDetail.TenMst == null ? "" : odrDetail.TenMst.MasterSbt,
                                        CmtCol1 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtCol1,
                                        CmtCol2 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtCol2,
                                        CmtCol3 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtCol3,
                                        CmtCol4 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtCol4,
                                        CmtColKeta1 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtColKeta1,
                                        CmtColKeta2 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtColKeta2,
                                        CmtColKeta3 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtColKeta3,
                                        CmtColKeta4 = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.CmtColKeta4,
                                        IsGetYakka = ipnKasanExcludeList.FirstOrDefault() == null && ipnKasanExcludeItemList.FirstOrDefault() == null,
                                        Yakka = ipnMinYakkaList.FirstOrDefault(),
                                        IsDeletedTenMst = odrDetail.TenMst == null ? 0 : odrDetail.TenMst.IsDeleted,
                                        SanteiItemCd = odrDetail.TenMst == null ? "" : odrDetail.TenMst.SanteiItemCd,
                                    };

        var detailWithKensaMstQuery = from detailWithTenMst in detailWithTenMstQuery
                                      join kensaMst in kensaMstQuery on detailWithTenMst.KensaItemCd equals kensaMst.KensaItemCd into kensaMstList
                                      from kensaMst in kensaMstList.DefaultIfEmpty()
                                      orderby detailWithTenMst.OdrDetail.RpNo,
                                              detailWithTenMst.OdrDetail.RpEdaNo,
                                              detailWithTenMst.OdrDetail.RowNo
                                      select new
                                      {
                                          OdrDetail = detailWithTenMst.OdrDetail,
                                          Ten = detailWithTenMst.Ten,
                                          HandanGrpKbn = detailWithTenMst.HandanGrpKbn,
                                          MasterSbt = detailWithTenMst.MasterSbt,
                                          CmtCol1 = detailWithTenMst.CmtCol1,
                                          CmtCol2 = detailWithTenMst.CmtCol2,
                                          CmtCol3 = detailWithTenMst.CmtCol3,
                                          CmtCol4 = detailWithTenMst.CmtCol4,
                                          CmtColKeta1 = detailWithTenMst.CmtColKeta1,
                                          CmtColKeta2 = detailWithTenMst.CmtColKeta2,
                                          CmtColKeta3 = detailWithTenMst.CmtColKeta3,
                                          CmtColKeta4 = detailWithTenMst.CmtColKeta4,
                                          IsGetYakka = detailWithTenMst.IsGetYakka,
                                          Yakka = detailWithTenMst.Yakka,
                                          KensaMst = kensaMst,
                                          IsDeletedTenMst = detailWithTenMst.IsDeletedTenMst,
                                          SanteiItemCd = detailWithTenMst.SanteiItemCd
                                      };

        List<TodayOdrInfDetailModel> allOdrInfDetails = detailWithKensaMstQuery.AsEnumerable()
                                                                               .Select(e => new TodayOdrInfDetailModel(e.OdrDetail)
                                                                               {
                                                                                   KensaMstModel = e.KensaMst == null ? new KensaMstModel() : new KensaMstModel(
                                                                                       e.KensaMst.KensaItemCd,
                                                                                       e.KensaMst.KensaItemSeqNo,
                                                                                       e.KensaMst.CenterCd ?? string.Empty,
                                                                                       e.KensaMst.KensaName ?? string.Empty,
                                                                                       e.KensaMst.KensaKana ?? string.Empty,
                                                                                       e.KensaMst.Unit ?? string.Empty,
                                                                                       e.KensaMst.MaterialCd,
                                                                                       e.KensaMst.ContainerCd,
                                                                                       e.KensaMst.MaleStd ?? string.Empty,
                                                                                       e.KensaMst.MaleStdLow ?? string.Empty,
                                                                                       e.KensaMst.MaleStdHigh ?? string.Empty,
                                                                                       e.KensaMst.FemaleStd ?? string.Empty,
                                                                                       e.KensaMst.FemaleStdLow ?? string.Empty,
                                                                                       e.KensaMst.FemaleStdHigh ?? string.Empty,
                                                                                       e.KensaMst.Formula ?? string.Empty,
                                                                                       e.KensaMst.OyaItemCd ?? string.Empty,
                                                                                       e.KensaMst.OyaItemSeqNo,
                                                                                       e.KensaMst.SortNo,
                                                                                       e.KensaMst.CenterItemCd1 ?? string.Empty,
                                                                                       e.KensaMst.CenterItemCd2 ?? string.Empty,
                                                                                       e.KensaMst.IsDelete
                                                                                   ),
                                                                                   Ten = e.Ten,
                                                                                   HandanGrpKbn = e.HandanGrpKbn,
                                                                                   MasterSbt = e.MasterSbt,
                                                                                   CmtCol1 = e.CmtCol1,
                                                                                   CmtCol2 = e.CmtCol2,
                                                                                   CmtCol3 = e.CmtCol3,
                                                                                   CmtCol4 = e.CmtCol4,
                                                                                   CmtColKeta1 = e.CmtColKeta1,
                                                                                   CmtColKeta2 = e.CmtColKeta2,
                                                                                   CmtColKeta3 = e.CmtColKeta3,
                                                                                   CmtColKeta4 = e.CmtColKeta4,
                                                                                   IsGetPriceInYakka = e.IsGetYakka,
                                                                                   IpnMinYakkaMstModel = e.Yakka == null ? new() : new IpnMinYakkaMstModel(
                                                                                       e.Yakka.IpnNameCd,
                                                                                       e.Yakka.StartDate,
                                                                                       e.Yakka.EndDate,
                                                                                       e.Yakka.Yakka,
                                                                                       e.Yakka.SeqNo,
                                                                                       e.Yakka.IsDeleted
                                                                                   ),
                                                                                   IsDeletedTenMst = e.IsDeletedTenMst,
                                                                                   SanteiItemCd = e.SanteiItemCd
                                                                               })
                                                                               .ToList();


        // Find By Hoken
        var hokenOdrInfs = allOdrInfs.GroupBy(odr => odr.HokenPid)
                                     .Select(grp => grp.FirstOrDefault())
                                     .ToList();
        List<TodayHokenOdrInfModel> result = new List<TodayHokenOdrInfModel>();

        foreach (OdrInf hokenOdrInf in hokenOdrInfs)
        {
            // Find By Group
            List<TodayGroupOdrInfModel> groupOdrInfModels = new List<TodayGroupOdrInfModel>();
            var groupOdrInfs = allOdrInfs.Where(odr => odr.HokenPid == hokenOdrInf.HokenPid)
                .GroupBy(odr => new
                {
                    odr.HokenPid,
                    odr.GroupKoui,
                    odr.InoutKbn,
                    odr.SyohoSbt,
                    odr.SikyuKbn,
                    odr.TosekiKbn,
                    odr.SanteiKbn
                })
                .Select(grp => grp.FirstOrDefault())
                .ToList();

            foreach (OdrInf groupOdrInf in groupOdrInfs)
            {
                // Find By RP
                List<TodayOdrInfModel> rpOdrInfModels = new List<TodayOdrInfModel>();
                var rpOdrInfs = allOdrInfs.Where(odrInf => odrInf.HokenPid == hokenOdrInf.HokenPid
                                                        && odrInf.GroupKoui == groupOdrInf.GroupKoui
                                                        && odrInf.InoutKbn == groupOdrInf.InoutKbn
                                                        && odrInf.SyohoSbt == groupOdrInf.SyohoSbt
                                                        && odrInf.SikyuKbn == groupOdrInf.SikyuKbn
                                                        && odrInf.TosekiKbn == groupOdrInf.TosekiKbn
                                                        && odrInf.SanteiKbn == groupOdrInf.SanteiKbn)
                                          .ToList();

                foreach (OdrInf rpOdrInf in rpOdrInfs)
                {
                    // Find OdrInfDetail
                    var odrInfDetails = allOdrInfDetails
                        .Where(detail => detail.RpNo == rpOdrInf.RpNo && detail.RpEdaNo == rpOdrInf.RpEdaNo)
                        .ToList(); ;
                    rpOdrInfModels.Add(new TodayOdrInfModel(rpOdrInf, odrInfDetails));
                }

                TodayGroupOdrInfModel groupOdrInfModel = new TodayGroupOdrInfModel(rpOdrInfModels);
                groupOdrInfModels.Add(groupOdrInfModel);
            }

            TodayHokenOdrInfModel todayHokenOdrInfModel = new TodayHokenOdrInfModel(groupOdrInfModels);

            result.Add(todayHokenOdrInfModel);
        }

        return result;
    }

    public List<CoPtHokenInfModel> FindPtHokenPatternList(int hpId, long ptId, int sinDay, bool isGetDeleted = false)
    {
        List<CoPtHokenInfModel> ptHokenPatternList = new List<CoPtHokenInfModel>();

        int dateTimeNow = CIUtil.DateTimeToInt(DateTime.Now);
        HpInf hpInf = NoTrackingDataContext.HpInfs.Where(x => x.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault(p => p.StartDate <= dateTimeNow) ?? new();
        if (hpInf == null)
        {
            hpInf = NoTrackingDataContext.HpInfs.Where(p => p.HpId == hpId).OrderByDescending(p => p.StartDate).FirstOrDefault() ?? new();
        }
        // PtInf
        PtInf ptInf = NoTrackingDataContext.PtInfs.Where(pt => pt.HpId == hpId && pt.PtId == ptId && pt.IsDelete == 0)
                                                  .FirstOrDefault() ?? new();

        if (ptInf == null) return ptHokenPatternList;

        var listPtHokenPattern = NoTrackingDataContext.PtHokenPatterns.Where(pattern => pattern.HpId == hpId &&
                                                                                        pattern.PtId == ptId &&
                                                                                        (pattern.IsDeleted == 0 || isGetDeleted))
                                                                      .ToList();

        if (listPtHokenPattern == null || listPtHokenPattern.Count <= 0) return ptHokenPatternList;

        var ptHokenInfRepos = NoTrackingDataContext.PtHokenInfs.Where(hoken => hoken.HpId == hpId &&
                                                                               hoken.PtId == ptId &&
                                                                               (hoken.IsDeleted == 0 || isGetDeleted));

        var predicateHokenInf = CreatePtHokenInfExpression(listPtHokenPattern.Select(item => item.HokenId).ToList());

        List<PtHokenInf> listPtHokenInf = new List<PtHokenInf>();
        if (predicateHokenInf != null)
        {
            listPtHokenInf = ptHokenInfRepos.Where(predicateHokenInf).ToList();
        }

        var ptKohiRepos = NoTrackingDataContext.PtKohis.Where(kohi => kohi.HpId == hpId
                                                                  && kohi.PtId == ptId
                                                                  && (kohi.IsDeleted == 0 || isGetDeleted));

        var predicateKohi = CreatePtKohiExpression(listPtHokenPattern);

        List<PtKohi> listPtKohi = new List<PtKohi>();
        if (predicateKohi != null)
        {
            listPtKohi = ptKohiRepos.Where(predicateKohi).ToList();
        }

        var predicateHokenMst = CreateHokenMstExpression(listPtHokenInf, listPtKohi);

        if (predicateHokenMst == null) return ptHokenPatternList;

        var hokenMstListRepo = NoTrackingDataContext.HokenMsts
            .Where(
                entity => entity.HpId == hpId
                          && (entity.PrefNo == hpInf.PrefNo
                              || entity.PrefNo == 0
                              || entity.IsOtherPrefValid == 1))
            .OrderBy(e => e.HpId)
            .ThenBy(e => e.HokenNo)
            .ThenBy(e => e.HokenEdaNo)
            .ThenByDescending(e => e.StartDate)
            .ThenBy(e => e.HokenSbtKbn)
            .ThenBy(e => e.SortNo);

        var hokenMstList = hokenMstListRepo.Where(predicateHokenMst).ToList();

        foreach (var ptHokenPattern in listPtHokenPattern)
        {
            var ptHokenInf = listPtHokenInf.FirstOrDefault(hk => hk.HokenId == ptHokenPattern.HokenId);
            var ptKohi1 = listPtKohi.FirstOrDefault(kohi => kohi.HokenId == ptHokenPattern.Kohi1Id);
            var ptKohi2 = listPtKohi.FirstOrDefault(kohi => kohi.HokenId == ptHokenPattern.Kohi2Id);
            var ptKohi3 = listPtKohi.FirstOrDefault(kohi => kohi.HokenId == ptHokenPattern.Kohi3Id);
            var ptKohi4 = listPtKohi.FirstOrDefault(kohi => kohi.HokenId == ptHokenPattern.Kohi4Id);

            ptHokenPatternList.Add(new CoPtHokenInfModel(sinDay,
                                                       ptInf.Birthday,
                                                       ptHokenPattern,
                                                       ptHokenInf == null ? new() : CreatePtHokenInfModel(ptHokenInf, hokenMstList.Where(item => item.HokenNo == ptHokenInf.HokenNo && item.HokenEdaNo == ptHokenInf.HokenEdaNo).ToList(), new(), sinDay),
                                                       ptKohi1 == null ? new() : CreatePtKohiModel(ptKohi1, hokenMstList.Where(item => item.HokenNo == ptKohi1.HokenNo && item.HokenEdaNo == ptKohi1.HokenEdaNo).ToList(), new(), sinDay),
                                                       ptKohi2 == null ? new() : CreatePtKohiModel(ptKohi2, hokenMstList.Where(item => item.HokenNo == ptKohi2.HokenNo && item.HokenEdaNo == ptKohi2.HokenEdaNo).ToList(), new(), sinDay),
                                                       ptKohi3 == null ? new() : CreatePtKohiModel(ptKohi3, hokenMstList.Where(item => item.HokenNo == ptKohi3.HokenNo && item.HokenEdaNo == ptKohi3.HokenEdaNo).ToList(), new(), sinDay),
                                                       ptKohi4 == null ? new() : CreatePtKohiModel(ptKohi4, hokenMstList.Where(item => item.HokenNo == ptKohi4.HokenNo && item.HokenEdaNo == ptKohi4.HokenEdaNo).ToList(), new(), sinDay)
            ));
        }

        return ptHokenPatternList;
    }

    public PtKohiModel CreatePtKohiModel(PtKohi eKohiInf, List<HokenMst> hokenMstLists, List<PtHokenCheckModel> ptHokenCheckModelList, int sinDay)
    {
        PtKohiModel kohiInfModel = null;
        if (eKohiInf != null)
        {
            HokenMst hokenMst;
            var hokMstMapped = hokenMstLists
               .FindAll(hk =>
               hk.HokenNo == eKohiInf.HokenNo
               && hk.HokenEdaNo == eKohiInf.HokenEdaNo)
               .OrderByDescending(hk => hk.StartDate);
            if (hokMstMapped.Count() > 1)
            {
                // pick one newest within startDate <= sinday
                var firstMapped = hokMstMapped.FirstOrDefault(hokMst => hokMst.StartDate <= sinDay);
                if (firstMapped == null)
                {
                    // does not exist any hoken master with startDate <= sinday, pick lastest hoken mst (with min start date)
                    // pick last cause by all hoken master is order by start date descending
                    hokenMst = hokMstMapped.LastOrDefault();
                }
                else
                {
                    hokenMst = firstMapped;
                }
            }
            else
            {
                // have just one hoken mst with HokenNo and HokenEdaNo
                hokenMst = hokMstMapped.FirstOrDefault();
            }

            CoHokenMstModel coHokenMstModel = new();
            if (hokenMst != null)
            {
                coHokenMstModel = new CoHokenMstModel(hokenMst);
            }
            kohiInfModel = new PtKohiModel(sinDay, eKohiInf, coHokenMstModel, ptHokenCheckModelList);
        }

        return kohiInfModel ?? new();
    }

    public PtHokenInfModel CreatePtHokenInfModel(PtHokenInf ePtHokenInf, List<HokenMst> hokenMstLists, List<PtHokenCheckModel> ptHokenCheckModelList, int sinDay)
    {
        PtHokenInfModel hokenInfModel = new();
        if (ePtHokenInf != null)
        {
            HokenMst hokenMst;
            var hokMstMapped = hokenMstLists
               .FindAll(hk =>
               hk.HokenNo == ePtHokenInf.HokenNo
               && hk.HokenEdaNo == ePtHokenInf.HokenEdaNo)
               .OrderByDescending(hk => hk.StartDate);

            if (hokMstMapped.Count() > 1)
            {
                // pick one newest within startDate <= sinday
                var firstMapped = hokMstMapped.FirstOrDefault(hokMst => hokMst.StartDate <= sinDay);
                if (firstMapped == null)
                {
                    // does not exist any hoken master with startDate <= sinday, pick lastest hoken mst (with min start date)
                    // pick last cause by all hoken master is order by start date descending
                    hokenMst = hokMstMapped.LastOrDefault() ?? new();
                }
                else
                {
                    hokenMst = firstMapped;
                }
            }
            else
            {
                // have just one hoken mst with HokenNo and HokenEdaNo
                hokenMst = hokMstMapped.FirstOrDefault() ?? new();
            }
            CoHokenMstModel hokenMstModel = new();
            if (hokenMst != null)
            {
                hokenMstModel = new CoHokenMstModel(hokenMst);
            }
            hokenInfModel = new PtHokenInfModel(sinDay, ePtHokenInf, hokenMstModel, ptHokenCheckModelList);
        }

        return hokenInfModel ?? new();
    }

    private Expression<Func<HokenMst, bool>> CreateHokenMstExpression(List<PtHokenInf> listPtHokenInf,
            List<PtKohi> listPtKohi)
    {
        var param = Expression.Parameter(typeof(HokenMst));
        Expression expression = null;

        CreateHokenMstExpression(listPtHokenInf, ref expression, ref param);
        CreateHokenMstExpression(listPtKohi, ref expression, ref param);

        return expression != null
            ? Expression.Lambda<Func<HokenMst, bool>>(body: expression, parameters: param)
            : null;
    }

    private void CreateHokenMstExpression(List<PtKohi> listPtKohi, ref Expression expression, ref ParameterExpression param)
    {
        if (listPtKohi != null && listPtKohi.Count > 0)
        {
            foreach (var item in listPtKohi)
            {
                if (item != null)
                {
                    var valHokenNo = Expression.Constant(item.HokenNo);
                    var memberHokenNo = Expression.Property(param, nameof(HokenMst.HokenNo));

                    var valHokenEdaNo = Expression.Constant(item.HokenEdaNo);
                    var memberHokenEdaNo = Expression.Property(param, nameof(HokenMst.HokenEdaNo));

                    var expressionKohi = Expression.And(Expression.Equal(valHokenNo, memberHokenNo),
                        Expression.Equal(valHokenEdaNo, memberHokenEdaNo));

                    expression = expression == null ? expressionKohi : Expression.Or(expression, expressionKohi);
                }
            }
        }
    }

    private void CreateHokenMstExpression(List<PtHokenInf> listPtHokenInf, ref Expression expression, ref ParameterExpression param)
    {
        if (listPtHokenInf != null)
        {
            foreach (var item in listPtHokenInf)
            {
                if (item != null)
                {
                    var valHokenNo = Expression.Constant(item.HokenNo);
                    var memberHokenNo = Expression.Property(param, nameof(HokenMst.HokenNo));

                    var valHokenEdaNo = Expression.Constant(item.HokenEdaNo);
                    var memberHokenEdaNo = Expression.Property(param, nameof(HokenMst.HokenEdaNo));

                    var expressionHoken = Expression.And(Expression.Equal(valHokenNo, memberHokenNo),
                        Expression.Equal(valHokenEdaNo, memberHokenEdaNo));

                    expression = expression == null ? expressionHoken : Expression.Or(expression, expressionHoken);
                }
            }
        }
    }

    private Expression<Func<PtKohi, bool>> CreatePtKohiExpression(List<PtHokenPattern> listPtHokenPattern)
    {
        var param = Expression.Parameter(typeof(PtKohi));
        Expression expression = null;

        if (listPtHokenPattern != null && listPtHokenPattern.Count > 0)
        {
            foreach (var pattern in listPtHokenPattern)
            {
                if (pattern.PtId > 0)
                {
                    CreatePtKohiExpression(new List<int>()
                        {
                            pattern.Kohi1Id,
                            pattern.Kohi2Id,
                            pattern.Kohi3Id,
                            pattern.Kohi4Id
                        }, ref expression, ref param);
                }
            }
        }

        return expression != null
            ? Expression.Lambda<Func<PtKohi, bool>>(body: expression, parameters: param)
            : null;
    }

    private void CreatePtKohiExpression(List<int> listKohiId, ref Expression expression, ref ParameterExpression param)
    {
        if (listKohiId != null && listKohiId.Count > 0)
        {
            foreach (var kohiId in listKohiId)
            {
                if (kohiId > 0)
                {
                    var valHokenId = Expression.Constant(kohiId);
                    var memberHokenId = Expression.Property(param, nameof(PtKohi.HokenId));
                    Expression expressionHokenId = Expression.Equal(valHokenId, memberHokenId);

                    expression = expression == null ? expressionHokenId : Expression.Or(expression, expressionHokenId);
                }
            }
        }
    }

    private Expression<Func<PtHokenInf, bool>> CreatePtHokenInfExpression(List<int> listHokenId)
    {
        var param = Expression.Parameter(typeof(PtHokenInf));
        Expression expression = null;

        if (listHokenId != null && listHokenId.Count > 0)
        {
            foreach (var hokenId in listHokenId)
            {
                if (hokenId > 0)
                {
                    var valHokenId = Expression.Constant(hokenId);
                    var memberHokenId = Expression.Property(param, nameof(PtHokenInf.HokenId));
                    Expression expressionHokenId = Expression.Equal(valHokenId, memberHokenId);

                    expression = expression == null ? expressionHokenId : Expression.Or(expression, expressionHokenId);
                }
            }
        }

        return expression != null
            ? Expression.Lambda<Func<PtHokenInf, bool>>(body: expression, parameters: param)
            : null;
    }


    public List<string> GetIpnKasanMst(int sinDate, List<string> ipnCdList)
    {
        return NoTrackingDataContext.IpnKasanMsts.Where(x => x.StartDate <= sinDate &&
                                                             x.EndDate >= sinDate &&
                                                             ipnCdList.Contains(x.IpnNameCd))
            .Select(x => x.IpnNameCd)
            .ToList();
    }

    public List<string> GetIpnKasanExclude(int sinDate, List<string> ipnCdList)
    {
        return NoTrackingDataContext.ipnKasanExcludes.Where(x => x.StartDate <= sinDate &&
                                                                 x.EndDate >= sinDate &&
                                                                 ipnCdList.Contains(x.IpnNameCd))
                                                     .Select(x => x.IpnNameCd)
                                                     .ToList();
    }

    public bool IsExistEpsRefs(int hpId, string prescriptionId)
    {
        return NoTrackingDataContext.EpsReferences.FirstOrDefault(x => x.HpId == hpId && x.PrescriptionId == prescriptionId) != null;
    }

    /// <summary>
    /// 有効な被保険者番号を確認できません。
    /// </summary>
    /// <param name="odrInf"></param>
    /// <param name="hokenPattern"></param>
    /// <returns></returns>
    public bool CheckHokenExpired(int hpId, int hokenPid, List<CoPtHokenInfModel> listHokenPattern, int sinDate, int selectPatternCheck)
    {
        var hokenPattern = listHokenPattern.FirstOrDefault(p => p.HokenPid == hokenPid);
        if (hokenPattern == null)
        {
            return true;
        }
        if (hokenPattern.HokenInf == null || ((hokenPattern.HokenInf.HokenKbn == 1 || hokenPattern.HokenInf.HokenKbn == 2) && (hokenPattern.HokenInf.StartDate > sinDate || hokenPattern.HokenInf.EndDate < sinDate)))
        {
            return true;
        }
        var firstDigit = CIUtil.Copy(hokenPattern.HokenSbtCd.AsString(), 1, 1).AsInteger();
        if (selectPatternCheck == 1)
        {
            if (firstDigit == 5)
            {
                if (hokenPattern.Kohi1Inf != null && hokenPattern.Kohi1Inf.IsDeleted == 0 && CIUtil.Copy(hokenPattern.Kohi1Inf.FutansyaNo, 1, 2) != "12")
                {
                    return true;
                }
                if (hokenPattern.Kohi2Inf != null && hokenPattern.Kohi2Inf.IsDeleted == 0 && CIUtil.Copy(hokenPattern.Kohi2Inf.FutansyaNo, 1, 2) != "12")
                {
                    return true;
                }
                if (hokenPattern.Kohi3Inf != null && hokenPattern.Kohi3Inf.IsDeleted == 0 && CIUtil.Copy(hokenPattern.Kohi3Inf.FutansyaNo, 1, 2) != "12")
                {
                    return true;
                }
                if (hokenPattern.Kohi4Inf != null && hokenPattern.Kohi4Inf.IsDeleted == 0 && CIUtil.Copy(hokenPattern.Kohi4Inf.FutansyaNo, 1, 2) != "12")
                {
                    return true;
                }
            }
        }
        else if (firstDigit == 5)
        {
            return true;
        }
        return false;
    }

    public bool CheckHokenNotCoverDrug(TodayOdrInfModel odrInf)
    {
        var listDetail = odrInf.OdrInfDetailModelsIgnoreEmpty;
        foreach (var odrInfDetail in listDetail)
        {
            int firstSanteiItemCd = CIUtil.Copy(odrInfDetail.SanteiItemCd, 1, 1).AsInteger();
            if (odrInfDetail.MasterSbt == "Y" && odrInfDetail.YohoKbn == 0 &&
                (firstSanteiItemCd != 6 ||
                odrInfDetail.SanteiItemCd?.Length != 9 || // fixbug 30233
                odrInfDetail.SanteiItemCd == "666660000")) // fixbug 30236
            {
                return true;
            }
        }
        return false;
    }

    public bool CheckDetailBunkatu(TodayOdrInfModel odrInf)
    {
        var listDetail = odrInf.OdrInfDetailModelsIgnoreEmpty;
        foreach (var odrInfDetail in listDetail)
        {
            if (odrInfDetail.ItemCd == ItemCdConst.Con_TouyakuOrSiBunkatu)
            {
                return true;
            }
        }
        return false;
    }

    public bool CheckDetailRefill(TodayOdrInfModel odrInf)
    {
        var listDetail = odrInf.OdrInfDetailModelsIgnoreEmpty;
        foreach (var odrInfDetail in listDetail)
        {
            if (odrInfDetail.ItemCd == ItemCdConst.Con_Refill)
            {
                return true;
            }
        }
        return false;
    }

    public bool CheckMedicalMaterialsNotCovered(TodayOdrInfModel odrInf)
    {
        var listDetail = odrInf.OdrInfDetailModelsIgnoreEmpty;
        foreach (var odrInfDetail in listDetail)
        {
            int firstSanteiItemCd = CIUtil.Copy(odrInfDetail.SanteiItemCd, 1, 1).AsInteger();
            if (odrInfDetail.MasterSbt == "T" &&
                (firstSanteiItemCd != 7 ||
                odrInfDetail.SanteiItemCd?.Length != 9 ||
                odrInfDetail.SanteiItemCd == "777770000"))
            {
                return true;
            }
        }
        return false;
    }

    public bool CheckContainsGenericNamesNotIncluded(TodayOdrInfModel odrInf, List<string> listIpnKasanMst, List<string> listIpnKasanExclude)
    {
        var listDetail = odrInf.OdrInfDetailModelsIgnoreEmpty;
        foreach (var odrInfDetail in listDetail)
        {
            if (odrInfDetail.OdrInfDetail.SyohoKbn == 3 &&
                (string.IsNullOrEmpty(odrInfDetail.IpnName) ||
                !listIpnKasanMst.Contains(odrInfDetail.IpnCd) ||
                listIpnKasanExclude.Contains(odrInfDetail.IpnCd)))
            {
                return true;
            }
        }
        return false;
    }

    public bool CheckHokenNotHealthInsurance(CoPtHokenInfModel hokenPattern)
    {
        if (hokenPattern == null)
        {
            return true;
        }
        return hokenPattern.HokenKbn != 1 && hokenPattern.HokenKbn != 2;
    }

    public bool GetEpsDispensingByResultType(int hpId, long ptId, long raiinNo, int sinDate, List<int> listResultType)
    {
        var epsPrescription = NoTrackingDataContext.EpsPrescriptions.Where(item => item.HpId == hpId && item.PtId == ptId && item.RaiinNo == raiinNo && item.SinDate == sinDate);
        var epsDispensing = NoTrackingDataContext.EpsDispensings.Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == 0 && listResultType.Contains(x.ResultType));
        var query = from prescription in epsPrescription
                    join dispensing in epsDispensing on prescription.PrescriptionId equals dispensing.PrescriptionId
                    select new
                    {
                        ResultType = dispensing.ResultType,
                        UpdateDate = prescription.UpdateDate
                    };
        return query.FirstOrDefault() != null;
    }

    public bool CheckManyHokenUsingForRp(List<CoPtHokenInfModel> listHokenPatternModel, List<TodayOdrInfModel> todayOdrInfModels)
    {
        var listPtHokenId = listHokenPatternModel.Where(x => (x.HokenInf?.HokenKbn == 1 || x.HokenInf?.HokenKbn == 2) && x.HokenInf?.Houbetu != "0")
                                                 .Select(x => x.HokenId).Distinct().ToList();
        var listHokenUsedCount = todayOdrInfModels.Where(x => listPtHokenId.Contains(x.HokenId)).Select(x => x.HokenId).Distinct().Count();
        return listHokenUsedCount > 1;
    }

    public List<EpsPrescription> GetEpsPrescription(int hpId, long ptId, long raiinNo)
    {
        return NoTrackingDataContext.EpsPrescriptions.Where(x => x.HpId == hpId &&
                                                                 x.PtId == ptId &&
                                                                 x.RaiinNo == raiinNo &&
                                                                 x.Status == 0).ToList();
    }
}
