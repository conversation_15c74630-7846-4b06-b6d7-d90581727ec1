﻿using Domain.Models.HpInf;
using Domain.Models.KensaIrai;
using Entity.Tenant;
using Helper.Constants;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Reporting.KensaHistory.Models;
using System.Linq.Dynamic.Core;
using static Domain.Models.KensaIrai.ListKensaInfDetailModel;

namespace Reporting.KensaHistory.DB
{
    public class CoKensaHistoryFinder : RepositoryBase, ICoKensaHistoryFinder
    {
        public CoKensaHistoryFinder(ITenantProvider tenantProvider) : base(tenantProvider)
        {
        }

        public void ReleaseResource()
        {
            DisposeDataContext();
        }

        public HpInfModel GetHpInf(int hpId, int sinDate)
        {
            var hpInf = NoTrackingDataContext.HpInfs.Where(item => item.HpId == hpId && item.StartDate < sinDate).OrderBy(x => x.StartDate).First();
            return hpInf != null ? new HpInfModel(hpId,
                                                    hpInf.StartDate,
                                                    hpInf.HpCd ?? string.Empty,
                                                    hpInf.RousaiHpCd ?? string.Empty,
                                                    hpInf.HpName ?? string.Empty,
                                                    hpInf.ReceHpName ?? string.Empty,
                                                    hpInf.KaisetuName ?? string.Empty,
                                                    hpInf.PostCd ?? string.Empty,
                                                    hpInf.PrefNo,
                                                    hpInf.Address1 ?? string.Empty,
                                                    hpInf.Address2 ?? string.Empty,
                                                    hpInf.Tel ?? string.Empty,
                                                    hpInf.FaxNo ?? string.Empty,
                                                    hpInf.OtherContacts ?? string.Empty
                                                ) : new HpInfModel();
        }

        public PtInf GetPtInf(int hpId, long ptId)
        {
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId);
            return ptInf;
        }

        private static (string, string) GetValueLowHigSdt(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return (string.Empty, string.Empty);
            }
            else
            {
                string[] values = input.Split("-");

                if (values.Length == 2)
                {
                    return (values[0], values[1]);
                }
                else
                {
                    return (string.Empty, string.Empty);
                }
            }
        }

        public (List<CoKensaResultMultiModel>, List<long>, List<string>) GetListKensaInfDetail(int hpId, int userId, long ptId, int setId, int startDate, int endDate, bool showAbnormalKbn, List<string> kensaItemCdList, string centerCd = "")
        {
            IQueryable<KensaInfDetail> kensaInfDetails;

            var userConf = NoTrackingDataContext.UserConfs.Where(x => x.UserId == userId && x.HpId == hpId && x.GrpCd == 1002);

            var kensaSetDetailById = NoTrackingDataContext.KensaSetDetails.Where(x => x.SetId == setId && x.HpId == hpId && x.IsDeleted == DeleteTypes.None).GroupBy(item => item.KensaItemCd)
               .Select(group => new
               {
                   KensaItemCd = group.Key,
                   SortNo = group.Min(item => item.SortNo)
               });

            bool SortIraiDateAsc = true;

            if (userConf.Where(x => x.GrpItemCd == 0).FirstOrDefault()?.Val == 1)
            {
                SortIraiDateAsc = false;
            }

            if (setId == 0)
            {
                kensaInfDetails = NoTrackingDataContext.KensaInfDetails
               .Where(x => x.HpId == hpId && x.PtId == ptId && x.IsDeleted == DeleteTypes.None && (!kensaItemCdList.Any() || kensaItemCdList.Contains(x.KensaItemCd)));
            }
            else
            {
                // Fllter data with KensaSet
                kensaInfDetails = (from t1 in NoTrackingDataContext.KensaInfDetails
                                   join t2 in kensaSetDetailById on t1.KensaItemCd equals t2.KensaItemCd
                                   where t1.HpId == hpId && t1.PtId == ptId && t1.IsDeleted == DeleteTypes.None && (kensaItemCdList.Count == 0 || kensaItemCdList.Contains(t1.KensaItemCd))
                                   select new
                                   {
                                       Result = t1
                                   }
                            ).Select(x => x.Result);
            }

            var kensInfs = NoTrackingDataContext.KensaInfs.Where(x => string.IsNullOrEmpty(centerCd) || x.CenterCd == centerCd);

            var internalData = (from t1 in kensaInfDetails
                                join t3 in kensInfs on new { t1.HpId, t1.PtId, t1.IraiCd } equals new { t3.HpId, t3.PtId, t3.IraiCd }
                                join t4 in NoTrackingDataContext.PtInfs on new { t1.PtId, t1.HpId } equals new { t4.PtId, t4.HpId }
                                where t3.CenterCd == CommonConstants.InHospitalCenterCd
                                      && t3.IsDeleted == DeleteTypes.None
                                      && t4.IsDelete == DeleteTypes.None
                                join t2 in NoTrackingDataContext.KensaMsts
                                    on new { t1.KensaItemCd, t1.HpId } equals new { t2.KensaItemCd, t2.HpId }
                                join t5 in NoTrackingDataContext.KensaCmtMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None)
                                    on t1.CmtCd1 equals t5.CmtCd into leftJoinT5
                                from t5 in leftJoinT5.DefaultIfEmpty()
                                join t6 in NoTrackingDataContext.KensaCmtMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None)
                                    on t1.CmtCd2 equals t6.CmtCd into leftJoinT6
                                from t6 in leftJoinT6.DefaultIfEmpty()
                                where t2.IsDelete == DeleteTypes.None
                                where t2.KensaItemSeqNo == NoTrackingDataContext.KensaMsts
                                    .Where(m => m.HpId == t2.HpId && m.KensaItemCd == t2.KensaItemCd && m.IsDelete == DeleteTypes.None)
                                    .Select(m => m.KensaItemSeqNo).DefaultIfEmpty().Min()
                                where t5 == null || t5.CmtSeqNo == NoTrackingDataContext.KensaCmtMsts
                                    .Where(m => m.HpId == t2.HpId && m.CmtCd == t5.CmtCd && m.IsDeleted == DeleteTypes.None)
                                    .Select(m => m.CmtSeqNo).DefaultIfEmpty().Min()
                                where t6 == null || t6.CmtSeqNo == NoTrackingDataContext.KensaCmtMsts
                                    .Where(m => m.HpId == t2.HpId && m.CmtCd == t6.CmtCd && m.IsDeleted == DeleteTypes.None)
                                    .Select(m => m.CmtSeqNo).DefaultIfEmpty().Min()
                                select new ListKensaInfDetailItemModel(
                                    t1.PtId, t1.IraiCd, t1.RaiinNo, t3.IraiDate, t1.SeqNo, t1.SeqParentNo,
                                    t2.KensaName ?? string.Empty, t2.KensaKana ?? string.Empty, t2.SortNo,
                                    t1.KensaItemCd ?? string.Empty, t1.ResultVal ?? string.Empty,
                                    t1.ResultType ?? string.Empty, t1.AbnormalKbn ?? string.Empty,
                                    t1.CmtCd1 ?? string.Empty, t1.CmtCd2 ?? string.Empty,
                                    (t3.CenterCd == t5.CenterCd || string.IsNullOrEmpty(t5.CenterCd)) ? (t5.CMT ?? string.Empty) : "不明",
                                    (t3.CenterCd == t6.CenterCd || string.IsNullOrEmpty(t6.CenterCd)) ? (t6.CMT ?? string.Empty) : "不明",
                                    t2.MaleStd ?? string.Empty, t2.FemaleStd ?? string.Empty,
                                    t2.MaleStdLow ?? string.Empty, t2.FemaleStdLow ?? string.Empty,
                                    t2.MaleStdHigh ?? string.Empty, t2.FemaleStdHigh ?? string.Empty,
                                    t2.Unit ?? string.Empty, t3.Nyubi ?? string.Empty,
                                    t3.Yoketu ?? string.Empty, t3.Bilirubin ?? string.Empty,
                                    t3.SikyuKbn, t3.TosekiKbn, t3.InoutKbn, t3.Status,
                                    DeleteTypes.None, t1.SeqGroupNo, string.Empty, t3.KensaTime,
                                    t3.CenterCd ?? string.Empty
                                )).ToList();

            var externalData = (from t1 in kensaInfDetails
                                join t3 in kensInfs on new { t1.HpId, t1.PtId, t1.IraiCd } equals new { t3.HpId, t3.PtId, t3.IraiCd }
                                join t4 in NoTrackingDataContext.PtInfs on new { t1.PtId, t1.HpId } equals new { t4.PtId, t4.HpId }
                                where t3.CenterCd != CommonConstants.InHospitalCenterCd
                                      && t3.IsDeleted == DeleteTypes.None
                                      && t4.IsDelete == DeleteTypes.None
                                      && t1.IsDeleted == DeleteTypes.None
                                join t2 in NoTrackingDataContext.CommonCenterKensaMst
                                    on new { t3.CenterCd, t1.KensaItemCd } equals new { t2.CenterCd, t2.KensaItemCd } into leftJoinT2
                                from t2 in leftJoinT2.DefaultIfEmpty()
                                join t6 in NoTrackingDataContext.CommonKensaCenterMst
                                    on t3.CenterCd equals t6.CenterCd into leftJoinT6
                                from t6 in leftJoinT6.DefaultIfEmpty()
                                join t7 in NoTrackingDataContext.CommonCenterStdMst
                                    on new { t3.CenterCd, t1.KensaItemCd } equals new { t7.CenterCd, t7.KensaItemCd } into leftJoinT7
                                from t7 in leftJoinT7.DefaultIfEmpty()
                                select new ListKensaInfDetailItemModel(
                                    t1.PtId, t1.IraiCd, t1.RaiinNo, t3.IraiDate, t1.SeqNo, t1.SeqParentNo,
                                    t2 != null ? t2.KensaName ?? string.Empty : string.Empty, t2 != null ? t2.KensaKana ?? string.Empty : string.Empty, t2 != null ? t2.SortNo : 0,
                                    t1.KensaItemCd ?? string.Empty, t1.ResultVal ?? string.Empty,
                                    t1.ResultType ?? string.Empty, t1.AbnormalKbn ?? string.Empty,
                                    t1.CmtCd1 ?? string.Empty, t1.CmtCd2 ?? string.Empty,
                                    string.Empty, string.Empty,
                                    t7 != null ? t7.MealStd ?? string.Empty : string.Empty, t7 != null ? t7.FemelStd ?? string.Empty : string.Empty,
                                    t7 != null ? t7.MealStdLow ?? string.Empty : string.Empty, t7 != null ? t7.FemelStdLow ?? string.Empty : string.Empty,
                                    t7 != null ? t7.MealStdHigh ?? string.Empty : string.Empty, t7 != null ? t7.FemelStdHigh ?? string.Empty : string.Empty,
                                    t2 != null ? t2.Unit ?? string.Empty : string.Empty, t3.Nyubi ?? string.Empty,
                                    t3.Yoketu ?? string.Empty, t3.Bilirubin ?? string.Empty,
                                    t3.SikyuKbn, t3.TosekiKbn, t3.InoutKbn, t3.Status,
                                    DeleteTypes.None, t1.SeqGroupNo, string.Empty, t3.KensaTime,
                                    t3.CenterCd ?? string.Empty
                                )).ToList();

            IEnumerable<ListKensaInfDetailItemModel> data = internalData.Concat(externalData);

            var sortedData = SortIraiDateAsc
                ? data.OrderBy(x => x.IraiDate.ToString() + x.KensaTime)
                : data.OrderByDescending(x => x.IraiDate.ToString() + x.KensaTime);

            var kensaInfDetailCol = sortedData
                .Where(x => x.IraiDate >= startDate && x.IraiDate <= endDate)
                .GroupBy(x => new { x.IraiCd, x.IraiDate, x.Nyubi, x.Yoketu, x.Bilirubin, x.SikyuKbn, x.TosekiKbn, x.KensaTime, x.CenterCd })
                .Select((group, index) => new KensaInfDetailColModel(
                    group.Key.IraiCd,
                    group.Key.IraiDate,
                    group.Key.Nyubi,
                    group.Key.Yoketu,
                    group.Key.Bilirubin,
                    group.Key.SikyuKbn,
                    group.Key.TosekiKbn,
                    index,
                    group.Key.KensaTime,
                    group.Key.CenterCd
                ))
                .DistinctBy(x => (x.KensaTime, x.IraiCd, x.IraiDate))
                .ToList();


            var kensaIraiCdSet = new HashSet<long>(kensaInfDetailCol.Select(item => item.IraiCd));

            if (!showAbnormalKbn)
            {
                data = data.Where(x => kensaIraiCdSet.Contains(x.IraiCd) && x.IraiDate >= startDate && x.IraiDate <= endDate);
            }

            var kensaItemDuplicate = data.GroupBy(x => new { x.KensaItemCd, x.KensaName, x.Unit, x.MaleStd, x.FemaleStd, x.IraiCd }).SelectMany(group => group.Skip(1)).Select(x => x);

            var seqNos = new HashSet<long>(kensaItemDuplicate.Select(item => item.SeqNo));

            var kensaItemWithOutDuplicate = data.Where(x => seqNos.Contains(x.SeqNo)).GroupBy(x => new { x.KensaItemCd, x.SeqGroupNo })
                                                .Select(group =>
                                                {
                                                    var newItem = group.First();
                                                    newItem.SetRowSeqId(string.Join("-", group.Select(x => x.SeqNo)));
                                                    return newItem;
                                                })
                                                .ToList();

            var kensaItemCds = data.GroupBy(x => new { x.KensaItemCd, x.KensaName, x.Unit, x.MaleStd, x.FemaleStd, x.SeqNo, x.SeqGroupNo, x.CenterCd }).Select(x => new { x.Key.KensaItemCd, x.Key.KensaName, x.Key.Unit, x.Key.MaleStd, x.Key.FemaleStd, x.Key.SeqNo, x.Key.SeqGroupNo, x.Key.CenterCd });

            var kensaInfDetailData = new List<KensaInfDetailDataModel>();

            foreach (var kensaMstItem in kensaItemCds)
            {
                var dynamicArray = new List<ListKensaInfDetailItemModel>();

                foreach (var item in kensaInfDetailCol)
                {
                    var dynamicDataItem = data.Where(x => x.SeqGroupNo == kensaMstItem.SeqGroupNo && x.IraiCd == item.IraiCd && x.KensaItemCd == kensaMstItem.KensaItemCd && kensaMstItem.CenterCd == x.CenterCd).FirstOrDefault();

                    if (dynamicDataItem == null)
                    {
                        dynamicArray.Add(new ListKensaInfDetailItemModel(
                            ptId,
                            item.IraiCd
                        ));
                    }
                    else
                    {
                        dynamicArray.Add(dynamicDataItem);
                    }
                }

                var rowData = new KensaInfDetailDataModel(
                     kensaMstItem.KensaItemCd,
                     kensaMstItem.KensaName,
                     kensaMstItem.Unit,
                     kensaMstItem.MaleStd,
                     kensaMstItem.FemaleStd,
                     kensaMstItem.SeqNo,
                     dynamicArray,
                     kensaMstItem.CenterCd
                );

                kensaInfDetailData.Add(rowData);
            }

            var kensaInfDetailRows = new List<KensaInfDetailDataModel>();

            var groupRowDataItem = data
                .GroupBy(x => new { x.KensaItemCd, x.SeqGroupNo, x.CenterCd })
                .ToDictionary(
                    group =>
                    {
                        var newItem = group.First();
                        newItem.SetRowSeqId(string.Join("-", group.Select(x => x.SeqNo)));
                        return newItem;
                    },
                    group => group.ToList());

            foreach (var item in groupRowDataItem)
            {
                var kensaInfDetailDataItem = kensaInfDetailData.FirstOrDefault(x => x.SeqNo == item.Key.SeqNo);
                var dspCenterName = "";
                if (item.Key.CenterCd == CommonConstants.InHospitalCenterCd)
                {
                    dspCenterName = "院内";
                } else
                {
                    var commonKensaCenterMst =
                            NoTrackingDataContext.CommonKensaCenterMst.FirstOrDefault(x => x.CenterCd == item.Key.CenterCd);
                    if (commonKensaCenterMst != null)
                    {
                        dspCenterName = commonKensaCenterMst.DspCenterName ?? string.Empty;
                    }
                }
                var rowData = new KensaInfDetailDataModel(
                     kensaInfDetailDataItem?.KensaItemCd ?? string.Empty,
                     kensaInfDetailDataItem?.KensaName ?? string.Empty,
                     kensaInfDetailDataItem?.Unit ?? string.Empty,
                     kensaInfDetailDataItem?.MaleStd ?? string.Empty,
                     kensaInfDetailDataItem?.FemaleStd ?? string.Empty,
                     item.Key.IraiDate,
                     item.Key.KensaKana,
                     item.Key.SortNo,
                     item.Key.SeqNo,
                     item.Key.SeqParentNo,
                     item.Key.RowSeqId,
                     kensaInfDetailDataItem?.DynamicArray ?? new(),
                     item.Key.KensaTime,
                     item.Key.CenterCd,
                     dspCenterName
                );
                kensaInfDetailRows.Add(rowData);
            }

            var kensaInfDetailRowItem = new List<KensaInfDetailDataModel>();

            if (setId == 0)
            {
                var sortCoulumItem = userConf.Where(x => x.GrpItemCd == 1 && x.GrpItemEdaNo == 0).FirstOrDefault()?.Val;
                var sortTypeItem = userConf.Where(x => x.GrpItemCd == 1 && x.GrpItemEdaNo == 1).FirstOrDefault()?.Val;

                // Get all parent item
                kensaInfDetailRowItem = kensaInfDetailRows.Where(x => x.SeqParentNo == 0).ToList();
                kensaInfDetailRowItem = SortRow(kensaInfDetailRowItem);

                // Children item
                var childrenItems = kensaInfDetailRows.Where(x => x.SeqParentNo > 0).ToList();

                // Append childrends
                for (int i = 0; i < kensaInfDetailRowItem.Count; i++)
                {
                    var item = kensaInfDetailRowItem[i];
                    var childrens = childrenItems.Where(x => item.RowSeqId.Contains(x.SeqParentNo.ToString())).ToList();
                    if (childrens != null && childrens.Any())
                    {
                        if (childrens.Count > 1)
                        {
                            childrens = SortRow(childrens);
                        }
                        kensaInfDetailRowItem.InsertRange(i + 1, childrens);
                    }
                }

                List<KensaInfDetailDataModel> SortRow(List<KensaInfDetailDataModel> data)
                {

                    switch (sortCoulumItem)
                    {
                        case SortKensaMstColumn.KensaItemCd:
                            if (sortTypeItem == 1)
                            {
                                return data.OrderByDescending(x => x.KensaItemCd).ToList();
                            }
                            else
                            {
                                return data.OrderBy(x => x.KensaItemCd).ToList();
                            }
                        case SortKensaMstColumn.KensaKana:
                            if (sortTypeItem == 1)
                            {
                                return data.OrderByDescending(x => x.KensaKana).ToList();
                            }
                            else
                            {
                                return data.OrderBy(x => x.KensaKana).ToList();
                            }
                        default:
                            if (sortTypeItem == 1)
                            {
                                return data.OrderByDescending(x => x.SortNo).ToList();
                            }
                            else
                            {
                                return data.OrderBy(x => x.SortNo).ToList();
                            }
                    }
                }
            }
            else
            {
                var kensasetDetail = NoTrackingDataContext.KensaSetDetails.Where(x => x.HpId == hpId && x.SetId == setId && x.IsDeleted == DeleteTypes.None).OrderBy(x => x.SortNo).ToList();
                foreach (var cunrentItemSet in kensasetDetail)
                {
                    var lastItemSet = kensasetDetail.LastOrDefault(x => x.KensaItemCd == cunrentItemSet.KensaItemCd);
                    if (cunrentItemSet == lastItemSet)
                    {
                        var listRow = kensaInfDetailRows.Where(x => x.KensaItemCd == cunrentItemSet.KensaItemCd).ToList();
                        if (listRow.Count > 0)
                        {
                            kensaInfDetailRowItem.AddRange(listRow);
                        }
                        else
                        {
                            var duplicatRow = kensaInfDetailRowItem.Where(x => x.KensaItemCd == cunrentItemSet.KensaItemCd).LastOrDefault();
                            if (duplicatRow != null)
                            {
                                kensaInfDetailRowItem.Add(duplicatRow);
                            }
                        }
                    }
                    else
                    {
                        var row = kensaInfDetailRows.FirstOrDefault(x => x.KensaItemCd == cunrentItemSet.KensaItemCd);
                        if (row == null)
                        {
                            var duplicatRow = kensaInfDetailRowItem.Where(x => x.KensaItemCd == cunrentItemSet.KensaItemCd).LastOrDefault();
                            if (duplicatRow != null)
                            {
                                kensaInfDetailRowItem.Add(duplicatRow);
                            }
                        }
                        else
                        {
                            kensaInfDetailRowItem.Add(row);
                            kensaInfDetailRows.Remove(row);
                        }
                    }
                }
            }

            //print Report
            List<string> itemName = new();
            foreach (var item in kensaInfDetailRowItem)
            {
                itemName.Add(item.KensaName);
            }

            itemName.Distinct();

            List<CoKensaResultMultiModel> result = new();
            List<KensaResultMultiItem> kensaResultMultiItems = new();
            var ptInf = NoTrackingDataContext.PtInfs.FirstOrDefault(x => x.HpId == hpId && x.PtId == ptId);

            foreach (var item in kensaInfDetailRowItem)
            {
                switch (ptInf?.Sex)
                {
                    case 1: result.Add(new CoKensaResultMultiModel(item.IraiDate, item.KensaName, item.Unit, item.MaleStd, new(), new(), item.SeqParentNo, item.RowSeqId, item.KensaTime, null, item.DspCenterName)); break;
                    case 2: result.Add(new CoKensaResultMultiModel(item.IraiDate, item.KensaName, item.Unit, item.FemaleStd, new(), new(), item.SeqParentNo, item.RowSeqId, item.KensaTime, null, item.DspCenterName)); break;
                }

            }

            foreach (var item in kensaInfDetailRowItem)
            {
                foreach (var kensaResultMultiItem in item.DynamicArray)
                {
                    kensaResultMultiItems.Add(new KensaResultMultiItem(kensaResultMultiItem.IraiDate, kensaResultMultiItem.ResultVal, kensaResultMultiItem.AbnormalKbn, kensaResultMultiItem.ResultType, kensaResultMultiItem.KensaTime));
                }
            }

            List<long> date = new();
            List<string> dateTime = new();

            if (itemName.Count == 0)
            {
                return (result, date, dateTime);
            }

            int count = kensaResultMultiItems.Count / itemName.Count;
            int j = 0;

            for (int i = 0; i < result.Count; i++)
            {
                for (int k = 0; k < count; k++)
                {
                    result[i].KensaResultMultiItems.Add(kensaResultMultiItems[j++]);
                }
            }

            date.AddRange(kensaInfDetailCol.Select(x => x.IraiDate).ToList());
            dateTime.AddRange(kensaInfDetailCol.Select(x => x.IraiDate.ToString() + x.KensaTime).ToList());
            date.Remove(0);

            if (userConf?.Where(x => x.GrpItemCd == 0).FirstOrDefault()?.Val == 0 || userConf?.Where(x => x.GrpItemCd == 0).FirstOrDefault()?.Val == null)
            {
                date = date.OrderBy(x => x).ToList();
                dateTime = dateTime.OrderBy(x => x).ToList();
            }
            else
            {
                date = date.OrderByDescending(x => x).ToList();
                dateTime = dateTime.OrderByDescending(x => x).ToList();
            }

            result.Add(new CoKensaResultMultiModel(0, "", "", "", new(), date, 0, "", "", dateTime));

            return (result, date, dateTime);
        }

        public ListKensaInfDetailModel GetListKensaInf(int hpId, int userId, long ptId, int setId, int iraiCdStart, bool getGetPrevious, bool showAbnormalKbn, List<string> kensaItemCdList, int startDate, int iraiCd = 0)
        {
            IEnumerable<KensaInfDetail> kensaInfDetails;

            var userConf = NoTrackingDataContext.UserConfs.Where(x => x.UserId == userId && x.HpId == hpId && x.GrpCd == 1002);

            var kensaSetDetailById = NoTrackingDataContext.KensaSetDetails.Where(x => x.SetId == setId && x.HpId == hpId && x.IsDeleted == DeleteTypes.None).GroupBy(item => item.KensaItemCd)
               .Select(group => new
               {
                   KensaItemCd = group.Key,
                   SortNo = group.Min(item => item.SortNo)
               });

            bool SortIraiDateAsc = true;

            if (userConf.Where(x => x.GrpItemCd == 0).FirstOrDefault()?.Val == 1)
            {
                SortIraiDateAsc = false;
            }

            if (setId == 0)
            {
                kensaInfDetails = NoTrackingDataContext.KensaInfDetails
                .Where(x => x.HpId == hpId &&
                            x.PtId == ptId &&
                            x.IsDeleted == DeleteTypes.None &&
                            x.IraiCd == iraiCd &&
                            !string.IsNullOrEmpty(x.KensaItemCd));

                if (kensaItemCdList.Any())
                {
                    kensaInfDetails = kensaInfDetails.Where(x => kensaItemCdList.Contains(x.KensaItemCd));
                }
            }
            else
            {
                // Flter data with KensaSet
                kensaInfDetails = (from t1 in NoTrackingDataContext.KensaInfDetails
                                   join t2 in kensaSetDetailById on t1.KensaItemCd equals t2.KensaItemCd
                                   where t1.HpId == hpId && t1.PtId == ptId && t1.IsDeleted == DeleteTypes.None && (kensaItemCdList.Count == 0 || kensaItemCdList.Contains(t1.KensaItemCd) && t1.IraiCd == iraiCd)
                                   select new
                                   {
                                       Result = t1
                                   }
                            ).Select(x => x.Result);
            }

            var internalData = from t1 in kensaInfDetails
                               join t3 in NoTrackingDataContext.KensaInfs on new { t1.HpId, t1.PtId, t1.IraiCd } equals new { t3.HpId, t3.PtId, t3.IraiCd }
                               join t4 in NoTrackingDataContext.PtInfs on new { t1.PtId, t1.HpId } equals new { t4.PtId, t4.HpId }
                               where t3.CenterCd == CommonConstants.InHospitalCenterCd
                                     && t3.IsDeleted == DeleteTypes.None
                                     && t4.IsDelete == DeleteTypes.None
                               join t2 in NoTrackingDataContext.KensaMsts
                                   on new { t1.KensaItemCd, t1.HpId } equals new { t2.KensaItemCd, t2.HpId }
                               join t5 in NoTrackingDataContext.KensaCmtMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None)
                                   on t1.CmtCd1 equals t5.CmtCd into leftJoinT5
                               from t5 in leftJoinT5.DefaultIfEmpty()
                               join t6 in NoTrackingDataContext.KensaCmtMsts.Where(x => x.HpId == hpId && x.IsDeleted == DeleteTypes.None)
                                   on t1.CmtCd2 equals t6.CmtCd into leftJoinT6
                               from t6 in leftJoinT6.DefaultIfEmpty()
                               where t2.IsDelete == DeleteTypes.None
                               select new ListKensaInfDetailItemModel
                               (
                                   t1.PtId,
                                   t1.IraiCd,
                                   t1.RaiinNo,
                                   t3.IraiDate,
                                   t1.SeqNo,
                                   t1.SeqParentNo,
                                   t2.KensaName ?? string.Empty,
                                   t2.KensaKana ?? string.Empty,
                                   t2.SortNo,
                                   t1.KensaItemCd ?? string.Empty,
                                   t1.ResultVal ?? string.Empty,
                                   t1.ResultType ?? string.Empty,
                                   t1.AbnormalKbn ?? string.Empty,
                                   t1.CmtCd1 ?? string.Empty,
                                   t1.CmtCd2 ?? string.Empty,
                                   (t5 == null || t3.CenterCd == t5.CenterCd || string.IsNullOrEmpty(t5.CenterCd)) ? (t5 != null ? t5.CMT ?? string.Empty : string.Empty) : "不明",
                                   (t6 == null || t3.CenterCd == t6.CenterCd || string.IsNullOrEmpty(t6.CenterCd)) ? (t6 != null ? t6.CMT ?? string.Empty : string.Empty) : "不明",
                                   t2 != null ? t2.MaleStd ?? string.Empty : string.Empty,
                                   t2 != null ? t2.FemaleStd ?? string.Empty : string.Empty,
                                   t2 != null ? t2.MaleStdLow ?? string.Empty : string.Empty,
                                   t2 != null ? t2.FemaleStdLow ?? string.Empty : string.Empty,
                                   t2 != null ? t2.MaleStdHigh ?? string.Empty : string.Empty,
                                   t2 != null ? t2.FemaleStdHigh ?? string.Empty : string.Empty,
                                   t2.Unit ?? string.Empty,
                                   t3.Nyubi ?? string.Empty,
                                   t3.Yoketu ?? string.Empty,
                                   t3.Bilirubin ?? string.Empty,
                                   t3.SikyuKbn,
                                   t3.TosekiKbn,
                                   t3.InoutKbn,
                                   t3.Status,
                                   DeleteTypes.None,
                                   t1.SeqGroupNo,
                                   string.Empty,
                                   t3.KensaTime
                               );

            var externalData = from t1 in kensaInfDetails
                               join t3 in NoTrackingDataContext.KensaInfs on new { t1.HpId, t1.PtId, t1.IraiCd } equals new { t3.HpId, t3.PtId, t3.IraiCd }
                               join t4 in NoTrackingDataContext.PtInfs on new { t1.PtId, t1.HpId } equals new { t4.PtId, t4.HpId }
                               where t3.CenterCd != CommonConstants.InHospitalCenterCd
                                     && t3.IsDeleted == DeleteTypes.None
                                     && t4.IsDelete == DeleteTypes.None
                               join t2 in NoTrackingDataContext.CommonCenterKensaMst
                                   on new { t3.CenterCd, t1.KensaItemCd } equals new { t2.CenterCd, t2.KensaItemCd } into leftJoinT2
                               from t2 in leftJoinT2.DefaultIfEmpty()
                               join t6 in NoTrackingDataContext.CommonKensaCenterMst
                                   on t3.CenterCd equals t6.CenterCd into leftJoinT6
                               from t6 in leftJoinT6.DefaultIfEmpty()
                               join t7 in NoTrackingDataContext.CommonCenterStdMst
                                   on new { t3.CenterCd, t1.KensaItemCd } equals new { t7.CenterCd, t7.KensaItemCd } into leftJoinT7
                               from t7 in leftJoinT7.DefaultIfEmpty()
                               where t1.IsDeleted == DeleteTypes.None
                               select new ListKensaInfDetailItemModel(
                                   t1.PtId,
                                   t1.IraiCd,
                                   t1.RaiinNo,
                                   t3.IraiDate,
                                   t1.SeqNo,
                                   t1.SeqParentNo,
                                   t2 != null ? t2.KensaName ?? string.Empty : string.Empty,
                                   t2 != null ? t2.KensaKana ?? string.Empty : string.Empty,
                                   t2 != null ? t2.SortNo : 0,
                                   t1.KensaItemCd ?? string.Empty,
                                   t1.ResultVal ?? string.Empty,
                                   t1.ResultType ?? string.Empty,
                                   t1.AbnormalKbn ?? string.Empty,
                                   t1.CmtCd1 ?? string.Empty,
                                   t1.CmtCd2 ?? string.Empty,
                                   string.Empty,
                                   string.Empty,
                                   t7 != null ? t7.MealStd ?? string.Empty : string.Empty,
                                   t7 != null ? t7.FemelStd ?? string.Empty : string.Empty,
                                   t7 != null ? t7.MealStdLow ?? string.Empty : string.Empty,
                                   t7 != null ? t7.FemelStdLow ?? string.Empty : string.Empty,
                                   t7 != null ? t7.MealStdHigh ?? string.Empty : string.Empty,
                                   t7 != null ? t7.FemelStdHigh ?? string.Empty : string.Empty,
                                   t2 != null ? t2.Unit ?? string.Empty : string.Empty,
                                   t3.Nyubi ?? string.Empty,
                                   t3.Yoketu ?? string.Empty,
                                   t3.Bilirubin ?? string.Empty,
                                   t3.SikyuKbn,
                                   t3.TosekiKbn,
                                   t3.InoutKbn,
                                   t3.Status,
                                   DeleteTypes.None,
                                   t1.SeqGroupNo,
                                   string.Empty,
                                   t3.KensaTime
                               );

            // Combine the results
            var data = internalData.Concat(externalData);

            #region Get Col dynamic

            // Sort col by IraiDate
            var sortedData = SortIraiDateAsc
                ? data.OrderBy(x => x.IraiDate)
                : data.OrderByDescending(x => x.IraiDate);



            var kensaInfDetailCol = sortedData
                .GroupBy(x => new { x.IraiCd, x.IraiDate, x.Nyubi, x.Yoketu, x.Bilirubin, x.SikyuKbn, x.TosekiKbn, x.KensaTime })
                .Select((group, index) => new KensaInfDetailColModel(
                    group.Key.IraiCd,
                    group.Key.IraiDate,
                    group.Key.Nyubi,
                    group.Key.Yoketu,
                    group.Key.Bilirubin,
                    group.Key.SikyuKbn,
                    group.Key.TosekiKbn,
                    index,
                    group.Key.KensaTime
                ));


            var totalCol = kensaInfDetailCol.Count();

            #endregion

            #region Get Row dynamic
            // Filter data with col
            var kensaIraiCdSet = new HashSet<long>(kensaInfDetailCol.Select(item => item.IraiCd));
            data = data.Where(x => kensaIraiCdSet.Contains(x.IraiCd));

            var groupRowData = data
                .GroupBy(x => new { x.KensaItemCd, x.SeqGroupNo })
                .ToDictionary(
                    group =>
                    {
                        var newItem = group.First();
                        newItem.SetRowSeqId(string.Join("-", group.Select(x => x.SeqNo)));
                        return newItem;
                    },
                    group => group.ToList());

            var kensaInfDetailData = new List<KensaInfDetailDataModel>();

            foreach (var item in groupRowData)
            {
                var row = item.Key;
                kensaInfDetailData.Add(new KensaInfDetailDataModel(
                row.KensaItemCd,
                row.KensaName,
                row.Unit,
                row.MaleStd,
                row.FemaleStd,
                row.KensaKana,
                row.SortNo,
                row.SeqNo,
                row.SeqParentNo,
                row.RowSeqId,
                item.Value
            ));
            }

            // Sort row by user config
            List<KensaInfDetailDataModel> kensaInfDetailRows = new List<KensaInfDetailDataModel>();

            if (setId == 0)
            {
                var sortCoulum = userConf.Where(x => x.GrpItemCd == 1 && x.GrpItemEdaNo == 0).FirstOrDefault()?.Val;
                var sortType = userConf.Where(x => x.GrpItemCd == 1 && x.GrpItemEdaNo == 1).FirstOrDefault()?.Val;

                // Get all parent item
                kensaInfDetailRows = kensaInfDetailData.Where(x => x.SeqParentNo == 0).ToList();
                kensaInfDetailRows = SortRow(kensaInfDetailRows);

                // Children item
                var childrenItems = kensaInfDetailData.Where(x => x.SeqParentNo > 0).ToList();

                // Append childrends
                for (int i = 0; i < kensaInfDetailRows.Count; i++)
                {
                    var item = kensaInfDetailRows[i];
                    var childrens = childrenItems.Where(x => item.RowSeqId.Contains(x.SeqParentNo.ToString())).ToList();
                    if (childrens != null && childrens.Any())
                    {
                        if (childrens.Count > 1)
                        {
                            childrens = SortRow(childrens);
                        }
                        kensaInfDetailRows.InsertRange(i + 1, childrens);
                    }
                }
                List<KensaInfDetailDataModel> SortRow(List<KensaInfDetailDataModel> data)
                {

                    switch (sortCoulum)
                    {
                        case SortKensaMstColumn.KensaItemCd:
                            if (sortType == 1)
                            {
                                return data.OrderByDescending(x => x.KensaItemCd).ToList();
                            }
                            else
                            {
                                return data.OrderBy(x => x.KensaItemCd).ToList();
                            }
                        case SortKensaMstColumn.KensaKana:
                            if (sortType == 1)
                            {
                                return data.OrderByDescending(x => x.KensaKana).ToList();
                            }
                            else
                            {
                                return data.OrderBy(x => x.KensaKana).ToList();
                            }
                        default:
                            if (sortType == 1)
                            {
                                return data.OrderByDescending(x => x.SortNo).ToList();
                            }
                            else
                            {
                                return data.OrderBy(x => x.SortNo).ToList();
                            }
                    }
                }
            }

            // Filter row by KensaSet
            else
            {
                var kensasetDetail = NoTrackingDataContext.KensaSetDetails.Where(x => x.HpId == hpId && x.SetId == setId && x.IsDeleted == DeleteTypes.None).OrderBy(x => x.SortNo).ToList();

                foreach (var cunrentItemSet in kensasetDetail)
                {
                    var lastItemSet = kensasetDetail.LastOrDefault(x => x.KensaItemCd == cunrentItemSet.KensaItemCd);
                    if (cunrentItemSet == lastItemSet)
                    {
                        var listRow = kensaInfDetailData.Where(x => x.KensaItemCd == cunrentItemSet.KensaItemCd).ToList();
                        if (listRow.Count > 0)
                        {
                            kensaInfDetailRows.AddRange(listRow);
                        }
                        else
                        {
                            var duplicatRow = kensaInfDetailRows.Where(x => x.KensaItemCd == cunrentItemSet.KensaItemCd).LastOrDefault();
                            if (duplicatRow != null)
                            {
                                kensaInfDetailRows.Add(duplicatRow);
                            }
                        }
                    }
                    else
                    {
                        var row = kensaInfDetailData.FirstOrDefault(x => x.KensaItemCd == cunrentItemSet.KensaItemCd);
                        if (row == null)
                        {
                            var duplicatRow = kensaInfDetailRows.Where(x => x.KensaItemCd == cunrentItemSet.KensaItemCd).LastOrDefault();
                            if (duplicatRow != null)
                            {
                                kensaInfDetailRows.Add(duplicatRow);
                            }
                        }
                        else
                        {
                            kensaInfDetailRows.Add(row);
                            kensaInfDetailData.Remove(row);
                        }
                    }
                }
            }
            #endregion

            var result = new ListKensaInfDetailModel(kensaInfDetailCol.ToList(), kensaInfDetailRows.OrderBy(x => x.KensaName).ToList(), totalCol);

            return result;
        }
    }
}
