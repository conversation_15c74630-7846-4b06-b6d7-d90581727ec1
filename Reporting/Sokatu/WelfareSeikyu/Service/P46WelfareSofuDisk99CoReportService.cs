﻿using Helper.Common;
using Microsoft.AspNetCore.Http.Internal;
using Reporting.Mappers.Common;
using Reporting.Sokatu.Common.Models;
using Reporting.Sokatu.KokhoSokatu.Mapper;
using Reporting.Sokatu.WelfareSeikyu.DB;
using Reporting.Sokatu.WelfareSeikyu.Models;
using Reporting.Structs;

namespace Reporting.Sokatu.WelfareSeikyu.Service
{
    public class P46WelfareSofuDisk99CoReportService : IP46WelfareSofuDisk99CoReportService
    {
        #region Constant
        private List<int> KohiHokenNos = new List<int> { 299 };
        #endregion

        #region Private properties

        /// <summary>
        /// Finder
        /// </summary>
        private ICoWelfareSeikyuFinder _welfareFinder;

        /// <summary>
        /// CoReport Model
        /// </summary>
        private List<CoWelfareReceInfModel> receInfs;
        private CoHpInfModel hpInf;
        private int hpId;
        private int seikyuYm;
        private SeikyuType seikyuType;
        private readonly Dictionary<string, string> _singleFieldData;
        private readonly Dictionary<int, List<ListTextObject>> _listTextData;
        private readonly Dictionary<int, Dictionary<string, string>> _setFieldData;
        private readonly Dictionary<string, string> _extralData;
        private bool hasNextPage;
        private int currentPage;
        private int diskKind;
        private int diskCnt;
        private string _formFileName;
        private readonly Dictionary<string, bool> _visibleFieldData;
        private readonly Dictionary<string, bool> _visibleAtPrint;
        #endregion

        #region Constructor and Init
        public P46WelfareSofuDisk99CoReportService(ICoWelfareSeikyuFinder welfareFinder)
        {
            _welfareFinder = welfareFinder;
            _singleFieldData = new();
            _listTextData = new();
            _setFieldData = new();
            _extralData = new();
            _visibleFieldData = new();
            _visibleAtPrint = new();
        }
        #endregion
        public CommonReportingRequestModel GetP46WelfareSofuDisk99CoReportingData(int hpId, int seikyuYm, SeikyuType seikyuType, int diskKind, int diskCnt)
        {
            this.hpId = hpId;
            this.seikyuYm = seikyuYm;
            this.seikyuType = seikyuType;
            this.diskKind = diskKind;
            this.diskCnt = diskCnt;

            currentPage = 1;
            var getData = GetData();
            hasNextPage = true;

            if (getData)
            {
                while (getData && hasNextPage)
                {
                    UpdateDrawForm();
                    currentPage++;
                }
            }

            var pageIndex = _listTextData.Select(item => item.Key).Distinct().Count();
            _extralData.Add("totalPage", pageIndex.ToString());
            return new KokhoSokatuMapper(_setFieldData, _listTextData, _extralData, _formFileName, _singleFieldData, _visibleFieldData, _visibleAtPrint).GetData();
        }

        #region Private function
        private bool GetData()
        {
            hpInf = _welfareFinder.GetHpInf(hpId, seikyuYm);
            var wrkReces = _welfareFinder.GetReceInf(hpId, seikyuYm, seikyuType, KohiHokenNos, FutanCheck.None, 0);
            //自己負担額がある人のみ
            receInfs = wrkReces.Where(r => r.PtFutan > 0).ToList();

            return (receInfs?.Count ?? 0) > 0;
        }
        private bool UpdateDrawForm()
        {
            bool _hasNextPage = false;
            #region SubMethod

            #region Header
            int UpdateFormHeader()
            {
                //医療機関コード
                SetFieldData("hpCode", hpInf.HpCd);
                //医療機関情報
                SetFieldData("address1", hpInf.Address1);
                SetFieldData("address2", hpInf.Address2);
                SetFieldData("hpName", hpInf.ReceHpName);
                SetFieldData("hpTel", hpInf.Tel);
                SetFieldData("kaisetuName", hpInf.KaisetuName);
                //請求年月
                CIUtil.WarekiYmd wrkYmd = CIUtil.SDateToShowWDate3(seikyuYm * 100 + 1);
                SetFieldData("seikyuGengo", wrkYmd.Gengo);
                SetFieldData("seikyuYear", wrkYmd.Year.ToString());
                SetFieldData("seikyuMonth", wrkYmd.Month.ToString());
                //提出年月日
                wrkYmd = CIUtil.SDateToShowWDate3(
                    CIUtil.ShowSDateToSDate(DateTime.Now.ToString("yyyy/MM/dd"))
                );
                SetFieldData("reportGengo", wrkYmd.Gengo);
                SetFieldData("reportYear", wrkYmd.Year.ToString());
                SetFieldData("reportMonth", wrkYmd.Month.ToString());
                SetFieldData("reportDay", wrkYmd.Day.ToString());

                return 1;
            }
            #endregion

            #region Body
            int UpdateFormBody()
            {
                List<ListTextObject> listDataPerPage = new();
                var pageIndex = _listTextData.Select(item => item.Key).Distinct().Count() + 1;
                //件数
                //自己負担額がある人のみ
                var wrkReces = receInfs.Where(r => r.PtFutan > 0);
                int count = wrkReces.Count();
                Dictionary<string, string> fieldDataPerPage = _setFieldData.ContainsKey(pageIndex) ? _setFieldData[pageIndex] : new();
                pageIndex = _listTextData.Select(item => item.Key).Distinct().Count() + 1;
                //媒体種類
                fieldDataPerPage.Add("circleCD", diskKind == 2 ? "1" : "0");
                fieldDataPerPage.Add("circlePaper", diskKind == 4 ? "1" : "0");
                //提出枚数
                if (diskKind == 2)
                {
                    SetFieldData("diskCnt", diskCnt.ToString());
                }
                else if (diskKind == 4)
                {
                   
                    fieldDataPerPage.Add("totalCount", count.ToString());

                    if (!_setFieldData.ContainsKey(pageIndex))
                    {
                        _setFieldData.Add(pageIndex, fieldDataPerPage);
                    }

                    _listTextData.Add(pageIndex, listDataPerPage);
                }

                return 1;
            }
            #endregion

            #endregion

            try
            {
                if (UpdateFormHeader() < 0 || UpdateFormBody() < 0)
                {
                    hasNextPage = _hasNextPage;
                    return false;
                }
            }
            catch (Exception e)
            {
                hasNextPage = _hasNextPage;
                return false;
            }

            hasNextPage = _hasNextPage;
            return true;
        }
        private void SetFieldData(string field, string value)
        {
            if (!string.IsNullOrEmpty(field) && !_singleFieldData.ContainsKey(field))
            {
                _singleFieldData.Add(field, value);
            }
        }
        #endregion
    }
}
