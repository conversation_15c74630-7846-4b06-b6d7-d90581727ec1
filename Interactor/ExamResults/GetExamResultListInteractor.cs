using Domain.Models.ExamResults;
using UseCase.ExamResults.GetExamResults;

namespace Interactor.ExamResults;

public class GetExamResultListInteractor : IGetExamResultListInputPort
{
    private readonly IExamResultsRepository _examResultRepository;

    public GetExamResultListInteractor(IExamResultsRepository examResultRepository)
    {
        _examResultRepository = examResultRepository;
    }

    public GetExamResultListOutputData Handle(GetExamResultListInputData inputData)
    {
        try
        {
            if (inputData.PtId <= 0)
            {
                return new GetExamResultListOutputData(GetExamResultListStatus.InvalidPtId);
            }

            var validate = ValidateStartDateAndEndDate(inputData.StartDate, inputData.EndDate);

            if (validate != null )
            {
                return validate;
            }
            return new GetExamResultListOutputData(GetExamResultItemList(inputData), GetExamResultListStatus.Successed);
        }
        finally
        {
            _examResultRepository.ReleaseResource();
        }
    }

    private GetExamResultListOutputData? ValidateStartDateAndEndDate(long startDate, long endDate)
    {
        if (endDate < startDate && startDate != 0 && endDate != 0)
        {
            return new GetExamResultListOutputData(GetExamResultListStatus.InvalidRangeDate);
        }
        return null;
    }

    private ExamResultsDto GetExamResultItemList(GetExamResultListInputData inputData)
    {
        return _examResultRepository.GetExamResults(inputData.HpId, inputData.PtId, inputData.StartDate, inputData.EndDate, inputData.KeyWord, inputData.TimeSequence, inputData.KensaItemCds, inputData.CenterCd);
    }
}
