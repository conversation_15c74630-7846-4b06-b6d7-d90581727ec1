﻿using Domain.Models.HistoryOrder;
using Domain.Models.Insurance;
using Domain.Models.InsuranceInfor;
using Domain.Models.OrdInfs;
using Domain.Models.PatientInfor;
using Domain.Models.Receipt.Recalculation;
using Domain.Models.User;
using Helper.Common;
using Helper.Constants;
using Infrastructure.Base;
using Infrastructure.Interfaces;
using Infrastructure.Options;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using UseCase.MedicalExamination.GetDataPrintKarte2;
using UseCase.MedicalExamination.GetHistory;
using UseCase.OrdInfs.GetListTrees;

namespace Interactor.MedicalExamination.HistoryCommon;

public class HistoryCommon : RepositoryBase, IHistoryCommon
{
    private readonly IInsuranceRepository _insuranceRepository;
    private readonly IHistoryOrderRepository _historyOrderRepository;
    private readonly IPatientInforRepository _patientInforRepository;
    private readonly IUserRepository _userRepository;
    private readonly IAmazonS3Service _amazonS3Service;
    private readonly AmazonS3Options _options;

    public HistoryCommon(ITenantProvider tenantProvider, IOptions<AmazonS3Options> optionsAccessor, IInsuranceRepository insuranceRepository, IHistoryOrderRepository historyOrderRepository, IAmazonS3Service amazonS3Service, IPatientInforRepository patientInforRepository, IUserRepository userRepository) : base(tenantProvider)
    {
        _insuranceRepository = insuranceRepository;
        _historyOrderRepository = historyOrderRepository;
        _amazonS3Service = amazonS3Service;
        _options = optionsAccessor.Value;
        _patientInforRepository = patientInforRepository;
        _userRepository = userRepository;
    }

    public GetMedicalExaminationHistoryOutputData GetHistoryOutput(int hpId, long ptId, int sinDate, (int totalCount, int totalKeyWordMatched, List<HistoryOrderModel> historyOrderModelList) historyList, List<SinKouiListModel> sinkouiList, string? keyWord = null, List<InsuranceSummaryModel>? insuranceModels = null)
    {
        ConcurrentBag<HistoryKarteOdrRaiinItem> historyKarteOdrRaiins = new();
        var ptInf = _patientInforRepository.GetById(hpId, ptId, 0, 0);
        var listUserIds = historyList.historyOrderModelList?.Select(d => d.UketukeId).ToList();
        var listUsers = listUserIds == null ? new List<UserMstModel>() : _userRepository.GetListAnyUser(hpId, listUserIds)?.ToList();
        Parallel.ForEach(historyList.historyOrderModelList ?? new(), history  =>
        {
            var karteInfs = history.KarteInfModels;
            var uketuke = listUsers?.FirstOrDefault(uke => uke.UserId == history.UketukeId);
            var karteInfHistoryItems = karteInfs.Select(karteInf => new KarteInfHistoryItem(karteInf.HpId, karteInf.RaiinNo, karteInf.KarteKbn, karteInf.SeqNo, karteInf.PtId, karteInf.SinDate, karteInf.Text, karteInf.UpdateDate, karteInf.CreateDate, karteInf.IsDeleted, karteInf.RichText, karteInf.CreateName)).ToList();
            List<GrpKarteHistoryItem> karteHistoryList = new List<GrpKarteHistoryItem> {
                        new GrpKarteHistoryItem(
                        karteInfHistoryItems.FirstOrDefault()?.KarteKbn ?? 0,
                        string.Empty,
                        string.Empty,
                        1,
                        0,
                        karteInfHistoryItems.OrderBy(k => k.SeqNo).ToList())
                    };

            var historyKarteOdrRaiin = new HistoryKarteOdrRaiinItem
                (
                    history.RaiinNo,
                    history.SinDate,
                    history.HokenPid,
                    history.HokenTitle,
                    history.HokenRate,
                    history.SyosaisinKbn,
                    history.JikanKbn,
                    history.TreatmentDepartmentId,
                    history.TreatmentDepartmentTitle,
                    history.KaId,
                    history.KaName,
                    history.TantoId,
                    history.TantoName,
                    history.TantoFullName,
                    history.SanteiKbn,
                    history.TagNo,
                    history.SinryoTitle,
                    history.HokenType,                   
                    new KarteEditionItem(
                        history.KarteEditionModel.HpId,
                        history.KarteEditionModel.PtId,
                        history.KarteEditionModel.RaiinNo,
                        history.KarteEditionModel.Edition,
                        history.KarteEditionModel.KarteStatus,
                        history.KarteEditionModel.IsDeleted,
                        history.KarteEditionModel.CreateDate,
                        history.KarteEditionModel.CreateId,
                        history.KarteEditionModel.UpdateDate,
                        history.KarteEditionModel.UpdateId,
                        history.KarteEditionModel.ApprovalDate,
                        history.KarteEditionModel.ApprovalId,
                        history.KarteEditionModel.UpdateName,
                        history.KarteEditionModel.ApprovalName,
                        new(),
                        karteHistoryList,
                        history.ListKarteFile.Select(item => new FileInfOutputItem(item))
                                            .OrderBy(item => item.SeqNo)
                                            .ToList()
                    ),
                    history.Status,
                    CIUtil.TimeToShowTime(ParseInt(CIUtil.Copy(history.UketukeTime, 1, 4))),
                    uketuke != null ? uketuke.Sname : string.Empty,
                    CIUtil.TimeToShowTime(ParseInt(CIUtil.Copy(history.SinStartTime, 1, 4))),
                    CIUtil.TimeToShowTime(ParseInt(CIUtil.Copy(history.SinEndTime, 1, 4))),
                    history.OrderHokenType
                );

            //Excute order
            ExcuteOrder(insuranceModels ?? new(), history.OrderInfList, historyKarteOdrRaiin, historyKarteOdrRaiins, sinkouiList);
        });
        int currentKeyWordMatched = 0;
        if (!string.IsNullOrEmpty(keyWord))
        {
            currentKeyWordMatched = CountCurrentKeyWordMatched(keyWord, historyKarteOdrRaiins);
        }
        var result = new GetMedicalExaminationHistoryOutputData(historyList.totalCount, historyKarteOdrRaiins.OrderByDescending(r => r.SinDate).ThenByDescending(r => r.UketukeTime).ThenByDescending(r => r.RaiinNo).ToList(), GetMedicalExaminationHistoryStatus.Successed, 0, keyWord ?? string.Empty, historyList.totalKeyWordMatched, currentKeyWordMatched);

        if (historyKarteOdrRaiins?.Count > 0)
            return result;
        else
            return new GetMedicalExaminationHistoryOutputData(0, new List<HistoryKarteOdrRaiinItem>(), GetMedicalExaminationHistoryStatus.NoData, 0, keyWord ?? string.Empty, 0, 0);
    }

    private int CountCurrentKeyWordMatched(string keyWord, ConcurrentBag<HistoryKarteOdrRaiinItem> historyKarteOdrRaiinItems)
    {
        // historyKarteOdrRaiinItems -> KarteEdition -> 1. KarteHistories -> Text
        //                                           -> 2 (not need). HokenGroups -> GroupOdrItems -> OdrInfs -> RpName
        //                                           -> 3. HokenGroups -> GroupOdrItems -> OdrInfs -> OdrDetails -> ItemName + CmtOpt
        //                                           -> 4. listKarteFiles -> dspFileName
        int count = 0;
        foreach (HistoryKarteOdrRaiinItem historyKarteOdrRaiinItem in historyKarteOdrRaiinItems)
        {
            // 1
            List<GrpKarteHistoryItem> KarteHistories = historyKarteOdrRaiinItem.KarteEdition.KarteHistories;
            foreach (GrpKarteHistoryItem karteHistory in KarteHistories)
            {
                foreach (KarteInfHistoryItem karteInfHistoryItem in karteHistory.KarteData)
                {
                    // count how many times keyWord appears in karteInfHistoryItem.Text
                    count += Regex.Matches(karteInfHistoryItem.Text, Regex.Escape(keyWord), RegexOptions.IgnoreCase).Count;
                }
            }

            // 2, 3
            List<HokenGroupHistoryItem> HokenGroups = historyKarteOdrRaiinItem.KarteEdition.HokenGroups;
            foreach (HokenGroupHistoryItem hokenGroup in HokenGroups)
            {
                foreach (GroupOdrGHistoryItem groupOdrGHistoryItem in hokenGroup.GroupOdrItems)
                {
                    foreach (OdrInfHistoryItem odrInfHistoryItem in groupOdrGHistoryItem.OdrInfs)
                    {
                        // // 2
                        // // count how many times keyWord appears in odrInfHistoryItem.RpName
                        // count +=  Regex.Matches(odrInfHistoryItem.RpName, Regex.Escape(keyWord), RegexOptions.IgnoreCase).Count;

                        // 3
                        foreach (OdrInfDetailItem odrInfDetailItem in odrInfHistoryItem.OdrDetails)
                        {
                            // count how many times keyWord appears in odrInfDetailItem.ItemName , CmtOpt
                            count += Regex.Matches(odrInfDetailItem.ItemName, Regex.Escape(keyWord), RegexOptions.IgnoreCase).Count;
                            count += Regex.Matches(odrInfDetailItem.CmtOpt, Regex.Escape(keyWord), RegexOptions.IgnoreCase).Count;
                        }
                    }
                }
            }

            // 4
            List<FileInfOutputItem> listKarteFiles = historyKarteOdrRaiinItem.KarteEdition.ListKarteFiles;
            foreach (FileInfOutputItem fileInfOutputItem in listKarteFiles)
            {
                // count how many times keyWord appears in fileInfOutputItem.DspFileName
                count += Regex.Matches(fileInfOutputItem.DspFileName, Regex.Escape(keyWord), RegexOptions.IgnoreCase).Count;
            }
        }
        return count;
    }

    public GetMedicalExaminationHistoryVersionOutputData GetHistoryVersionOutput(int hpId, long ptId, int sinDate, (int totalCount, List<HistoryOrderVersionModel> historyOrderVersionModelList) historyList, List<SinKouiListModel> sinkouiList)
    {
        var historyKarteOdrRaiins = new List<HistoryKarteOdrRaiinItem>();
        var ptInf = _patientInforRepository.GetById(hpId, ptId, 0, 0);
        var listUserIds = historyList.historyOrderVersionModelList?.Select(d => d.UketukeId).ToList();
        var listUsers = listUserIds == null ? new List<UserMstModel>() : _userRepository.GetListAnyUser(hpId, listUserIds)?.ToList();
        var insuranceModelList = _insuranceRepository.GetInsuranceList(hpId, ptId, sinDate, true);
        foreach (HistoryOrderVersionModel history in historyList.historyOrderVersionModelList ?? new())
        {
            var karteInfs = history.KarteInfModels;
            var uketuke = listUsers?.FirstOrDefault(uke => uke.UserId == history.UketukeId);
            var karteInfHistoryItems = karteInfs.Select(karteInf => new KarteInfHistoryItem(karteInf.HpId, karteInf.RaiinNo, karteInf.KarteKbn, karteInf.SeqNo, karteInf.PtId, karteInf.SinDate, karteInf.Text, karteInf.UpdateDate, karteInf.CreateDate, karteInf.IsDeleted, karteInf.RichText, string.Empty)).ToList();
            List<GrpKarteHistoryItem> karteHistoryList = new List<GrpKarteHistoryItem> {
                        new GrpKarteHistoryItem(
                        karteInfHistoryItems.FirstOrDefault()?.KarteKbn ?? 0,
                        string.Empty,
                        string.Empty,
                        1,
                        0,
                        karteInfHistoryItems.OrderBy(k => k.SeqNo).ToList())
                    };

            var historyKarteOdrRaiin = new HistoryKarteOdrRaiinItem
                (
                    history.RaiinNo,
                    history.SinDate,
                    history.HokenPid,
                    history.HokenTitle,
                    history.HokenRate,
                    history.SyosaisinKbn,
                    history.JikanKbn,
                    history.TreatmentDepartmentId,
                    history.TreatmentDepartmentTitle,
                    history.KaId,
                    history.KaName,
                    history.TantoId,
                    history.TantoName,
                    history.TantoFullName,
                    history.SanteiKbn,
                    history.TagNo,
                    history.SinryoTitle,
                    history.HokenType,
                    new KarteEditionItem(
                        history.KarteEditionModel.HpId,
                        history.KarteEditionModel.PtId,
                        history.KarteEditionModel.RaiinNo,
                        history.KarteEditionModel.Edition,
                        history.KarteEditionModel.KarteStatus,
                        history.KarteEditionModel.IsDeleted,
                        history.KarteEditionModel.CreateDate,
                        history.KarteEditionModel.CreateId,
                        history.KarteEditionModel.UpdateDate,
                        history.KarteEditionModel.UpdateId,
                        history.KarteEditionModel.ApprovalDate,
                        history.KarteEditionModel.ApprovalId,
                        history.KarteEditionModel.UpdateName,
                        history.KarteEditionModel.ApprovalName,
                        new(),
                        karteHistoryList,
                        history.ListKarteFile.Select(item => new FileInfOutputItem(item))
                                            .OrderBy(item => item.SeqNo)
                                            .ToList()
                    ),
                    history.Status,
                    CIUtil.TimeToShowTime(ParseInt(CIUtil.Copy(history.UketukeTime, 1, 4))),
                    uketuke != null ? uketuke.Sname : string.Empty,
                    CIUtil.TimeToShowTime(ParseInt(CIUtil.Copy(history.SinStartTime, 1, 4))),
                    CIUtil.TimeToShowTime(ParseInt(CIUtil.Copy(history.SinEndTime, 1, 4))),
                    history.OrderHokenType
                );

            //Excute order
            ExcuteOrderVersion(insuranceModelList, history.OrderInfList, historyKarteOdrRaiin, historyKarteOdrRaiins, sinkouiList);
        }
        var result = new GetMedicalExaminationHistoryVersionOutputData(historyList.totalCount, historyKarteOdrRaiins, GetMedicalExaminationHistoryStatus.Successed, 0);

        if (historyKarteOdrRaiins?.Count > 0)
            return result;
        else
            return new GetMedicalExaminationHistoryVersionOutputData(0, new List<HistoryKarteOdrRaiinItem>(), GetMedicalExaminationHistoryStatus.NoData, 0);
    }

    public void FilterData(ref List<HistoryKarteOdrRaiinItem> historyKarteOdrRaiinItems, GetDataPrintKarte2InputData inputData)
    {
        List<OrderHokenType> GetListAcceptedHokenType()
        {
            List<OrderHokenType> result = new();
            if (inputData.IsCheckedHoken)
            {
                result.Add(OrderHokenType.Hoken);
            }
            if (inputData.IsCheckedJihi)
            {
                result.Add(OrderHokenType.Jihi);
            }
            if (inputData.IsCheckedHokenJihi)
            {
                result.Add(OrderHokenType.HokenJihi);
            }
            if (inputData.IsCheckedJihiRece)
            {
                result.Add(OrderHokenType.JihiRece);
            }
            if (inputData.IsCheckedHokenRousai)
            {
                result.Add(OrderHokenType.Rousai);
            }
            if (inputData.IsCheckedHokenJibai)
            {
                result.Add(OrderHokenType.Jibai);
            }
            return result;
        }

        //if (!inputData.IsIncludeTempSave)
        //{
        //    historyKarteOdrRaiinItems = historyKarteOdrRaiinItems.Where(k => k.Status != 3).ToList();
        //}

        List<OrderHokenType> listAcceptedHokenType = GetListAcceptedHokenType();

        //Filter raiin as hoken setting
        List<HistoryKarteOdrRaiinItem> filteredKaruteList = new();
        foreach (var history in historyKarteOdrRaiinItems)
        {
            if (listAcceptedHokenType.Contains((OrderHokenType)history.OrderHokenType))
            {
                filteredKaruteList.Add(history);
                continue;
            }

            if (history.KarteEdition.HokenGroups == null || !history.KarteEdition.HokenGroups.Any())
            {
                continue;
            }

            if (inputData.DeletedOdrVisibilitySetting == 0)
            {
                foreach (var hokenGroup in history.KarteEdition.HokenGroups)
                {
                    bool isDataExisted = false;
                    foreach (var group in hokenGroup.GroupOdrItems)
                    {
                        isDataExisted = group.OdrInfs.Any(o => o.IsDeleted == 0);
                        if (isDataExisted)
                        {
                            break;
                        }
                    }

                    if (isDataExisted && listAcceptedHokenType.Contains((OrderHokenType)history.OrderHokenType))
                    {
                        filteredKaruteList.Add(history);
                        break;
                    }
                }
            }
            else if (inputData.DeletedOdrVisibilitySetting == 2)
            {
                foreach (var hokenGroup in history.KarteEdition.HokenGroups)
                {
                    bool isDataExisted = false;
                    foreach (var group in hokenGroup.GroupOdrItems)
                    {
                        isDataExisted = group.OdrInfs.Any(o => o.IsDeleted != 2);
                        if (isDataExisted)
                        {
                            break;
                        }
                    }

                    if (isDataExisted && listAcceptedHokenType.Contains((OrderHokenType)history.OrderHokenType))
                    {
                        filteredKaruteList.Add(history);
                        break;
                    }
                }
            }
        }

        historyKarteOdrRaiinItems = filteredKaruteList;

        //Filter karte and order empty
        historyKarteOdrRaiinItems = historyKarteOdrRaiinItems.Where(k => k.KarteEdition.HokenGroups != null && k.KarteEdition.HokenGroups.Any() && k.KarteEdition.KarteHistories != null && k.KarteEdition.KarteHistories.Any()).ToList();
    }

    public GetMedicalExaminationHistoryOutputData GetDataKarte2(GetDataPrintKarte2InputData inputData)
    {
        try
        {
            var patientInfo = _patientInforRepository.GetById(inputData.HpId, inputData.PtId, inputData.SinDate, 0);
            var insuranceModels = _insuranceRepository.GetInsuranceSummaryList(inputData.HpId, inputData.PtId, inputData.SinDate, true);

            (int totalCount, int totalKeyWordMatched, List<HistoryOrderModel> historyOrderModelList) historyList = new();
            if (!inputData.EmptyMode)
            {
                historyList = _historyOrderRepository.GetList(inputData.HpId,
                                                              inputData.PtId,
                                                              inputData.SinDate,
                                                              inputData.StartDate,
                                                              inputData.EndDate,
                                                              1,
                                                              1,
                                                              1,
                                                              null,
                                                              inputData.IncludeDraft,
                                                              insuranceModels,
                                                              isCheckedJihi: inputData.IsCheckedJihi
                                                              );
            }
            List<SinKouiListModel> sinkouiList = new();
            if (historyList.historyOrderModelList != null)
            {
                var raiinNoList = inputData.RaiinNo > 0 ? new List<long>() { inputData.RaiinNo } : historyList.historyOrderModelList.Select(item => item.RaiinNo).Distinct().ToList();
                var mainHokenPIdList = historyList.historyOrderModelList.Select(item => item.HokenPid).Distinct().ToList();
                var sindateList = historyList.historyOrderModelList.Select(item => item.SinDate).Distinct().ToList();
                sinkouiList = _historyOrderRepository.GetSinkouiList(inputData.HpId,
                                                                     inputData.PtId,
                                                                     sindateList,
                                                                     raiinNoList,
                                                                     mainHokenPIdList);
            }
            if (inputData.RaiinNo > 0 && historyList.historyOrderModelList != null)
            {
                historyList.historyOrderModelList = historyList.historyOrderModelList.Where(item => item.RaiinNo == inputData.RaiinNo).ToList();
                historyList.totalCount = historyList.historyOrderModelList.Count;
            }
            var result = GetHistoryOutput(inputData.HpId, inputData.PtId, inputData.SinDate, historyList, sinkouiList);
            List<HistoryKarteOdrRaiinItem> historyKarteOdrRaiinList = result.RaiinfList.OrderBy(r => r.SinDate).ThenBy(r => r.RaiinNo).ToList();
            FilterData(ref historyKarteOdrRaiinList, inputData);

            // Get Version Data
            if (inputData.IsGetVersionData)
            {
                if (inputData.RaiinNo > 0)
                {
                    (int totalCountVersion, List<HistoryOrderVersionModel> historyOrderVersionModelList) historyListVersion = _historyOrderRepository.GetListVersion(
                        inputData.RaiinNo,
                        inputData.HpId,
                        0,
                        inputData.PtId,
                        inputData.SinDate,
                        0,
                        0,
                        1,
                        inputData.IncludeDraft
                    );
                    GetMedicalExaminationHistoryVersionOutputData versionData = GetHistoryVersionOutput(inputData.HpId, inputData.PtId, inputData.SinDate, historyListVersion, new());
                    if (historyKarteOdrRaiinList.Count > 0) // only 1 item
                    {
                        historyKarteOdrRaiinList[0].VersionData = versionData;
                    }
                }
                else
                {
                    Dictionary<long, List<HistoryOrderVersionModel>> allVersionData = _historyOrderRepository.GetListVersionAllRaiinNo(inputData.HpId, inputData.PtId, inputData.SinDate, 1, inputData.IncludeDraft);
                    foreach (HistoryKarteOdrRaiinItem history in historyKarteOdrRaiinList)
                    {
                        allVersionData.TryGetValue(history.RaiinNo, out List<HistoryOrderVersionModel>? orderInfListTemp);
                        GetMedicalExaminationHistoryVersionOutputData? versionData = orderInfListTemp != null ? GetHistoryVersionOutput(inputData.HpId, inputData.PtId, inputData.SinDate, (orderInfListTemp.Count, orderInfListTemp), new()) : null;

                        if (versionData != null)
                        {
                            // order by RaiinfList by Edition
                            versionData.RaiinfList = versionData.RaiinfList.OrderByDescending(r => r.KarteEdition.Edition).ToList();
                            history.VersionData = versionData;
                        }
                    }
                }
            }

            return new GetMedicalExaminationHistoryOutputData(result.Total, historyKarteOdrRaiinList, GetMedicalExaminationHistoryStatus.Successed, 0, inputData, patientInfo ?? new(), string.Empty, 0, 0);
        }
        finally
        {
            ReleaseResources();
        }
    }

    private int ParseInt(string input)
    {
        try
        {
            return int.Parse(input);
        }
        catch
        {
            return 0;
        }
    }

    #region private function
    /// <summary>
    /// Excute Order
    /// </summary>
    /// <param name="insuranceData"></param>
    /// <param name="allOdrInfs"></param>
    /// <param name="historyKarteOdrRaiin"></param>
    /// <param name="historyKarteOdrRaiins"></param>
    private static void ExcuteOrder(List<InsuranceSummaryModel> insuranceData, List<OrdInfModel> orderInfList, HistoryKarteOdrRaiinItem historyKarteOdrRaiin, ConcurrentBag<HistoryKarteOdrRaiinItem> historyKarteOdrRaiins, List<SinKouiListModel> sinkouiList)
    {
        var odrInfListByRaiinNo = orderInfList.OrderBy(odr => odr.OdrKouiKbn)
                                  .ThenBy(odr => odr.RpNo)
                                  .ThenBy(odr => odr.RpEdaNo)
                                  .ThenBy(odr => odr.SortNo)
                                  .ToList();

        // Find By Hoken
        List<int> hokenPidList = odrInfListByRaiinNo.GroupBy(odr => odr.HokenPid).Select(grp => grp.Key).ToList();
        bool existGairaiKanriKasan = false;
        DateTime? createDate = null;
        int? createId = null;
        string? createName = null;

        foreach (var hokenPid in hokenPidList)
        {
            var hoken = insuranceData?.FirstOrDefault(c => c.HokenPid == hokenPid);
            var hokenGrp = new HokenGroupHistoryItem(hokenPid, hoken == null ? string.Empty : hoken.HokenName, new List<GroupOdrGHistoryItem>());

            var groupOdrInfList = odrInfListByRaiinNo.Where(odr => odr.HokenPid == hokenPid)
                .GroupBy(odr => new
                {
                    odr.HokenPid,
                    odr.GroupKoui,
                    odr.InoutKbn,
                    odr.SyohoSbt,
                    odr.SikyuKbn,
                    odr.TosekiKbn,
                    odr.SanteiKbn
                })
                .Select(grp => grp.FirstOrDefault())
                .ToList();
            foreach (var groupOdrInf in groupOdrInfList)
            {
                if (groupOdrInf != null)
                {
                    sinkouiList = sinkouiList.Where(p => p.RaiinNo == groupOdrInf.RaiinNo && p.SinDate == groupOdrInf.SinDate && p.HokenPid == groupOdrInf.HokenPid && p.IsNodspKarte == 0).ToList();
                }

                var group = new GroupOdrGHistoryItem(hokenPid, string.Empty, new List<OdrInfHistoryItem>());

                var rpOdrInfs = odrInfListByRaiinNo.Where(odrInf => odrInf.HokenPid == hokenPid
                                                && odrInf.GroupKoui.Value == groupOdrInf?.GroupKoui.Value
                                                && odrInf.InoutKbn == groupOdrInf?.InoutKbn
                                                && odrInf.SyohoSbt == groupOdrInf?.SyohoSbt
                                                && odrInf.SikyuKbn == groupOdrInf?.SikyuKbn
                                                && odrInf.TosekiKbn == groupOdrInf?.TosekiKbn
                                                && odrInf.SanteiKbn == groupOdrInf?.SanteiKbn)
                                            .ToList();
                foreach (var rpOdrInf in rpOdrInfs.OrderBy(c => c.IsDeleted))
                {
                    if (rpOdrInf.IsDeleted == DeleteTypes.None && rpOdrInf.OrdInfDetails.Any(p => p.ItemCd == ItemCdConst.GairaiKanriKasan || p.ItemCd == ItemCdConst.GairaiKanriKasanRousai))
                    {
                        existGairaiKanriKasan = true;
                    }

                    if (rpOdrInf.OdrKouiKbn == 10)
                    {
                        createDate = rpOdrInf.CreateDate;
                        createId = rpOdrInf.CreateId;
                        createName = rpOdrInf.CreateName;
                    }
                    var odrModel = new OdrInfHistoryItem(
                                                    rpOdrInf.HpId,
                                                    rpOdrInf.RaiinNo,
                                                    rpOdrInf.RpNo,
                                                    rpOdrInf.RpEdaNo,
                                                    rpOdrInf.PtId,
                                                    rpOdrInf.SinDate,
                                                    rpOdrInf.HokenPid,
                                                    rpOdrInf.OdrKouiKbn,
                                                    rpOdrInf.RpName,
                                                    rpOdrInf.InoutKbn,
                                                    rpOdrInf.SikyuKbn,
                                                    rpOdrInf.SyohoSbt,
                                                    rpOdrInf.SanteiKbn,
                                                    rpOdrInf.TosekiKbn,
                                                    rpOdrInf.DaysCnt,
                                                    rpOdrInf.SortNo,
                                                    rpOdrInf.Id,
                                                    rpOdrInf.GroupKoui.Value,
                                                    rpOdrInf.OrdInfDetails.OrderBy(o => o.RowNo).Select(od =>
                                                        new OdrInfDetailItem(
                                                            od.HpId,
                                                            od.RaiinNo,
                                                            od.RpNo,
                                                            od.RpEdaNo,
                                                            od.RowNo,
                                                            od.PtId,
                                                            od.SinDate,
                                                            od.SinKouiKbn,
                                                            od.ItemCd,
                                                            od.ItemName,
                                                            od.DisplayItemName,
                                                            od.Suryo,
                                                            od.UnitName,
                                                            od.UnitSbt,
                                                            od.TermVal,
                                                            od.KohatuKbn,
                                                            od.SyohoKbn,
                                                            od.SyohoLimitKbn,
                                                            od.DrugKbn,
                                                            od.YohoKbn,
                                                            od.Kokuji1,
                                                            od.Kokuji2,
                                                            od.IsNodspRece,
                                                            od.IpnCd,
                                                            od.IpnName,
                                                            od.JissiKbn,
                                                            od.JissiDate,
                                                            od.JissiId,
                                                            od.JissiMachine,
                                                            od.ReqCd,
                                                            od.Bunkatu,
                                                            od.CmtName,
                                                            od.CmtOpt,
                                                            od.FontColor,
                                                            od.CommentNewline,
                                                            od.Yakka,
                                                            od.IsGetPriceInYakka,
                                                            od.Ten,
                                                            od.BunkatuKoui,
                                                            od.AlternationIndex,
                                                            od.KensaGaichu,
                                                            od.OdrTermVal,
                                                            od.CnvTermVal,
                                                            od.YjCd,
                                                            od.MasterSbt,
                                                            od.YohoSets,
                                                            od.Kasan1,
                                                            od.Kasan2,
                                                            od.CnvUnitName,
                                                            od.OdrUnitName,
                                                            od.HasCmtName,
                                                            od.CenterItemCd1,
                                                            od.CenterItemCd2,
                                                            od.CmtColKeta1,
                                                            od.CmtColKeta2,
                                                            od.CmtColKeta3,
                                                            od.CmtColKeta4,
                                                            od.CmtCol1,
                                                            od.CmtCol2,
                                                            od.CmtCol3,
                                                            od.CmtCol4,
                                                            od.HandanGrpKbn,
                                                            od.IsKensaMstEmpty,
                                                            od.BikoComment,
                                                            od.BuiKbn,
                                                            od.RousaiKbn,
                                                            od.CenterCd,
                                                            od.CenterName,
                                                            od.RelationItem
                                                    )
                                                    ).OrderBy(odrDetail => odrDetail.RpNo)
                                                    .ThenBy(odrDetail => odrDetail.RpEdaNo)
                                                    .ThenBy(odrDetail => odrDetail.RowNo).ToList(),
                                                    rpOdrInf.CreateDate,
                                                    rpOdrInf.CreateId,
                                                    rpOdrInf.CreateName,
                                                    rpOdrInf.UpdateDate,
                                                    rpOdrInf.IsDeleted,
                                                    rpOdrInf.CreateMachine,
                                                    rpOdrInf.UpdateMachine,
                                                    rpOdrInf.UpdateName
                                                 );

                    group.OdrInfs.Add(odrModel);
                }
                hokenGrp.GroupOdrItems.Add(group);
                if (!existGairaiKanriKasan)
                {
                    var sinkouiGairaiKanriKasan = sinkouiList.FirstOrDefault(p => p.ItemCd == ItemCdConst.GairaiKanriKasan || p.ItemCd == ItemCdConst.GairaiKanriKasanRousai);
                    if (sinkouiGairaiKanriKasan != null)
                    {
                        existGairaiKanriKasan = true;
                        var odrModel = new OdrInfHistoryItem(
                                                        groupOdrInf?.HpId ?? 0,
                                                        groupOdrInf?.RaiinNo ?? 0,
                                                        0,
                                                        0,
                                                        groupOdrInf?.PtId ?? 0,
                                                        groupOdrInf?.SinDate ?? 0,
                                                        groupOdrInf?.HokenPid ?? 0,
                                                        sinkouiGairaiKanriKasan?.SinKouiKbn ?? 0,
                                                        string.Empty,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        new() {
                                                            new OdrInfDetailItem(
                                                                groupOdrInf?.HpId??0,
                                                                groupOdrInf ?.RaiinNo ?? 0,
                                                                0,
                                                                0,
                                                                0,
                                                                groupOdrInf ?.PtId ?? 0,
                                                                groupOdrInf ?.SinDate ?? 0,
                                                                sinkouiGairaiKanriKasan?.SinKouiKbn??0,
                                                                sinkouiGairaiKanriKasan?.TenItemCd??string.Empty,
                                                                sinkouiGairaiKanriKasan?.Name??string.Empty,
                                                                sinkouiGairaiKanriKasan?.Name??string.Empty,
                                                                0,
                                                                string.Empty,
                                                                0,
                                                                0,
                                                                sinkouiGairaiKanriKasan?.KohatuKbn??0,
                                                                0,
                                                                0,
                                                                sinkouiGairaiKanriKasan?.DrugKbn??0,
                                                                0,
                                                                string.Empty,
                                                                string.Empty,
                                                                0,
                                                                sinkouiGairaiKanriKasan?.IpnNameCd??string.Empty,
                                                                string.Empty,
                                                                0,
                                                                DateTime.MinValue,
                                                                0,
                                                                string.Empty,
                                                                string.Empty,
                                                                string.Empty,
                                                                string.Empty,
                                                                string.Empty,
                                                                string.Empty,
                                                                0,
                                                                0,
                                                                false,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                string.Empty,
                                                                string.Empty,
                                                                new(),
                                                                0,
                                                                0,
                                                                string.Empty,
                                                                string.Empty,
                                                                false,
                                                                string.Empty,
                                                                string.Empty,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                false,
                                                                0
                                                        )
                                                        },
                                                        createDate ?? sinkouiGairaiKanriKasan?.CreateDate ?? DateTime.MinValue,
                                                        createId ?? sinkouiGairaiKanriKasan?.CreateId ?? 0,
                                                        createName ?? sinkouiGairaiKanriKasan?.CreateName ?? string.Empty,
                                                        createDate ?? sinkouiGairaiKanriKasan?.CreateDate ?? DateTime.MinValue,
                                                        0,
                                                        string.Empty,
                                                        string.Empty,
                                                        createName ?? sinkouiGairaiKanriKasan?.CreateName ?? string.Empty
                                                     );

                        List<OdrInfHistoryItem> listOdrInfHistoryItem = new()
                        {
                            odrModel
                        };

                        hokenGrp.GroupOdrItems.Add(new GroupOdrGHistoryItem(listOdrInfHistoryItem));
                    }
                }
            }
            historyKarteOdrRaiin.KarteEdition.HokenGroups.Add(hokenGrp);
        }
        historyKarteOdrRaiins.Add(historyKarteOdrRaiin);
    }

    private static void ExcuteOrderVersion(List<InsuranceModel> insuranceData, List<DelOrdInfModel> orderInfList, HistoryKarteOdrRaiinItem historyKarteOdrRaiin, List<HistoryKarteOdrRaiinItem> historyKarteOdrRaiins, List<SinKouiListModel> sinkouiList)
    {
        var odrInfListByRaiinNo = orderInfList.OrderBy(odr => odr.OdrKouiKbn)
                                  .ThenBy(odr => odr.RpNo)
                                  .ThenBy(odr => odr.RpEdaNo)
                                  .ThenBy(odr => odr.SortNo)
                                  .ToList();

        // Find By Hoken
        List<int> hokenPidList = odrInfListByRaiinNo.GroupBy(odr => odr.HokenPid).Select(grp => grp.Key).ToList();
        bool existGairaiKanriKasan = false;
        DateTime? createDate = null;
        int? createId = null;
        string? createName = null;

        foreach (var hokenPid in hokenPidList)
        {
            var hoken = insuranceData?.FirstOrDefault(c => c.HokenPid == hokenPid);
            var hokenGrp = new HokenGroupHistoryItem(hokenPid, hoken == null ? string.Empty : hoken.HokenName, new List<GroupOdrGHistoryItem>());

            var groupOdrInfList = odrInfListByRaiinNo.Where(odr => odr.HokenPid == hokenPid)
                .GroupBy(odr => new
                {
                    odr.HokenPid,
                    odr.GroupKoui,
                    odr.InoutKbn,
                    odr.SyohoSbt,
                    odr.SikyuKbn,
                    odr.TosekiKbn,
                    odr.SanteiKbn
                })
                .Select(grp => grp.FirstOrDefault())
                .ToList();
            foreach (var groupOdrInf in groupOdrInfList)
            {
                if (groupOdrInf != null)
                {
                    sinkouiList = sinkouiList.Where(p => p.RaiinNo == groupOdrInf.RaiinNo && p.SinDate == groupOdrInf.SinDate && p.HokenPid == groupOdrInf.HokenPid && p.IsNodspKarte == 0).ToList();
                }

                var group = new GroupOdrGHistoryItem(hokenPid, string.Empty, new List<OdrInfHistoryItem>());

                var rpOdrInfs = odrInfListByRaiinNo.Where(odrInf => odrInf.HokenPid == hokenPid
                                                && odrInf.GroupKoui.Value == groupOdrInf?.GroupKoui.Value
                                                && odrInf.InoutKbn == groupOdrInf?.InoutKbn
                                                && odrInf.SyohoSbt == groupOdrInf?.SyohoSbt
                                                && odrInf.SikyuKbn == groupOdrInf?.SikyuKbn
                                                && odrInf.TosekiKbn == groupOdrInf?.TosekiKbn
                                                && odrInf.SanteiKbn == groupOdrInf?.SanteiKbn)
                                            .ToList();
                foreach (var rpOdrInf in rpOdrInfs.OrderBy(c => c.IsDeleted))
                {
                    if (rpOdrInf.IsDeleted == DeleteTypes.None && rpOdrInf.DelOrdInfDetails.Any(p => p.ItemCd == ItemCdConst.GairaiKanriKasan || p.ItemCd == ItemCdConst.GairaiKanriKasanRousai))
                    {
                        existGairaiKanriKasan = true;
                    }

                    if (rpOdrInf.OdrKouiKbn == 10)
                    {
                        createDate = rpOdrInf.CreateDate;
                        createId = rpOdrInf.CreateId;
                        createName = string.Empty;
                    }
                    var odrModel = new OdrInfHistoryItem(
                                                    rpOdrInf.HpId,
                                                    rpOdrInf.RaiinNo,
                                                    rpOdrInf.RpNo,
                                                    rpOdrInf.RpEdaNo,
                                                    rpOdrInf.PtId,
                                                    rpOdrInf.SinDate,
                                                    rpOdrInf.HokenPid,
                                                    rpOdrInf.OdrKouiKbn,
                                                    rpOdrInf.RpName,
                                                    rpOdrInf.InoutKbn,
                                                    rpOdrInf.SikyuKbn,
                                                    rpOdrInf.SyohoSbt,
                                                    rpOdrInf.SanteiKbn,
                                                    rpOdrInf.TosekiKbn,
                                                    rpOdrInf.DaysCnt,
                                                    rpOdrInf.SortNo,
                                                    rpOdrInf.Id,
                                                    rpOdrInf.GroupKoui.Value,
                                                    rpOdrInf.DelOrdInfDetails.OrderBy(o => o.RowNo).Select(od =>
                                                        new OdrInfDetailItem(
                                                            od.HpId,
                                                            od.RaiinNo,
                                                            od.RpNo,
                                                            od.RpEdaNo,
                                                            od.RowNo,
                                                            od.PtId,
                                                            od.SinDate,
                                                            od.SinKouiKbn,
                                                            od.ItemCd,
                                                            od.ItemName,
                                                            od.DisplayItemName,
                                                            od.Suryo,
                                                            od.UnitName,
                                                            od.UnitSbt,
                                                            od.TermVal,
                                                            od.KohatuKbn,
                                                            od.SyohoKbn,
                                                            od.SyohoLimitKbn,
                                                            od.DrugKbn,
                                                            od.YohoKbn,
                                                            od.Kokuji1,
                                                            od.Kokuji2,
                                                            od.IsNodspRece,
                                                            od.IpnCd,
                                                            od.IpnName,
                                                            od.JissiKbn,
                                                            od.JissiDate,
                                                            od.JissiId,
                                                            od.JissiMachine,
                                                            od.ReqCd,
                                                            od.Bunkatu,
                                                            od.CmtName,
                                                            od.CmtOpt,
                                                            od.FontColor,
                                                            od.CommentNewline,
                                                            od.Yakka,
                                                            od.IsGetPriceInYakka,
                                                            od.Ten,
                                                            od.BunkatuKoui,
                                                            od.AlternationIndex,
                                                            od.KensaGaichu,
                                                            od.OdrTermVal,
                                                            od.CnvTermVal,
                                                            od.YjCd,
                                                            od.MasterSbt,
                                                            od.YohoSets,
                                                            od.Kasan1,
                                                            od.Kasan2,
                                                            od.CnvUnitName,
                                                            od.OdrUnitName,
                                                            od.HasCmtName,
                                                            od.CenterItemCd1,
                                                            od.CenterItemCd2,
                                                            od.CmtColKeta1,
                                                            od.CmtColKeta2,
                                                            od.CmtColKeta3,
                                                            od.CmtColKeta4,
                                                            od.CmtCol1,
                                                            od.CmtCol2,
                                                            od.CmtCol3,
                                                            od.CmtCol4,
                                                            od.HandanGrpKbn,
                                                            od.IsKensaMstEmpty,
                                                            od.BikoComment,
                                                            od.BuiKbn,
                                                            od.Rousai,
                                                            od.CenterCd,
                                                            od.CenterName,
                                                            od.RelationItem
                                                    )
                                                    ).ToList(),
                                                    rpOdrInf.CreateDate,
                                                    rpOdrInf.CreateId,
                                                    string.Empty,
                                                    rpOdrInf.UpdateDate,
                                                    rpOdrInf.IsDeleted,
                                                    rpOdrInf.CreateMachine,
                                                    rpOdrInf.UpdateMachine,
                                                    string.Empty
                                                 );

                    group.OdrInfs.Add(odrModel);
                }
                hokenGrp.GroupOdrItems.Add(group);
                if (!existGairaiKanriKasan)
                {
                    var sinkouiGairaiKanriKasan = sinkouiList.FirstOrDefault(p => p.ItemCd == ItemCdConst.GairaiKanriKasan || p.ItemCd == ItemCdConst.GairaiKanriKasanRousai);
                    if (sinkouiGairaiKanriKasan != null)
                    {
                        existGairaiKanriKasan = true;
                        var odrModel = new OdrInfHistoryItem(
                                                        groupOdrInf?.HpId ?? 0,
                                                        groupOdrInf?.RaiinNo ?? 0,
                                                        0,
                                                        0,
                                                        groupOdrInf?.PtId ?? 0,
                                                        groupOdrInf?.SinDate ?? 0,
                                                        groupOdrInf?.HokenPid ?? 0,
                                                        sinkouiGairaiKanriKasan?.SinKouiKbn ?? 0,
                                                        string.Empty,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        0,
                                                        new() {
                                                            new OdrInfDetailItem(
                                                                groupOdrInf?.HpId??0,
                                                                groupOdrInf ?.RaiinNo ?? 0,
                                                                0,
                                                                0,
                                                                0,
                                                                groupOdrInf ?.PtId ?? 0,
                                                                groupOdrInf ?.SinDate ?? 0,
                                                                sinkouiGairaiKanriKasan?.SinKouiKbn??0,
                                                                sinkouiGairaiKanriKasan?.TenItemCd??string.Empty,
                                                                sinkouiGairaiKanriKasan?.Name??string.Empty,
                                                                sinkouiGairaiKanriKasan?.Name??string.Empty,
                                                                0,
                                                                string.Empty,
                                                                0,
                                                                0,
                                                                sinkouiGairaiKanriKasan?.KohatuKbn??0,
                                                                0,
                                                                0,
                                                                sinkouiGairaiKanriKasan?.DrugKbn??0,
                                                                0,
                                                                string.Empty,
                                                                string.Empty,
                                                                0,
                                                                sinkouiGairaiKanriKasan?.IpnNameCd??string.Empty,
                                                                string.Empty,
                                                                0,
                                                                DateTime.MinValue,
                                                                0,
                                                                string.Empty,
                                                                string.Empty,
                                                                string.Empty,
                                                                string.Empty,
                                                                string.Empty,
                                                                string.Empty,
                                                                0,
                                                                0,
                                                                false,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                string.Empty,
                                                                string.Empty,
                                                                new(),
                                                                0,
                                                                0,
                                                                string.Empty,
                                                                string.Empty,
                                                                false,
                                                                string.Empty,
                                                                string.Empty,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                0,
                                                                false,
                                                                0
                                                        )
                                                        },
                                                        createDate ?? sinkouiGairaiKanriKasan?.CreateDate ?? DateTime.MinValue,
                                                        createId ?? sinkouiGairaiKanriKasan?.CreateId ?? 0,
                                                        createName ?? sinkouiGairaiKanriKasan?.CreateName ?? string.Empty,
                                                        createDate ?? sinkouiGairaiKanriKasan?.CreateDate ?? DateTime.MinValue,
                                                        0,
                                                        string.Empty,
                                                        string.Empty,
                                                        createName ?? sinkouiGairaiKanriKasan?.CreateName ?? string.Empty
                                                     );

                        List<OdrInfHistoryItem> listOdrInfHistoryItem = new()
                        {
                            odrModel
                        };

                        hokenGrp.GroupOdrItems.Add(new GroupOdrGHistoryItem(listOdrInfHistoryItem));
                    }
                }
            }
            historyKarteOdrRaiin.KarteEdition.HokenGroups.Add(hokenGrp);
        }
        historyKarteOdrRaiins.Add(historyKarteOdrRaiin);
    }
    #endregion

    public void ReleaseResources()
    {
        _historyOrderRepository.ReleaseResource();
        _insuranceRepository.ReleaseResource();
        _patientInforRepository.ReleaseResource();
        _userRepository.ReleaseResource();
    }
}
