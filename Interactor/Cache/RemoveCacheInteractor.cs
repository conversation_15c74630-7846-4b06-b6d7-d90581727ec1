﻿using Domain.Models.Cacche;
using UseCase.Cache.RemoveCache;
using CalculateService.Interface;

namespace Interactor.Cache;

public class RemoveCacheInteractor : IRemoveCacheInputPort
{
    private readonly IRemoveCacheRepository _removeCacheRepository;
    private readonly IEmrLogger _emrLogger;

    public RemoveCacheInteractor(IRemoveCacheRepository removeCacheRepository, IEmrLogger emrLogger)
    {
        _removeCacheRepository = removeCacheRepository;
        _emrLogger = emrLogger;
    }
    public RemoveCacheOutputData Handle(RemoveCacheInputData inputData)
    {
        var success = _removeCacheRepository.RemoveCache(inputData.Key);

        if (!success)
        {
            _emrLogger.WriteLogWarn(this, "RemoveCache", $"Failed to remove cache: {inputData.Key}");
        }
        // エラーを返されても顧客にできることはないので、全て成功として返す
        return new RemoveCacheOutputData(RemoveCacheStatus.Successed);
    }
}
