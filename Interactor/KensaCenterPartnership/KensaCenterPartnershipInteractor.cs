﻿using Domain.Models.KensaCenterPartnership;
using UseCase.KensaCenterPartnership;
using Infrastructure.Repositories;
using CalculateService.Interface;

namespace Interactor.KensaCenterPartnership;

public class GetKensaCenterPartnershipListInteractor : IGetKensaCenterPartnershipInputPort
{
    private readonly IKensaCenterPartnershipRepository _repository;

    public GetKensaCenterPartnershipListInteractor(IKensaCenterPartnershipRepository mstItemRepository)
    {
        _repository = mstItemRepository;
    }

    /// <summary>
    /// hp_id に紐づく検査会社一覧と、それぞれの会社のクリニックとの登録期間を取得する
    /// </summary>
    public GetKensaCenterPartnershipOutputData Handle(GetKensaCenterPartnershipInputData inputData)
    {
        try
        {
            var data = _repository.GetKensaCenterPartnership(
                hpId: inputData.HpId,
                centerCd: inputData.CenterCd
            );
            if (data.Count == 0)
            {
                return new GetKensaCenterPartnershipOutputData(new(), GetKensaCenterPartnershipStatus.NoData);
            }
            else
            {
                return new GetKensaCenterPartnershipOutputData(data, GetKensaCenterPartnershipStatus.Successful);
            }
        }
        finally
        {
            _repository.ReleaseResource();
        }
    }
}

public class RegisterKensaCenterPartnershipInteractor : IRegisterKensaCenterPartnershipInputPort
{
    private readonly IKensaCenterPartnershipRepository _repository;

    public RegisterKensaCenterPartnershipInteractor(IKensaCenterPartnershipRepository mstItemRepository)
    {
        _repository = mstItemRepository;
    }

    /// <summary>
    /// hp_id と center_id を 関係テーブルに一括登録する
    /// </summary>
    public RegisterKensaCenterPartnershipOutputData Handle(RegisterKensaCenterPartnershipInputData inputData)
    {
        try
        {
            if (inputData.EndDate == 0)
            {
                inputData.EndDate = 99999999;
            }

            var result = _repository.RegisterKensaCenterPartnership(inputData.HpId, inputData.CenterCd, inputData.StartDate, inputData.EndDate);
            return new RegisterKensaCenterPartnershipOutputData(result, RegisterKensaCenterPartnershipStatus.Successful);
        }
        catch (KensaCenterPartnershipSpecialException)
        {
            return new RegisterKensaCenterPartnershipOutputData(null!, RegisterKensaCenterPartnershipStatus.AleadyExists);
        }
        finally
        {
            _repository.ReleaseResource();
        }
    }
}

public class UpdateKensaCenterPartnershipInteractor : IUpdateKensaCenterPartnershipInputPort
{
    private readonly IKensaCenterPartnershipRepository _repository;

    public UpdateKensaCenterPartnershipInteractor(IKensaCenterPartnershipRepository mstItemRepository)
    {
        _repository = mstItemRepository;
    }

    /// <summary>
    /// 主に契約期間の更新
    /// </summary>
    public UpdateKensaCenterPartnershipOutputData Handle(UpdateKensaCenterPartnershipInputData inputData)
    {
        try
        {
            var data = _repository.UpdateKensaCenterPartnership(inputData.HpId, inputData.CenterCd, inputData.OldStartDate, inputData.StartDate, inputData.EndDate);
            return new UpdateKensaCenterPartnershipOutputData(data, UpdateKensaCenterPartnershipStatus.Successful);
        }
        finally
        {
            _repository.ReleaseResource();
        }
    }
}

public class UnregisterKensaCenterPartnershipInteractor : IUnregisterKensaCenterPartnershipInputPort
{
    private readonly IKensaCenterPartnershipRepository _repository;
    private readonly IEmrLogger _emrLogger;

    public UnregisterKensaCenterPartnershipInteractor(IKensaCenterPartnershipRepository mstItemRepository, IEmrLogger emrLogger)
    {
        _repository = mstItemRepository;
        _emrLogger = emrLogger;
    }

    /// <summary>
    /// 契約解除
    /// </summary>
    public UnregisterKensaCenterPartnershipOutputData Handle(UnregisterKensaCenterPartnershipInputData inputData)
    {
        try
        {
            bool success = _repository.UnregisterKensaCenterPartnership(inputData.HpId, inputData.CenterCd, inputData.StartDate);
            return new UnregisterKensaCenterPartnershipOutputData(UnregisterKensaCenterPartnershipStatus.Successful);
        }
        catch (KensaCenterPartnershipSpecialException ex)
        {
            _emrLogger.WriteLogWarn(this, "UnregisterKensaCenterPartnership", $"楽観的同時実行制御エラーが発生しました。: {ex.Message}");
            _emrLogger.WriteLogWarn(this, "UnregisterKensaCenterPartnership", $"論理削除されているはずですが、必要に応じて kensa_center_partnerships を確認してください: HpId: {inputData.HpId}, CenterCd: {inputData.CenterCd}, StartDate: {inputData.StartDate}");
            return new UnregisterKensaCenterPartnershipOutputData(UnregisterKensaCenterPartnershipStatus.Successful);
        }
        catch (Exception ex)
        {
            _emrLogger.WriteLogError(this, "UnregisterKensaCenterPartnership", ex);
            return new UnregisterKensaCenterPartnershipOutputData(UnregisterKensaCenterPartnershipStatus.AlreadyDeleted);
        }
        finally
        {
            _repository.ReleaseResource();
        }
    }
}

public class UpdateKensaCenterPartnershipMstUpdateDateInteractor : IUpdateKensaCenterPartnershipMstUpdateDateInputPort
{
    private readonly IKensaCenterPartnershipRepository _repository;

    public UpdateKensaCenterPartnershipMstUpdateDateInteractor(IKensaCenterPartnershipRepository mstItemRepository)
    {
        _repository = mstItemRepository;
    }

    public UpdateKensaCenterPartnershipMstUpdateDateOutputData Handle(UpdateKensaCenterPartnershipMstUpdateDateInputData inputData)
    {
        try
        {
            _repository.UpdateKensaCenterPartnershipMstUpdateDate(inputData.HpId);
            return new UpdateKensaCenterPartnershipMstUpdateDateOutputData(UpdateKensaCenterPartnershipMstUpdateDateStatus.Successful);
        }
        finally
        {
            _repository.ReleaseResource();
        }
    }
} 
