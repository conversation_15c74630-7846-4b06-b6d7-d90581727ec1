﻿using Domain.Models.Reception;
using Entity.Tenant;
using UseCase.Reception.Get;

namespace Interactor.Reception
{
    public class GetReceptionInteractor : IGetReceptionInputPort
    {
        private readonly IReceptionRepository _receptionRepository;
        public GetReceptionInteractor(IReceptionRepository receptionRepository)
        {
            _receptionRepository = receptionRepository;
        }

        public GetReceptionOutputData Handle(GetReceptionInputData inputData)
        {
            try
            {
                if (inputData.RaiinNo <= 0)
                {
                    return new GetReceptionOutputData(new(), GetReceptionStatus.InvalidRaiinNo);
                }

                var receptionModel = _receptionRepository.Get(inputData.HpId, inputData.RaiinNo, inputData.Flag);
                if (receptionModel.HpId == 0 && receptionModel.PtId == 0 && receptionModel.SinDate == 0 && receptionModel.RaiinNo == 0)
                {
                    return new GetReceptionOutputData(new(), GetReceptionStatus.ReceptionNotExisted);
                }

                return new GetReceptionOutputData(new ReceptionDto(
                        receptionModel.HpId,
                        receptionModel.PtId,
                        receptionModel.SName,
                        receptionModel.KaSname,
                        receptionModel.Sex,
                        receptionModel.PtMemo,
                        receptionModel.PrintEpsReference,
                        receptionModel.PrescriptionIssueType,
                        receptionModel.KaId,
                        receptionModel.TantoId,
                        receptionModel.TreatmentDepartmentId,
                        receptionModel.SinDate,
                        receptionModel.YoyakuTime,
                        receptionModel.YoyakuEndTime,
                        receptionModel.IsYoyaku,
                        receptionModel.SinryoKbn,
                        receptionModel.HokenPid,
                        receptionModel.ConfirmationType,
                        receptionModel.InfoConsFlg,
                        receptionModel.PrescriptionDeliInfo,
                        receptionModel.Status,
                        receptionModel.TantoName,
                        receptionModel.KaSname,
                        receptionModel.DepartmentSName,
                        receptionModel.HokenName,
                        receptionModel.CanCombine,
                        receptionModel.RaiinNo,
                        receptionModel.HasMessage,
                        receptionModel.Labels,
                        receptionModel.KateEditionRaiinNo,
                        receptionModel.Reservedetailid,
                        receptionModel.PortalCustomerPharmacy,
                        receptionModel.PrescriptionReceiveMethod,
                        receptionModel.Comment,
                        receptionModel.SyosaisinKbn,
                        receptionModel.JikanKbn,
                        receptionModel.OnlineConfirmationHistory,
                        receptionModel.IsLinkCard,
                        receptionModel.CanEditPrescription,
                        receptionModel.MeetingId
                    ), GetReceptionStatus.Successed);
            }
            finally
            {
                _receptionRepository.ReleaseResource();
            }
        }
    }
}
