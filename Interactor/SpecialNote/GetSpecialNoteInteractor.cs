﻿using Domain.Models.ExamResults;
using Domain.Models.Family;
using Domain.Models.KarteAllergy;
using Domain.Models.KarteMedicalHistory;
using Domain.Models.KarteVSPHYS;
using Domain.Models.PtCmtInf;
using Domain.Models.SpecialNote.ImportantNote;
using Domain.Models.SpecialNote.PatientInfo;
using Domain.Models.SpecialNote.SummaryInf;
using Domain.Models.Summary;
using Helper.Extension;
using UseCase.SpecialNote.Get;

namespace Interactor.SpecialNote
{
    public class GetSpecialNoteInteractor : IGetSpecialNoteInputPort
    {
        private readonly IKarteAllergyOthersRepository _karteAllergyElseRepository;
        private readonly IKarteAllergyFoodRepository _karteAllergyFoodRepository;
        private readonly IKarteAllergyMedicineRepository _karteAllergyDrugRepository;
        private readonly IImportantNoteRepository _importantNoteRepository1;
        private readonly IImportantNoteRepository _importantNoteRepository2;
        private readonly IImportantNoteRepository _importantNoteRepository3;
        private readonly IImportantNoteRepository _importantNoteRepository4;
        private readonly IKarteMedicalHistoryRepository _karteMedicalHistoryRepository1;
        private readonly IKarteMedicalHistoryRepository _karteMedicalHistoryRepository2;
        private readonly IKarteMedicalHistoryRepository _karteMedicalHistoryRepository3;
        private readonly IFamilyRepository _familyRepository;
        private readonly IKarteVSPHYSRepository _karteVSPHYSRepository;
        private readonly IExamResultsRepository _examResultRepository;
        private readonly ISummaryRepository _summaryRepository;
        private readonly IImportantNoteRepository _importantInfectionRepository;
        private readonly IPtCmtInfRepository _ptCmtInfRepository;
        private readonly IPatientInfoRepository _patientInfoSeikaturekiRepository;
        private readonly IPatientInfoRepository _patientInfoPregnancyRepository;

        public GetSpecialNoteInteractor(IKarteAllergyOthersRepository karteAllergyElseRepository,
            IKarteAllergyFoodRepository karteAllergyFoodRepository,
            IKarteAllergyMedicineRepository karteAllergyDrugRepository,
            IImportantNoteRepository importantNoteRepository1,
            IImportantNoteRepository importantNoteRepository2,
            IImportantNoteRepository importantNoteRepository3,
            IImportantNoteRepository importantNoteRepository4,
            IKarteMedicalHistoryRepository karteMedicalHistoryRepository1,
            IKarteMedicalHistoryRepository karteMedicalHistoryRepository2,
            IKarteMedicalHistoryRepository karteMedicalHistoryRepository3,
            IFamilyRepository familyRepository,
            IKarteVSPHYSRepository karteVSPHYSRepository,
            IExamResultsRepository examResultRepository,
            ISummaryRepository summaryRepository,
            IImportantNoteRepository importantInfectionRepository,
            IPtCmtInfRepository ptCmtInfRepository,
            IPatientInfoRepository patientInfoSeikaturekiRepository,
            IPatientInfoRepository patientInfoPregnancyRepository
            )
        {
            _karteAllergyElseRepository = karteAllergyElseRepository;
            _karteAllergyFoodRepository = karteAllergyFoodRepository;
            _karteAllergyDrugRepository = karteAllergyDrugRepository;
            _importantNoteRepository1 = importantNoteRepository1;
            _importantNoteRepository2 = importantNoteRepository2;
            _importantNoteRepository3 = importantNoteRepository3;
            _importantNoteRepository4 = importantNoteRepository4;
            _karteMedicalHistoryRepository1 = karteMedicalHistoryRepository1;
            _karteMedicalHistoryRepository2 = karteMedicalHistoryRepository2;
            _karteMedicalHistoryRepository3 = karteMedicalHistoryRepository3;
            _familyRepository = familyRepository;
            _karteVSPHYSRepository = karteVSPHYSRepository;
            _examResultRepository = examResultRepository;
            _summaryRepository = summaryRepository;
            _importantInfectionRepository = importantInfectionRepository;
            _ptCmtInfRepository = ptCmtInfRepository;
            _patientInfoSeikaturekiRepository = patientInfoSeikaturekiRepository;
            _patientInfoPregnancyRepository = patientInfoPregnancyRepository;
        }

        public GetSpecialNoteOutputData Handle(GetSpecialNoteInputData inputData)
        {
            try
            {
                if (inputData.PtId <= 0)
                {
                    return new GetSpecialNoteOutputData(GetSpecialNoteStatus.InvalidPtId);
                }
                var taskKarteAllergyTab = Task<KarteAllergyModel>.Factory.StartNew(() => GetKarteAllergyTab(inputData.HpId, inputData.PtId)).GetAwaiter().GetResult();
                var (taskKarteMedicalHistory, taskInfectionList, taskCmtInfItem, taskSeikaturekiInfItem, pregnancyItem) = GetKarteMedicalHistoryModel(inputData.HpId, inputData.PtId);

                if (taskCmtInfItem.Id == 0) taskCmtInfItem = null;
                if (taskSeikaturekiInfItem.Id == 0) taskSeikaturekiInfItem = null;
                var taskSummary = Task<SummaryInfModel>.Factory.StartNew(() => GetSummary(inputData.HpId, inputData.PtId));
                if (taskSummary.Id == 0) taskSummary = null;

                return new GetSpecialNoteOutputData(taskKarteAllergyTab, taskKarteMedicalHistory.OtherDrugs, taskKarteMedicalHistory.OctDrugs, taskKarteMedicalHistory.Supples, taskKarteMedicalHistory.KioRekis,
                                                            taskKarteMedicalHistory.SocialHistorys, taskKarteMedicalHistory.Pregnants, taskKarteMedicalHistory.Families, GetKarteVSPHYSItemList(inputData).PysicalInfoModels,
                                                            GetExamResultItemList(inputData).ExamResultsModels, taskSummary.GetAwaiter().GetResult(),
                                                            GetSpecialNoteStatus.Successed, taskInfectionList, taskCmtInfItem, taskSeikaturekiInfItem, pregnancyItem);
            }
            finally
            {
                _karteAllergyDrugRepository.ReleaseResource();
                _karteAllergyFoodRepository.ReleaseResource();
                _karteAllergyElseRepository.ReleaseResource();
                _importantNoteRepository1.ReleaseResource();
                _importantNoteRepository2.ReleaseResource();
                _importantNoteRepository3.ReleaseResource();
                _importantNoteRepository4.ReleaseResource();
                _karteMedicalHistoryRepository1.ReleaseResource();
                _karteMedicalHistoryRepository2.ReleaseResource();
                _karteMedicalHistoryRepository3.ReleaseResource();
                _familyRepository.ReleaseResource();
                _karteVSPHYSRepository.ReleaseResource();
                _examResultRepository.ReleaseResource();
                _summaryRepository.ReleaseResource();
                _importantInfectionRepository.ReleaseResource();
                _ptCmtInfRepository.ReleaseResource();
                _patientInfoSeikaturekiRepository.ReleaseResource();
                _patientInfoPregnancyRepository.ReleaseResource();
            }
        }

        #region Get data for tab
        private KarteAllergyModel GetKarteAllergyTab(int hpId, long ptId)
        {
            var taskAlrgyElseList = Task<List<PtAlrgyElseModel>>.Factory.StartNew(() => _karteAllergyElseRepository.GetAllergyElseList(hpId, ptId));
            var taskAlrgyFoodList = Task<List<PtAlrgyFoodModel>>.Factory.StartNew(() => _karteAllergyFoodRepository.GetAllergyFoodList(hpId, ptId));
            var taskAlrgyDrugList = Task<List<PtAlrgyDrugModel>>.Factory.StartNew(() => _karteAllergyDrugRepository.GetAllergyDrugList(hpId, ptId));

            Task.WaitAll(taskAlrgyElseList, taskAlrgyFoodList, taskAlrgyDrugList);

            return new KarteAllergyModel(taskAlrgyFoodList.GetAwaiter().GetResult(), taskAlrgyElseList.GetAwaiter().GetResult(), taskAlrgyDrugList.GetAwaiter().GetResult());
        }
        #endregion

        private (KarteMedicalHistoryModel, List<PtInfectionModel>, PtCmtInfModel, SeikaturekiInfModel, List<PtPregnancyModel>) GetKarteMedicalHistoryModel(int hpId, long ptId)
        {
            var taskOtherDrugList = Task<List<PtOtherDrugModel>>.Factory.StartNew(() => _importantNoteRepository1.GetOtherDrugList(hpId, ptId));
            var taskOctDrugList = Task<List<PtOtcDrugModel>>.Factory.StartNew(() => _importantNoteRepository2.GetOtcDrugList(hpId, ptId));
            var taskSuppleList = Task<List<PtSuppleModel>>.Factory.StartNew(() => _importantNoteRepository3.GetSuppleList(hpId, ptId));
            var taskPregnantList = Task<List<PtPregnancyRelatedModel>>.Factory.StartNew(() => _karteMedicalHistoryRepository1.GetPregnantList(hpId, ptId));
            var taskKioRekiList = Task<List<PtKioRekiModel>>.Factory.StartNew(() => _importantNoteRepository4.GetKioRekiList(hpId, ptId));
            var taskSocialHistoryList = Task<List<PtSmokingRelatedModel>>.Factory.StartNew(() => _karteMedicalHistoryRepository2.GetPtSocialHistoryList(hpId, ptId));
            var taskFamilyList = Task<List<PtFamilyRekiModel>>.Factory.StartNew(() => _karteMedicalHistoryRepository3.GetFamilyList(hpId, ptId));
            var taskInfectionList = Task<List<PtInfectionModel>>.Factory.StartNew(() => _importantInfectionRepository.GetInfectionList(hpId, ptId));
            var taskCmtInfItem = Task<PtCmtInfModel>.Factory.StartNew(() => _ptCmtInfRepository.GetList(ptId, hpId).FirstOrDefault() ?? new());
            var taskSeikaturekiInfItem = Task<SeikaturekiInfModel>.Factory.StartNew(() => _patientInfoSeikaturekiRepository.GetSeikaturekiInfList(ptId, hpId).FirstOrDefault() ?? new());
            var taskPregnancyItem = Task<List<PtPregnancyModel>>.Factory.StartNew(() => _patientInfoPregnancyRepository.GetPregnancyList(ptId, hpId) ?? new());
            Task.WaitAll(taskOtherDrugList, taskOctDrugList, taskSuppleList, taskPregnantList, taskKioRekiList, taskSocialHistoryList, taskFamilyList);

            return (new KarteMedicalHistoryModel(taskOtherDrugList.GetAwaiter().GetResult(), taskOctDrugList.GetAwaiter().GetResult(), taskSuppleList.GetAwaiter().GetResult(), taskKioRekiList.GetAwaiter().GetResult(), taskSocialHistoryList.GetAwaiter().GetResult(), taskPregnantList.GetAwaiter().GetResult(), taskFamilyList.GetAwaiter().GetResult()),
                taskInfectionList.GetAwaiter().GetResult(), taskCmtInfItem.GetAwaiter().GetResult(), taskSeikaturekiInfItem.GetAwaiter().GetResult(), taskPregnancyItem.GetAwaiter().GetResult());
        }

        private KarteVSPHYModel GetKarteVSPHYSItemList(GetSpecialNoteInputData inputData)
        {
            return _karteVSPHYSRepository.GetKarteVSPHYSList(inputData.HpId, inputData.PtId, false, 0, 0, inputData.RaiinNo.AsInteger());
        }

        private ExamResultsDto GetExamResultItemList(GetSpecialNoteInputData inputData)
        {
            return _examResultRepository.GetExamResults(inputData.HpId, inputData.PtId, 0, 0, string.Empty, false, new List<string>());
        }

        #region Get data for tab
        private SummaryInfModel GetSummary(int hpId, long ptId)
        {
            var taskSummaryList = Task<SummaryInfModel>.Factory.StartNew(() => _summaryRepository.GetSummary(hpId, ptId));

            Task.WaitAll(taskSummaryList);

            return taskSummaryList.GetAwaiter().GetResult();
        }
        #endregion
    }
}
