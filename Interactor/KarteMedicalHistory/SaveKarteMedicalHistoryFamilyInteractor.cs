﻿using Domain.Models.KarteMedicalHistory;
using UseCase.KarteMedicalHistory.SaveKarteMedicalHistoryFamily;

namespace Interactor.KarteMedicalHistoryFamily
{
    public class SaveKarteMedicalHistoryFamilyInteractor : ISaveKarteMedicalHistoryFamilyInputPort
    {
        private readonly IKarteMedicalHistoryFamilyRepository _karteMedicalHistoryFamilyRepository;

        public SaveKarteMedicalHistoryFamilyInteractor(IKarteMedicalHistoryFamilyRepository karteMedicalHistoryFamilyRepository)
        {
            _karteMedicalHistoryFamilyRepository = karteMedicalHistoryFamilyRepository;
        }

        public SaveKarteMedicalHistoryFamilyOutputData Handle(SaveKarteMedicalHistoryFamilyInputData inputData)
        {
            try
            {
                var status = ValidateInput(inputData);
                if (status != SaveKarteMedicalHistoryFamilyStatus.Successed)
                {
                    return new SaveKarteMedicalHistoryFamilyOutputData(status);
                }

                var result = _karteMedicalHistoryFamilyRepository.SavePtFamilies(inputData.UserId, inputData.HpId, inputData.PtId, inputData.PtFamilyRekis);

                if (!result)
                {
                    return new SaveKarteMedicalHistoryFamilyOutputData(SaveKarteMedicalHistoryFamilyStatus.Failed);
                }

                return new SaveKarteMedicalHistoryFamilyOutputData(SaveKarteMedicalHistoryFamilyStatus.Successed);
            }
            finally
            {
                _karteMedicalHistoryFamilyRepository.ReleaseResource();
            }
        }

        private SaveKarteMedicalHistoryFamilyStatus ValidateInput(SaveKarteMedicalHistoryFamilyInputData inputData)
        {
            if (inputData.HpId <= 0)
            {
                return SaveKarteMedicalHistoryFamilyStatus.InvalidHpId;
            }
            if (inputData.PtId <= 0)
            {
                return SaveKarteMedicalHistoryFamilyStatus.InvalidPtId;
            }

            foreach (var item in inputData.PtFamilyRekis)
            {
                if (item.ZokugaraElse.Length > 20)
                {
                    return SaveKarteMedicalHistoryFamilyStatus.InvalidOtherZokugara;
                }
                if (item.Cmt.Length > 100)
                {
                    return SaveKarteMedicalHistoryFamilyStatus.InvalidCmtLengthNotEquals100;
                }
            }
            return SaveKarteMedicalHistoryFamilyStatus.Successed;
        }
    }
}