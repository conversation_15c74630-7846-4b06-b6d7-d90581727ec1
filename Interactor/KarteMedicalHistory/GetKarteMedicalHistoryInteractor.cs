﻿using Domain.Models.Family;
using Domain.Models.KarteMedicalHistory;
using Domain.Models.SpecialNote.ImportantNote;
using UseCase.KarteMedicalHistory.GetKarteMedicalHistory;

namespace Interactor.KarteMedicalHistory
{
    public class GetKarteMedicalHistoryInteractor : IGetKarteMedicalHistoryInputPort
    {
        private readonly IImportantNoteRepository _importantNoteRepository1;
        private readonly IImportantNoteRepository _importantNoteRepository2;
        private readonly IImportantNoteRepository _importantNoteRepository3;
        private readonly IImportantNoteRepository _importantNoteRepository4;
        private readonly IKarteMedicalHistoryRepository _karteMedicalHistoryRepository1;
        private readonly IKarteMedicalHistoryRepository _karteMedicalHistoryRepository2;
        private readonly IKarteMedicalHistoryRepository _karteMedicalHistoryRepository3;
        private readonly IFamilyRepository _familyRepository;

        public GetKarteMedicalHistoryInteractor(IImportantNoteRepository importantNoteRepository1, IImportantNoteRepository importantNoteRepository2, IImportantNoteRepository importantNoteRepository3, IImportantNoteRepository importantNoteRepository4, IKarteMedicalHistoryRepository karteMedicalHistoryRepository1, IKarteMedicalHistoryRepository karteMedicalHistoryRepository2, IKarteMedicalHistoryRepository karteMedicalHistoryRepository3, IFamilyRepository familyRepository)
        {
            _importantNoteRepository1 = importantNoteRepository1;
            _importantNoteRepository2 = importantNoteRepository2;
            _importantNoteRepository3 = importantNoteRepository3;
            _importantNoteRepository4 = importantNoteRepository4;
            _karteMedicalHistoryRepository1 = karteMedicalHistoryRepository1;
            _karteMedicalHistoryRepository2 = karteMedicalHistoryRepository2;
            _karteMedicalHistoryRepository3 = karteMedicalHistoryRepository3;
            _familyRepository = familyRepository;
        }

        public GetKarteMedicalHistoryOutputData Handle(GetKarteMedicalHistoryInputData inputData)
        {
            try
            {
                if (inputData.HpId <= 0)
                {
                    return new GetKarteMedicalHistoryOutputData(GetKarteMedicalHistoryStatus.InvalidHpId);
                }

                if (inputData.PtId <= 0)
                {
                    return new GetKarteMedicalHistoryOutputData(GetKarteMedicalHistoryStatus.InvalidPtId);
                }

                var taskKarteMedicalHistory = GetKarteMedicalHistoryModel(inputData.HpId, inputData.PtId);

                return new GetKarteMedicalHistoryOutputData(taskKarteMedicalHistory.OtherDrugs, taskKarteMedicalHistory.OctDrugs, taskKarteMedicalHistory.Supples, taskKarteMedicalHistory.KioRekis,
                                                            taskKarteMedicalHistory.SocialHistorys, taskKarteMedicalHistory.Pregnants, taskKarteMedicalHistory.Families, GetKarteMedicalHistoryStatus.Successed);
            }
            finally
            {
                _importantNoteRepository1.ReleaseResource();
                _importantNoteRepository2.ReleaseResource();
                _importantNoteRepository3.ReleaseResource();
                _importantNoteRepository4.ReleaseResource();
                _karteMedicalHistoryRepository1.ReleaseResource();
                _karteMedicalHistoryRepository2.ReleaseResource();
                _karteMedicalHistoryRepository3.ReleaseResource();
                _familyRepository.ReleaseResource();
            }
        }

        private KarteMedicalHistoryModel GetKarteMedicalHistoryModel(int hpId, long ptId)
        {
            var taskOtherDrugList = Task<List<PtOtherDrugModel>>.Factory.StartNew(() => _importantNoteRepository1.GetOtherDrugList(hpId, ptId));
            var taskOctDrugList = Task<List<PtOtcDrugModel>>.Factory.StartNew(() => _importantNoteRepository2.GetOtcDrugList(hpId, ptId));
            var taskSuppleList = Task<List<PtSuppleModel>>.Factory.StartNew(() => _importantNoteRepository3.GetSuppleList(hpId, ptId));
            var taskPregnantList = Task<List<PtPregnancyRelatedModel>>.Factory.StartNew(() => _karteMedicalHistoryRepository1.GetPregnantList(hpId, ptId));
            var taskKioRekiList = Task<List<PtKioRekiModel>>.Factory.StartNew(() => _importantNoteRepository4.GetKioRekiList(hpId, ptId));
            var taskSocialHistoryList = Task<List<PtSmokingRelatedModel>>.Factory.StartNew(() => _karteMedicalHistoryRepository2.GetPtSocialHistoryList(hpId, ptId));
            var taskFamilyList = Task<List<PtFamilyRekiModel>>.Factory.StartNew(() => _karteMedicalHistoryRepository3.GetFamilyList(hpId, ptId));
            Task.WaitAll(taskOtherDrugList, taskOctDrugList, taskSuppleList, taskPregnantList, taskKioRekiList, taskSocialHistoryList, taskFamilyList);

            return new KarteMedicalHistoryModel(taskOtherDrugList.Result, taskOctDrugList.Result, taskSuppleList.Result, taskKioRekiList.Result, taskSocialHistoryList.Result, taskPregnantList.Result, taskFamilyList.Result);
        }
    }
}
