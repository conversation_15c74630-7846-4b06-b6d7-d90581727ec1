﻿using UseCase.FcoLink.SaveDeposits;
using Domain.Models.AuditLog;
using Domain.Models.FcoLink;
using Domain.Models.Reception;
using Helper.Constants;
using Helper.Enum;

namespace Interactor.FcoLink
{
    public class SaveDepositsInteractor : ISaveDepositsInputPort
    {
        private readonly IFcoLinkRepository _fcoLinkRepository;
        private readonly IAuditLogRepository _auditLogRepository;
        private readonly IReceptionRepository _receptionRepository;

        public SaveDepositsInteractor(IFcoLinkRepository fcoLinkRepository, IAuditLogRepository auditLogRepository, IReceptionRepository receptionRepository)
        {
            _fcoLinkRepository = fcoLinkRepository;
            _auditLogRepository = auditLogRepository;
            _receptionRepository = receptionRepository;
        }

        public SaveDepositsOutputData Handle(SaveDepositsInputData inputData)
        {
            try
            {
                var validateResult = ValidateData(inputData);
                if (validateResult != SaveDepositsStatus.ValidateSuccess)
                {
                    return new SaveDepositsOutputData(validateResult);
                }

                // RaiinInf/ SyunoSeikyu/ KaiinInf
                var syunoSeikyuModel = _fcoLinkRepository.GetListSeikyuInf(inputData.HpId, inputData.PtId, inputData.RaiinNo);
                if (!syunoSeikyuModel.Any())
                {
                    return new SaveDepositsOutputData(SaveDepositsStatus.Nodata);
                }

                //親来院番号一意性チェック（システムエラー扱い）
                bool hasOnlyOneType = syunoSeikyuModel.Select(item => item.OyaRaiinNo).Distinct().Count() == 1;
                if (!hasOnlyOneType)
                {
                    return new SaveDepositsOutputData(SaveDepositsStatus.Failed);
                }

                //ステータスチェック(FCO精算待ちで未精算ではない場合)
                var statusCheck = syunoSeikyuModel.Where(item => item.Status == RaiinState.FcoWaiting && item.NyukinKbn != (int)NyukinKbnEnums.Unsettled);
                if (statusCheck.Any())
                {
                    return new SaveDepositsOutputData(SaveDepositsStatus.PaymentNodata);
                }

                //請求金額と入金額のチェック
                var totalFutan = syunoSeikyuModel.Sum(x => x.TotalPtFutan);
                if(totalFutan != inputData.Amount){
                    return new SaveDepositsOutputData(SaveDepositsStatus.UnMatchSeikyuGaku);
                }

                //請求額一致チェック
                foreach (var model in syunoSeikyuModel){
                    if(model.SeikyuGaku != model.NewSeikyuGaku){
                        return new SaveDepositsOutputData(SaveDepositsStatus.ChangePayment);
                    }
                }

                //データ更新
                var updateSeikyuModel = ProcessNyukin(syunoSeikyuModel, inputData.DepositMethod);

                var save = _fcoLinkRepository.SaveChanges() > 0;
                if (save)
                {
                    var receptionInfos = new List<ReceptionForViewDto>();
                    // これで入金された情報を繰り返して更新する
                    foreach (var model in updateSeikyuModel)
                    {
                        AddAuditTrailLog(model.HpId, model.PtId, model.SinDate, model.RaiinNo, 0, model.NyukinDate, model.NyukinGaku);
                        // 成功時に受付情報を取得
                        var receptionInfo = GetUpdatedReceptionInfos(model.HpId, model.SinDate, model.RaiinNo);
                        receptionInfos.AddRange(receptionInfo);
                    }
                    receptionInfos = receptionInfos.DistinctBy(x => x.RaiinNo).ToList();
                    var sameVisitList = new List<SameVisitModel>(); // 現在は空のリスト

                    return new SaveDepositsOutputData(SaveDepositsStatus.Successed, receptionInfos, sameVisitList);
                }
                return new  SaveDepositsOutputData(SaveDepositsStatus.Failed);

            }
            catch (Exception)
            {
                return new  SaveDepositsOutputData(SaveDepositsStatus.Failed);;
            }
            finally
            {
                _fcoLinkRepository.ReleaseResource();
                _receptionRepository.ReleaseResource();
            }
        }

        /// <summary>
        /// 更新された受付情報を取得
        /// </summary>
        /// <param name="hpId">病院ID</param>
        /// <param name="sinDate">診察日</param>
        /// <param name="raiinNos">来院番号リスト</param>
        /// <returns>受付情報リスト</returns>
        private List<ReceptionForViewDto> GetUpdatedReceptionInfos(int hpId, int sinDate, long raiinNo)
        {
            var raiinNos = new List<long>{raiinNo};
            try
            {
                return _receptionRepository.GetReceptionListByRaiinNoes(hpId, sinDate, raiinNos);
            }
            catch (Exception ex)
            {
                // エラー時は空のリストを返す
                Console.WriteLine($"Failed to get reception infos: {ex.Message}");
                return new List<ReceptionForViewDto>();
            }
        }

        private SaveDepositsStatus ValidateData(SaveDepositsInputData input)
        {
            //APIキーチェック
            if(string.IsNullOrEmpty(input.ApiKey)){
                return SaveDepositsStatus.Unauthorized;
            }
            var isFcoApiKey =_fcoLinkRepository.IsExistsFcoApiKeys(input.HpId, input.ApiKey);
            if(!isFcoApiKey){
                return SaveDepositsStatus.Unauthorized;
            }

            // validate simple param
            if (input.HpId < 0 || input.PtId < 0 || input.RaiinNo.Count < 1 ||
                input.Amount < 0 || input.Date < 0)
            {
                return SaveDepositsStatus.InvalidInputData;
            }

            if (!Enum.IsDefined(typeof(DepositMethodEnum), input.DepositMethod))
            {
                return SaveDepositsStatus.Failed;
            }

            return SaveDepositsStatus.ValidateSuccess;
        }


        private List<SyunoSeikyuModel> ProcessNyukin(List<SyunoSeikyuModel> syunoSeikyuModels, int depositMethod)
        {
            var syunoSeikyuList = new List<SyunoSeikyuModel>();
            foreach (var model in syunoSeikyuModels)
            {
                var result =_fcoLinkRepository.FcoSaveNyukin(model, depositMethod);
                if(result != null){
                    syunoSeikyuList.Add(model);
                }
            }
            return syunoSeikyuList;
        }

        #region AddAuditTrailLog
        public void AddAuditTrailLog(int hpId,  long ptId, int sinDate, long raiinNo, int misyu, int nyukinDate, int nyukin)
        {

            var arg = new ArgumentModel(
                            EventCode.FcoLinkSaveNyukin,
                            ptId,
                            sinDate,
                            raiinNo,
                            misyu,
                            nyukinDate,
                            nyukin,
                            1,
                            string.Empty
            );

                _auditLogRepository.AddAuditTrailLog(0, 0, "SYSTEM", arg, hpId);
        }
        #endregion
    }
}
