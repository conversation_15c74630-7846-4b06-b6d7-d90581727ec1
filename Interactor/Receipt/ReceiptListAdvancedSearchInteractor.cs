﻿using Domain.Models.Receipt;
using Domain.Models.Receipt.ReceiptListAdvancedSearch;
using Helper.Enum;
using UseCase.Receipt.ReceiptListAdvancedSearch;
using Microsoft.Extensions.Logging;

namespace Interactor.Receipt;

public class ReceiptListAdvancedSearchInteractor : IReceiptListAdvancedSearchInputPort
{
    private readonly IReceiptRepository _receiptRepository;
    private readonly ILogger<ReceiptListAdvancedSearchInteractor> _logger;

    public ReceiptListAdvancedSearchInteractor(IReceiptRepository receiptRepository, ILogger<ReceiptListAdvancedSearchInteractor> logger)
    {
        _receiptRepository = receiptRepository;
        _logger = logger;
    }

    public ReceiptListAdvancedSearchOutputData Handle(ReceiptListAdvancedSearchInputData inputData)
    {
        try
        {
            _logger.LogInformation("[DEBUG] Handle - Starting receipt list search for HpId: {HpId}, SeikyuYm: {SeikyuYm}",
                inputData.HpId, inputData.SeikyuYm);
            _logger.LogInformation("[DEBUG] Handle - IsAdvanceSearch: {IsAdvanceSearch}, IsAll: {IsAll}, Limit: {Limit}",
                inputData.IsAdvanceSearch, inputData.IsAll, inputData.Limit);
            _logger.LogInformation("[DEBUG] Handle - CursorPtId: {CursorPtId}, CursorSinYm: {CursorSinYm}, CursorHokenId: {CursorHokenId}",
                inputData.CursorPtId, inputData.CursorSinYm, inputData.CursorHokenId);

            var result = _receiptRepository.GetReceiptList(inputData.HpId, inputData.SeikyuYm, ConvertToInputAdvancedSearch(inputData));
            var TotalCount = result?.Count ?? 0;
            var DisplayTensu = result?.Sum(item => item.Tensu) ?? 0;

            _logger.LogInformation("[DEBUG] Handle - Repository returned {TotalCount} records", TotalCount);

            // カーソル・ページネーション
            if (result != null)
            {
                result = PaginateByCursor(result, ConvertToInputAdvancedSearch(inputData));
            }
            else
            {
                result = new List<ReceiptListModel>();
                _logger.LogWarning("[DEBUG] Handle - Repository returned null, using empty list");
            }

            _logger.LogInformation("[DEBUG] Handle - After pagination: {FinalCount} records", result.Count);

            return new ReceiptListAdvancedSearchOutputData(result, TotalCount, DisplayTensu, ReceiptListAdvancedSearchStatus.Successed);
        }
        finally
        {
            _receiptRepository.ReleaseResource();
        }
    }

    public ReceiptListAdvancedSearchInput ConvertToInputAdvancedSearch(ReceiptListAdvancedSearchInputData inputData)
    {
        var itemList = inputData.ItemList.Select(item => new ItemSearchModel(
                                                                                item.ItemCd,
                                                                                item.InputName,
                                                                                item.RangeSeach,
                                                                                item.Amount,
                                                                                item.OrderStatus,
                                                                                item.IsComment
                                                                            ))
                                                                            .ToList();

        var byomeiCdList = inputData.ByomeiCdList.Select(item => new SearchByoMstModel(
                                                                                            item.ByomeiCd,
                                                                                            item.InputName,
                                                                                            item.IsComment
                                                                                       ))
                                                                                       .ToList();

        return new ReceiptListAdvancedSearchInput(
                                                    inputData.IsAdvanceSearch,
                                                    inputData.Tokki,
                                                    inputData.HokenSbts,
                                                    inputData.IsAll,
                                                    inputData.IsNoSetting,
                                                    inputData.IsSystemSave,
                                                    inputData.IsSave1,
                                                    inputData.IsSave2,
                                                    inputData.IsSave3,
                                                    inputData.IsTempSave,
                                                    inputData.IsDone,
                                                    inputData.ReceSbtCenter,
                                                    inputData.ReceSbtRight,
                                                    inputData.HokenHoubetu,
                                                    inputData.Kohi1Houbetu,
                                                    inputData.Kohi2Houbetu,
                                                    inputData.Kohi3Houbetu,
                                                    inputData.Kohi4Houbetu,
                                                    inputData.IsIncludeSingle,
                                                    inputData.HokensyaNoFrom,
                                                    inputData.HokensyaNoTo,
                                                    inputData.HokensyaNoFromLong,
                                                    inputData.HokensyaNoToLong,
                                                    inputData.PtId,
                                                    inputData.PtIdFrom,
                                                    inputData.PtIdTo,
                                                    (PtIdSearchOptionEnum)inputData.PtSearchOption,
                                                    inputData.TensuFrom,
                                                    inputData.TensuTo,
                                                    inputData.LastRaiinDateFrom,
                                                    inputData.LastRaiinDateTo,
                                                    inputData.BirthDayFrom,
                                                    inputData.BirthDayTo,
                                                    itemList,
                                                    (QuerySearchEnum)inputData.ItemQuery,
                                                    inputData.IsOnlySuspectedDisease,
                                                    (QuerySearchEnum)inputData.ByomeiQuery,
                                                    byomeiCdList,
                                                    inputData.IsFutanIncludeSingle,
                                                    inputData.FutansyaNoFromLong,
                                                    inputData.FutansyaNoToLong,
                                                    inputData.KaId,
                                                    inputData.DoctorId,
                                                    inputData.Name,
                                                    inputData.IsTestPatientSearch,
                                                    inputData.IsNotDisplayPrinted,
                                                    inputData.GroupSearchModels,
                                                    inputData.SeikyuKbnAll,
                                                    inputData.SeikyuKbnDenshi,
                                                    inputData.SeikyuKbnPaper
                                                );
    }

    private List<ReceiptListModel> PaginateByCursor(List<ReceiptListModel> searchdata, ReceiptListAdvancedSearchInput searchModel)
    {
        // DEBUG: Log pagination parameters
        _logger.LogInformation("[DEBUG] PaginateByCursor - Total records: {TotalRecords}", searchdata.Count);
        _logger.LogInformation("[DEBUG] PaginateByCursor - Limit: {Limit}", searchModel.Limit);
        _logger.LogInformation("[DEBUG] PaginateByCursor - CursorPtId: {CursorPtId}", searchModel.CursorPtId);
        _logger.LogInformation("[DEBUG] PaginateByCursor - CursorSinYm: {CursorSinYm}", searchModel.CursorSinYm);
        _logger.LogInformation("[DEBUG] PaginateByCursor - CursorHokenId: {CursorHokenId}", searchModel.CursorHokenId);

        // Log first few records for debugging
        if (searchdata.Count > 0)
        {
            _logger.LogInformation("[DEBUG] PaginateByCursor - First 3 records:");
            for (int i = 0; i < Math.Min(3, searchdata.Count); i++)
            {
                var record = searchdata[i];
                _logger.LogInformation("[DEBUG] Record {Index}: PtId={PtId}, SinYm={SinYm}, HokenId={HokenId}, Name={Name}",
                    i, record.PtId, record.SinYm, record.HokenId, record.Name);
            }
        }

        // キー指定でポジション取得
        int startIndex = 0;
        if (searchModel.CursorPtId.HasValue && searchModel.CursorSinYm.HasValue && searchModel.CursorHokenId.HasValue)
        {
            long cursorPtId = searchModel.CursorPtId.Value;
            int cursorSinYm = searchModel.CursorSinYm.Value;
            int cursorHokenId = searchModel.CursorHokenId.Value;

            _logger.LogInformation("[DEBUG] PaginateByCursor - Using cursor: PtId={CursorPtId}, SinYm={CursorSinYm}, HokenId={CursorHokenId}",
                cursorPtId, cursorSinYm, cursorHokenId);

            startIndex = searchdata.FindIndex(x =>
                x.PtId == cursorPtId &&
                x.SinYm == cursorSinYm &&
                x.HokenId == cursorHokenId
            );

            _logger.LogInformation("[DEBUG] PaginateByCursor - Cursor found at index: {StartIndex}", startIndex);

            if (startIndex < 0)
            {
                startIndex = 0; // 見つからなければ先頭から
                _logger.LogInformation("[DEBUG] PaginateByCursor - Cursor not found, starting from beginning");
            }
            else
            {
                startIndex++; // カーソル値の「次」から取得
                _logger.LogInformation("[DEBUG] PaginateByCursor - Starting from index: {StartIndex} (after cursor)", startIndex);
            }
        }
        else
        {
            _logger.LogInformation("[DEBUG] PaginateByCursor - No cursor provided, starting from beginning");
        }
        // 3. 指定件数だけ取得(1000件をデフォルト値とする)
        int limit = 2;
        _logger.LogInformation("[DEBUG] PaginateByCursor - StartIndex: {StartIndex}, Limit: {Limit}", startIndex, limit);

        var result = searchdata.Skip(startIndex).Take(limit).ToList();

        _logger.LogInformation("[DEBUG] PaginateByCursor - Result count: {ResultCount}", result.Count);

        // Log first few results for debugging
        if (result.Count > 0)
        {
            _logger.LogInformation("[DEBUG] PaginateByCursor - First 3 results:");
            for (int i = 0; i < Math.Min(3, result.Count); i++)
            {
                var record = result[i];
                _logger.LogInformation("[DEBUG] Result {Index}: PtId={PtId}, SinYm={SinYm}, HokenId={HokenId}, Name={Name}",
                    i, record.PtId, record.SinYm, record.HokenId, record.Name);
            }
        }

        return result;
    }
}
