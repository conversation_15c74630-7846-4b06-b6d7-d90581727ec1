using Domain.Models.Accounting;
using Domain.Models.AccountDue;
using Domain.Models.AuditLog;
using Domain.Models.HpInf;
using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using Domain.Models.SystemConf;
using Domain.Models.User;
using Domain.Models.Fincode;
using EventProcessor.Interfaces;
// using EventProcessor.Model;
using Helper.Constants;
using Helper.Exceptions;
using Infrastructure.Interfaces;
using Infrastructure.Logger;
using UseCase.Accounting.SaveAccounting;
using static Helper.Constants.UserConst;
using Helper.Enum;
using Microsoft.IdentityModel.Tokens;

namespace Interactor.Accounting
{
    public class SaveAccountingInteractor : ISaveAccountingInputPort
    {
        private readonly IAccountingRepository _accountingRepository;
        private readonly ISystemConfRepository _systemConfRepository;
        private readonly IUserRepository _userRepository;
        private readonly IHpInfRepository _hpInfRepository;
        private readonly IPatientInforRepository _patientInforRepository;
        private readonly IReceptionRepository _receptionRepository;
        private readonly IAuditLogRepository _auditLogRepository;
        private readonly IFincodeRepository _fincodeRepository;
        public SaveAccountingInteractor(IAccountingRepository accountingRepository, ISystemConfRepository systemConfRepository, IUserRepository userRepository, IHpInfRepository hpInfRepository, IPatientInforRepository patientInforRepository, IReceptionRepository receptionRepository, IAuditLogRepository auditLogRepository, IFincodeRepository fincodeRepository)
        {
            _accountingRepository = accountingRepository;
            _systemConfRepository = systemConfRepository;
            _userRepository = userRepository;
            _hpInfRepository = hpInfRepository;
            _patientInforRepository = patientInforRepository;
            _receptionRepository = receptionRepository;
            _auditLogRepository = auditLogRepository;
            _fincodeRepository = fincodeRepository;
        }

        public SaveAccountingOutputData Handle(SaveAccountingInputData inputData)
        {
            try
            {
                var validateResult = ValidateInputData(inputData);
                if (validateResult != SaveAccountingStatus.ValidateSuccess) return new SaveAccountingOutputData(validateResult, new(), new(), new());

                //来院情報取得
                var raiinInfDB = _accountingRepository.GetRaiinInfModel(inputData.HpId, inputData.RaiinNo);
                
                //オンライン診察
                if (raiinInfDB != null && raiinInfDB.IsOnlineTreatment()){
                    //予約IDが存在しないオンライン診療
                    if(raiinInfDB.ReserveDetail?.ReserveDetailId == null){
                        return new SaveAccountingOutputData(SaveAccountingStatus.InputDataNull, new(), new(), new());;
                    }
                    // オンライン診察の請求処理
                    SaveAccountingOutputData saveAccountingOutputData = OnlineSinAccounting(inputData);
                    return saveAccountingOutputData;
                }

                //オンライン診療以外のオンライン決済NG
                if (inputData.PayType == (int)PaymentMethodCdEnum.Online)
                {
                    return new SaveAccountingOutputData(SaveAccountingStatus.InvalidPayType, new(), new(), new());
                }                

                //親の来院でステータスが一時保存以上で精算済みは除く
                var excludedStatuses = new HashSet<int> { RaiinState.Paid, RaiinState.FcoWaiting, RaiinState.Deleted };
                var raiinInfList = _accountingRepository.GetListRaiinInf(inputData.HpId, inputData.PtId, inputData.SinDate, inputData.RaiinNo)
                    .Where(item => !excludedStatuses.Contains(item.Status))
                    .ToList();
                var raiinNoList = raiinInfList.Select(r => r.RaiinNo).ToList();

                //今回の来院の請求情報 
                var listSyunoSeikyu = _accountingRepository.GetListSyunoSeikyu(inputData.HpId, inputData.PtId, inputData.SinDate, raiinNoList);
                listSyunoSeikyu = listSyunoSeikyu.Where(item => item.NyukinKbn == (int)NyukinKbnEnums.Unsettled).ToList();
                if (listSyunoSeikyu.Count < 1)
                {
                    return new SaveAccountingOutputData(SaveAccountingStatus.InputDataNull, new(), new(), new());
                }
                var seikyuGaku = listSyunoSeikyu.Sum(item => item.SeikyuGaku - item.AdjustFutan -
                                                  item.SyunoNyukinModels.Sum(itemNyukin =>
                                                      itemNyukin.NyukinGaku + itemNyukin.AdjustFutan));


                //今回の来院以外の一部精算済みの請求情報
                var listAllSyunoSeikyu = _accountingRepository.GetListSyunoSeikyu(inputData.HpId, inputData.PtId, inputData.SinDate, new List<long>(), true)
                .Where(item => item.NewSeikyuGaku == 0 || item.SeikyuGaku == item.NewSeikyuGaku)
                .ToList();
                //一部精算済みの未収金額合計
                var misyuGaku = listAllSyunoSeikyu.Sum(item => item.SeikyuGaku - item.AdjustFutan -
                                                  item.SyunoNyukinModels.Sum(itemNyukin =>
                                                      itemNyukin.NyukinGaku + itemNyukin.AdjustFutan));


                //GRP_CD:3020 GRP_EDA_NO:0 精算画面の入金額の既定値 0:前回未収分を含む 1:前回未収分を含まない
                bool isPreviousUnpaidIncluded = _systemConfRepository.GetSettingValue(3020, 0, inputData.HpId) == 0;

                List<long> listRaiinNoPrint = new List<long>();

                //免除の場合
                var nyukinGaku = inputData.Credit;
                var adjustFutan = inputData.ThisWari;
                if (inputData.IsDisCharged)
                {
                    nyukinGaku = 0;
                    adjustFutan = 0;
                }

                // 支払方法：FCO連携が選択されている場合。（免除の場合は判定SKIP）
                if (inputData.PayType == (int)PaymentMethodCdEnum.FcoPayment && !inputData.IsDisCharged) {
                    // validation NGならエラー
                    var status = ValidatePaymentMethodFCO(nyukinGaku + adjustFutan, seikyuGaku, misyuGaku);
                    if (status != SaveAccountingStatus.Success) {
                        return new SaveAccountingOutputData(status, new(), new(), new());
                    } 
                    ProcessNyukin(listSyunoSeikyu, inputData.UserId, nyukinGaku, adjustFutan, inputData.PayType, inputData.Comment, inputData.IsDisCharged, inputData.KaikeiTime, listRaiinNoPrint, 0, true, true);
                } 
                 // 支払方法：FCO連携以外は通常処理
                else
                {
                    bool isCreditAndWariEqualToSeikyuAndMisyukin = inputData.Credit + inputData.ThisWari == seikyuGaku + misyuGaku;
                    if (isCreditAndWariEqualToSeikyuAndMisyukin && misyuGaku < 0)
                    {
                        nyukinGaku -= misyuGaku;
                    }

                    //今回の請求に入金
                    int unusedNyukinGaku = ProcessNyukin(listSyunoSeikyu, inputData.UserId, nyukinGaku, adjustFutan, inputData.PayType, inputData.Comment, inputData.IsDisCharged, inputData.KaikeiTime, listRaiinNoPrint, 0, true);
                    if (isCreditAndWariEqualToSeikyuAndMisyukin && misyuGaku < 0)
                    {
                        unusedNyukinGaku += misyuGaku;
                    }

                    //前回未収分に入金
                    if (isPreviousUnpaidIncluded && !inputData.IsDisCharged && misyuGaku != 0 && unusedNyukinGaku != 0)
                    {
                        var nyukinDate = listSyunoSeikyu.Any() ? listSyunoSeikyu.FirstOrDefault().SinDate : 0;
                        ProcessNyukin(listAllSyunoSeikyu, inputData.UserId, unusedNyukinGaku, 0, inputData.PayType, inputData.Comment, inputData.IsDisCharged, inputData.KaikeiTime, listRaiinNoPrint, nyukinDate, false);
                    }
                }

                

                var save = _accountingRepository.SaveChanges() > 0;
                if (save)
                {
                    AddAuditTrailLog(inputData.HpId, inputData.UserId, inputData.PtId, inputData.SinDate, inputData.RaiinNo, misyuGaku, inputData.SinDate, inputData.Credit, inputData.IsDisCharged, inputData.IsOperator, inputData.OperatorName);
                    var receptionInfos = _receptionRepository.GetRecptionList(inputData.HpId, inputData.SinDate, ptId: inputData.PtId);
                    var sameVisitList = _receptionRepository.GetListSameVisit(inputData.HpId, inputData.PtId, inputData.SinDate);
                    return new SaveAccountingOutputData(SaveAccountingStatus.Success, receptionInfos, sameVisitList, listRaiinNoPrint);
                }
                return new SaveAccountingOutputData(SaveAccountingStatus.Failed, new(), new(), new());
            }
            catch (Exception ex)
            {
                string name = GetType().Namespace ?? string.Empty;
                throw new InteractorCustomException(name, ex);
            }
            finally
            {
                _accountingRepository.ReleaseResource();
                _systemConfRepository.ReleaseResource();
                _userRepository.ReleaseResource();
                _hpInfRepository.ReleaseResource();
                _patientInforRepository.ReleaseResource();
                _receptionRepository.ReleaseResource();
                _auditLogRepository.ReleaseResource();
            }
        }

        private int ProcessNyukin(List<SyunoSeikyuModel> syunoSeikyuModels, int userId, int nyukinGaku, int adjustFutan, int payType, string comment, bool isDisCharged, string kaikeiTime, List<long> listRaiinNoPrint, int nyukinDate, bool updateRaiinInf = true, bool isFCO = false)
        {
            foreach (var item in syunoSeikyuModels)
            {
                int thisSeikyuGaku = CalculateUnpaidAmount(item); //未収金の請求額
                int sort = item.SyunoNyukinModels.Select(itemNyukin => itemNyukin.SortNo).DefaultIfEmpty(0).Max() + 1;
                var updateResult = ParseValueUpdate(thisSeikyuGaku, adjustFutan, nyukinGaku, isDisCharged, isFCO);
                adjustFutan = updateResult.RemainingAdjustFutan;
                nyukinGaku = updateResult.RemainingNyukinGaku;
                nyukinDate = updateRaiinInf ? item.SinDate : nyukinDate;
                _accountingRepository.CreateSyunoNyukin(item, sort, nyukinDate, updateResult.AdjustFutan, updateResult.NyukinGaku, payType, comment, userId);
                _accountingRepository.UpdateStatusSyunoSeikyu(item.HpId, item.PtId, item.RaiinNo, updateResult.NyukinKbn, userId);
                if (updateRaiinInf)
                {
                    _accountingRepository.UpdateStatusRaiinInf(item.HpId, item.PtId, item.RaiinNo, userId, kaikeiTime, isFCO);
                }

                listRaiinNoPrint.Add(item.RaiinNo);
            }
            return nyukinGaku;
        }

        private int CalculateUnpaidAmount(SyunoSeikyuModel item)
        {
            int thisNyukinGaku = item.SyunoNyukinModels.Sum(itemNyukin => itemNyukin.NyukinGaku);
            int thisAdjustFutan = item.SyunoNyukinModels.Sum(itemNyukin => itemNyukin.AdjustFutan);
            return item.SeikyuGaku - item.AdjustFutan - thisNyukinGaku - thisAdjustFutan;
        }

        private SettlementResult ParseValueUpdate(int thisSeikyuGaku, int adjustFutan, int nyukinGaku, bool isDisCharged, bool isFCO)
        {
            if (isDisCharged)
            {
                return new SettlementResult(0, 0, (int)NyukinKbnEnums.NoBilling, adjustFutan, nyukinGaku);
            }

            int credit = adjustFutan + nyukinGaku;
            int outAdjustFutan, outNyukinGaku;

            if (thisSeikyuGaku < 0)
            {
                if (credit <= thisSeikyuGaku)
                {
                    outAdjustFutan = Math.Max(adjustFutan, thisSeikyuGaku);
                    outNyukinGaku = thisSeikyuGaku - outAdjustFutan;
                    adjustFutan -= outAdjustFutan;
                    nyukinGaku -= outNyukinGaku;
                }
                else
                {
                    outAdjustFutan = adjustFutan;
                    outNyukinGaku = nyukinGaku;
                    adjustFutan = 0;
                    nyukinGaku = 0;
                }
            }
            else
            {
                if (credit >= thisSeikyuGaku)
                {
                    outAdjustFutan = Math.Min(adjustFutan, thisSeikyuGaku);
                    outNyukinGaku = thisSeikyuGaku - outAdjustFutan;
                    adjustFutan -= outAdjustFutan;
                    nyukinGaku -= outNyukinGaku;
                }
                else
                {
                    outAdjustFutan = adjustFutan;
                    outNyukinGaku = nyukinGaku;
                    adjustFutan = 0;
                    nyukinGaku = 0;
                }
            }

            int outNyukinKbn = thisSeikyuGaku - (outAdjustFutan + outNyukinGaku) == 0
                ? (int)NyukinKbnEnums.FullySettled
                : (int)NyukinKbnEnums.PartiallySettled;

            // FCO連携処理の場合は入金区分を未精算、入金額を0にする。
            if (isFCO) {
                outNyukinKbn = (int)NyukinKbnEnums.Unsettled;
                outNyukinGaku = 0;
            } 

            return new SettlementResult(outAdjustFutan, outNyukinGaku, outNyukinKbn, adjustFutan, nyukinGaku);
        }

        public SaveAccountingStatus ValidateInputData(SaveAccountingInputData inputData)
        {
            if (inputData.HpId <= 0 || !_hpInfRepository.CheckHpId(inputData.HpId))
            {
                return SaveAccountingStatus.InvalidHpId;
            }
            else if (inputData.UserId <= 0 || !_userRepository.CheckExistedUserId(inputData.HpId, inputData.UserId))
            {
                return SaveAccountingStatus.InvalidUserId;
            }
            else if (inputData.PtId <= 0 || !_patientInforRepository.CheckExistIdList(inputData.HpId, new List<long> { inputData.PtId }))
            {
                return SaveAccountingStatus.InvalidPtId;
            }
            else if (inputData.PayType < 0)
            {
                return SaveAccountingStatus.InvalidPayType;
            }
            else if (inputData.Comment.Length > 100)
            {
                return SaveAccountingStatus.InvalidComment;
            }
            else if (inputData.SinDate.ToString().Length != 8)
            {
                return SaveAccountingStatus.InvalidSindate;
            }
            else if (inputData.RaiinNo <= 0)
            {
                return SaveAccountingStatus.InvalidRaiinNo;
            }
            else if (_userRepository.GetPermissionByScreenCode(inputData.HpId, inputData.UserId, FunctionCode.Accounting) != PermissionType.Unlimited)
            {
                return SaveAccountingStatus.NoPermission;
            }
            else if (inputData.SumAdjust > 0)
            {
                if (inputData.Credit > inputData.SumAdjust || inputData.ThisWari > inputData.SumAdjust || (inputData.Credit + inputData.ThisWari) > inputData.SumAdjust)
                {
                    return SaveAccountingStatus.ValidPaymentAmount;
                }
            }
            else if (inputData.SumAdjust < 0)
            {
                if (inputData.Credit < inputData.SumAdjust || inputData.ThisWari < inputData.SumAdjust || (inputData.Credit + inputData.ThisWari) < inputData.SumAdjust)
                {
                    return SaveAccountingStatus.ValidPaymentAmount;
                }
            }
            return SaveAccountingStatus.ValidateSuccess;
        }

        #region AddAuditTrailLog
        private void AddAuditTrailLog(int hpId, int userId, long ptId, int sinDate, long raiinNo, int misyu, int nyukinDate, int nyukin, bool isDisCharged, int isOperator, string operatorName)
        {
            if (isDisCharged)
            {
                var arg = new ArgumentModel(
                                EventCode.DisCharged,
                                ptId,
                                sinDate,
                                raiinNo,
                                misyu,
                                nyukinDate,
                                0,
                                0,
                                string.Empty
                );

                _auditLogRepository.AddAuditTrailLog(userId, isOperator, operatorName, arg, hpId);
            }
            else
            {
                var arg = new ArgumentModel(
                                EventCode.AccountingExecute,
                                ptId,
                                sinDate,
                                raiinNo,
                                misyu,
                                nyukinDate,
                                nyukin,
                                1,
                                string.Empty
                );

                _auditLogRepository.AddAuditTrailLog(userId, isOperator, operatorName, arg, hpId);
            }
        }
        private SaveAccountingOutputData OnlineSinAccounting(SaveAccountingInputData inputData)
        {
            var listSyunoSeikyuDB = _accountingRepository.GetListSyunoSeikyuModel(inputData.HpId, inputData.RaiinNo);
            var raiinInfDB = _accountingRepository.GetRaiinInfModel(inputData.HpId, inputData.RaiinNo);

            //来院情報の取得失敗
            if(raiinInfDB == null){
                return new SaveAccountingOutputData(SaveAccountingStatus.InputDataNull, new(), new(), new());
            }

            //オンライン診療入力値チェック
            var validateOnlinePaymentResult = ValidateOnlinePayment(inputData, raiinInfDB);
            if (validateOnlinePaymentResult != SaveAccountingStatus.ValidateSuccess)
            {
                return new SaveAccountingOutputData(validateOnlinePaymentResult, new(), new(), new());
            }

            //請求情報のチェック
            var validateInvalidNyukinKbnResult = ValidateInvalidNyukinKbn(inputData, listSyunoSeikyuDB);
            if (validateInvalidNyukinKbnResult != SaveAccountingStatus.ValidateSuccess)
            {
                return new SaveAccountingOutputData(validateInvalidNyukinKbnResult, new(), new(), new());
            }
            
            //免除の場合(未収額はオンラインなので0円)
            var misyuGaku = 0;
            var nyukinKbn = listSyunoSeikyuDB?.FirstOrDefault()?.NyukinKbn ?? 0;
            var accountingModel = ConvertToAccountingModel(inputData, nyukinKbn);

            //免除の場合
            var nyukinGaku = accountingModel.NyukinGaku;
            var adjustFutan = accountingModel.AdjustFutan;
            accountingModel.SetNyukinKbn((int)NyukinKbnEnums.FullySettled);
            if (inputData.IsDisCharged)
            {
                nyukinGaku = 0;
                adjustFutan = 0;
                accountingModel.SetNyukinKbn((int)NyukinKbnEnums.NoBilling);
            }

            //1. smartkarute-serverがdenkaru-serverの決済APIを実行する(requestReservePayment)
            //2. denkaru-serverでfincode APIを実行し、payment_clinic_detailのデータを更新（これはdenkaru側の処理実装済み）
            //   現金や免除の場合はfincodeのAPIは実行せずにpayment_clinic_detailのデータを更新するだけ
            //オンライン診療の決済
            long? paymentClinicDetailId = null;
            if (raiinInfDB != null && raiinInfDB.ReserveDetail != null)
            {
                var res = RequestPayment(inputData.HpId, inputData.UserId, inputData.IsDisCharged, nyukinGaku, inputData.PayType, raiinInfDB.ReserveDetail);
                if (res.PaymentClinicDetailId != 0)
                {
                    paymentClinicDetailId = res.PaymentClinicDetailId;
                    if (!string.IsNullOrEmpty(res.ErrorCode))
                    {
                        accountingModel.CardError = true;
                        accountingModel.ErrorCode = res.ErrorCode ?? string.Empty;
                        accountingModel.UserMessage = res.UserMessage ?? string.Empty;
                    }
                }
                else
                {
                    //クレジットカード以外のエラー
                    accountingModel.ErrorCode = res.ErrorCode ?? string.Empty;
                    accountingModel.UserMessage = res.UserMessage ?? string.Empty;
                    return new SaveAccountingOutputData(SaveAccountingStatus.Failed, new(), new(), new(),res.ErrorCode ?? string.Empty, res.UserMessage ?? string.Empty);
                }
            }

            // カードで失敗  入金区分/一部精算
            var nyukinStatus = NyukinStatusEnums.Success;
            if(accountingModel.CardError){
                nyukinStatus = NyukinStatusEnums.Failed;
                accountingModel.SetNyukinKbn((int)NyukinKbnEnums.PartiallySettled);
            }

            // オンライン決済実行
            var result = _accountingRepository.SaveAccounting(
                                                    inputData.HpId,
                                                    inputData.PtId,
                                                    inputData.UserId,
                                                    inputData.RaiinNo,
                                                    inputData.KaikeiTime,
                                                    accountingModel,
                                                    paymentClinicDetailId,
                                                    nyukinGaku,
                                                    adjustFutan,
                                                    (int)nyukinStatus
                                                );

            var save = _accountingRepository.SaveChanges() > 0;
            if (save)
            {
                AddAuditTrailLog(inputData.HpId, inputData.UserId, inputData.PtId, inputData.SinDate, inputData.RaiinNo, misyuGaku, inputData.SinDate, inputData.Credit, inputData.IsDisCharged, inputData.IsOperator, inputData.OperatorName);
                var receptionInfos = _receptionRepository.GetRecptionList(inputData.HpId, inputData.SinDate, ptId: inputData.PtId);
                var sameVisitList = _receptionRepository.GetListSameVisit(inputData.HpId, inputData.PtId, inputData.SinDate);
                var listRaiinNoPrint = new List<long>{inputData.RaiinNo};
                if(accountingModel.CardError){
                    return new SaveAccountingOutputData(SaveAccountingStatus.CardError, receptionInfos, sameVisitList, listRaiinNoPrint, accountingModel.ErrorCode, accountingModel. UserMessage);
                }
                return new SaveAccountingOutputData(SaveAccountingStatus.Success, receptionInfos, sameVisitList, listRaiinNoPrint);
            }
            return new SaveAccountingOutputData(SaveAccountingStatus.Failed, new(), new(), new());
        }
    private SaveAccountingStatus ValidateInvalidNyukinKbn(SaveAccountingInputData inputData, List<SyunoSeikyuModel> listSyunoSeikyuDB)
        {
            //リクエストの入金額+調整額がDBの請求額(seikyu_gaku)と一致しない場合NGとする
            var seikyuGaku = inputData.Credit + inputData.ThisWari;
            var seikyuDB = listSyunoSeikyuDB.Where(item=>item.RaiinNo == inputData.RaiinNo).FirstOrDefault();
            var seikyuGakuDB = seikyuDB?.SeikyuGaku ?? 0;

            //請求DBの情報なし
            if(seikyuDB == null)
            {
                return  SaveAccountingStatus.NoDataSeikyu;
            }

            //精算済みであればエラー
            if(seikyuDB.NyukinKbn != (int)NyukinKbnEnums.Unsettled)
            {
                return  SaveAccountingStatus.NotAllowedPartialNyukin;
            }

            if(!inputData.IsDisCharged)
            {
                //DB請求額と入力された請求額の比較
                if(seikyuGaku != seikyuGakuDB){
                return  SaveAccountingStatus.UnMatchSeikyuGaku;

                }
            }
            return SaveAccountingStatus.ValidateSuccess;
        }

        private SaveAccountingStatus ValidateOnlinePayment(SaveAccountingInputData inputData, RaiinInfOyaModel raiinInfDB)
        {
            //子来院番号で情報が複数件取得できた場合にはこの情報があるため処理対象外
            var oyaRaiinfCnt = _accountingRepository.GetRaiinInfOyaCnt(inputData.HpId, raiinInfDB.OyaRaiinNo);
            if(oyaRaiinfCnt > 1)
            {
                return SaveAccountingStatus.InvalidRaiinNo;
            }

            return SaveAccountingStatus.ValidateSuccess;
        }

        private static SaveAccountingStatus ValidatePaymentMethodFCO(int nyukinGaku, int seikyuGaku, int misyukin) {
            if (nyukinGaku <= 0) {
                return SaveAccountingStatus.InvalidNyukinGaku;
            }
            if (misyukin != 0) {
                return SaveAccountingStatus.UnCollectedBalanceInFCO;
            }
            if (nyukinGaku != seikyuGaku) {
                return SaveAccountingStatus.UnMatchSeikyuAndNyukinInFCO;
            }

            return SaveAccountingStatus.Success;
        }

        private RequestReservePayment RequestPayment(int hpId, int userId, bool isDisCharged, int nyukinGaku, int paymentMethodCd, ReserveDetailModel reserveDetail)
        {
            var paymentType = ((PaymentMethodCdEnum)paymentMethodCd).ConvertToPaymentType();
            if (isDisCharged)
            {
                paymentType = PaymentTypeEnums.NoBilling;
            }
            var task = _fincodeRepository.RequestReservePayment(hpId, reserveDetail.ReserveDetailId, (int)paymentType, nyukinGaku, userId);
            if (task.Result != null && task.Result.Data != null && task.Result.Data.RequestReservePayment != null)
            {
                return task.Result.Data.RequestReservePayment;
            }

            var res = new RequestReservePayment(0, string.Empty, string.Empty);

            if (task.Result != null && task.Result.Errors.Count > 0)
            {
                if (task.Result.Errors[0].Extensions?.Code != null)
                {
                    res.ErrorCode = task.Result.Errors[0].Extensions?.Code;
                }
                if (task.Result.Errors[0].Extensions?.UserMessage != null)
                {
                    res.UserMessage = task.Result.Errors[0].Extensions?.UserMessage;
                }
                return res;
            }

            return res;
        }

        private AccountingModel ConvertToAccountingModel(SaveAccountingInputData inputData, int nyukinKbn)
        {
            var accountingModels = new AccountingModel(
                                                inputData.HpId,
                                                inputData.PtId,
                                                inputData.SinDate,
                                                inputData.RaiinNo,
                                                inputData.ThisWari,
                                                inputData.Credit,
                                                inputData.PayType,
                                                inputData.Comment,
                                                inputData.SumAdjust,
                                                nyukinKbn
                                            );
            return accountingModels;
        }
        #endregion

        public class SettlementResult
        {
            public int AdjustFutan { get; }
            public int NyukinGaku { get; }
            public int NyukinKbn { get; }
            public int RemainingAdjustFutan { get; }
            public int RemainingNyukinGaku { get; }

            public SettlementResult(int adjustFutan, int nyukinGaku, int nyukinKbn, int remainingAdjustFutan, int remainingNyukinGaku)
            {
                AdjustFutan = adjustFutan;
                NyukinGaku = nyukinGaku;
                NyukinKbn = nyukinKbn;
                RemainingAdjustFutan = remainingAdjustFutan;
                RemainingNyukinGaku = remainingNyukinGaku;
            }
        }
    }
}
