﻿using Domain.Constant;
using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using Domain.Models.SpecialNote.PatientInfo;
using Domain.Models.SystemConf;
using GraphQL.Client.Abstractions.Utilities;
using Helper;
using Helper.Common;
using Helper.Extension;
using Infrastructure.Logger;
using Infrastructure.Repositories;
using UseCase.PatientInfor.Save;
using UseCase.PatientInfor.SaveBasicInfo.SavePatient;

namespace Interactor.PatientInfor.SaveBasicPatientInfo
{
    public class SaveBasicPatientInforInteractor : ISaveBasicPatientInfoInputPort
    {
        private readonly IPatientInforRepository _patientInforRepository;
        private readonly ISystemConfRepository _systemConfRepository;
        private readonly ILoggingHandler? _loggingHandler;
        private readonly IReceptionRepository _receptionRepository;

        public SaveBasicPatientInforInteractor(IPatientInforRepository patientInforRepository, ISystemConfRepository systemConfRepository, IReceptionRepository receptionRepository)
        {
            _patientInforRepository = patientInforRepository;
            _systemConfRepository = systemConfRepository;
            _receptionRepository = receptionRepository;
            //_loggingHandler = loggingHandler;
        }

        public SaveBasicPatientInforOutputData Handle(SaveBasicPatientInforInputData inputData)
        {
            try
            {
                #region Patient Info
                var resultMessages = new List<SavePatientInfoValidationResult>();
                PatientInforModel patientInforModel = new();
                List<ReceptionForViewDto> receptionInfos = new();

                if (string.IsNullOrEmpty(inputData.Patient.KanaName))
                    resultMessages.Add(new SavePatientInfoValidationResult(string.Format(ErrorMessage.MessageType_mInp00010, new string[] { "氏名(カナ)" }), nameof(inputData.Patient.KanaName).ToCamelCase(), SavePatientInforValidationCode.InvalidKanaName, TypeMessage.TypeMessageInline));

                if (inputData.Patient.Birthday <= 0)
                    resultMessages.Add(new SavePatientInfoValidationResult(string.Format(ErrorMessage.MessageType_mInp00010, new string[] { "生年月日" }), nameof(inputData.Patient.Birthday).ToCamelCase(), SavePatientInforValidationCode.InvalidBirthday, TypeMessage.TypeMessageInline));

                if (inputData.Patient.Sex <= 0)
                    resultMessages.Add(new SavePatientInfoValidationResult(string.Format(ErrorMessage.MessageType_mInp00010, new string[] { "性別" }), nameof(inputData.Patient.Sex).ToCamelCase(), SavePatientInforValidationCode.InvalidSex, TypeMessage.TypeMessageInline));

                if (string.IsNullOrEmpty(inputData.Patient.Tel1))
                    resultMessages.Add(new SavePatientInfoValidationResult(string.Format(ErrorMessage.MessageType_mInp00010, new string[] { "電話番号（1）" }), nameof(inputData.Patient.Tel1).ToCamelCase(), SavePatientInforValidationCode.InvalidTel1, TypeMessage.TypeMessageInline));

                if(!string.IsNullOrEmpty(inputData.Patient.Name) && !IsValidName(inputData.Patient.Name))
                {
                    resultMessages.Add(new SavePatientInfoValidationResult(ErrorMessage.MessageType_NameValid, nameof(inputData.Patient.Name).ToCamelCase(), SavePatientInforValidationCode.InvalidName, TypeMessage.TypeMessageInline));
                }

                if (!IsValidName(inputData.Patient.KanaName))
                {
                    resultMessages.Add(new SavePatientInfoValidationResult(ErrorMessage.MessageType_NameValid, nameof(inputData.Patient.KanaName).ToCamelCase(), SavePatientInforValidationCode.InvalidKanaName, TypeMessage.TypeMessageInline));
                }

                if(inputData.Patient.Name.Length > 100)
                {
                    resultMessages.Add(new SavePatientInfoValidationResult(ErrorMessage.MessageType_InvalidLength, nameof(inputData.Patient.Name).ToCamelCase(), SavePatientInforValidationCode.InvalidFirstNameKanjiLength, TypeMessage.TypeMessageInline));
                }

                if (inputData.Patient.KanaName.Length > 100)
                {
                    resultMessages.Add(new SavePatientInfoValidationResult(ErrorMessage.MessageType_InvalidLength, nameof(inputData.Patient.KanaName).ToCamelCase(), SavePatientInforValidationCode.InvalidFirstNameKanaLength, TypeMessage.TypeMessageInline));
                }

                if (resultMessages.Any())
                {
                    return new SaveBasicPatientInforOutputData(resultMessages, SavePatientInfoStatus.Failed, 0, patientInforModel, receptionInfos, 0, 0);
                }

                var result = _patientInforRepository.SavePatientInfo(inputData.Patient, inputData.UserId, inputData.PtMemo, inputData.IsSaveDuplicateInfo, inputData.IsConfirmOnline, inputData.OnlineConfirmationId, inputData.SinDate);
                if (result.resultSave)
                {
                    patientInforModel = _patientInforRepository.GetById(inputData.Patient.HpId, result.ptId, 0, 0) ?? new();
                    long deletedOnlineCfHisId = 0;
                    if(result.raiinNo > 0)
                    {
                        receptionInfos = _receptionRepository.GetRecptionList(inputData.Patient.HpId, inputData.SinDate, result.raiinNo);
                        deletedOnlineCfHisId = inputData.OnlineConfirmationId;
                    }
                    return new SaveBasicPatientInforOutputData(new List<SavePatientInfoValidationResult>(), SavePatientInfoStatus.Successful, result.ptId, patientInforModel, receptionInfos, result.raiinNo, deletedOnlineCfHisId);
                }
                else
                {
                    if (result.ptId != 0)
                    {
                        resultMessages.Add(new SavePatientInfoValidationResult(ErrorMessage.MessageType_DuplicatePtNum, SavePatientInforValidationCode.DuplicatePtNum, TypeMessage.TypeMessageConfirmation));
                        return new SaveBasicPatientInforOutputData(resultMessages, SavePatientInfoStatus.IsValidate, result.ptId, patientInforModel, receptionInfos, result.raiinNo, 0);
                    }

                    if(result.listPtNum.Count() > 0 && !inputData.IsSaveDuplicateInfo)
                    {
                        resultMessages.Add(new SavePatientInfoValidationResult("", SavePatientInforValidationCode.DuplicatePtInfo, TypeMessage.TypeMessageConfirmation, result.listPtNum));
                        return new SaveBasicPatientInforOutputData(resultMessages, SavePatientInfoStatus.IsValidate, result.ptId, patientInforModel, receptionInfos, result.raiinNo, 0);
                    }
                    return new SaveBasicPatientInforOutputData(new List<SavePatientInfoValidationResult>(), SavePatientInfoStatus.Failed, 0, patientInforModel, receptionInfos, 0, 0);
                }
                #endregion Patient Info
            }
            catch (Exception ex)
            {
                //if (_loggingHandler != null)
                //{
                //    _loggingHandler.WriteLogExceptionAsync(ex);
                //}
                Console.WriteLine(ex.Message);
                throw;
            }
            finally
            {
                _patientInforRepository.ReleaseResource();
                _systemConfRepository.ReleaseResource();
                //if (_loggingHandler != null)
                //{
                //    _loggingHandler.Dispose();
                //}
            }
        }

        public IEnumerable<SavePatientInfoValidationResult> IsValidKanjiName(string kanaName, string kanjiName, int hpId, ReactSavePatientInfo react)
        {

            var resultMessages = new List<SavePatientInfoValidationResult>();
            SplitName(kanaName, out string firstNameKana, out string lastNameKana);
            SplitName(kanjiName, out string firstNameKanji, out string lastNameKanji);
            bool isValidateFullName = _systemConfRepository.GetSettingValue(1017, 0, hpId) == 0;

            string message = string.Empty;
            if (string.IsNullOrEmpty(firstNameKana))
            {
                message = string.Format(ErrorMessage.MessageType_mInp00010, new string[] { "カナ" });
                resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidFirstNameKana, TypeMessage.TypeMessageError));

            }

            if (string.IsNullOrEmpty(firstNameKanji))
            {
                message = string.Format(ErrorMessage.MessageType_mInp00010, new string[] { "氏名" });
                resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidFirstNameKanji, TypeMessage.TypeMessageError));
            }

            // validate full name if setting
            if (isValidateFullName)
            {
                if (string.IsNullOrEmpty(lastNameKana) && !resultMessages.Any(x => x.Code == SavePatientInforValidationCode.InvalidFirstNameKana))
                {
                    message = string.Format(ErrorMessage.MessageType_mInp00010, new string[] { "カナ" });
                    resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidLastKanaName, TypeMessage.TypeMessageError));
                }

                if (string.IsNullOrEmpty(lastNameKanji) && !resultMessages.Any(x => x.Code == SavePatientInforValidationCode.InvalidFirstNameKanji))
                {
                    message = string.Format(ErrorMessage.MessageType_mInp00010, new string[] { "氏名" });
                    resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidLastKanjiName, TypeMessage.TypeMessageError));
                }
            }

            int FKanNmChkJIS = (int)_systemConfRepository.GetSettingValue(1003, 0, hpId);

            // 患者氏名チェック（受付）※JISコード
            if (FKanNmChkJIS > 0)
            {
                // 患者名_漢字 JisｺｰﾄﾞCheck
                string sBuf2 = string.Empty;
                string sBuf = CIUtil.Chk_JISKj(firstNameKanji, out sBuf2);
                if (!string.IsNullOrEmpty(sBuf))
                {
                    if (FKanNmChkJIS == 1 && !react.ConfirmInvalidJiscodeCheck)
                    {
                        message = "漢字名に '" + sBuf + "' の文字が入力されています。" + "\n\r" + "登録しますか？";
                        resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidJiscodeCheck, TypeMessage.TypeMessageWarning));
                    }
                    else if (FKanNmChkJIS == 2)
                    {
                        message = string.Format(ErrorMessage.MessageType_mInp00140, new string[] { "漢字名", "'" + sBuf + "'" + " の文字" });
                        resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidChineseCharacterName, TypeMessage.TypeMessageError));
                    }
                }
                if (isValidateFullName)
                {
                    sBuf2 = string.Empty;
                    // 患者姓_漢字 JisｺｰﾄﾞCheck
                    sBuf = CIUtil.Chk_JISKj(lastNameKanji, out sBuf2);
                    if (!string.IsNullOrEmpty(sBuf))
                    {
                        if (FKanNmChkJIS == 1 && !react.ConfirmInvalidJiscodeCheck)
                        {
                            message = "漢字姓に '" + sBuf + "' の文字が入力されています。" + "\n\r" + "登録しますか？";
                            resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidJiscodeCheck, TypeMessage.TypeMessageWarning));
                        }
                        else if (FKanNmChkJIS == 2)
                        {
                            message = string.Format(ErrorMessage.MessageType_mInp00140, new string[] { "漢字姓", "'" + sBuf + "'" + " の文字" });
                            resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidChineseCharacterName, TypeMessage.TypeMessageError));
                        }
                    }
                }
            }

            if (firstNameKana.Length > 20)
            {
                message = string.Format(ErrorMessage.MessageType_mFree00030, new string[] { "患者名（カナ）は２０文字以下を入力してください。" });
                resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidFirstNameKanaLength, TypeMessage.TypeMessageError));
            }

            if (firstNameKanji.Length > 30)
            {
                message = string.Format(ErrorMessage.MessageType_mFree00030, new string[] { "患者名は３０文字以下を入力してください。" });
                resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidFirstNameKanjiLength, TypeMessage.TypeMessageError));
            }

            if (isValidateFullName)
            {
                if (lastNameKana.Length > 20)
                {
                    message = string.Format(ErrorMessage.MessageType_mFree00030, new string[] { "患者姓（カナ）は２０文字以下を入力してください。" });
                    resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidLastKanaNameLength, TypeMessage.TypeMessageError));
                }

                if (lastNameKanji.Length > 30)
                {
                    message = string.Format(ErrorMessage.MessageType_mFree00030, new string[] { "患者姓は３０文字以下を入力してください。" });
                    resultMessages.Add(new SavePatientInfoValidationResult(message, SavePatientInforValidationCode.InvalidLastKanjiNameLength, TypeMessage.TypeMessageError));
                }
            }
            return resultMessages;
        }

        public void SplitName(string name, out string firstName, out string lastName)
        {
            firstName = "";
            lastName = "";
            char[] arraySpace = { '　', ' ' };
            if (!string.IsNullOrEmpty(name))
            {
                if (!arraySpace.Any(u => name.Any(c => c == u)))
                {
                    firstName = name;
                    return;
                }
                for (int i = 0; i < arraySpace.Length; i++)
                {
                    var arrayName = name.Split(arraySpace[i]);
                    if (arrayName != null && arrayName.Length >= 2)
                    {
                        int index = name.IndexOf(arraySpace[i]);
                        lastName = name.Substring(0, index);
                        firstName = name.Substring(index + 1).Trim();
                        break;
                    }
                }
            }
        }

        public bool IsValidAgeCheckConfirm(int ageCheck, int confirmDate, int birthDay, int sinDay)
        {
            // 但し、2日生まれ以降の場合は翌月１日を誕生日とする。
            if (CIUtil.Copy(birthDay.AsString(), 7, 2) != "01")
            {
                int firstDay = birthDay / 100 * 100 + 1;
                int nextMonth = CIUtil.DateTimeToInt(CIUtil.IntToDate(firstDay).AddMonths(1));
                birthDay = nextMonth;
            }

            if (CIUtil.AgeChk(birthDay, sinDay, ageCheck)
                && !CIUtil.AgeChk(birthDay, confirmDate, ageCheck))
            {
                return false;
            }
            return true;
        }

        private bool IsValidName(string name)
        {
            var nameTrim = name.Trim();
            char[] arraySpace = { '　', ' ' };
            return arraySpace.Any(u => nameTrim.Any(c => c == u));
        }
    }
}
