﻿using Domain.Models.PatientInfor;
using Helper.Common;
using Helper.Extension;
using UseCase.PatientInfor.GetPmhKohiDefault;

namespace Interactor.PatientInfor
{
    public class GetPmhKohiDefaultInteractor : IGetPmhKohiDefaultInputPort
    {
        private readonly IPatientInforRepository _patientInforRepository;

        public GetPmhKohiDefaultInteractor(IPatientInforRepository patientInforRepository)
        {
            _patientInforRepository = patientInforRepository;
        }

        public GetPmhKohiDefaultOuputData Handle(GetPmhKohiDefaultInputData inputData)
        {
            try
            {
                if (inputData.HpId < 0)
                {
                    return new GetPmhKohiDefaultOuputData(GetPmhKohiDefaultStatus.InvalidHpId);
                }

                if (inputData.SinDate <= 0)
                {
                    return new GetPmhKohiDefaultOuputData(GetPmhKohiDefaultStatus.InvalidSinDate);
                }

                if (inputData.MedicalSubsidy == null)
                {
                    return new GetPmhKohiDefaultOuputData(GetPmhKohiDefaultStatus.Failed);
                }

                var prefNo = _patientInforRepository.GetPrefNo(inputData.HpId);
                var dto = GetPmhKohiDefault(inputData.HpId, inputData.SinDate, inputData.MedicalSubsidy, prefNo);

                return new GetPmhKohiDefaultOuputData(GetPmhKohiDefaultStatus.Success, dto);
            }
            finally
            {
                _patientInforRepository.ReleaseResource();
            }
        }

        private GetPmhKohiDefaultDto GetPmhKohiDefault(int hpId, int sinDate, MedicalSubsidy medicalSubsidy, int prefNo)
        {
            var dto = new GetPmhKohiDefaultDto();
            dto.FutansyaNo = medicalSubsidy.MedicalSubsidyInsurerNumber;
            if (medicalSubsidy.MedicalSubsidyRecipientNumber.Length <= 7)
            {
                dto.JyukyusyaNo = medicalSubsidy.MedicalSubsidyRecipientNumber;
            }
            else
            {
                dto.TokusyuNo = medicalSubsidy.MedicalSubsidyRecipientNumber;
            }
            dto.StartDate = medicalSubsidy.ValidFrom.AsInteger();
            dto.EndDate = medicalSubsidy.ValidTo.AsInteger();

            string houbetu = CIUtil.Copy(medicalSubsidy.MedicalSubsidyInsurerNumber, 1, 2);
            var hokenMsts = _patientInforRepository.GetHokenMstForPmhKohi(hpId, houbetu, sinDate);

            int amountPerTimes = 0;
            int amountPerDay = 0;
            int amountPerMonth = 0;
            int rate = 0;
            int gendogakuTime = 0;
            int gendogakuDay = 0;
            int gendogakuMonth = 0;

            if (medicalSubsidy.IsAlwaysReimbursement.AsInteger() == 1)
            {
                hokenMsts = hokenMsts.Where(item => item.FutanKbn == 1 && item.FutanRate == 100).ToList();
            }
            else
            {
                var maxCopaymentAmount = GetMaxCopaymentAmount(medicalSubsidy);
                if (maxCopaymentAmount == null)
                {
                    return dto;
                }

                int burdenRatioPerTimes = maxCopaymentAmount.BurdenRatioPerTimes?.AsInteger() ?? 0;
                int burdenRatioPerDay = maxCopaymentAmount.BurdenRatioPerDay?.AsInteger() ?? 0;
                int burdenRatioPerMonth = maxCopaymentAmount.BurdenRatioPerMonth?.AsInteger() ?? 0;

                amountPerTimes = maxCopaymentAmount.AmountPerTimes?.AsInteger() ?? 0;
                amountPerDay = maxCopaymentAmount.AmountPerDay?.AsInteger() ?? 0;
                amountPerMonth = maxCopaymentAmount.AmountPerMonth?.AsInteger() ?? 0;
                bool isZeroFutanKbn = false;

                // 4-1
                if (burdenRatioPerTimes == 0 && burdenRatioPerDay == 0 && burdenRatioPerMonth == 0 &&
                    amountPerTimes == 0 && amountPerDay == 0 && amountPerMonth == 0)
                {
                    hokenMsts = hokenMsts.Where(p => p.FutanKbn == 0).ToList();
                    // go to 5
                    isZeroFutanKbn = true;
                }
                else
                {
                    hokenMsts = hokenMsts.Where(p => p.FutanKbn == 1).ToList();
                }

                if (!isZeroFutanKbn)
                {
                    // 4-2
                    int burdenRate = GetBurdenRate(maxCopaymentAmount);
                    if (hokenMsts.Any(p => p.FutanRate == burdenRate))
                    {
                        hokenMsts = hokenMsts.Where(p => p.FutanRate == burdenRate).ToList();
                    }
                    // ※1
                    else if (burdenRate > 0)
                    {
                        hokenMsts = hokenMsts.Where(p => p.FutanRate > 0).ToList();
                        rate = burdenRate;
                    }

                    // 4-3
                    if (hokenMsts.Any(p => p.KaiLimitFutan == amountPerTimes))
                    {
                        hokenMsts = hokenMsts.Where(p => p.KaiLimitFutan == amountPerTimes).ToList();
                    }
                    // ※2
                    else if (amountPerTimes > 0)
                    {
                        hokenMsts = hokenMsts.Where(p => p.KaiLimitFutan > 0).ToList();
                        gendogakuTime = amountPerTimes;
                    }

                    // 4-4
                    if (hokenMsts.Any(p => p.DayLimitFutan == amountPerDay))
                    {
                        hokenMsts = hokenMsts.Where(p => p.DayLimitFutan == amountPerDay).ToList();
                    }
                    // ※3
                    else if (amountPerDay > 0)
                    {
                        hokenMsts = hokenMsts.Where(p => p.DayLimitFutan > 0).ToList();
                        gendogakuDay = amountPerDay;
                    }

                    // 4-5
                    if (hokenMsts.Any(p => p.MonthLimitFutan == amountPerMonth))
                    {
                        hokenMsts = hokenMsts.Where(p => p.MonthLimitFutan == amountPerMonth).ToList();
                    }
                    // ※4
                    else if (amountPerMonth > 0)
                    {
                        hokenMsts = hokenMsts.Where(p => p.MonthLimitFutan > 0).ToList();
                        gendogakuMonth = amountPerMonth;
                    }

                    // 4-6
                    hokenMsts = hokenMsts.Where(p => p.DayLimitCount == maxCopaymentAmount.MaxNumberOfTimesPerDay?.AsInteger()).ToList();

                    // 4-7
                    hokenMsts = hokenMsts.Where(p => p.MonthLimitCount == maxCopaymentAmount.MaxNumberOfTimesPerMonth?.AsInteger()).ToList();

                    // 4-8
                    if (maxCopaymentAmount.IsForFirstVisitOnly != null)
                    {
                        int isForFirstVisitOnly = maxCopaymentAmount.IsForFirstVisitOnly.AsInteger();
                        if (prefNo == 4)
                        {
                            if (isForFirstVisitOnly == 0)
                            {
                                hokenMsts = hokenMsts.Where(p => p.CalcSpKbn != 2).ToList();
                            }
                            else if (isForFirstVisitOnly == 1)
                            {
                                hokenMsts = hokenMsts.Where(p => p.CalcSpKbn == 2).ToList();
                            }
                        }
                        else if (prefNo == 34)
                        {
                            if (isForFirstVisitOnly == 0)
                            {
                                hokenMsts = hokenMsts.Where(p => p.CalcSpKbn != 1).ToList();
                            }
                            else if (isForFirstVisitOnly == 1)
                            {
                                hokenMsts = hokenMsts.Where(p => p.CalcSpKbn == 1).ToList();
                            }
                        }
                    }
                }
            }

            // 5
            var hokenMst = hokenMsts.OrderBy(p => p.PrefNo).ThenBy(p => p.HokenNo).ThenBy(p => p.HokenEdaNo).FirstOrDefault();
            if (hokenMst != null)
            {
                dto.PrefNo = hokenMst.PrefNo;
                dto.HokenNo = hokenMst.HokenNo;
                dto.HokenEdaNo = hokenMst.HokenEdaNo;

                // ※1 true
                if (rate > 0)
                {
                    dto.Rate = rate;
                }

                // ※2 true
                if (gendogakuTime > 0)
                {
                    dto.GendoGaku = gendogakuTime;
                }

                // ※3 true
                if (hokenMst.KaiLimitFutan == 0 && gendogakuDay > 0)
                {
                    dto.GendoGaku = gendogakuDay;
                }

                // ※4 true
                if (hokenMst.KaiLimitFutan == 0 && hokenMst.DayLimitFutan == 0 && gendogakuMonth > 0)
                {
                    dto.GendoGaku = gendogakuMonth;
                }
            }

            if (medicalSubsidy.MedicalSubsidyRecipientNumber.Length > 7 || (hokenMst != null && hokenMst.IsTokusyuNoCheck == 1 && hokenMst.IsJyukyusyaNoCheck == 0))
            {
                dto.JyukyusyaNo = string.Empty;
                dto.TokusyuNo = medicalSubsidy.MedicalSubsidyRecipientNumber;
            }
            else
            {
                dto.JyukyusyaNo = medicalSubsidy.MedicalSubsidyRecipientNumber;
                dto.TokusyuNo = string.Empty;
            }

            return dto;
        }

        private MaxCopaymentAmount? GetMaxCopaymentAmount(MedicalSubsidy medicalSubsidy)
        {
            if (medicalSubsidy.MaxCopaymentAmount == null || medicalSubsidy.MaxCopaymentAmount.Length == 0)
            {
                return null;
            }

            var types = new List<int>() { 7, 5, 3, 1 };
            return medicalSubsidy.MaxCopaymentAmount.Where(item => types.Contains(item.Type?.AsInteger() ?? 0)).OrderByDescending(p => p.Type).FirstOrDefault();
        }

        private int GetBurdenRate(MaxCopaymentAmount maxCopaymentAmount)
        {
            if (maxCopaymentAmount.BurdenRatioPerMonth != null && maxCopaymentAmount.BurdenRatioPerMonth.AsInteger() > 0)
            {
                return maxCopaymentAmount.BurdenRatioPerMonth.AsInteger();
            }
            if (maxCopaymentAmount.BurdenRatioPerDay != null && maxCopaymentAmount.BurdenRatioPerDay.AsInteger() > 0)
            {
                return maxCopaymentAmount.BurdenRatioPerDay.AsInteger();
            }
            if (maxCopaymentAmount.BurdenRatioPerTimes != null && maxCopaymentAmount.BurdenRatioPerTimes.AsInteger() > 0)
            {
                return maxCopaymentAmount.BurdenRatioPerTimes.AsInteger();
            }

            return 0;
        }
    }
}
