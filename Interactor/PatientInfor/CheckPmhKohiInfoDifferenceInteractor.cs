﻿using Domain.Models.Insurance;
using Domain.Models.PatientInfor;
using Helper.Extension;
using System.Globalization;
using UseCase.PatientInfor.CheckPmhKohiInfoDifference;

namespace Interactor.PatientInfor
{
    public class CheckPmhKohiInfoDifferenceInteractor : ICheckPmhKohiInfoDifferenceInputPort
    {
        private readonly IPatientInforRepository _patientInforRepository;

        public CheckPmhKohiInfoDifferenceInteractor(IPatientInforRepository patientInforRepository)
        {
            _patientInforRepository = patientInforRepository;
        }

        public CheckPmhKohiInfoDifferenceOuputData Handle(CheckPmhKohiInfoDifferenceInputData inputData)
        {
            try
            {
                if (inputData.HpId < 0)
                {
                    return new CheckPmhKohiInfoDifferenceOuputData(CheckPmhKohiInfoDifferenceStatus.InvalidHpId);
                }

                if (inputData.PtId <= 0)
                {
                    return new CheckPmhKohiInfoDifferenceOuputData(CheckPmhKohiInfoDifferenceStatus.InvalidPtId);
                }

                if (inputData.SinDate <= 0)
                {
                    return new CheckPmhKohiInfoDifferenceOuputData(CheckPmhKohiInfoDifferenceStatus.InvalidSinDate);
                }

                if (inputData.MedicalSubsidy == null)
                {
                    return new CheckPmhKohiInfoDifferenceOuputData(CheckPmhKohiInfoDifferenceStatus.Failed);
                }

                var listKohi = _patientInforRepository.GetListPmhKohiInf(inputData.HpId, inputData.PtId, inputData.SinDate, inputData.MedicalSubsidy.MedicalSubsidyInsurerNumber, inputData.MedicalSubsidy.MedicalSubsidyRecipientNumber);

                var dto = new CheckPmhKohiInfoDifferenceDto();
                if (listKohi.Count <= 0)
                {
                    dto.IsNotExistKohiCompare = true;
                    return new CheckPmhKohiInfoDifferenceOuputData(CheckPmhKohiInfoDifferenceStatus.Success, dto);
                }

                var prefNo = _patientInforRepository.GetPrefNo(inputData.HpId);
                var maxCopaymentAmount = GetMaxCopaymentAmount(inputData.MedicalSubsidy);

                var listKohiNoDifference = GetListPmhKohiInfNoDifference(inputData.MedicalSubsidy, listKohi, prefNo, maxCopaymentAmount, out bool isContinue);
                if (isContinue)
                {
                    dto.IsContinue = true;
                    return new CheckPmhKohiInfoDifferenceOuputData(CheckPmhKohiInfoDifferenceStatus.Success, dto);
                }

                if (listKohiNoDifference.Count > 0)
                {
                    dto.IsMapAll = true;
                    return new CheckPmhKohiInfoDifferenceOuputData(CheckPmhKohiInfoDifferenceStatus.Success, dto);
                }

                var kohi = listKohi.OrderByDescending(item => item.HokenId).First();

                dto = GetPmhKohiInfoDifferenceDto(inputData.MedicalSubsidy, kohi, prefNo, maxCopaymentAmount);

                return new CheckPmhKohiInfoDifferenceOuputData(CheckPmhKohiInfoDifferenceStatus.Success, dto);
            }
            finally
            {
                _patientInforRepository.ReleaseResource();
            }
        }

        private List<KohiInfModel> GetListPmhKohiInfNoDifference(MedicalSubsidy medicalSubsidy, List<KohiInfModel> listKohi, int prefNo, MaxCopaymentAmount? maxCopaymentAmount, out bool isContinue)
        {
            isContinue = false;
            string status = string.Empty;
            int burdenRate = 0;
            string amount = string.Empty;

            if (medicalSubsidy.IsAlwaysReimbursement.AsInteger() == 1)
            {
                listKohi = listKohi.Where(item => item.HokenMstModel.FutanKbn == 1 && item.HokenMstModel.FutanRate == 100).ToList();
            }
            else
            {
                if (maxCopaymentAmount != null)
                {
                    // ※1 負担率
                    burdenRate = GetBurdenRate(maxCopaymentAmount);
                    listKohi = listKohi.Where(item => item.BurdenRate == burdenRate).ToList();

                    // ※2 金額
                    int amountPerDay = maxCopaymentAmount.AmountPerDay?.AsInteger() ?? 0;
                    int amountPerMonth = maxCopaymentAmount.AmountPerMonth?.AsInteger() ?? 0;
                    int amountPerTimes = maxCopaymentAmount.AmountPerTimes?.AsInteger() ?? 0;

                    if (amountPerDay > 0 || amountPerMonth > 0 || amountPerTimes > 0 || burdenRate > 0)
                    {
                        listKohi = listKohi.Where(item => item.HokenMstModel.FutanKbn == 1).ToList();
                        var tempMatchKohies = new List<KohiInfModel>();
                        foreach (var matchPmhKohi in listKohi)
                        {
                            if (matchPmhKohi.GendoGaku > 0)
                            {
                                if ((matchPmhKohi.HokenMstModel.KaiLimitFutan > 0 && amountPerTimes == matchPmhKohi.GendoGaku) ||
                                    (matchPmhKohi.HokenMstModel.DayLimitFutan > 0 && amountPerDay == matchPmhKohi.GendoGaku) ||
                                    (matchPmhKohi.HokenMstModel.MonthLimitFutan > 0 && amountPerMonth == matchPmhKohi.GendoGaku))
                                {
                                    tempMatchKohies.Add(matchPmhKohi);
                                }
                            }
                            else
                            {
                                if (matchPmhKohi.HokenMstModel.KaiLimitFutan == amountPerTimes &&
                                    matchPmhKohi.HokenMstModel.DayLimitFutan == amountPerDay &&
                                    matchPmhKohi.HokenMstModel.MonthLimitFutan == amountPerMonth)
                                {
                                    tempMatchKohies.Add(matchPmhKohi);
                                }
                            }
                        }
                        listKohi = tempMatchKohies;
                    }
                    else
                    {
                        listKohi = listKohi.Where(item => item.HokenMstModel.FutanKbn == 0).ToList();
                    }

                    int maxNumberOfTimesPerDay = maxCopaymentAmount.MaxNumberOfTimesPerDay?.AsInteger() ?? 0;
                    int maxNumberOfTimesPerMonth = maxCopaymentAmount.MaxNumberOfTimesPerMonth?.AsInteger() ?? 0;
                    // ※3 上限回数
                    listKohi = listKohi.Where(item => item.HokenMstModel.DayLimitCount == maxNumberOfTimesPerDay &&
                                                      item.HokenMstModel.MonthLimitCount == maxNumberOfTimesPerMonth).ToList();

                    if (maxCopaymentAmount.IsForFirstVisitOnly != null)
                    {
                        int isForFirstVisitOnly = maxCopaymentAmount.IsForFirstVisitOnly.AsInteger();

                        // ※4 初診のみ適用
                        if (prefNo == 4)
                        {
                            listKohi = listKohi.Where(item => (isForFirstVisitOnly == 0 && item.CalcSpKbn != 2) ||
                                                              (isForFirstVisitOnly == 1 && item.CalcSpKbn == 2)).ToList();
                        }
                        else if (prefNo == 34)
                        {
                            listKohi = listKohi.Where(item => (isForFirstVisitOnly == 0 && item.CalcSpKbn != 1) ||
                                                              (isForFirstVisitOnly == 1 && item.CalcSpKbn == 1)).ToList();
                        }
                    }
                }
                else
                {
                    isContinue = true;
                }
            }

            listKohi = listKohi.Where(item => item.StartDate == medicalSubsidy.ValidFrom.AsInteger() && item.EndDate == medicalSubsidy.ValidTo.AsInteger()).ToList();

            return listKohi;
        }

        private MaxCopaymentAmount? GetMaxCopaymentAmount(MedicalSubsidy medicalSubsidy)
        {
            if (medicalSubsidy.MaxCopaymentAmount == null || medicalSubsidy.MaxCopaymentAmount.Length == 0)
            {
                return null;
            }

            var types = new List<int>() { 7, 5, 3, 1 };
            return medicalSubsidy.MaxCopaymentAmount.Where(item => types.Contains(item.Type?.AsInteger() ?? 0)).OrderByDescending(p => p.Type).FirstOrDefault();
        }

        private int GetBurdenRate(MaxCopaymentAmount? maxCopaymentAmount)
        {
            if (maxCopaymentAmount == null)
            {
                return -1;
            }

            if (maxCopaymentAmount.BurdenRatioPerMonth != null && maxCopaymentAmount.BurdenRatioPerMonth.AsInteger() > 0)
            {
                return maxCopaymentAmount.BurdenRatioPerMonth.AsInteger();
            }
            if (maxCopaymentAmount.BurdenRatioPerDay != null && maxCopaymentAmount.BurdenRatioPerDay.AsInteger() > 0)
            {
                return maxCopaymentAmount.BurdenRatioPerDay.AsInteger();
            }
            if (maxCopaymentAmount.BurdenRatioPerTimes != null && maxCopaymentAmount.BurdenRatioPerTimes.AsInteger() > 0)
            {
                return maxCopaymentAmount.BurdenRatioPerTimes.AsInteger();
            }

            return 0;
        }

        private CheckPmhKohiInfoDifferenceDto GetPmhKohiInfoDifferenceDto(MedicalSubsidy medicalSubsidy, KohiInfModel kohi, int prefNo, MaxCopaymentAmount? maxCopaymentAmount)
        {
            var differenceProperty = new List<PmhKohiInfoDifferenceProperty>();
            differenceProperty.Add(new PmhKohiInfoDifferenceProperty()
            {
                Index = 1,
                Name = nameof(kohi.FutansyaNo),
                XmlValue = medicalSubsidy.MedicalSubsidyInsurerNumber,
                KohiValue = kohi.FutansyaNo,
                IsMap = true
            });

            if (kohi.JyukyusyaNo == medicalSubsidy.MedicalSubsidyRecipientNumber)
            {
                differenceProperty.Add(new PmhKohiInfoDifferenceProperty()
                {
                    Index = 2,
                    Name = nameof(kohi.JyukyusyaNo),
                    XmlValue = medicalSubsidy.MedicalSubsidyRecipientNumber,
                    KohiValue = kohi.JyukyusyaNo,
                    IsMap = true
                });
            }
            else
            {
                differenceProperty.Add(new PmhKohiInfoDifferenceProperty()
                {
                    Index = 2,
                    Name = nameof(kohi.TokusyuNo),
                    XmlValue = medicalSubsidy.MedicalSubsidyRecipientNumber,
                    KohiValue = kohi.TokusyuNo,
                    IsMap = true
                });
            }

            differenceProperty.Add(new PmhKohiInfoDifferenceProperty()
            {
                Index = 3,
                Name = nameof(kohi.StartDate),
                XmlValue = medicalSubsidy.ValidFrom,
                KohiValue = kohi.StartDate.AsString(),
                IsMap = kohi.StartDate == medicalSubsidy.ValidFrom.AsInteger()
            });

            differenceProperty.Add(new PmhKohiInfoDifferenceProperty()
            {
                Index = 4,
                Name = nameof(kohi.EndDate),
                XmlValue = medicalSubsidy.ValidTo,
                KohiValue = kohi.EndDate.AsString(),
                IsMap = kohi.EndDate == medicalSubsidy.ValidTo.AsInteger()
            });

            var isMap = GetIsMapRateAndAmount(medicalSubsidy, maxCopaymentAmount, kohi, prefNo);

            var burdenRate = new PmhKohiInfoDifferenceProperty();
            burdenRate.Index = 5;
            burdenRate.Name = "BurdenRate";
            burdenRate.XmlValue = GetXmlBurdenRate(medicalSubsidy, maxCopaymentAmount);
            burdenRate.KohiValue = GetKohiBurdenRate(kohi);
            burdenRate.IsMap = isMap.IsMapRate;
            differenceProperty.Add(burdenRate);

            var copaymentAmount = new PmhKohiInfoDifferenceProperty();
            copaymentAmount.Index = 6;
            copaymentAmount.Name = "CopaymentAmount";
            copaymentAmount.XmlValue = GetXmlCopaymentAmount(medicalSubsidy, maxCopaymentAmount, prefNo, kohi.GendoGaku);
            copaymentAmount.KohiValue = GetKohiCopaymentAmount(kohi, prefNo, maxCopaymentAmount);
            copaymentAmount.IsMap = isMap.IsMapCopaymentAmount;
            differenceProperty.Add(copaymentAmount);

            var dto = new CheckPmhKohiInfoDifferenceDto();
            dto.SeqNo = kohi.SeqNo;
            dto.HokenId = kohi.HokenId;
            dto.HokenName = FormatKohiName(kohi);
            dto.PmhKohiInfoDifferenceProperties = differenceProperty;

            return dto;
        }

        private (bool IsMapRate, bool IsMapCopaymentAmount) GetIsMapRateAndAmount(MedicalSubsidy medicalSubsidy, MaxCopaymentAmount? maxCopaymentAmount, KohiInfModel kohi, int prefNo)
        {
            var isMapRate = false;
            var isMapCopaymentAmount = false;

            if (medicalSubsidy.IsAlwaysReimbursement.AsInteger() == 1)
            {
                if (kohi.HokenMstModel.FutanKbn == 1 && kohi.HokenMstModel.FutanRate == 100)
                {
                    isMapRate = true;
                    isMapCopaymentAmount = true;
                }

                return (isMapRate, isMapCopaymentAmount);
            }

            if (maxCopaymentAmount != null)
            {
                int burdenRate = 0;
                bool isMapAmount = false;
                bool isMapLimitCount = false;
                bool isMapFirstVisitOnly = false;

                // ※1 負担率
                burdenRate = GetBurdenRate(maxCopaymentAmount);
                isMapRate = burdenRate == kohi.BurdenRate;

                // ※2 金額
                int amountPerDay = maxCopaymentAmount.AmountPerDay?.AsInteger() ?? 0;
                int amountPerMonth = maxCopaymentAmount.AmountPerMonth?.AsInteger() ?? 0;
                int amountPerTimes = maxCopaymentAmount.AmountPerTimes?.AsInteger() ?? 0;

                if (amountPerDay > 0 || amountPerMonth > 0 || amountPerTimes > 0 || burdenRate > 0)
                {
                    if (kohi.HokenMstModel.FutanKbn == 1)
                    {
                        if (kohi.GendoGaku > 0)
                        {
                            if ((kohi.HokenMstModel.KaiLimitFutan > 0 && amountPerTimes == kohi.GendoGaku) ||
                                (kohi.HokenMstModel.DayLimitFutan > 0 && amountPerDay == kohi.GendoGaku) ||
                                (kohi.HokenMstModel.MonthLimitFutan > 0 && amountPerMonth == kohi.GendoGaku))
                            {
                                isMapAmount = true;
                            }
                        }
                        else
                        {
                            if (kohi.HokenMstModel.KaiLimitFutan == amountPerTimes &&
                                kohi.HokenMstModel.DayLimitFutan == amountPerDay &&
                                kohi.HokenMstModel.MonthLimitFutan == amountPerMonth)
                            {
                                isMapAmount = true;
                            }
                        }
                    }
                }
                else if (kohi.HokenMstModel.FutanKbn == 0)
                {
                    isMapAmount = true;
                }

                int maxNumberOfTimesPerDay = maxCopaymentAmount.MaxNumberOfTimesPerDay?.AsInteger() ?? 0;
                int maxNumberOfTimesPerMonth = maxCopaymentAmount.MaxNumberOfTimesPerMonth?.AsInteger() ?? 0;
                // ※3 上限回数
                if (kohi.HokenMstModel.DayLimitCount == maxNumberOfTimesPerDay &&
                    kohi.HokenMstModel.MonthLimitCount == maxNumberOfTimesPerMonth)
                {
                    isMapLimitCount = true;
                }

                if (maxCopaymentAmount.IsForFirstVisitOnly != null)
                {
                    int isForFirstVisitOnly = maxCopaymentAmount.IsForFirstVisitOnly.AsInteger();

                    // ※4 初診のみ適用
                    if (prefNo == 4)
                    {
                        if ((isForFirstVisitOnly == 0 && kohi.CalcSpKbn != 2) ||
                            (isForFirstVisitOnly == 1 && kohi.CalcSpKbn == 2))
                        {
                            isMapFirstVisitOnly = true;
                        }
                    }
                    else if (prefNo == 34)
                    {
                        if ((isForFirstVisitOnly == 0 && kohi.CalcSpKbn != 1) ||
                            (isForFirstVisitOnly == 1 && kohi.CalcSpKbn == 1))
                        {
                            isMapFirstVisitOnly = true;
                        }
                    }
                    else
                    {
                        isMapFirstVisitOnly = true;
                    }
                }
                else
                {
                    isMapFirstVisitOnly = true;
                }

                isMapCopaymentAmount = isMapAmount && isMapLimitCount && isMapFirstVisitOnly;
            }

            return (isMapRate, isMapCopaymentAmount);
        }

        private string GetXmlBurdenRate(MedicalSubsidy medicalSubsidy, MaxCopaymentAmount? maxCopaymentAmount)
        {
            if (medicalSubsidy.IsAlwaysReimbursement.AsInteger() == 1)
            {
                return "100%";
            }

            var burdenRate = GetBurdenRate(maxCopaymentAmount);
            return burdenRate > 0 ? $"{burdenRate}%" : "-";
        }

        private string GetKohiBurdenRate(KohiInfModel kohi)
        {
            if (kohi.HokenMstModel.FutanRate == 0)
            {
                return "-";
            }

            return $"{kohi.BurdenRate}%";
        }

        private string GetXmlCopaymentAmount(MedicalSubsidy medicalSubsidy, MaxCopaymentAmount? maxCopaymentAmount, int prefNo, int gendoGaku)
        {
            string amount = string.Empty;
            int amountPerTimes = maxCopaymentAmount?.AmountPerTimes?.AsInteger() ?? 0;
            int amountPerDay = maxCopaymentAmount?.AmountPerDay?.AsInteger() ?? 0;
            int amountPerMonth = maxCopaymentAmount?.AmountPerMonth?.AsInteger() ?? 0;
            int maxNumberOfTimesPerDay = maxCopaymentAmount?.MaxNumberOfTimesPerDay?.AsInteger() ?? 0;
            int maxNumberOfTimesPerMonth = maxCopaymentAmount?.MaxNumberOfTimesPerMonth?.AsInteger() ?? 0;

            string amountTimes = string.Empty;
            string amountDay = string.Empty;
            string amountMonth = string.Empty;
            string maxDay = string.Empty;
            string maxMonth = string.Empty;

            if (amountPerTimes == 0 &&
                amountPerDay == 0 &&
                amountPerMonth == 0)
            {
                amount = "0円 ";
            }

            if (maxNumberOfTimesPerDay > 0)
            {
                maxDay = FormatAmountView(maxNumberOfTimesPerDay) + "回/日 ";
            }
            if (maxNumberOfTimesPerMonth > 0)
            {
                maxMonth = FormatAmountView(maxNumberOfTimesPerMonth) + "回/月 ";
            }

            if (gendoGaku > 0 &&
                (gendoGaku == amountPerTimes ||
                 gendoGaku == amountPerDay ||
                 gendoGaku == amountPerMonth))
            {
                if (amountPerTimes == gendoGaku)
                {
                    amountTimes = FormatAmountView(amountPerTimes) + "円/回 ";
                }
                else if (amountPerDay == gendoGaku)
                {
                    amountDay = FormatAmountView(amountPerDay) + "円/日 ";
                }
                else if (amountPerMonth == gendoGaku)
                {
                    amountMonth = FormatAmountView(amountPerMonth) + "円/月 ";
                }
            }
            else
            {
                if (amountPerTimes > 0)
                {
                    amountTimes = FormatAmountView(amountPerTimes) + "円/回 ";
                }
                if (amountPerDay > 0)
                {
                    amountDay = FormatAmountView(amountPerDay) + "円/日 ";
                }
                if (amountPerMonth > 0)
                {
                    amountMonth = FormatAmountView(amountPerMonth) + "円/月 ";
                }
            }

            amount += amountTimes + maxDay + amountDay + maxMonth + amountMonth;

            if (maxCopaymentAmount?.IsForFirstVisitOnly?.AsInteger() == 1 && (prefNo == 4 || prefNo == 34))
            {
                amount += "(初診のみ)";
            }

            return amount.Trim();
        }

        private string GetKohiCopaymentAmount(KohiInfModel kohi, int prefNo, MaxCopaymentAmount? maxCopaymentAmount)
        {
            bool isExistFirstVisitOnlyTag = maxCopaymentAmount?.IsForFirstVisitOnly != null;
            int amountPerTimes = maxCopaymentAmount?.AmountPerTimes?.AsInteger() ?? 0;
            int amountPerDay = maxCopaymentAmount?.AmountPerDay?.AsInteger() ?? 0;
            int amountPerMonth = maxCopaymentAmount?.AmountPerMonth?.AsInteger() ?? 0;

            var amount = string.Empty;
            if (kohi.HokenMstModel.FutanKbn == 0)
            {
                amount += "0円 ";
            }
            else
            {
                string amountTimes = string.Empty;
                string amountDay = string.Empty;
                string amountMonth = string.Empty;
                string maxDay = string.Empty;
                string maxMonth = string.Empty;

                if (kohi.HokenMstModel.KaiLimitFutan == 0 &&
                    kohi.HokenMstModel.DayLimitFutan == 0 &&
                    kohi.HokenMstModel.MonthLimitFutan == 0)
                {
                    amount += "0円 ";
                }

                if (kohi.HokenMstModel.DayLimitCount > 0)
                {
                    maxDay = FormatAmountView(kohi.HokenMstModel.DayLimitCount) + "回/日 ";
                }
                if (kohi.HokenMstModel.MonthLimitCount > 0)
                {
                    maxMonth = FormatAmountView(kohi.HokenMstModel.MonthLimitCount) + "回/月 ";
                }

                if (kohi.GendoGaku > 0 &&
                    (kohi.GendoGaku == amountPerTimes ||
                     kohi.GendoGaku == amountPerDay ||
                     kohi.GendoGaku == amountPerMonth))
                {
                    if (kohi.GendoGaku == amountPerTimes)
                    {
                        amountTimes = FormatAmountView(kohi.GendoGaku) + "円/回 ";
                    }
                    else if (kohi.GendoGaku == amountPerDay)
                    {
                        amountDay = FormatAmountView(kohi.GendoGaku) + "円/日 ";
                    }
                    else if (kohi.GendoGaku == amountPerMonth)
                    {
                        amountMonth = FormatAmountView(kohi.GendoGaku) + "円/月 ";
                    }
                }
                else
                {
                    if (kohi.HokenMstModel.KaiLimitFutan > 0)
                    {
                        amountTimes = FormatAmountView(kohi.HokenMstModel.KaiLimitFutan) + "円/回 ";
                    }
                    if (kohi.HokenMstModel.DayLimitFutan > 0)
                    {
                        amountDay = FormatAmountView(kohi.HokenMstModel.DayLimitFutan) + "円/日 ";
                    }
                    if (kohi.HokenMstModel.MonthLimitFutan > 0)
                    {
                        amountMonth = FormatAmountView(kohi.HokenMstModel.MonthLimitFutan) + "円/月 ";
                    }
                }

                amount += amountTimes + maxDay + amountDay + maxMonth + amountMonth;
            }

            if (isExistFirstVisitOnlyTag)
            {
                if ((prefNo == 4 && kohi.HokenMstModel.CalcSpKbn == 2) ||
                    (prefNo == 34 && kohi.HokenMstModel.CalcSpKbn == 1))
                {
                    amount += "(初診のみ)";
                }
            }

            return amount.Trim();
        }

        private string FormatKohiName(KohiInfModel kohi)
        {
            return $"{kohi.HokenId:D3} {kohi.HokenMstModel.HokenNameCd}";
        }

        private string FormatAmountView(int number)
        {
            return number.ToString("N0", CultureInfo.InvariantCulture);
        }
    }
}
