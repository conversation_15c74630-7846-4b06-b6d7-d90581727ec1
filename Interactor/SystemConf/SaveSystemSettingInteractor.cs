﻿using Domain.Models.HpInf;
using Domain.Models.MstItem;
using Domain.Models.Santei;
using Domain.Models.SystemConf;
using Domain.Models.SystemGenerationConf;
using Helper.Constants;
using Helper.Exceptions;
using UseCase.SystemConf.SaveSystemSetting;
using CalculateService.Interface;
using static Helper.Constants.StatusConstant;

namespace Interactor.SystemConf
{
    public class SaveSystemSettingInteractor : ISaveSystemSettingInputPort
    {
        private readonly ISystemConfRepository _systemConfRepository;
        private readonly IHpInfRepository _hpInfRepository;
        private readonly ISanteiInfRepository _santeiInfRepository;
        private readonly IMstItemRepository _mstItemRepository;
        private readonly IEmrLogger _emrLogger;

        public SaveSystemSettingInteractor(ISystemConfRepository systemConfRepository, IHpInfRepository hpInfRepository, ISanteiInfRepository santeiInfRepository, IMstItemRepository mstItemRepository, IEmrLogger emrLogger)
        {
            _systemConfRepository = systemConfRepository;
            _hpInfRepository = hpInfRepository;
            _santeiInfRepository = santeiInfRepository;
            _mstItemRepository = mstItemRepository;
            _emrLogger = emrLogger;
        }

        public SaveSystemSettingOutputData Handle(SaveSystemSettingInputData inputData)
        {
            try
            {
                if (inputData.HpInfs.Any())
                {
                    var hpInfs = ConvertItemToHpInfModel(inputData.HpInfs, inputData.HpId);
                    _hpInfRepository.SaveHpInf(inputData.UserId, hpInfs);
                }

                if (inputData.SystemConfMenus.Any())
                {
                    var systemConfMenu = ConvertItemToSystemConfItemModel(inputData.SystemConfMenus, inputData.HpId);
                    _systemConfRepository.SaveSystemGenerationConf(inputData.UserId, inputData.HpId, systemConfMenu);
                    var notFoundMenuModels = _systemConfRepository.SaveSystemSetting(inputData.HpId, inputData.UserId, systemConfMenu);
                    if (notFoundMenuModels.Any())
                    {
                        // system_conf にレコードがないことはあり得る前提。
                        var notFoundStr = string.Join(", ", notFoundMenuModels.Select(model => $"[HpId: {model.HpId}, GrpCd: {model.GrpCd}, GrpEdaNo: {model.GrpEdaNo}]"));
                        _emrLogger.WriteLogWarn(this, "SaveSystemSetting", $"更新対象に指定された以下のデータが見つかりませんでした。必要に応じて system_conf にレコードを追加してください。: {notFoundStr}");
                    }
                }

                if (inputData.SanteiInfs.Any())
                {
                    if (!ValidateSanteiInfs(inputData))
                        return new SaveSystemSettingOutputData(SaveSystemSettingStatus.AutoSanteiDateOverlap);

                    var santeiInfs = ConvertItemToSanteiInfModel(inputData.SanteiInfs);
                    _santeiInfRepository.SaveAutoSanteiMst(inputData.HpId, inputData.UserId, santeiInfs);
                }

                if (inputData.KensaCenters.Any())
                {
                    var kensaCenters = ConvertItemToKensaCenterMstModel(inputData.KensaCenters, inputData.HpId);
                    _mstItemRepository.SaveKensaCenterMst(inputData.UserId, kensaCenters);
                }

                return new SaveSystemSettingOutputData(SaveSystemSettingStatus.Successed);
            }
            catch (Exception ex)
            {
                string name = GetType().Namespace ?? string.Empty;
                throw new InteractorCustomException(name, ex);
            }
            finally
            {
                _hpInfRepository.ReleaseResource();
                _systemConfRepository.ReleaseResource();
                _santeiInfRepository.ReleaseResource();
                _mstItemRepository.ReleaseResource();
            }
        }

        private List<HpInfModel> ConvertItemToHpInfModel(List<HpInfItem> hpInfs, int hpId)
        {
            var result = new List<HpInfModel>();

            foreach (var item in hpInfs)
            {
                var validationStatus = item.Validation();
                if (validationStatus != ValidationHpInfStatus.None)
                {
                    return new();
                }

                result.Add(new HpInfModel(
                    hpId,
                    item.StartDate,
                    item.HpCd,
                    item.RousaiHpCd,
                    item.HpName,
                    item.ReceHpName,
                    item.KaisetuName,
                    item.PostCd,
                    item.PrefNo,
                    item.Address1,
                    item.Address2,
                    item.Tel,
                    item.FaxNo,
                    item.OtherContacts,
                    item.UpdateId,
                    item.HpInfModelStatus));
            }
            return result;
        }

        private List<SystemConfMenuModel> ConvertItemToSystemConfItemModel(List<SystemConfMenuItem> systemConfs, int hpId)
        {
            var result = new List<SystemConfMenuModel>();

            foreach (var item in systemConfs)
            {
                result.Add(new SystemConfMenuModel(
                        !item.SystemGenerationConfs.Any() ? new() :
                        item.SystemGenerationConfs.Select(x => new SystemGenerationConfModel(
                            x.Id,
                            hpId,
                            x.GrpCd,
                            x.GrpEdaNo,
                            x.StartDate,
                            x.EndDate,
                            x.Val,
                            x.Param,
                            x.Biko,
                            x.SystemGenerationConfStatus)).ToList(),

                        item.SystemConf == null ? new() :
                            new SystemConfModel(
                                hpId,
                                item.SystemConf.GrpCd,
                                item.SystemConf.GrpEdaNo,
                                item.SystemConf.Val,
                                item.SystemConf.Param,
                                item.SystemConf.Biko,
                                item.SystemConf.IsUpdatePtRyosyo,
                                item.SystemConf.SystemSettingModelStatus
                            )
                        ));
            }

            return result;
        }

        private List<SanteiInfDetailModel> ConvertItemToSanteiInfModel(List<SanteiInfDetailItem> santeiInfs)
        {
            var result = new List<SanteiInfDetailModel>();

            foreach (var item in santeiInfs)
            {
                result.Add(new SanteiInfDetailModel(
                    item.Id,
                    item.PtId,
                    item.ItemCd,
                    item.StartDate,
                    item.EndDate,
                    item.KisanSbt,
                    item.KisanDate,
                    item.Byomei,
                    item.HosokuComment,
                    item.Comment,
                    item.IsDeleted,
                    item.AutoSanteiMstModelStatus));
            }

            return result;
        }

        private List<KensaCenterMstModel> ConvertItemToKensaCenterMstModel(List<KensaCenterMstItem> kensaCenterMsts, int hpId)
        {
            var result = new List<KensaCenterMstModel>();
            foreach (var item in kensaCenterMsts)
            {
                result.Add(new KensaCenterMstModel(
                                                    item.Id,
                                                    hpId,
                                                    item.CenterCd,
                                                    item.CenterName,
                                                    item.PrimaryKbn,
                                                    item.SortNo,
                                                    item.KensaCenterMstModelStatus));
            }

            return result;
        }

        private bool ValidateSanteiInfs(SaveSystemSettingInputData inputData)
        {
            var groupedByItemCd = inputData.SanteiInfs.GroupBy(x => x.ItemCd);

            foreach (var group in groupedByItemCd)
            {
                var itemCd = group.Key;
                var existingAutoSanteiMst = _santeiInfRepository.GetAutoSanteiMstByItemCd(inputData.HpId, itemCd).ToList();

                foreach (var input in inputData.SanteiInfs.Where(x => x.ItemCd == itemCd))
                {
                    if (input.StartDate >= input.EndDate)
                        return false;

                    switch (input.AutoSanteiMstModelStatus)
                    {
                        case ModelStatus.Deleted:
                            existingAutoSanteiMst.RemoveAll(x => x.HpId == inputData.HpId && x.ItemCd == input.ItemCd && x.Id == input.Id);
                            break;
                        case ModelStatus.Modified:
                            var idx = existingAutoSanteiMst.FindIndex(x => x.HpId == inputData.HpId && x.ItemCd == input.ItemCd && x.Id == input.Id);
                            if (idx >= 0)
                                existingAutoSanteiMst[idx] = new AutoSanteiMstModel(input.Id, input.ItemCd, inputData.HpId, input.StartDate, input.EndDate);
                            break;
                        case ModelStatus.Added:
                            existingAutoSanteiMst.Add(new AutoSanteiMstModel(input.Id, input.ItemCd, inputData.HpId, input.StartDate, input.EndDate));
                            break;
                    }
                }

                var sorted = existingAutoSanteiMst.OrderBy(x => x.StartDate).ToList();
                int? lastEnd = null;
                foreach (var item in sorted)
                {
                    if (item.StartDate >= item.EndDate)
                        return false;

                    if (lastEnd != null && item.StartDate <= lastEnd)
                        return false;
                    if (lastEnd == null || item.EndDate > lastEnd)
                        lastEnd = item.EndDate;
                }
            }
            return true;
        }
    }
}
