﻿using Domain.Models.Insurance;
using Domain.Models.Online.QualificationConfirmation;
using Domain.Models.Online;
using System.Globalization;
using System.Xml.Serialization;
using UseCase.Insurance.SaveKohi;
using UseCase.PatientInfor.Save;
using UseCase.PatientInfor.SaveBasicInfo.SaveInsuranceInfo;
using Helper.Common;

namespace Interactor.InsuranceMst
{
    public class SaveKohiInteractor : ISaveKohiInputPort
    {
        private readonly IInsuranceRepository _insuranceReponsitory;

        public SaveKohiInteractor(IInsuranceRepository insuranceReponsitory)
        {
            _insuranceReponsitory = insuranceReponsitory;
        }

        public SaveKohiOutputData Handle(SaveKohiInputData inputData)
        {
            var status = ValidateInputData(inputData);
            if (status != SaveKohiStatus.Successed)
                return new SaveKohiOutputData(status, 0, 0);

            try
            {
                DateTime confirmDateInsert = DateTime.MinValue;

                if (inputData.IsConfirmOnline)
                {
                    var xmlObject = new XmlSerializer(typeof(QCXmlMsgResponse)).Deserialize(new StringReader(inputData.OnlineConfirmationHistory.ConfirmationResult)) as QCXmlMsgResponse;
                    if (xmlObject != null)
                    {
                        var onlineConfirmationDate = xmlObject.MessageHeader.ProcessExecutionTime;
                        var isConvert = DateTime.TryParseExact(onlineConfirmationDate, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out confirmDateInsert);
                        if (!isConvert) return new SaveKohiOutputData(SaveKohiStatus.Failed, 0, 0);

                        CIUtil.ConvertJapanTimeToUtc(ref confirmDateInsert);
                        confirmDateInsert = TimeZoneInfo.ConvertTimeToUtc(confirmDateInsert);
                    }
                }
                var model = new OnlineConfirmationHistoryModel(0, inputData.PtId, confirmDateInsert, inputData.OnlineConfirmationHistory.ConfirmationType, inputData.OnlineConfirmationHistory.InfoConsFlg, inputData.OnlineConfirmationHistory.ConfirmationResult, inputData.OnlineConfirmationHistory.PrescriptionIssueType, inputData.OnlineConfirmationHistory.UketukeStatus, inputData.HpId);

                var kohi = _insuranceReponsitory.SaveKohi(inputData.HpId, inputData.UserId, inputData.PtId, inputData.Kohi, inputData.LimitList, inputData.PatientInfo, inputData.PtKyuseiModel, model, inputData.IsConfirmOnline, inputData.EndDateModel);

                return new SaveKohiOutputData(SaveKohiStatus.Successed, kohi.kohiId, kohi.onlineConfirmHistoryId);
            }
            finally
            {

                _insuranceReponsitory.DeleteKeyInsuranceList(inputData.HpId, inputData.PtId);
                _insuranceReponsitory.ReleaseResource();
            }
        }

        private SaveKohiStatus ValidateInputData(SaveKohiInputData inputData)
        {
            if (inputData.PtId <= 0)
                return SaveKohiStatus.InvalidPtId;

            return SaveKohiStatus.Successed;
        }
    }
}
