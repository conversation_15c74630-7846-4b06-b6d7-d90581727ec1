﻿using Domain.Models.Online;
using Domain.Models.Reception;
using Helper.Constants;
using UseCase.Online.CreateOnlineConfirmationByXml;

namespace Interactor.Online
{
    public class CreateOnlineConfirmationByXmlInteractor : ICreateOnlineConfirmationByXmlInputPort
    {
        private readonly IOnlineRepository _onlineRepository;
        private readonly IReceptionRepository _receptionRepository;
        public CreateOnlineConfirmationByXmlInteractor(IOnlineRepository onlineRepository, IReceptionRepository receptionRepository)
        {
            _onlineRepository = onlineRepository;
            _receptionRepository = receptionRepository;
        }

        public CreateOnlineConfirmationByXmlOutputData Handle(CreateOnlineConfirmationByXmlInputData inputData)
        {
            try
            {
                var receptionDtoChanged = new List<ReceptionForViewDto>();
                var receptionDtoMew = new List<ReceptionRowModel>();

                if (inputData.HpId < 0)
                {
                    return new CreateOnlineConfirmationByXmlOutputData(CreateOnlineConfirmationByXmlStatus.InvalidHpid);
                }

                var res = _onlineRepository.AddConfDataHisByXml(inputData.HpId, inputData.XmlFileContent, inputData.UserId, inputData.PmhStatus, inputData.PmhResult);

                foreach (var raiino in res.Item2)
                {
                    var receptionInfos = _receptionRepository.GetRecptionList(inputData.HpId, 0, raiino);
                    receptionDtoChanged.AddRange(receptionInfos);
                }
                if (res.Item3 > 0)
                {
                    var receptionInfos = _receptionRepository.GetRecptionList(inputData.HpId, res.Item5, res.Item3);
                    receptionDtoChanged.AddRange(receptionInfos);
                }
                if (res.Item4 > 0)
                {
                    var receptionData = _receptionRepository.GetOnlinePatientData(0, inputData.HpId, res.Item4);
                    receptionDtoChanged.AddRange(receptionData);
                }

                return new CreateOnlineConfirmationByXmlOutputData(res.Item1, receptionDtoChanged, receptionDtoMew, CreateOnlineConfirmationByXmlStatus.Successed);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw e;
            }
            finally
            {
                _onlineRepository.ReleaseResource();
            }
        }
    }
}
