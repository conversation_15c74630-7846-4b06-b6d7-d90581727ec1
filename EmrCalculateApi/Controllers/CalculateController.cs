﻿using Domain.Models.Futan;
using CalculateService.Interface;
using CalculateService.Requests;
using EmrCalculateApi.Responses;
using EmrCalculateApi.Constants;
using EmrCalculateApi.Realtime;
using Helper.Messaging;
using Helper.Messaging.Data;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.Threading;
using Helper.Constants;
using System.Text;

namespace EmrCalculateApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CalculateController : ControllerBase
    {
        private readonly IIkaCalculateViewModel _ikaCalculate;
        private readonly IWebSocketService _webSocketService;
        private readonly IMessenger _messenger;
        private CancellationToken? _cancellationToken;

        public CalculateController(IIkaCalculateViewModel ikaCalculate, IWebSocketService webSocketService, IMessenger messenger)
        {
            _ikaCalculate = ikaCalculate;
            _webSocketService = webSocketService;
            _messenger = messenger;
        }

        [HttpPost("RunCalculateOne")]
        public ActionResult RunCalculateOne([FromBody] CalculateOneRequest calculateOneRequest)
        {
            _ikaCalculate.RunCalculateOne(
                calculateOneRequest.HpId,
                calculateOneRequest.PtId,
                calculateOneRequest.SinDate,
                calculateOneRequest.SeikyuUp,
                calculateOneRequest.Prefix);
            _ikaCalculate.Dispose();
            return Ok();
        }

        [HttpPost("RunCalculate")]
        public ActionResult RunCalculate([FromBody] CalculateRequest calculateRequest)
        {
            _ikaCalculate.RunCalculate(
                calculateRequest.HpId,
                calculateRequest.PtId,
                calculateRequest.SinDate,
                calculateRequest.SeikyuUp,
                calculateRequest.Prefix);
            _ikaCalculate.Dispose();
            return Ok();
        }

        [HttpPost("RunTrialCalculate")]
        public ActionResult RunTrialCalculate([FromBody] RunTraialCalculateRequest calculateRequest)
        {
            var data = _ikaCalculate.RunTraialCalculate(
                calculateRequest.HpId,
                calculateRequest.OrderInfoList,
                calculateRequest.Reception,
                calculateRequest.CalcFutan);
            var result = new RunTraialCalculateResponse(data.sinMeis, data.kaikeis.Select(k => new KaikeiInfItemResponse(k)).ToList(), data.calcLogs);
            _ikaCalculate.Dispose();
            return Ok(result);
        }

        [HttpPost("RunCalculateMonth")]
        public async Task<ActionResult> RunCalculateMonth([FromBody] RunCalculateMonthRequest monthRequest, CancellationToken cancellationToken)
        {
            _cancellationToken = cancellationToken;
            try
            {
                _messenger.Register<RecalculationStatus>(this, UpdateRecalculationStatus);
                _messenger.Register<StopCalcStatus>(this, StopCalculation);

                _ikaCalculate.RunCalculateMonth(
                              monthRequest.HpId,
                              monthRequest.SeikyuYm,
                              monthRequest.PtIds,
                              monthRequest.PreFix,
                              monthRequest.UniqueKey);
            }
            catch (Exception ex)
            {
                RecalculationStatus status = new RecalculationStatus(false, CalculateStatusConstant.Invalid, 0, 0, ex.Message, monthRequest.UniqueKey);
                _messenger.Send(status);
            }
            finally
            {
                _messenger.Deregister<RecalculationStatus>(this, UpdateRecalculationStatus);
                _messenger.Deregister<StopCalcStatus>(this, StopCalculation);
                await HttpContext.Response.CompleteAsync();
                HttpContext.Response.Body.Close();
                _ikaCalculate.Dispose();
            }
            return Ok();
        }

        private void StopCalculation(StopCalcStatus stopCalcStatus)
        {
            if (!_cancellationToken.HasValue)
            {
                stopCalcStatus.CallFailCallback(false);
            }
            else
            {
                stopCalcStatus.CallSuccessCallback(_cancellationToken!.Value.IsCancellationRequested);
            }
        }

        private async void UpdateRecalculationStatus(RecalculationStatus status)
        {
            string result = "\n" + JsonSerializer.Serialize(status);
            if (status.Length == status.SuccessCount || status.Type == CalculateStatusConstant.Invalid)
            {
                result += "\n";
            }
            var resultForFrontEnd = Encoding.UTF8.GetBytes(result.ToString());
            await HttpContext.Response.Body.WriteAsync(resultForFrontEnd, 0, resultForFrontEnd.Length);
            await HttpContext.Response.Body.FlushAsync();
        }
    }
}