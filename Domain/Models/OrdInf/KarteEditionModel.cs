﻿namespace Domain.Models.OrdInfs
{
    public class KarteEditionModel
    {
        public int HpId { get; private set; }
        public long PtId { get; private set; }
        public long RaiinNo { get; private set; }
        public int Edition { get; private set; }
        public int KarteStatus { get; private set; }
        public int IsDeleted { get; private set; }
        public DateTime CreateDate { get; private set; }
        public int CreateId { get; private set; }
        public DateTime UpdateDate { get; private set; }
        public int UpdateId { get; private set; }
        public DateTime? ApprovalDate { get; private set; }
        public int ApprovalId { get; private set; }
        public string UpdateName { get; private set; }
        public string ApprovalName { get; private set; }

        public KarteEditionModel()
        {
        }

        public KarteEditionModel(int hpId, long ptId, long raiinNo, int edition, int karteStatus, int isDeleted, DateTime createDate, int createId, DateTime updateDate, int updateId, DateTime? approvalDate, int approvalId)
        {
            HpId = hpId;
            PtId = ptId;
            RaiinNo = raiinNo;
            Edition = edition;
            KarteStatus = karteStatus;
            IsDeleted = isDeleted;
            CreateDate = createDate;
            CreateId = createId;
            UpdateDate = updateDate;
            UpdateId = updateId;
            ApprovalDate = approvalDate;
            ApprovalId = approvalId;
        }

        public KarteEditionModel(int hpId, long ptId, long raiinNo, int edition, int karteStatus, int isDeleted, DateTime createDate, int createId, DateTime updateDate, int updateId, DateTime? approvalDate, int approvalId, string updateName, string approvalName)
        {
            HpId = hpId;
            PtId = ptId;
            RaiinNo = raiinNo;
            Edition = edition;
            KarteStatus = karteStatus;
            IsDeleted = isDeleted;
            CreateDate = createDate;
            CreateId = createId;
            UpdateDate = updateDate;
            UpdateId = updateId;
            ApprovalDate = approvalDate;
            ApprovalId = approvalId;
            UpdateName = updateName;
            ApprovalName = approvalName;
        }
    }
}
