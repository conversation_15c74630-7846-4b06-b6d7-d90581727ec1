﻿using Domain.Models.Insurance;
using Helper.Common;
using Helper.Constants;

namespace Domain.Models.Reception
{
    public class ReceptionForViewDto
    {
        public ReceptionForViewDto(
            int hpId, long ptId, int sinDate, int isDeleted, long raiinNo, int uketukeNo, string uketukeTime, string yoyakuTime,
            string yoyakuEndTime, int status, int statusMstKbn, DateTime? statusKbnUpdateTime, long ptNum, int portalPtId, string kanaName,
            string name, int sex, int birthday, int treatmentDepartmentId, int kaId, string kaName, int tantoId, string tantoName, int? monshinStatus,
            string text, string memo, int reserveType, int meetingStatus, int hasUnread, List<Label> labels, List<Label> colorLabels,
            long onlineConfirmationId, List<KohiInfModel> kohi, string houbetu, int hokenSbtCd, long odrId, long karteEditionRaiinNo,
            int sortNoStatusMst, DateTime onlineConfirmationDate,
            int? portalCustomerId,
            int typeAlert, int reverseId, int reverseDetailId, long referenceNo, 
            int meetingId = 0,
            int hokenKbn = 0,
            string? errorCode = null,
            string? errorMessage = null,
            UserConfCommon.DateTimeFormart dateTimeFormart = UserConfCommon.DateTimeFormart.JapaneseCalendar)
        {
            HpId = hpId;
            PtId = ptId;
            SinDate = sinDate;
            IsDeleted = isDeleted;
            RaiinNo = raiinNo;
            UketukeNo = uketukeNo;
            UketukeTime = uketukeTime;
            YoyakuTime = yoyakuTime;
            YoyakuEndTime = yoyakuEndTime;
            Status = status;
            StatusMstKbn = statusMstKbn;
            StatusKbnUpdateTime = statusKbnUpdateTime;
            PtNum = ptNum;
            PortalPtId = portalPtId;
            KanaName = kanaName;
            Name = name;
            Sex = GetGenderText(sex);
            Birthday = birthday;
            Age = CIUtil.SDateToAge(birthday, sinDate);
            TreatmentDepartmentId = treatmentDepartmentId;
            KaId = kaId;
            KaName = kaName;
            TantoId = tantoId;
            TantoName = tantoName;
            MonshinStatus = monshinStatus;
            RaiinMemo = text;
            PtMemo = memo;
            ReverseType = reserveType;
            ReverseName = GetReserveName(reserveType);
            MeetingStatus = meetingStatus;
            MeetingStatusName = GetMeetingStatusName(meetingStatus);
            HasUnread = hasUnread;
            Labels = GetLabels(labels, colorLabels);
            OnlineConfirmationId = onlineConfirmationId;
            HokenName = Common.GetHokenName.CreateHokenName(houbetu, hokenSbtCd, kohi, hokenKbn);
            OdrId = odrId;
            KarteEditionRaiinNo = karteEditionRaiinNo;
            IsDisableButton = false;
            SortNoStatusMst = sortNoStatusMst;
            PortalCustomerId = portalCustomerId;
            OnlineConfirmationDate = CIUtil.DateTimeToInt(onlineConfirmationDate);
            TypeAlert = typeAlert;
            ErrorConfirmOnlineCode = errorCode;
            ErrorConfirmOnlineMessage = errorMessage;
            ReverseId = reverseId;
            ReverseDetailId = reverseDetailId;
            ReferenceNo = referenceNo;
            MeetingId = meetingId;
        }

        public ReceptionForViewDto()
        {
            HpId = 0;
            PtId = 0;
            SinDate = 0;
            IsDeleted = 0;
            RaiinNo = 0;
            UketukeNo = 0;
            UketukeTime = string.Empty;
            YoyakuTime = string.Empty;
            YoyakuEndTime = string.Empty;
            Status = 0;
            StatusMstKbn = 0;
            StatusKbnUpdateTime = DateTime.Now;
            PtNum = 0;
            PortalPtId = 0;
            KanaName = string.Empty;
            Name = string.Empty;
            Sex = string.Empty;
            Birthday = 0;
            Age = 0;
            TreatmentDepartmentId = 0;
            KaId = 0;
            KaName = string.Empty;
            TantoId = 0;
            TantoName = string.Empty;
            MonshinStatus = 0;
            RaiinMemo = string.Empty;
            PtMemo = string.Empty;
            ReverseName = string.Empty;
            MeetingStatusName = string.Empty;
            HasUnread = 0;
            Labels = new List<Label>();
            ReverseId = 0;
            ReverseDetailId = 0;
        }

        public int HpId { get; private set; }

        public long PtId { get; private set; }

        public int SinDate { get; private set; }

        public int IsDeleted { get; private set; }

        public long RaiinNo { get; private set; }

        // 順番
        public int UketukeNo { get; private set; }
        public string UketukeTime { get; private set; }
        public string YoyakuTime { get; private set; }
        public string YoyakuEndTime { get; private set; }
        // 状態
        public int Status { get; private set; }
        public int StatusMstKbn { get; private set; }
        public DateTime? StatusKbnUpdateTime { get; private set; }
        // 患者番号
        public long PtNum { get; private set; }
        public int PortalPtId { get; private set; }
        // カナ氏名
        public string KanaName { get; private set; }
        // 氏名
        public string Name { get; private set; }
        // 性
        public string Sex { get; private set; }
        // 生年月日
        public int Birthday { get; private set; }
        // 年齢
        public int Age { get; private set; }
        public int TreatmentDepartmentId { get; private set; }
        // 診療科
        public int KaId { get; private set; }
        public string KaName { get; private set; }
        public int TantoId { get; private set; }
        public string TantoName { get; private set; }
        public int? MonshinStatus { get; private set; }
        public string RaiinMemo { get; private set; }
        public string PtMemo { get; private set; }
        public int HasUnread { get; private set; }
        public int ReverseType { get; private set; }
        public string ReverseName { get; private set; }
        public int MeetingStatus { get; private set; }
        public string MeetingStatusName { get; private set; }
        public List<Label> Labels { get; set; }
        public long? OnlineConfirmationId { get; private set; }
        public string HokenName { get; private set; }
        public long KarteEditionRaiinNo { get; private set; }
        public long OdrId { get; private set; }
        public bool IsDisableButton { get; private set; }
        public int SortNoStatusMst { get; private set; }
        public int? PortalCustomerId { get; private set; }
        public int OnlineConfirmationDate { get; private set; }
        public int TypeAlert { get; private set; }
        public string? ErrorConfirmOnlineCode { get; private set; }
        public string? ErrorConfirmOnlineMessage { get; private set; }
        public int ReverseId { get; private set; }
        public int ReverseDetailId { get; private set; }
        public long ReferenceNo { get; private set; }
        public int MeetingId { get; private set; }

        private List<Label> GetLabels(List<Label> labels, List<Label> colorLabels)
        {
            foreach (var label in labels)
            {
                var colorLabel = colorLabels.Find(e => e.GrpId == label.GrpId && e.HpId == label.HpId);
                if (colorLabel != null) label.ColorCd = colorLabel.ColorCd;

            }
            return labels;
        }
        private string GetGenderText(int sex) => sex switch
        {
            1 => "M",
            2 => "F",
            _ => string.Empty
        };
        private string GetReserveName(int reserveType) => reserveType switch
        {
            1 => "オンライン診療",
            0 => "対面診療",
            _ => string.Empty
        };

        private string GetMeetingStatusName(int meetingStatus) => meetingStatus switch
        {
            0 => "未入室",
            1 => "入室済",
            2 => "入室済",
            3 => "入室済",
            _ => string.Empty
        };


        private string GetConfirmationStateText(int confirmationState) => confirmationState switch
        {
            1 => "有効",
            2 => "無効",
            3 => "無効（新しい資格あり）",
            4 => "該当資格なし",
            5 => "複数該当",
            97 => "確認エラー",
            98 => "確認中",
            99 => "確認完了",
            _ => string.Empty
        };

        public ReceptionForViewDto SetSinDate(int sinDate)
        {
            SinDate = sinDate;
            return this;
        }
        //public int HokenPid { get; private set; }
    }

    public class Label
    {
        public Label(int grpId, string name, string colorCd, int sortNo, int hpId)
        {
            GrpId = grpId;
            Name = name;
            ColorCd = colorCd;
            SortNo = sortNo;
            HpId = hpId;
        }

        public int GrpId { get; private set; }
        public string Name { get; private set; }
        public string ColorCd { get; set; }
        public int SortNo { get; private set; }
        public int HpId { get; private set; }
    }
}
