﻿using Domain.Common;

namespace Domain.Models.Family;

public interface IFamilyRepository : IRepositoryBase
{
    List<PtFamilyRekiModel> GetFamilyList(int hpId, long ptId, int sinDate);

    //List<PtFamilyRekiModel> GetFamilyReverserList(int hpId, long familyPtId, List<long> ptIdInputList);

    //bool SaveFamilyList(int hpId, int userId, List<PtFamilyRekiModel> familyList);

    List<PtFamilyRekiModel> GetListByPtId(int hpId, long ptId);

    //bool CheckExistFamilyRekiList(int hpId, List<long> familyRekiIdList);

    List<RaiinInfModel> GetRaiinInfListByPtId(int hpId, long ptId);

    List<PtFamilyRekiModel> GetFamilyListByPtId(int hpId, long ptId, int sinDate);

    //List<PtFamilyRekiModel> GetMaybeFamilyList(int hpId, long ptId, int sinDate);
}
