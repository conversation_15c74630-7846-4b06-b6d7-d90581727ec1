﻿using Domain.Common;

namespace Domain.Models.KensaCenterPartnership;
public interface IKensaCenterPartnershipRepository : IRepositoryBase
{
    List<KensaCenterPartnershipModel> GetKensaCenterPartnership(int hpId, string? centerCd);
    KensaCenterPartnershipModel RegisterKensaCenterPartnership(int hpId, string centerCd, int startDate, int endDate);
    KensaCenterPartnershipModel UpdateKensaCenterPartnership(int hpId, string centerCd, int oldStartDate, int startDate, int endDate);
    bool UnregisterKensaCenterPartnership(int hpId, string centerCd, int startDate);
    List<KensaCenterPartnershipModel> GetKensaCenterPartnershipBySinDate(int hpId, int sinDate);
    void UpdateKensaCenterPartnershipMstUpdateDate(int hpId);
}
