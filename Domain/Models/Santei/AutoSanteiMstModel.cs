namespace Domain.Models.Santei

{
    public class AutoSanteiMstModel
    {
        public long Id { get; set; }
        public int HpId { get; set; }
        public string ItemCd { get; set; } = string.Empty;
        public int SeqNo { get; set; }
        public int StartDate { get; set; }
        public int EndDate { get; set; }
        public DateTime CreateDate { get; set; }
        public int CreateId { get; set; }
        public string? CreateMachine { get; set; } = string.Empty;
        public DateTime UpdateDate { get; set; }
        public int UpdateId { get; set; }
        public string? UpdateMachine { get; set; } = string.Empty;

        public AutoSanteiMstModel(long id, string itemCd, int hpId, int startDate, int endDate)
        {
            Id = id;
            ItemCd = itemCd;
            HpId = hpId;
            StartDate = startDate;
            EndDate = endDate;
        }
    }
}