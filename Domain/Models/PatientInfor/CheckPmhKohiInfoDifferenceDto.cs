﻿namespace Domain.Models.PatientInfor
{
    public class CheckPmhKohiInfoDifferenceDto
    {
        public bool IsMapAll { get; set; }

        public bool IsNotExistKohiCompare { get; set; }

        public bool IsContinue { get; set; }

        public long SeqNo { get; set; }

        public int HokenId { get; set; }

        public string HokenName { get; set; }

        public List<PmhKohiInfoDifferenceProperty> PmhKohiInfoDifferenceProperties { get; set; }
    }

    public class PmhKohiInfoDifferenceProperty
    {
        public int Index { get; set; }

        public string Name { get; set; }

        public string XmlValue { get; set; }

        public string KohiValue { get; set; }

        public bool IsMap { get; set; }
    }
}
