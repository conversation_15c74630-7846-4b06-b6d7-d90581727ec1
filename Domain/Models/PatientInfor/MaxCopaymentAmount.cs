﻿namespace Domain.Models.PatientInfor
{
    public class MaxCopaymentAmount
    {
        public string? Type { get; set; }

        public string? IsForFirstVisitOnly { get; set; }

        public string? BurdenRatioPerDay { get; set; }

        public string? BurdenRatioPerMonth { get; set; }

        public string? BurdenRatioPerTimes { get; set; }

        public string? AmountPerDay { get; set; }

        public string? AmountPerMonth { get; set; }

        public string? AmountPerTimes { get; set; }

        public string? MaxNumberOfTimesPerDay { get; set; }

        public string? MaxNumberOfTimesPerMonth { get; set; }
    }
}
