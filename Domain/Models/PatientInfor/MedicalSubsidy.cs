﻿namespace Domain.Models.PatientInfor
{
    public class MedicalSubsidy
    {
        public string MedicalSubsidyInsurerNumber { get; set; }

        public string MedicalSubsidyRecipientNumber { get; set; }

        public string IsAlwaysReimbursement { get; set; }

        public MaxCopaymentAmount[] MaxCopaymentAmount { get; set; }

        public string ValidFrom { get; set; }

        public string ValidTo { get; set; }
    }
}
