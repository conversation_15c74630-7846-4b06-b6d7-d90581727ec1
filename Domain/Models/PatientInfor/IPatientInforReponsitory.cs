﻿using Domain.Common;
using Domain.Models.CalculationInf;
using Domain.Models.GroupInf;
using Domain.Models.Insurance;
using Domain.Models.InsuranceInfor;
using Domain.Models.InsuranceMst;
using Domain.Models.MaxMoney;
using Domain.Models.PortalCustomerFile;
using Domain.Models.PortalCustomerLogin;
using Domain.Models.Online;
using HokenInfModel = Domain.Models.Insurance.HokenInfModel;
using System.Collections.Generic;

namespace Domain.Models.PatientInfor
{
    public interface IPatientInforRepository : IRepositoryBase
    {
        PatientInforModel? GetById(int hpId, long ptId, int sinDate, long raiinNo, bool isShowKyuSeiName = false, List<int>? listStatus = null);

        (PatientInforModel ptInfModel, bool isFound) SearchExactlyPtNum(long ptNum, int hpId, int sinDate);

        List<PatientInforModel> SearchContainPtNum(long ptNum, string keyword, int hpId, int pageIndex, int pageSize, Dictionary<string, string> sortData);

        List<PatientInforModel> SearchBySindate(int sindate, int hpId, int pageIndex, int pageSize, Dictionary<string, string> sortData);

        List<PatientInforModel> SearchPhone(string keyword, bool isContainMode, int hpId, int pageIndex, int pageSize, Dictionary<string, string> sortData);

        List<PatientInforModel> SearchName(string originKeyword, string halfsizeKeyword, bool isContainMode, int hpId, int pageIndex, int pageSize, Dictionary<string, string> sortData);

        List<PatientInforModel> SearchSimple(string keyword, bool isContainMode, int hpId);

        List<PatientInforModel> GetAdvancedSearchResults(PatientAdvancedSearchInput input, int hpId, int pageIndex, int pageSize, Dictionary<string, string> sortData);

        PatientInforModel PatientCommentModels(int hpId, long ptId);

        PatientInforModel GetPtInfByRefNo(int hpId, long refNo);

        List<PatientInforModel> SearchEmptyId(int hpId, long ptNum, int pageIndex, int pageSize, bool isPtNumCheckDigit, int autoSetting);

        bool CheckExistIdList(int hpId, List<long> ptIds);

        List<TokkiMstModel> GetListTokki(int sinDate);

        List<DefHokenNoModel> GetDefHokenNoModels(int hpId, string futansyaNo);

        List<PtKyuseiInfModel> PtKyuseiInfModels(int hpId, long ptId, bool isDeleted);

        PtKyuseiInfModel GetDocumentKyuSeiInf(int hpId, long ptId, int sinDay);

        bool SaveInsuranceMasterLinkage(List<DefHokenNoModel> defHokenNoModels, int hpId, int userId);

        (bool resultSave, long ptId) CreatePatientInfo(PatientInforSaveModel ptInf, List<PtKyuseiModel> ptKyuseis, List<CalculationInfModel> ptSanteis, List<InsuranceModel> insurances, List<HokenInfModel> hokenInfs, List<KohiInfModel> hokenKohis, List<GroupInfModel> ptGrps, List<LimitListModel> maxMoneys, Func<int, long, long, IEnumerable<InsuranceScanModel>> handlerInsuranceScans, int userId);

        (bool resultSave, long ptId) UpdatePatientInfo(PatientInforSaveModel ptInf, List<PtKyuseiModel> ptKyuseis, List<CalculationInfModel> ptSanteis, List<InsuranceModel> insurances, List<HokenInfModel> hokenInfs, List<KohiInfModel> hokenKohis, List<GroupInfModel> ptGrps, List<LimitListModel> maxMoneys, Func<int, long, long, IEnumerable<InsuranceScanModel>> handlerInsuranceScans, int userId, List<int> hokenIdList);

        bool DeletePatientInfo(long ptId, int hpId, int userId);
        bool IsAllowDeletePatient(int hpId, long ptId);

        HokenMstModel GetHokenMstByInfor(int hpId, int hokenNo, int hokenEdaNo, int sinDate);

        HokensyaMstModel GetHokenSyaMstByInfor(int hpId, string houbetu, string hokensya);

        PatientInforModel GetPtInf(int hpId, long ptId);

        List<PatientInforModel> SearchPatient(int hpId, long ptId, int pageIndex, int pageSize);

        List<PatientInforModel> SearchPatient(int hpId, int startDate, string startTime, int endDate, string endTime);

        List<PatientInforModel> SearchPatient(int hpId, List<long> ptIdList);

        int GetIsRyosyoDetail(int hpId, long ptId);

        long GetPtIdFromPtNum(int hpId, long ptNum);

        long IsPatientExist(int hpId, long ptNum);

        int GetCountRaiinAlreadyPaidOfPatientByDate(int hpId, int fromDate, int toDate, long ptId, int raiintStatus);

        List<PatientInforModel> FindSamePatient(int hpId, string kanjiName, int sex, int birthDay);

        List<PatientInforModel> GetPtInfModelsByName(int hpId, string kanaName, string name, int birthDate, int sex1, int sex2);

        List<PatientInforModel> GetPtInfModels(int hpId, long refNo);

        bool SavePtKyusei(int hpId, int userId, List<PtKyuseiModel> ptKyuseiList);

        List<VisitTimesManagementModel> GetVisitTimesManagementModels(int hpId, int sinYm, long ptId, int kohiId);

        bool UpdateVisitTimesManagement(int hpId, int userId, long ptId, int kohiId, int sinYm, List<VisitTimesManagementModel> visitTimesManagementList);

        bool UpdateVisitTimesManagementNeedSave(int hpId, int userId, long ptId, List<VisitTimesManagementModel> visitTimesManagementList);
        
        CheckDrawerLedgerDataExistedModel CheckDrawerLedgerDataExisted(int hpId, long ptId, long raiinNo, int sinDate);

        (bool resultSave, long ptId, List<long> listPtNum, long raiinNo) SavePatientInfo(PatientInforSaveModel ptInf, int userId, string ptMemo, bool isSaveDuplicateInfo, bool isConfirmOnline, int onlineConfirmationId = 0, int sinDate = 0);

        (bool resultSave, long ptId, int hokenId, long onlineConfirmHistoryId) SaveInsuranceInfo(int hpId, int ptId, HokenInfModel hokenInf, int userId, int sinDate, List<PatientInforConfirmOnlineDto> patientInfo, PtKyuseiModel? ptKyuseiModel, OnlineConfirmationHistoryModel onlineConfirmationHistory, bool isConfirmOnline, EndDateModel? endDateModel);

        CheckPatientHokenInfoDifferenceDto CheckPatientHokenInfoDifference(int hpid, long ptId, int age, int sinDate, CheckPatientHokenInfoDifferenceModel checkPatientHokenInfoDifferenceModel);
        (bool isExist, KohiInfModel kohiModel) GetKohiInfByInf(int hpId, int hokenId, long ptId);
        (bool isExist, HokenInfModel hokenModel) GetHokenInfByInfo(int hpId, int hokenId, long ptId);
        List<ConfirmDateModel> GetCheckDateByInfo(int hpId, int hokenId, long ptId, int hokenGrp);
        bool IsValidDuplicateHoken(int hpId, int ptId, string hokensyaNo, int startDate, int endDate, int hokenId);

        HokenMstModel GetHokenMst(int hpId, int hokenNo, int hokenEdaNo, int sinDate);

        (int minStartdate, int maxEnddate) GetExpiredHokenMst(int hpId, int hokenNo, int hokenEdaNo, int sinDate);

        BasicPatientInfoModel GetBasicPatientInfo(long ptId, int hpId);
        List<PortalCustomerFileModel> GetInsuranceImageCard(long ptId, int hpId);
        bool EditBasicInfoOverwrite(int hpId, long ptId, int userId,
                    string name, bool isNameUpdated, string kaneName, bool isKanaNameUpdated,
                    int birthday, bool isBirthdayUpdated, string address, bool isAddressUpdated,
                    string homePost, bool isHomePostUpdated, string tel, bool isTelUpdated, int sex, 
                    bool isSexUpdate, string mail, bool isMailUpdated, string address2);

        bool SaveHokenCheck(int hpId, int userId, long ptId, List<SaveHokenCheckDto> hokenCheckDtos);

        CheckKohiInfoDifferenceDto CheckKohiInfoDifference(int hpid, long ptId, bool isMarucho,CheckKohiInfoDifferenceModel checkKohiInfoDifferenceModel);
        (bool result, List<int> hokenIds, List<int> kohiIds) SaveOnlineMyCardBeforeReception(int hpId,
            int ptId,
            int onlineConfirmHistoryId,
            int userId,
            int sinDate,
            List<PatientInforConfirmOnlineDto> patientInfoFields,
            PtKyuseiModel? ptKyuseiModel,
            List<HokenInfModel> hokenInfs,
            List<KohiInfModel> kohiModels,
            List<EndDateModel>? endDateModels);

        CheckPatientInfoDifferenceModel CheckPatientInfoDifference(int hpid, long ptId, long onlineConfirmationId);
        bool BulkUpdateHoumonAgreed(List<PatientInforModel> ptInforModels, int hpId, int houmonAgreed);

        List<KohiInfModel> GetListPmhKohiInf(int hpId, long ptId, int sinDate, string medicalSubsidyInsurerNumber, string medicalSubsidyRecipientNumber);

        int GetPrefNo(int hpId);

        List<HokenMstModel> GetHokenMstForPmhKohi(int hpId, string houbetu, int sinDate);
    }
}