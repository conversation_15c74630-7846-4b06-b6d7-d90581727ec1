﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Domain.Models.Online.OQSmutic01res;
using Entity.Tenant;
using Helper.Common;

namespace Domain.Models.Online
{
    public class OnlineConfirmationModel
    {
        public OnlineConfirmationModel()
        {
            ReceptionNo = string.Empty;
        }
        public OnlineConfirmationModel(string receptionNo, DateTime receptionDateTime, DateTime processTime, int yoyakuDate, int sin_ym, int consentFrom, int consentTo, int examinationFrom, int examinationTo, int batchConfirmationType)
        {
            if(processTime != DateTime.MinValue)
                CIUtil.ConvertJapanTimeToUtc(ref processTime);
            if(receptionDateTime != DateTime.MinValue)
                CIUtil.ConvertJapanTimeToUtc(ref receptionDateTime);
            ReceptionNo = receptionNo;
            ReceptionDateTime = receptionDateTime;
            ProcessTime = processTime;
            YoyakuDate = yoyakuDate;
            ExaminationFrom = examinationFrom;
            ExaminationTo = examinationTo;
            SinYm = sin_ym;
            ConsentFrom = consentFrom;
            ConsentTo = consentTo;
            BatchConfirmationType = batchConfirmationType;
        }

        public string ReceptionNo { get; private set; } 
        public DateTime ReceptionDateTime { get; private set; }
        public int YoyakuDate { get; private set; }
        public string? SegmentOfResult { get; private set; }
        public string? ErrorMessage { get; private set; }     
        public int ConfirmationType { get; private set; }
        public int ExaminationTo { get; private set; }
        public int SinYm { get; private set; }
        public int ConsentFrom { get; private set; }
        public int ConsentTo { get; private set; }
        public int ExaminationFrom { get; private set; }
        public int BatchConfirmationType { get; private set; }
        public DateTime? ProcessTime { get; private set; }
    }



}
