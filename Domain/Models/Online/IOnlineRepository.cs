﻿using Domain.Common;
using Domain.Models.Insurance;
using Domain.Models.Online.QualificationConfirmation;
using Domain.Models.Online.ViewResult;
using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using Helper.Constants;

namespace Domain.Models.Online;

public interface IOnlineRepository : IRepositoryBase
{
    List<long> InsertOnlineConfirmHistory(int userId, List<OnlineConfirmationHistoryModel> onlineList, int hpId);

    List<OnlineConfirmationHistoryModel> GetRegisterdPatientsFromOnline(int confirmDate, int hpId, int id = 0, int confirmType = 1);

    bool UpdateOnlineConfirmationHistory(int uketukeStatus, int id, int userId);

    bool UpdateOnlineHistoryById(int userId, long id, long ptId, int uketukeStatus, int confirmationType, int hpId);

    bool CheckExistIdList(List<long> idList, int hpId);

    bool UpdateOQConfirmation(int hpId, int userId, long onlineHistoryId, Dictionary<string, string> onlQuaResFileDict, Dictionary<string, (int confirmationType, string infConsFlg)> onlQuaConfirmationTypeDict);

    bool SaveAllOQConfirmation(int hpId, int userId, long ptId, Dictionary<string, string> onlQuaResFileDict, Dictionary<string, (int confirmationType, string infConsFlg)> onlQuaConfirmationTypeDict);

    bool SaveOQConfirmation(int hpId, int userId, long onlineHistoryId, long ptId, string confirmationResult, string onlineConfirmationDateString, int confirmationType, string infConsFlg, int uketukeStatus = 0, bool isUpdateRaiinInf = true);

    bool UpdateOnlineInRaiinInf(int hpId, int userId, long ptId, DateTime onlineConfirmationDate, int confirmationType, string infConsFlg);

    long UpdateRefNo(int hpId, long ptId, int userId);

    bool UpdatePtInfOnlineQualify(int hpId, int userId, long ptId, List<PtInfConfirmationModel> resultList);

    List<OnlineConfirmationHistoryModel> GetListOnlineConfirmationHistoryModel(long ptId, int hpId);

    List<OnlineConfirmationHistoryModel> GetListOnlineConfirmationHistoryModelById(int hpId, long onlineConfirmationId);

    List<OnlineConfirmationHistoryModel> GetListOnlineConfirmationHistoryModel(int userId, Dictionary<string, string> onlQuaResFileDict, Dictionary<string, (int confirmationType, string infConsFlg)> onlQuaConfirmationTypeDict, int hpId);

    List<OnlineConsentModel> GetOnlineConsentModel(long ptId, int hpId, bool isContainAgreedConsent = false);

    bool UpdateOnlineConsents(int userId, long ptId, List<QCXmlMsgResponse> responseList, int hpId);

    List<QualificationInfModel> GetListQualificationInf(int hpId);

    bool SaveOnlineConfirmation(int userId, QualificationInfModel qualificationInf, ModelStatus status, int hpId);
    bool UpdateOnlineConfirmation(int userId, QualificationInfModel qualificationInf, int hpId);

    bool InsertListOnlConfirmHistory(int userId, List<OnlineConfirmationHistoryModel> listOnlineConfirmationHistoryModel, int hpId);

    (bool, List<ReceptionRowModel> receptions) UpdateRaiinInfByResResult(int hpId, int userId, List<ConfirmResultModel> listResResult);

    bool ExistOnlineConsent(long ptId, int sinDate, int hpId);

    List<BatchOnlineCheckModel> GetBatchOnlineChecks(int hpId, int batchConfirmationType);

    List<PatientInforModel> GetPatientHomeVisit(int hpId, int ptNum);

    bool CancelPatientHomeVisit(int hpId, long ptId, int userId);

    long DeleteConfirmationHis(int hpId, int userId, long onlineHisId);

    Tuple<bool, List<long>, long, long, int> AddConfDataHisByXml(int hpId, string xmlContent, int userId, int pmhStatus, string pmhResult);

    long SaveOnlineConfirmationHistory(OnlineConfirmationHistoryModel onlineConfirmationHistoryModel, int userId);

    void SaveOnlineConfirmationComposite(int userId, int hpId, List<OnlineConfirmationCompositeModel> onlineConfirmationComposite);
    void UpdateHokenCheck(ConfirmDateModel model, int hpId, int checkDate);
    bool ProcessOQSXML(int hpId,int userId, OnlineConfirmationModel onlineConfirmationModel);

    (bool, long) ViewResultConfirm(int hpId, long ptId, long onlineDetailId, int userId, int confirmType, string xmlValue, bool isRegiterPatient);

    List<OnlineConfirmationDetailDto> GetListViewResult(int hpId, string receptionNo, bool? isConfirmState, bool? isStatus);

    OnlineConfirmationDetailDto GetOnlineConfirmationDetailById(long id);

    bool IsViewDrugInformation(long ptId);
}
