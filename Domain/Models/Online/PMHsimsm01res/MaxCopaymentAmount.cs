﻿using System.Xml.Serialization;

namespace Domain.Models.Online.PMHsimsm01res
{
    [Serializable]
    [XmlRoot(ElementName = "MaxCopaymentAmount")]
    public class MaxCopaymentAmount
    {
        [XmlElement(ElementName = "Type")]
        public string Type { get; set; } = default!;

        [XmlElement(ElementName = "BurdenDefinition")]
        public string BurdenDefinition { get; set; } = default!;

        [XmlElement(ElementName = "IsForFirstVisitOnly")]
        public string IsForFirstVisitOnly { get; set; } = default!;

        [XmlElement(ElementName = "BurdenRatioPerDay")]
        public string BurdenRatioPerDay { get; set; } = default!;

        [XmlElement(ElementName = "BurdenRatioPerMonth")]
        public string BurdenRatioPerMonth { get; set; } = default!;

        [XmlElement(ElementName = "BurdenRatioPerTimes")]
        public string BurdenRatioPerTimes { get; set; } = default!;

        [XmlElement(ElementName = "AmountPerDay")]
        public string AmountPerDay { get; set; } = default!;

        [XmlElement(ElementName = "AmountPerMonth")]
        public string AmountPerMonth { get; set; } = default!;

        [XmlElement(ElementName = "AmountPerTimes")]
        public string AmountPerTimes { get; set; } = default!;

        [XmlElement(ElementName = "MaxNumberOfTimesPerDay")]
        public string MaxNumberOfTimesPerDay { get; set; } = default!;

        [XmlElement(ElementName = "MaxNumberOfTimesPerMonth")]
        public string MaxNumberOfTimesPerMonth { get; set; } = default!;

        [XmlElement(ElementName = "DietaryTherapyCostBurden")]
        public string DietaryTherapyCostBurden { get; set; } = default!;

        [XmlElement(ElementName = "DietaryTherapyMaxCopaymentAmountPerDay")]
        public string DietaryTherapyMaxCopaymentAmountPerDay { get; set; } = default!;
    }
}
