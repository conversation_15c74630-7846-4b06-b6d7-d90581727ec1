﻿using System.Xml.Serialization;

namespace Domain.Models.Online.PMHsimsm01res
{
    [Serializable]
    [XmlRoot(ElementName = "MedicalSubsidies")]
    public class MedicalSubsidies
    {
        [XmlElement(ElementName = "MedicalSubsidyInsurerNumber")]
        public string MedicalSubsidyInsurerNumber { get; set; } = default!;

        [XmlElement(ElementName = "IssuerLocalGovCode")]
        public string IssuerLocalGovCode { get; set; } = default!;

        [XmlElement(ElementName = "CertificationName")]
        public string CertificationName { get; set; } = default!;

        [XmlElement(ElementName = "MedicalSubsidyRecipientNumber")]
        public string MedicalSubsidyRecipientNumber { get; set; } = default!;

        [XmlElement(ElementName = "Segment")]
        public string Segment { get; set; } = default!;

        [XmlElement(ElementName = "IncomeSegment")]
        public string IncomeSegment { get; set; } = default!;

        [XmlElement(ElementName = "AdditionalConditions")]
        public AdditionalConditions[]? AdditionalConditions { get; set; }

        [XmlElement(ElementName = "DesignatedMedicalInstitution")]
        public string DesignatedMedicalInstitution { get; set; } = default!;

        [XmlElement(ElementName = "IsAlwaysReimbursement")]
        public string IsAlwaysReimbursement { get; set; } = default!;

        [XmlElement(ElementName = "IsMaxCopaymentAmountAggregated")]
        public string IsMaxCopaymentAmountAggregated { get; set; } = default!;

        [XmlElement(ElementName = "IsUnlistedConditionsOnMaxCopaymentAmount")]
        public string IsUnlistedConditionsOnMaxCopaymentAmount { get; set; } = default!;

        [XmlElement(ElementName = "MaxCopaymentAmount")]
        public MaxCopaymentAmount[]? MaxCopaymentAmount { get; set; }

        [XmlElement(ElementName = "ValidFrom")]
        public string ValidFrom { get; set; } = default!;

        [XmlElement(ElementName = "ValidTo")]
        public string ValidTo { get; set; } = default!;

        [XmlElement(ElementName = "UpdateVerificationHash")]
        public string UpdateVerificationHash { get; set; } = default!;

        [XmlElement(ElementName = "CertificationDetail")]
        public CertificationDetail CertificationDetail { get; set; } = new();
    }
}
