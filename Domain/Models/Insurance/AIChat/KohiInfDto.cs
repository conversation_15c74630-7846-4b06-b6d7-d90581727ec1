﻿using Domain.Models.InsuranceMst;
using Helper.Common;
using Helper.Extension;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Models.Insurance.AIChat
{
    public class KohiInfDto
    {
        public KohiInfDto(string futansyaNo, string jyukyusyaNo, int hokenId, int startDate, int endDate, int checkDate, string tokusyuNo, int hokenSbtKbn, int hokenNo, int hokenEdaNo, int sinDate, int isDeleted, List<HokenCheckDto> listHokenCheck, string hokenNameCd, string houbetu, int sinDateRecentUse, long seqNo)
        {
            FutansyaNo = futansyaNo;
            JyukyusyaNo = jyukyusyaNo;
            HokenId = hokenId;
            StartDate = startDate;
            EndDate = endDate;
            CheckDate = checkDate;
            TokusyuNo = tokusyuNo;
            HokenSbtKbn = hokenSbtKbn;
            HokenNo = hokenNo;
            HokenEdaNo = hokenEdaNo;
            SinDate = sinDate;
            IsDeleted = isDeleted;
            ListHokenCheck = listHokenCheck;
            HokenNameCd = hokenNameCd;
            Houbetu = houbetu;
            SinDateRecentUse = sinDateRecentUse;
            SeqNo = seqNo;
        }

        public KohiInfDto()
        {
            FutansyaNo = string.Empty;
            JyukyusyaNo = string.Empty;
            TokusyuNo = string.Empty;
            HokenNameCd = string.Empty;
            Houbetu = string.Empty;
            ListHokenCheck = new();
        }

        public KohiInfDto(string futansyaNo, string jyukyusyaNo, int hokenId, int startDate, int endDate, int checkDate, string tokusyuNo, int hokenSbtKbn, int hokenNo, int hokenEdaNo, int sinDate, int isDeleted, List<HokenCheckDto> listHokenCheck, string hokenNameCd, string houbetu, int sinDateRecentUse, long seqNo, int rate, int gendoGaku, int sikakuDate, int kofuDate, int onlineConfirmCheckDate, int prefNo, int countKohi)
        {
            FutansyaNo = futansyaNo;
            JyukyusyaNo = jyukyusyaNo;
            HokenId = hokenId;
            StartDate = startDate;
            EndDate = endDate;
            CheckDate = checkDate;
            TokusyuNo = tokusyuNo;
            HokenSbtKbn = hokenSbtKbn;
            HokenNo = hokenNo;
            HokenEdaNo = hokenEdaNo;
            SinDate = sinDate;
            IsDeleted = isDeleted;
            ListHokenCheck = listHokenCheck;
            HokenNameCd = hokenNameCd;
            Houbetu = houbetu;
            SinDateRecentUse = sinDateRecentUse;
            SeqNo = seqNo;
            Rate = rate;
            GendoGaku = gendoGaku;
            SikakuDate = sikakuDate;
            KofuDate = kofuDate;
            OnlineConfirmCheckDate = onlineConfirmCheckDate;
            PrefNo = prefNo;
            CountKohi = countKohi;
        }

        public KohiInfDto(string futansyaNo, string jyukyusyaNo, int hokenId, int hokenKbn)
        {
            FutansyaNo = futansyaNo;
            JyukyusyaNo = jyukyusyaNo;
            HokenId = hokenId;
            HokenKbn = hokenKbn;
            TokusyuNo = string.Empty;
            HokenNameCd = string.Empty;
            Houbetu = string.Empty;
            ListHokenCheck = new();
        }

        public string FutansyaNo { get; private set; }

        public string JyukyusyaNo { get; private set; }

        public int HokenId { get; private set; }

        public int StartDate { get; private set; }

        public int EndDate { get; private set; }

        public int CheckDate { get; private set; }

        public string TokusyuNo { get; private set; }

        public int HokenSbtKbn { get; private set; }

        public int HokenNo { get; private set; }

        public int HokenEdaNo { get; private set; }

        public int SinDate { get; private set; }

        public int IsDeleted { get; private set; }

        public string HokenNameCd { get; private set; }

        public string Houbetu { get; private set; }

        public int OnlineConfirmCheckDate { get; private set; }

        public List<HokenCheckDto> ListHokenCheck { get; private set; }
        public bool IsEmptyModel => HokenId == 0;

        public bool IsExpirated
        {
            get
            {
                return !(StartDate <= SinDate && EndDate >= SinDate);
            }
        }

        public int SinDateRecentUse { get; private set; }

        public long SeqNo { get; private set; }

        public int Rate { get; private set; }

        public int GendoGaku { get; private set; }

        public int SikakuDate { get; private set; }

        public int KofuDate { get; private set; }

        public int PrefNo { get; private set; }

        public int CountKohi { get; private set; }

        public bool IsDefault { get; set; }

        public int HokenKbn { get; private set; }

        //public int IsLimitList { get => HokenMstModel.IsLimitList; }

        //public bool HasDateConfirmed
        //{
        //    get
        //    {
        //        if (ConfirmDateList == null) return false;
        //        if (ConfirmDateList.Count == 0)
        //        {
        //            return false;
        //        }
        //        var isValidHokenChecks = ConfirmDateList
        //            .Where(x => x.IsDeleted == 0)
        //            .OrderByDescending(x => x.ConfirmDate)
        //            .ToList();
        //        int sinYM = CIUtil.Copy(SinDate.AsString(), 1, 6).AsInteger();
        //        foreach (var ptHokenCheck in isValidHokenChecks)
        //        {
        //            int currentConfirmYM = CIUtil.Copy(ptHokenCheck.ConfirmDate.AsString(), 1, 6).AsInteger();
        //            if (currentConfirmYM == sinYM)
        //            {
        //                return true;
        //            }
        //        }
        //        return false;
        //    }
        //}

        //public int LastDateConfirmed
        //{
        //    get
        //    {
        //        if (!ConfirmDateList.Any()) return 0;

        //        return CIUtil.Copy(ConfirmDateList.OrderByDescending(item => item.ConfirmDate).First().ConfirmDate.AsString(), 1, 8).AsInteger();
        //    }
        //}

        public string KohiName
        {
            get
            {
                var result = $"{GetKohiName(false)} {FutansyaNo}";
                if (IsExpirated)
                {
                    result = "×" + result + "（有効期限切れ）";
                }
                return result;
            }
        }

        public string GetKohiName(bool isUseHobetu)
        {
            string result = string.Empty;
            result = HokenId.ToString().PadLeft(2, '0');
            if (!string.IsNullOrEmpty(HokenNameCd))
            {
                result += " " + HokenNameCd;
            }
            if (!string.IsNullOrEmpty(Houbetu) && isUseHobetu)
            {
                result += Houbetu;
            }
            return result;
        }
    }
}
