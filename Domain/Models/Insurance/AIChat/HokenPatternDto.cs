﻿using Domain.Models.PatientInfor;
using Helper.Constants;

namespace Domain.Models.Insurance.AIChat
{
    public class HokenPatternDto
    {
        public HokenPatternDto(int hpId, long ptId, int hokenSbtCd, int hokenPid, int hokenKbn, string hokenMemo, int sinDate, int isDeleted, HokenInfDto hokenInf, KohiInfDto kohi1, KohiInfDto kohi2, KohiInfDto kohi3, KohiInfDto kohi4)
        {
            HpId = hpId;
            PtId = ptId;
            HokenSbtCd = hokenSbtCd;
            HokenPid = hokenPid;
            HokenKbn = hokenKbn;
            HokenMemo = hokenMemo;
            SinDate = sinDate;
            IsDeleted = isDeleted;
            HokenInf = hokenInf;
            Kohi1 = kohi1;
            Kohi2 = kohi2;
            Kohi3 = kohi3;
            Kohi4 = kohi4;
        }
        public HokenPatternDto(int hpId, long ptId, int hokenSbtCd, int hokenPid, int hokenKbn, string hokenMemo, int sinDate, int isDeleted, HokenInfDto hokenInf, KohiInfDto kohi1, KohiInfDto kohi2, KohiInfDto kohi3, KohiInfDto kohi4, int hokenId, int sinDateRecentUse, long seqNo, UsedPattern? usedPattern = null, int isUsedPattern = 0)
        {
            HpId = hpId;
            PtId = ptId;
            HokenSbtCd = hokenSbtCd;
            HokenPid = hokenPid;
            HokenKbn = hokenKbn;
            HokenMemo = hokenMemo;
            SinDate = sinDate;
            IsDeleted = isDeleted;
            HokenInf = hokenInf;
            Kohi1 = kohi1;
            Kohi2 = kohi2;
            Kohi3 = kohi3;
            Kohi4 = kohi4;
            HokenId = hokenId;
            SinDateRecentUse = sinDateRecentUse;
            SeqNo = seqNo;
            UsedPattern = usedPattern;
            IsUsedPattern = isUsedPattern;
        }

        public int HpId { get; private set; }

        public long PtId { get; private set; }

        public int HokenSbtCd { get; private set; }

        public int HokenPid { get; private set; }

        public int HokenKbn { get; private set; }

        public string HokenMemo { get; private set; }

        public int SinDate { get; private set; }

        public int IsDeleted { get; private set; }

        public HokenInfDto HokenInf { get; private set; }

        public KohiInfDto Kohi1 { get; private set; }

        public KohiInfDto Kohi2 { get; private set; }

        public KohiInfDto Kohi3 { get; private set; }

        public KohiInfDto Kohi4 { get; private set; }

        public int CountKohi 
        { 
            get
            {
                int count = 0;
                if(!Kohi1.IsEmptyModel) count++;
                if(!Kohi2.IsEmptyModel) count++;
                if(!Kohi3.IsEmptyModel) count++;
                if(!Kohi4.IsEmptyModel) count++;
                return count;
            }
        }
        
        public bool IsExpirated
        {
            get
            {
                if(HokenInf.IsExpirated || (!Kohi1.IsEmptyModel && Kohi1.IsExpirated) || (!Kohi2.IsEmptyModel && Kohi2.IsExpirated) 
                    || (!Kohi3.IsEmptyModel && Kohi3.IsExpirated) || (!Kohi4.IsEmptyModel && Kohi4.IsExpirated))
                {
                    return true;
                }    
                return false;
            }
        }

        //public bool IsJihi
        //{
        //    get
        //    {
        //        if (IsHaveHokenMst)
        //        {
        //            return HokenMst.HokenSbtKbn == 8;
        //        }
        //        return HokenKbn == 0 && (Houbetu == HokenConstant.HOUBETU_JIHI_108 || Houbetu == HokenConstant.HOUBETU_JIHI_109);
        //    }
        //}


        public int HokenId { get; private set; }
        public int SinDateRecentUse { get; private set; }
        public long SeqNo { get; private set; }

        public int IsUsedPattern {  get; private set; }

        public UsedPattern? UsedPattern { get; set; }

        public bool IsDefault { get; set; }

        public string PatternName
        {
            get
            {
                string result = string.Empty;
                result += HokenInf.GetHokenName();
                if (!Kohi1.IsEmptyModel)
                {
                    result += $" + {Kohi1.GetKohiName(true)}";
                }
                if (!Kohi2.IsEmptyModel)
                {
                    result += $" + {Kohi2.GetKohiName(true)}";
                }
                if (!Kohi3.IsEmptyModel)
                {
                    result += $" + {Kohi3.GetKohiName(true)}";
                }
                if (!Kohi4.IsEmptyModel)
                {
                    result += $" + {Kohi4.GetKohiName(true)}";
                }
                if(IsExpirated)
                {
                    result = "×" + result + "（有効期限切れ）";
                }    
                return result;
            }
        }
    }
}
