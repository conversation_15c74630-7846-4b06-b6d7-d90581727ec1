﻿using System.Text.Json.Serialization;

namespace Domain.Models.Insurance
{
    public class InsuranceSummaryModel
    {
        public InsuranceSummaryModel() {
            HokenTitle = string.Empty;
            HokenRate = string.Empty;
            HokenName = string.Empty;
            HokenSName = string.Empty;
        }

        [JsonConstructor]
        public InsuranceSummaryModel(long hokenPid, string hokenTitle, string hokenRate, string hokenName, string hokenSName, int hokenType, int orderHokenType)
        {
            HokenPid = hokenPid;
            HokenTitle = hokenTitle;
            HokenRate = hokenRate;
            HokenName = hokenName;
            HokenSName = hokenSName;
            HokenType = hokenType;
            OrderHokenType = orderHokenType;
        }

        public long HokenPid { private set; get; }

        public string HokenTitle { private set; get; }

        public string HokenRate { private set; get; }

        public string HokenName { private set; get; }

        public string HokenSName { private set; get; }

        public int HokenType { private set; get; }

        public int OrderHokenType { private set; get; }
    }
}
