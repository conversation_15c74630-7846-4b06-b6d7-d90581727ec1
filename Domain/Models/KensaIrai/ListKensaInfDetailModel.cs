﻿namespace Domain.Models.KensaIrai
{
    public class ListKensaInfDetailModel
    {
        public ListKensaInfDetailModel()
        {
            KensaInfDetailCol = new List<KensaInfDetailColModel>();
            KensaInfDetailData = new List<KensaInfDetailDataModel>();
        }
        public ListKensaInfDetailModel(List<KensaInfDetailColModel> kensaInfDetailCol, List<KensaInfDetailDataModel> kensaInfDetailData, int totalCol)
        {
            KensaInfDetailCol = kensaInfDetailCol;
            KensaInfDetailData = kensaInfDetailData;
            TotalCol = totalCol;
        }

        public List<KensaInfDetailColModel> KensaInfDetailCol { get; private set; }

        public List<KensaInfDetailDataModel> KensaInfDetailData { get; private set; }
        public int TotalCol { get; private set; }

        public class KensaInfDetailColModel
        {
            public KensaInfDetailColModel(long iraiCd, long iraiDate, string nyubi, string yoketu, string bilirubin, int sikyuKbn, int tosekiKbn, int index, string kensaTime = "", string centerCd = "")
            {
                IraiCd = iraiCd;
                IraiDate = iraiDate;
                Nyubi = nyubi;
                Yoketu = yoketu;
                Bilirubin = bilirubin;
                SikyuKbn = sikyuKbn;
                TosekiKbn = tosekiKbn;
                Index = index;
                KensaTime = kensaTime;
            }

            public KensaInfDetailColModel(long iraiCd, long iraiDate, string nyubi, string yoketu, string bilirubin, int sikyuKbn, int tosekiKbn, long seqGroupNo, int index, string centerCd = "")
            {
                IraiCd = iraiCd;
                IraiDate = iraiDate;
                Nyubi = nyubi;
                Yoketu = yoketu;
                Bilirubin = bilirubin;
                SikyuKbn = sikyuKbn;
                TosekiKbn = tosekiKbn;
                SeqGroupNo = seqGroupNo;
                Index = index;
            }

            public long IraiCd { get; private set; }

            public long IraiDate { get; private set; }

            public string Nyubi { get; private set; }

            public string Yoketu { get; private set; }

            public string Bilirubin { get; private set; }

            public int SikyuKbn { get; private set; }

            public int TosekiKbn { get; private set; }

            public int Index { get; private set; }

            public long SeqGroupNo { get; private set; }

            public string KensaTime { get; private set; } = string.Empty;

            public void SetIndex(int newIndex)
            {
                Index = newIndex;
            }

            public string CenterCd { get; private set; } = string.Empty;
        }

        public class KensaInfDetailDataModel
        {
            public KensaInfDetailDataModel(string kensaItemCd, string kensaName, string unit, string maleStd, string femaleStd, List<ListKensaInfDetailItemModel> dynamicArray, string centerCd = "", string dspCenterName = "")
            {
                KensaItemCd = kensaItemCd;
                KensaName = kensaName;
                Unit = unit;
                MaleStd = maleStd;
                FemaleStd = femaleStd;
                DynamicArray = dynamicArray;
                KensaKana = string.Empty;
                RowSeqId = string.Empty;
                CenterCd = centerCd;
                DspCenterName = dspCenterName;
            }

            public KensaInfDetailDataModel(string kensaItemCd, string kensaName, string unit, string maleStd, string femaleStd, string kensaKana, long sortNo, long seqNo, long seqParentNo, string rowSeqId, List<ListKensaInfDetailItemModel> dynamicArray, string centerCd = "", string dspCenterName = "")
            {
                KensaItemCd = kensaItemCd;
                KensaName = kensaName;
                Unit = unit;
                MaleStd = maleStd;
                FemaleStd = femaleStd;
                KensaKana = kensaKana;
                SortNo = sortNo;
                SeqNo = seqNo;
                SeqParentNo = seqParentNo;
                RowSeqId = rowSeqId;
                DynamicArray = dynamicArray;
                CenterCd = centerCd;
                DspCenterName = dspCenterName;
            }

            public KensaInfDetailDataModel(string kensaItemCd, string kensaName, string unit, string maleStd, string femaleStd, long iraiDate, string kensaKana, long sortNo, long seqNo, long seqParentNo, string rowSeqId, List<ListKensaInfDetailItemModel> dynamicArray, string kensaTime = "", string centerCd = "", string dspCenterName = "")
            {
                KensaItemCd = kensaItemCd;
                KensaName = kensaName;
                Unit = unit;
                MaleStd = maleStd;
                FemaleStd = femaleStd;
                IraiDate = iraiDate;
                KensaKana = kensaKana;
                SortNo = sortNo;
                SeqNo = seqNo;
                SeqParentNo = seqParentNo;
                RowSeqId = rowSeqId;
                DynamicArray = dynamicArray;
                KensaTime = kensaTime;
                CenterCd = centerCd;
                DspCenterName = dspCenterName;
            }

            public KensaInfDetailDataModel(string kensaItemCd, string kensaName, string unit, string maleStd, string femaleStd, long seqNo, List<ListKensaInfDetailItemModel> dynamicArray, string centerCd = "", string dspCenterName = "")
            {
                KensaItemCd = kensaItemCd;
                KensaName = kensaName;
                Unit = unit;
                MaleStd = maleStd;
                FemaleStd = femaleStd;
                SeqNo = seqNo;
                DynamicArray = dynamicArray;
                KensaKana = string.Empty;
                RowSeqId = string.Empty;
                CenterCd = centerCd;
                DspCenterName = dspCenterName;
            }

            public KensaInfDetailDataModel(string kensaItemCd, string kensaName, string unit, string maleStd, string femaleStd, string kensaKana, long sortNo, long seqNo, long seqParentNo, List<ListKensaInfDetailItemModel> dynamicArray, string centerCd = "", string dspCenterName = "")
            {
                KensaItemCd = kensaItemCd;
                KensaName = kensaName;
                Unit = unit;
                MaleStd = maleStd;
                FemaleStd = femaleStd;
                KensaKana = kensaKana;
                SortNo = sortNo;
                SeqNo = seqNo;
                SeqParentNo = seqParentNo;
                DynamicArray = dynamicArray;
                RowSeqId = string.Empty;
                CenterCd = centerCd;
                DspCenterName = dspCenterName;
            }

            public long IraiDate { get; private set; }

            public string KensaItemCd { get; private set; }

            public string KensaName { get; private set; }

            public string Unit { get; private set; }

            public string MaleStd { get; private set; }

            public string FemaleStd { get; private set; }

            public string KensaKana { get; private set; }

            public long SortNo { get; private set; }

            public long SeqNo { get; private set; }

            public long SeqParentNo { get; private set; }

            public string RowSeqId { get; private set; }

            public List<ListKensaInfDetailItemModel> DynamicArray { get; private set; }

            public string KensaTime { get; private set; } = string.Empty;

            public string CenterCd { get; private set; } = string.Empty;

            public string DspCenterName { get; private set; } = string.Empty;
        }
    }
}
