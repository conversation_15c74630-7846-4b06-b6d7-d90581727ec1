﻿using static Helper.Constants.OrderInfConst;

namespace Domain.Models.MedicalExamination
{
    public class CheckedOrderModel
    {
        public CheckedOrderModel(CheckingType checkingType, bool santei, string checkingContent, string itemCd, int sinKouiKbn, string itemName, int inOutKbn)
        {
            CheckingType = checkingType;
            Santei = santei;
            CheckingContent = checkingContent;
            ItemCd = itemCd;
            SinKouiKbn = sinKouiKbn;
            ItemName = itemName;
            InOutKbn = inOutKbn;
            OdrUnitName = string.Empty;
            UnitName = string.Empty;
            IpnCD = string.Empty;
            CnvUnitName = string.Empty;
            MasterSbt = string.Empty;
            CenterName = string.Empty;
            YjCd = string.Empty;
            IpnName = string.Empty;
            IpnNameCd = string.Empty;
            Kokuji1 = string.Empty;
            Kokuji2 = string.Empty;
            CenterCd = string.Empty;
        }

        public CheckedOrderModel()
        {
            CheckingContent = string.Empty;
            ItemCd = string.Empty;
            ItemName = string.Empty;
            CheckingType = 0;
            OdrUnitName = string.Empty;
            UnitName = string.Empty;
            IpnCD = string.Empty;
            CnvUnitName = string.Empty;
            MasterSbt = string.Empty;
            CenterName = string.Empty;
            YjCd = string.Empty;
            IpnName = string.Empty;
            IpnNameCd = string.Empty;
            Kokuji1 = string.Empty;
            Kokuji2 = string.Empty;
            CenterCd = string.Empty;
        }

        public CheckedOrderModel(CheckingType checkingType, bool santei, string checkingContent, string itemCd, int sinKouiKbn, string itemName, int inOutKbn, string odrUnitName, string unitName, string ipnCD, string cnvUnitName, int drugKbn, string masterSbt, int yohoKbn, int buiKbn, int isAdopted, int senteiRyoyoKbn, string centerName, string yjCd, int kohatuKbn, string ipnName, string ipnNameCd, double defaultValue, int cmtColKeta1, int cmtColKeta2, int cmtColKeta3, int cmtColKeta4, string kokuji1, string kokuji2, int cmtCol1, int cmtCol2, int cmtCol3, int cmtCol4, string centerCd, int rousaiKbn, int startDate) : this(checkingType, santei, checkingContent, itemCd, sinKouiKbn, itemName, inOutKbn)
        {
            OdrUnitName = odrUnitName;
            UnitName = unitName;
            IpnCD = ipnCD;
            CnvUnitName = cnvUnitName;
            DrugKbn = drugKbn;
            MasterSbt = masterSbt;
            YohoKbn = yohoKbn;
            BuiKbn = buiKbn;
            IsAdopted = isAdopted;
            SenteiRyoyoKbn = senteiRyoyoKbn;
            CenterName = centerName;
            YjCd = yjCd;
            KohatuKbn = kohatuKbn;
            IpnName = ipnName;
            IpnNameCd = ipnNameCd;
            DefaultValue = defaultValue;
            CmtColKeta1 = cmtColKeta1;
            CmtColKeta2 = cmtColKeta2;
            CmtColKeta3 = cmtColKeta3;
            CmtColKeta4 = cmtColKeta4;
            Kokuji1 = kokuji1;
            Kokuji2 = kokuji2;
            CmtCol1 = cmtCol1;
            CmtCol2 = cmtCol2;
            CmtCol3 = cmtCol3;
            CmtCol4 = cmtCol4;
            CenterCd = centerCd;
            RousaiKbn = rousaiKbn;
            StartDate = startDate;
        }

        public CheckingType CheckingType { get; private set; }

        public bool Santei { get; private set; }

        public string CheckingContent { get; private set; }

        public string ItemCd { get; private set; }

        public int SinKouiKbn { get; private set; }

        public string ItemName { get; private set; }

        public int InOutKbn { get; private set; }

        public bool CheckDefaultValue()
        {
            return string.IsNullOrEmpty(ItemCd);
        }

        public bool IsEnableSantei
        {
            get => !CheckDefaultValue();
        }

        public string CheckingTypeDisplay
        {
            get
            {
                switch (CheckingType)
                {
                    case CheckingType.MissingCalculate:
                        return "算定漏れ";
                    case CheckingType.Order:
                        return "オーダー";
                    default:
                        return string.Empty;
                }
            }
        }

        public CheckedOrderModel ChangeSantei(bool santei)
        {
            Santei = santei;
            return this;
        }

        public string OdrUnitName { get; private set; }

        public string UnitName { get; private set; }

        public string IpnCD { get; private set; }

        public string CnvUnitName { get; private set; }

        public int DrugKbn { get; private set; }

        public string MasterSbt { get; private set; }

        public int YohoKbn { get; private set; }

        public int BuiKbn { get; private set; }

        public int IsAdopted { get; private set; }

        public int SenteiRyoyoKbn { get; private set; }

        public string CenterName { get; private set; }

        public string YjCd { get; private set; }

        public int KohatuKbn { get; private set; }

        public string IpnName { get; private set; }

        public string IpnNameCd { get; private set; }

        public double DefaultValue { get; private set; }

        public int CmtColKeta1 { get; private set; }

        public int CmtColKeta2 { get; private set; }

        public int CmtColKeta3 { get; private set; }

        public int CmtColKeta4 { get; private set; }

        public string Kokuji1 { get; private set; }

        public string Kokuji2 { get; private set; }

        public int CmtCol1 { get; private set; }

        public int CmtCol2 { get; private set; }

        public int CmtCol3 { get; private set; }

        public int CmtCol4 { get; private set; }

        public string CenterCd { get; private set; }

        public int RousaiKbn { get; private set; }

        public int StartDate { get; private set; }

        public bool IsSelectiveComment { get; private set; }

    }
}

