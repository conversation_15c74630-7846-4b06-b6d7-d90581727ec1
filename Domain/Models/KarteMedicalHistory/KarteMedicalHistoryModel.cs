﻿using Domain.Models.Family;
using Domain.Models.SpecialNote.ImportantNote;

namespace Domain.Models.KarteMedicalHistory
{
    public class KarteMedicalHistoryModel
    {
        public KarteMedicalHistoryModel(List<PtOtherDrugModel> otherDrugs, List<PtOtcDrugModel> octDrugs, List<PtSuppleModel> supples, List<PtKioRekiModel> kioRekis, List<PtSmokingRelatedModel> socialHistorys, List<PtPregnancyRelatedModel> pregnants, List<PtFamilyRekiModel> families)
        {
            OtherDrugs = otherDrugs;
            OctDrugs = octDrugs;
            Supples = supples;
            KioRekis = kioRekis;
            SocialHistorys = socialHistorys;
            Pregnants = pregnants;
            Families = families;
        }

        public List<PtOtherDrugModel> OtherDrugs { get; private set; }

        public List<PtOtcDrugModel> OctDrugs { get; private set; }

        public List<PtSuppleModel> Supples { get; private set; }

        public List<PtKioRekiModel> KioRekis { get; private set; }

        public List<PtSmokingRelatedModel> SocialHistorys { get; private set; }

        public List<PtPregnancyRelatedModel> Pregnants { get; private set; }

        public List<PtFamilyRekiModel> Families { get; private set; }
    }
}