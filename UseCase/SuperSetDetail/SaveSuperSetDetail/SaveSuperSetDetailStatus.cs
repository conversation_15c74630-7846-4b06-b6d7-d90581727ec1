﻿namespace UseCase.SuperSetDetail.SaveSuperSetDetail;

public enum SaveSuperSetDetailStatus : byte
{
    Successed = 1,
    Failed = 2,
    SaveSetByomeiFailed = 3,
    SaveSetKarteInfFailed = 4,
    SaveSetOrderInfFailed = 5,
    ValidateSuccess = 6,
    InvalidSetByomeiId = 7,
    InvalidHpId = 8,
    InvalidSetCd = 9,
    InvalidUserId = 10,
    InvalidSikkanKbn = 11,
    InvalidNanByoCd = 12,
    InvalidByomeiCdOrSyusyokuCd = 13,
    FullByomeiMaxlength160 = 14,
    ByomeiCmtMaxlength80 = 15,
    SetCdNotExist = 16,
    InvalidSetOrderInfId = 17,
    InvalidSetOrderInfRpNo = 18,
    InvalidSetOrderInfRpEdaNo = 19,
    InvalidSetOrderInfKouiKbn = 20,
    RpNameMaxLength240 = 21,
    InvalidSetOrderInfInoutKbn = 22,
    InvalidSetOrderInfSikyuKbn = 23,
    InvalidSetOrderInfSyohoSbt = 24,
    InvalidSetOrderInfSanteiKbn = 25,
    InvalidSetOrderInfTosekiKbn = 26,
    InvalidSetOrderInfDaysCnt = 27,
    InvalidSetOrderInfSortNo = 28,
    InvalidSetOrderSinKouiKbn = 29,
    ItemCdMaxLength10 = 30,
    ItemNameMaxLength240 = 31,
    UnitNameMaxLength24 = 32,
    InvalidSetOrderSuryo = 33,
    InvalidSetOrderUnitSBT = 34,
    InvalidSetOrderTermVal = 35,
    InvalidSetOrderKohatuKbn = 36,
    InvalidSetOrderSyohoKbn = 37,
    InvalidSetOrderSyohoLimitKbn = 38,
    InvalidSetOrderDrugKbn = 39,
    InvalidSetOrderYohoKbn = 40,
    Kokuji1MaxLength1 = 41,
    Kokuji2MaxLength1 = 42,
    InvalidSetOrderIsNodspRece = 43,
    IpnCdMaxLength12 = 44,
    IpnNameMaxLength120 = 45,
    BunkatuMaxLength10 = 46,
    CmtNameMaxLength240 = 47,
    CmtOptMaxLength38 = 48,
    FontColorMaxLength8 = 49,
    InvalidSetOrderCommentNewline = 50,
    RpNoOrRpEdaNoIsNotExist = 51,
    ValidateOrderSuccess = 52,
    ValidateOrderDetailSuccess = 53,
    MedicalScreenLocked = 55,
    InvalidSetName = 56,
    SaveSetInfoFailed = 57,
    FolderHasBeenDeleted = 58,
    KarteInfoIsNotExist = 59
}
