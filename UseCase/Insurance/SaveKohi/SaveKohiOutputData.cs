﻿using UseCase.Core.Sync.Core;

namespace UseCase.Insurance.SaveKohi

{
    public class SaveKohiOutputData : IOutputData
    {
        public SaveKohiStatus Status { get; private set; }

        public int KohiId { get; private set; } 

        public long OnlineConfirmationHisId { get; private set; }
        public SaveKohiOutputData(SaveKohiStatus status, int kohiId, long onlineConfirmationHisId)
        {
            Status = status;
            KohiId = kohiId;
            OnlineConfirmationHisId = onlineConfirmationHisId;
        }
    }
}