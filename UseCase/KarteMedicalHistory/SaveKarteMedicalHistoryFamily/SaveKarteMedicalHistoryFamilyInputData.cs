﻿using Domain.Models.Family;
using UseCase.Core.Sync.Core;

namespace UseCase.KarteMedicalHistory.SaveKarteMedicalHistoryFamily;

public class SaveKarteMedicalHistoryFamilyInputData : IInputData<SaveKarteMedicalHistoryFamilyOutputData>
{
    public SaveKarteMedicalHistoryFamilyInputData(int hpId, int userId, long ptId, List<PtFamilyRekiModel> ptFamilyRekis)
    {
        HpId = hpId;
        UserId = userId;
        PtId = ptId;
        PtFamilyRekis = ptFamilyRekis;
    }

    public int HpId { get; private set; }

    public int UserId { get; private set; }

    public long PtId { get; private set; }

    public List<PtFamilyRekiModel> PtFamilyRekis { get; private set; }
}