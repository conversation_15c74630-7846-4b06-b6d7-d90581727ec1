﻿using Domain.Models.Family;
using Domain.Models.KarteMedicalHistory;
using Domain.Models.SpecialNote.ImportantNote;
using UseCase.Core.Sync.Core;

namespace UseCase.KarteMedicalHistory.GetKarteMedicalHistory
{
    public class GetKarteMedicalHistoryOutputData : IOutputData
    {
        public GetKarteMedicalHistoryOutputData(List<PtOtherDrugModel> otherDrugs, List<PtOtcDrugModel> octDrugs, List<PtSuppleModel> supples, List<PtKioRekiModel> kioRekis, List<PtSmokingRelatedModel> socialHistorys, List<PtPregnancyRelatedModel> pregnants, List<PtFamilyRekiModel> families, GetKarteMedicalHistoryStatus status)
        {
            OtherDrugs = otherDrugs;
            OctDrugs = octDrugs;
            Supples = supples;
            KioRekis = kioRekis;
            SocialHistorys = socialHistorys;
            Pregnants = pregnants;
            Families = families;
            Status = status;
        }

        public GetKarteMedicalHistoryOutputData(GetKarteMedicalHistoryStatus status)
        {
            OtherDrugs = new();
            OctDrugs = new();
            Supples = new();
            KioRekis = new();
            SocialHistorys = new();
            Pregnants = new();
            Families = new();
            Status = status;
        }

        public List<PtOtherDrugModel> OtherDrugs { get; private set; }

        public List<PtOtcDrugModel> OctDrugs { get; private set; }

        public List<PtSuppleModel> Supples { get; private set; }

        public List<PtKioRekiModel> KioRekis { get; private set; }

        public List<PtSmokingRelatedModel> SocialHistorys { get; private set; }

        public List<PtPregnancyRelatedModel> Pregnants { get; private set; }

        public List<PtFamilyRekiModel> Families { get; private set; }

        public GetKarteMedicalHistoryStatus Status { get; private set; }
    }
}