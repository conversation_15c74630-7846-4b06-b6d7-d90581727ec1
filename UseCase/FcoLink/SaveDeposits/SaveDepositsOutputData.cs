﻿using Domain.Models.PatientInfor;
using Domain.Models.OrdInfs;
using Domain.Models.Reception;
using UseCase.Core.Sync.Core;

namespace UseCase.FcoLink.SaveDeposits
{
    public class SaveDepositsOutputData : IOutputData
    {

        public SaveDepositsOutputData(SaveDepositsStatus status)
        {
            Status = status;
            ReceptionInfos = new List<ReceptionForViewDto>();
            SameVisitList = new List<SameVisitModel>();
        }

        public SaveDepositsOutputData(SaveDepositsStatus status, List<ReceptionForViewDto> receptionInfos, List<SameVisitModel> sameVisitList)
        {
            Status = status;
            ReceptionInfos = receptionInfos;
            SameVisitList = sameVisitList;
        }

        public SaveDepositsStatus Status { get; private set; }

        public List<ReceptionForViewDto> ReceptionInfos { get; private set; }

        public List<SameVisitModel> SameVisitList { get; private set; }
    }
}
