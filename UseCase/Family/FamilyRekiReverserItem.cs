﻿using Domain.Models.Family;

namespace UseCase.Family;

public class FamilyRekiReverserItem
{
    public FamilyRekiReverserItem(PtFamilyRekiModel model, string zokugaraCd)
    {
        Id = model.Id;
        HpId = model.HpId;
        PtId = model.PtId;
        FamilyId = model.FamilyId;
        ByomeiCd = model.ByomeiCd;
        ByotaiCd = model.ByotaiCd;
        Byomei = model.Byomei;
        Cmt = model.Cmt;
        SortNo = model.SortNo;
        SeqNo = model.SeqNo;
        IsDeleted = model.IsDeleted;
        ZokugaraCd = zokugaraCd;
        ZokugaraElse = model.ZokugaraElse;
    }

    public long Id { get; private set; }

    public int HpId { get; private set; }

    public long PtId { get; private set; }

    public long FamilyId { get; private set; }

    public string ByomeiCd { get; private set; }

    public string ByotaiCd { get; private set; }

    public string Byomei { get; private set; }

    public string Cmt { get; private set; }

    public int SortNo { get; private set; }

    public long SeqNo { get; private set; }

    public bool IsDeleted { get; private set; }

    public string ZokugaraCd { get; private set; }

    public string ZokugaraElse { get; private set; } = string.Empty;
}
