﻿using Domain.Models.Family;
using System.Text.Json.Serialization;

namespace UseCase.Family;

public class FamilyRekiItem
{
    [JsonConstructor]
    public FamilyRekiItem(long id, string byomeiCd, string byomei, string cmt, int sortNo, bool isDeleted, int hpId, long ptId, long familyId, int seqNo, string zokugaraCd = "", string zokugaraElse = "")
    {
        Id = id;
        HpId = hpId;
        PtId = ptId;
        FamilyId = familyId;
        ByomeiCd = byomeiCd;
        Byomei = byomei;
        Cmt = cmt;
        SortNo = sortNo;
        SeqNo = seqNo;
        IsDeleted = isDeleted;
        ZokugaraCd = zokugaraCd;
        ZokugaraElse = zokugaraElse;
        ByotaiCd = string.Empty;
    }

    public FamilyRekiItem(PtFamilyRekiModel model)
    {
        Id = model.Id;
        ByomeiCd = model.ByomeiCd;
        Byomei = model.Byomei;
        Cmt = model.Cmt;
        SortNo = model.SortNo;
        IsDeleted = false;
        ZokugaraCd = model.ZokugaraCd;
        ZokugaraElse = model.ZokugaraElse;
    }

    public long Id { get; private set; }

    public int HpId { get; private set; }

    public long PtId { get; private set; }

    public long FamilyId { get; private set; }

    public string ByomeiCd { get; private set; }

    public string ByotaiCd { get; private set; }

    public string Byomei { get; private set; }

    public string Cmt { get; private set; }

    public int SortNo { get; private set; }

    public int SeqNo { get; private set; }

    public bool IsDeleted { get; private set; }

    public string ZokugaraCd { get; private set; }

    public string ZokugaraElse { get; private set; } = string.Empty;
}
