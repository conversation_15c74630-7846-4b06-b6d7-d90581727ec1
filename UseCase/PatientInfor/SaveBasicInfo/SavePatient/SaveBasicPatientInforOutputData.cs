﻿using Domain.Models.PatientInfor;
using Domain.Models.Reception;
using UseCase.Core.Sync.Core;
using UseCase.PatientInfor.Save;

namespace UseCase.PatientInfor.SaveBasicInfo.SavePatient
{
    public class SaveBasicPatientInforOutputData : IOutputData
    {
        public SaveBasicPatientInforOutputData(IEnumerable<SavePatientInfoValidationResult> validateDetails, SavePatientInfoStatus status, long ptID, PatientInforModel patientInforModel, List<ReceptionForViewDto> receptionInfos, long raiinNo, long deletedOnlineConfirmationHistoryId)
        {
            ValidateDetails = validateDetails;
            Status = status;
            PtID = ptID;
            PatientInforModel = patientInforModel;
            ReceptionInfos = receptionInfos;
            RaiinNo = raiinNo;
            DeletedOnlineConfirmationHistoryId = deletedOnlineConfirmationHistoryId;
        }

        public IEnumerable<SavePatientInfoValidationResult> ValidateDetails { get; private set; }

        public SavePatientInfoStatus Status { get; private set; }

        public long PtID { get; private set; }
        public PatientInforModel PatientInforModel { get; private set; }
        public List<ReceptionForViewDto> ReceptionInfos { get; private set; }
        public long RaiinNo { get; private set; }
        public long DeletedOnlineConfirmationHistoryId { get; private set; }
    }
}
