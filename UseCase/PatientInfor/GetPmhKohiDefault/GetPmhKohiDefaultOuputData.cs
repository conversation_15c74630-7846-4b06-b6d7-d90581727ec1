﻿using Domain.Models.PatientInfor;
using UseCase.Core.Sync.Core;

namespace UseCase.PatientInfor.GetPmhKohiDefault
{
    public class GetPmhKohiDefaultOuputData : IOutputData
    {
        public GetPmhKohiDefaultOuputData(GetPmhKohiDefaultStatus status)
        {
            Status = status;
            GetPmhKohiDefaultDto = new();
        }

        public GetPmhKohiDefaultOuputData(GetPmhKohiDefaultStatus status, GetPmhKohiDefaultDto getPmhKohiDefaultDto)
        {
            Status = status;
            GetPmhKohiDefaultDto = getPmhKohiDefaultDto;
        }

        public GetPmhKohiDefaultStatus Status { get; private set; }

        public GetPmhKohiDefaultDto GetPmhKohiDefaultDto { get; private set; }
    }
}
