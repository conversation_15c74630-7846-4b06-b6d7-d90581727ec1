﻿using Domain.Models.PatientInfor;
using UseCase.Core.Sync.Core;

namespace UseCase.PatientInfor.GetPmhKohiDefault
{
    public class GetPmhKohiDefaultInputData : IInputData<GetPmhKohiDefaultOuputData>
    {
        public GetPmhKohiDefaultInputData(int hpId, int sinDate, MedicalSubsidy medicalSubsidy)
        {
            HpId = hpId;
            SinDate = sinDate;
            MedicalSubsidy = medicalSubsidy;
        }

        public int HpId { get; private set; }

        public int SinDate { get; set; }

        public MedicalSubsidy MedicalSubsidy { get; set; }
    }
}
