﻿using Domain.Models.PatientInfor;
using UseCase.Core.Sync.Core;

namespace UseCase.PatientInfor.CheckPmhKohiInfoDifference
{
    public class CheckPmhKohiInfoDifferenceInputData : IInputData<CheckPmhKohiInfoDifferenceOuputData>
    {
        public CheckPmhKohiInfoDifferenceInputData(int hpId, long ptId, int sinDate, MedicalSubsidy medicalSubsidy)
        {
            HpId = hpId;
            PtId = ptId;
            SinDate = sinDate;
            MedicalSubsidy = medicalSubsidy;
        }

        public int HpId { get; private set; }

        public long PtId { get; set; }

        public int SinDate { get; set; }

        public MedicalSubsidy MedicalSubsidy { get; set; }
    }
}
