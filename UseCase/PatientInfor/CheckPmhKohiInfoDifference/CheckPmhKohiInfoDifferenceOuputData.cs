﻿using Domain.Models.PatientInfor;
using UseCase.Core.Sync.Core;

namespace UseCase.PatientInfor.CheckPmhKohiInfoDifference
{
    public class CheckPmhKohiInfoDifferenceOuputData : IOutputData
    {
        public CheckPmhKohiInfoDifferenceOuputData(CheckPmhKohiInfoDifferenceStatus status)
        {
            Status = status;
            CheckPmhKohiInfoDifferenceDto = new();
        }

        public CheckPmhKohiInfoDifferenceOuputData(CheckPmhKohiInfoDifferenceStatus status, CheckPmhKohiInfoDifferenceDto checkPmhKohiInfoDifferenceDto)
        {
            Status = status;
            CheckPmhKohiInfoDifferenceDto = checkPmhKohiInfoDifferenceDto;
        }

        public CheckPmhKohiInfoDifferenceStatus Status { get; private set; }

        public CheckPmhKohiInfoDifferenceDto CheckPmhKohiInfoDifferenceDto { get; private set; }
    }
}
