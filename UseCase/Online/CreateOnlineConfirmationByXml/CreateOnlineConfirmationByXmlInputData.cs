﻿using Microsoft.AspNetCore.Http;
using UseCase.Core.Sync.Core;

namespace UseCase.Online.CreateOnlineConfirmationByXml
{
    public class CreateOnlineConfirmationByXmlInputData : IInputData<CreateOnlineConfirmationByXmlOutputData>
    {
        public CreateOnlineConfirmationByXmlInputData(int hpId, string xmlFileContent, int userId, int pmhStatus, string pmhResult)
        {
            HpId = hpId;
            XmlFileContent = xmlFileContent;
            UserId = userId;
            PmhStatus = pmhStatus;
            PmhResult = pmhResult;
        }

        public int HpId { get; set; }
        public string XmlFileContent { get; set; }
        public int UserId { get; set; }

        public int PmhStatus { get; set; }

        public string PmhResult { get; set; }
    }
}
