﻿using UseCase.Core.Sync.Core;

namespace UseCase.Online.UpdateRefNo;

public class UpdateRefNoInputData : IInputData<UpdateRefNoOutputData>
{
    public UpdateRefNoInputData(int hpId, long ptId, int userId)
    {
        HpId = hpId;
        PtId = ptId;
        UserId = userId;
    }

    public int HpId { get; private set; }

    public long PtId { get; private set; }

    public int UserId { get; private set; }
}
