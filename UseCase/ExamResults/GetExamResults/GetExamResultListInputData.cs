using UseCase.Core.Sync.Core;

namespace UseCase.ExamResults.GetExamResults;

public class GetExamResultListInputData : IInputData<GetExamResultListOutputData>
{
    public GetExamResultListInputData(int hpId, long ptId, long startDate, long endDate, string keyWord, bool timeSequence, List<string> kensaItemCds, string centerCd)
    {
        HpId = hpId;
        PtId = ptId;
        StartDate = startDate;
        EndDate = endDate;
        KeyWord = keyWord;
        TimeSequence = timeSequence;
        KensaItemCds = kensaItemCds;
        CenterCd = centerCd;
    }

    public int HpId { get; private set; }

    public long PtId { get; private set; }

    public long StartDate { get; private set; }

    public long EndDate { get; private set; }

    public string KeyWord {  get; private set; }

    public List<string> KensaItemCds {  get; private set; }
    
    public bool TimeSequence {  get; private set; }

    public string CenterCd { get; private set; } = string.Empty;
}
