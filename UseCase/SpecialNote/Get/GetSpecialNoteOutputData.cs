﻿using Domain.Models.ExamResults;
using Domain.Models.Family;
using Domain.Models.KarteAllergy;
using Domain.Models.KarteMedicalHistory;
using Domain.Models.PtCmtInf;
using Domain.Models.SpecialNote.ImportantNote;
using Domain.Models.SpecialNote.PatientInfo;
using Domain.Models.SpecialNote.SummaryInf;
using UseCase.Core.Sync.Core;

namespace UseCase.SpecialNote.Get
{
    public class GetSpecialNoteOutputData : IOutputData
    {
        public GetSpecialNoteOutputData(KarteAllergyModel allergies, List<PtOtherDrugModel> otherDrugs, List<PtOtcDrugModel> otcDrugs, List<PtSuppleModel> supples, List<PtKioRekiModel> kioRekis,
            List<PtSmokingRelatedModel> socialHistorys, List<PtPregnancyRelatedModel> pregnants, List<PtFamilyRekiModel> families, List<PhysicalInfoModel> physicalInfos, List<ExamResultsModel> examResults, 
            SummaryInfModel summaryInf, GetSpecialNoteStatus status, List<PtInfectionModel> ptInfectionModels, PtCmtInfModel ptCmtInfModel, SeikaturekiInfModel seikaturekiInfModel, List<PtPregnancyModel> pregnancyItem)
        {
            AlrgyModel = allergies;
            OtherDrugs = otherDrugs;
            OtcDrugs = otcDrugs;
            Supples = supples;
            KioRekis = kioRekis;
            SocialHistorys = socialHistorys;
            Pregnants = pregnants;
            Families = families;
            PhysicalInfos = physicalInfos;
            ExamResults = examResults;
            SummaryInf = summaryInf;
            Status = status;
            InfectionList = ptInfectionModels;
            CmtInfItem = ptCmtInfModel;
            SeikaturekiInfItem = seikaturekiInfModel;
            PregnancyItem = pregnancyItem;
        }

        public GetSpecialNoteOutputData(GetSpecialNoteStatus status)
        {
            Status = status;
        }

        public KarteAllergyModel AlrgyModel { get; private set; }

        public List<PtOtherDrugModel> OtherDrugs { get; private set; }

        public List<PtOtcDrugModel> OtcDrugs { get; private set; }

        public List<PtSuppleModel> Supples { get; private set; }

        public List<PtKioRekiModel> KioRekis { get; private set; }

        public List<PtSmokingRelatedModel> SocialHistorys { get; private set; }

        public List<PtPregnancyRelatedModel> Pregnants { get; private set; }

        public List<PtFamilyRekiModel> Families { get; private set; }

        public List<PhysicalInfoModel> PhysicalInfos { get; private set; }

        public List<ExamResultsModel> ExamResults { get; private set; }

        public SummaryInfModel SummaryInf { get; private set; }

        public GetSpecialNoteStatus Status { get; private set; }

        public List<PtInfectionModel> InfectionList { get; set; }

        public PtCmtInfModel CmtInfItem { get; set; }

        public SeikaturekiInfModel SeikaturekiInfItem { get; set; }

        public List<PtPregnancyModel> PregnancyItem { get; set; }
    }
}
