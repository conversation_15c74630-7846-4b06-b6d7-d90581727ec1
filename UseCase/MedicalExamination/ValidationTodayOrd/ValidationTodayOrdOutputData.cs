﻿using Domain.Models.OrdInfs;
using Domain.Models.TodayOdr;
using UseCase.Core.Sync.Core;
using static Helper.Constants.KarteConst;
using static Helper.Constants.OrderInfConst;
using static Helper.Constants.RaiinInfConst;

namespace UseCase.OrdInfs.ValidationTodayOrd
{
    public class ValidationTodayOrdOutputData : IOutputData
    {
        public ValidationTodayOrdOutputData(ValidationTodayOrdStatus status, List<KeyValuePair<string, KeyValuePair<string, OrdInfValidationStatus>>> validations, RaiinInfTodayOdrValidationStatus validationRaiinInf, KarteValidationStatus validationKarte, HokenInfo? hokenName, List<HokenInfo>? kohiNames, List<OrdInfModel> ordInfs)
        {
            Status = status;
            Validations = validations;
            ValidationRaiinInf = validationRaiinInf;
            ValidationKarte = validationKarte;
            HokenName = hokenName;
            KohiNames = kohiNames;
            OrdInfs = ordInfs;
        }

        public ValidationTodayOrdStatus Status { get; private set; }
        public List<KeyValuePair<string, KeyValuePair<string, OrdInfValidationStatus>>> Validations { get; private set; }
        public RaiinInfTodayOdrValidationStatus ValidationRaiinInf { get; private set; }
        public KarteValidationStatus ValidationKarte { get; private set; }
        public HokenInfo? HokenName { get; private set; }
        public List<HokenInfo>? KohiNames { get; private set; }
        public List<OrdInfModel> OrdInfs { get; private set; } = new();
    }
}
