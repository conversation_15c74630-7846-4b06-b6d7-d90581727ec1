﻿using Domain.Models.TodayOdr;
using UseCase.Core.Sync.Core;
using UseCase.MedicalExamination.UpsertTodayOrd;

namespace UseCase.OrdInfs.ValidationTodayOrd
{
    public class ValidationTodayOrdInputData : IInputData<ValidationTodayOrdOutputData>
    {
        public ValidationTodayOrdInputData(int hpId, int syosaiKbn, int jikanKbn, int hokenPid, int santeiKbn, string uketukeTime, string sinStartTime, string sinEndTime, int userId, byte status, int karteStatus, List<OdrInfItemInputData> odrItems, KarteItemInputData karteInf)
        {
            HpId = hpId;
            SyosaiKbn = syosaiKbn;
            JikanKbn = jikanKbn;
            HokenPid = hokenPid;
            SanteiKbn = santeiKbn;
            UketukeTime = uketukeTime;
            SinStartTime = sinStartTime;
            SinEndTime = sinEndTime;
            UserId = userId;
            Status = status;
            KarteStatus = karteStatus;
            OdrItems = odrItems;
            KarteInf = karteInf;
        }

        public int HpId { get; private set; }

        public int SyosaiKbn { get; private set; }

        public int JikanKbn { get; private set; }

        public int HokenPid { get; private set; }

        public int SanteiKbn { get; private set; }

        public string UketukeTime { get; private set; }

        public string SinStartTime { get; private set; }

        public string SinEndTime { get; private set; }

        public int UserId { get; private set; }

        public byte Status { get; private set; }

        public int KarteStatus { get; private set; }

        public List<OdrInfItemInputData> OdrItems { get; private set; }

        public KarteItemInputData KarteInf { get; private set; }
       
    }
}
