﻿using Domain.Models.Reception;
using UseCase.Core.Sync.Core;

namespace UseCase.Accounting.SaveAccounting
{
    public class SaveAccountingOutputData : IOutputData
    {
        public SaveAccountingOutputData(SaveAccountingStatus status, List<ReceptionForViewDto> receptionInfos, List<SameVisitModel> sameVisitList, List<long> raiinNoPrint) 
        {
            Status = status;
            ReceptionInfos = receptionInfos;
            SameVisitList = sameVisitList;
            RaiinNoPrint = raiinNoPrint;
            ErrorCode = string.Empty;
            UserMessage = string.Empty;
        }

        public SaveAccountingOutputData(SaveAccountingStatus status, List<ReceptionForViewDto> receptionInfos, List<SameVisitModel> sameVisitList, List<long> raiinNoPrint, string errorCode, string userMessage) 
        {
            Status = status;
            ReceptionInfos = receptionInfos;
            SameVisitList = sameVisitList;
            RaiinNoPrint = raiinNoPrint;
            ErrorCode = errorCode;
            UserMessage = userMessage;
        }

        public SaveAccountingStatus Status { get; private set; }

        public List<ReceptionForViewDto> ReceptionInfos { get; private set; }

        public List<SameVisitModel> SameVisitList { get; private set; }

        public List<long> RaiinNoPrint { get; private set; }

        public string ErrorCode { get; set; }

        public string UserMessage { get; set; }
    }
}
