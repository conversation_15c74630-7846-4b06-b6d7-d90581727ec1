﻿using Domain.Models.MstItem;
using UseCase.Core.Sync.Core;

namespace UseCase.MstItem.SaveAddressMst
{
    public class SaveAddressMstInputData : IInputData<SaveAddressMstOutputData>
    {
        public SaveAddressMstInputData(int userId, List<PostCodeMstModel> postCodeMsts)
        {
            UserId = userId;
            PostCodeMsts = postCodeMsts;
        }

        public int UserId { get; private set; }

        public List<PostCodeMstModel> PostCodeMsts { get; private set; }
    }
}
