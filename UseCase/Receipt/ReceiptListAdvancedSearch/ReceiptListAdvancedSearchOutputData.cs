﻿using Domain.Models.Receipt.ReceiptListAdvancedSearch;
using UseCase.Core.Sync.Core;

namespace UseCase.Receipt.ReceiptListAdvancedSearch;

public class ReceiptListAdvancedSearchOutputData : IOutputData
{
    public ReceiptListAdvancedSearchOutputData(List<ReceiptListModel> receiptList, int totalCount, int displayTensu, ReceiptListAdvancedSearchStatus status)
    {
        ReceiptList = receiptList.Select(item => new ReceiptListAdvancedSearchItem(item)).ToList();
        Status = status;
        TotalCount = totalCount;
        DisplayTensu = displayTensu;
    }

    public ReceiptListAdvancedSearchOutputData(ReceiptListAdvancedSearchStatus status)
    {
        ReceiptList = new();
        Status = status;
        TotalCount = 0;
        DisplayTensu = 0;
    }

    public List<ReceiptListAdvancedSearchItem> ReceiptList { get; private set; }

    public ReceiptListAdvancedSearchStatus Status { get; private set; }

    public int TotalCount { get; private set; }

    public int DisplayTensu { get; private set; }
}
