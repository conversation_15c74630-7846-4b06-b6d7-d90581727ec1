﻿using UseCase.Core.Sync.Core;
using Domain.Models.KensaCenterPartnership;

namespace UseCase.KensaCenterPartnership;

public class GetKensaCenterPartnershipInputData : IInputData<GetKensaCenterPartnershipOutputData>
{
    public GetKensaCenterPartnershipInputData(int hpId, string centerCd)
    {
        HpId = hpId;
        CenterCd = centerCd;
    }

    public int HpId { get; private set; }

    public string CenterCd {  get; private set; }
}

public class RegisterKensaCenterPartnershipInputData : IInputData<RegisterKensaCenterPartnershipOutputData>
{
    public RegisterKensaCenterPartnershipInputData(int hpId, string centerCd, int startDate, int endDate)
    {
        HpId = hpId;
        CenterCd = centerCd;
        StartDate = startDate;
        EndDate = endDate;
    }

    public int HpId { get; set; }
    public string CenterCd { get; set; }
    public int StartDate { get; set; }
    public int EndDate { get; set; }
}

public class UpdateKensaCenterPartnershipInputData : IInputData<UpdateKensaCenterPartnershipOutputData>
{
    public UpdateKensaCenterPartnershipInputData(int hpId, string centerCd, int oldStartDate, int startDate, int endDate)
    {
        HpId = hpId;
        CenterCd = centerCd;
        OldStartDate = oldStartDate;
        StartDate = startDate;
        EndDate = endDate;
    }

    public int HpId { get; set; }
    public string CenterCd { get; set; }
    public int OldStartDate { get; set; }
    public int StartDate { get; set; }
    public int EndDate { get; set; }
}

public class UnregisterKensaCenterPartnershipInputData : IInputData<UnregisterKensaCenterPartnershipOutputData>
{
    public UnregisterKensaCenterPartnershipInputData(int hpId, string centerCd, int startDate)
    {
        HpId = hpId;
        CenterCd = centerCd;
        StartDate = startDate;
    }

    public int HpId { get; set; }
    public string CenterCd { get; set; }
    public int StartDate { get; set; }
}

public class UpdateKensaCenterPartnershipMstUpdateDateInputData : IInputData<UpdateKensaCenterPartnershipMstUpdateDateOutputData>
{
    public UpdateKensaCenterPartnershipMstUpdateDateInputData(int hpId)
    {
        HpId = hpId;
    }

    public int HpId { get; private set; }
}