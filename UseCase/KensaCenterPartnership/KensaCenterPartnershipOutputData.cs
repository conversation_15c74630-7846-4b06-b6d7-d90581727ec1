﻿using Domain.Models.KensaCenterPartnership;
using UseCase.Core.Sync.Core;

namespace UseCase.KensaCenterPartnership;

public class GetKensaCenterPartnershipOutputData : IOutputData
{
    public GetKensaCenterPartnershipOutputData(List<KensaCenterPartnershipModel> kensaCenterPartnerships, GetKensaCenterPartnershipStatus status)
    {
        KensaCenterPartnerships = kensaCenterPartnerships;
        Status = status;
    }

    public List<KensaCenterPartnershipModel> KensaCenterPartnerships { get; private set; }

    public GetKensaCenterPartnershipStatus Status { get; private set; }
}

public class RegisterKensaCenterPartnershipOutputData : IOutputData
{
    public RegisterKensaCenterPartnershipOutputData(KensaCenterPartnershipModel kensaCenterPartnership, RegisterKensaCenterPartnershipStatus status)
    {
        KensaCenterPartnership = kensaCenterPartnership;
        Status = status;
    }

    public KensaCenterPartnershipModel KensaCenterPartnership { get; private set; }

    public RegisterKensaCenterPartnershipStatus Status { get; private set; }
}

public class UpdateKensaCenterPartnershipOutputData : IOutputData
{
    public UpdateKensaCenterPartnershipOutputData(KensaCenterPartnershipModel kensaCenterPartnership, UpdateKensaCenterPartnershipStatus status)
    {
        KensaCenterPartnership = kensaCenterPartnership;
        Status = status;
    }

    public KensaCenterPartnershipModel KensaCenterPartnership { get; private set; }

    public UpdateKensaCenterPartnershipStatus Status { get; private set; }
}

public class UnregisterKensaCenterPartnershipOutputData : IOutputData
{
    public UnregisterKensaCenterPartnershipOutputData(UnregisterKensaCenterPartnershipStatus status)
    {
        Status = status;
    }

    public UnregisterKensaCenterPartnershipStatus Status { get; private set; }
}

public class UpdateKensaCenterPartnershipMstUpdateDateOutputData : IOutputData
{
    public UpdateKensaCenterPartnershipMstUpdateDateOutputData(UpdateKensaCenterPartnershipMstUpdateDateStatus status)
    {
        Status = status;
    }

    public UpdateKensaCenterPartnershipMstUpdateDateStatus Status { get; private set; }
}