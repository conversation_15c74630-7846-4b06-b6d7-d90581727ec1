﻿using UseCase.Core.Sync.Core;

namespace UseCase.KensaCenterPartnership;

public interface IGetKensaCenterPartnershipInputPort : IInputPort<GetKensaCenterPartnershipInputData, GetKensaCenterPartnershipOutputData>
{
}

public interface IRegisterKensaCenterPartnershipInputPort : IInputPort<RegisterKensaCenterPartnershipInputData, RegisterKensaCenterPartnershipOutputData>
{
}

public interface IUpdateKensaCenterPartnershipInputPort : IInputPort<UpdateKensaCenterPartnershipInputData, UpdateKensaCenterPartnershipOutputData>
{
}

public interface IUnregisterKensaCenterPartnershipInputPort : IInputPort<UnregisterKensaCenterPartnershipInputData, UnregisterKensaCenterPartnershipOutputData>
{
}

public interface IUpdateKensaCenterPartnershipMstUpdateDateInputPort : IInputPort<UpdateKensaCenterPartnershipMstUpdateDateInputData, UpdateKensaCenterPartnershipMstUpdateDateOutputData>
{
}
