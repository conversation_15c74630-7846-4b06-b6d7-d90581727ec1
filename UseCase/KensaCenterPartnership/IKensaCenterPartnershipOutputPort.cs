﻿using UseCase.Core.Sync.Core;

namespace UseCase.KensaCenterPartnership;

public interface IGetKensaCenterPartnershipOutputPort : IOutputPort<GetKensaCenterPartnershipOutputData>
{
}

public interface IRegisterKensaCenterPartnershipOutputPort : IOutputPort<RegisterKensaCenterPartnershipOutputData>
{
}

public interface IUpdateKensaCenterPartnershipOutputPort : IOutputPort<UpdateKensaCenterPartnershipOutputData>
{
}

public interface IUnregisterKensaCenterPartnershipOutputPort : IOutputPort<UnregisterKensaCenterPartnershipOutputData>
{
}

public interface IUpdateKensaCenterPartnershipMstUpdateDateOutputPort : IOutputPort<UpdateKensaCenterPartnershipMstUpdateDateOutputData>
{
} 
